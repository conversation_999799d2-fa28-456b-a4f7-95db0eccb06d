package com.sgs.ecom.order.dto.export;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;
import lombok.Data;

@Data
public class ExpActivityDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userPhone;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userEmail;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String mainProds;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String salesChannel;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String qualityField;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String promotionCode;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String personCode;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String createDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String stateDate;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String stateShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userName;
	/**
	 * 用户微信
	 */
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userWx;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String industry;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String managerPersonCode;

	public String getManagerPersonCode() {
		return managerPersonCode;
	}

	public void setManagerPersonCode(String managerPersonCode) {
		this.managerPersonCode = managerPersonCode;
	}

	public String getIndustry() {
		return industry;
	}

	public void setIndustry(String industry) {
		this.industry = industry;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getMainProds() {
		return mainProds;
	}

	public void setMainProds(String mainProds) {
		this.mainProds = mainProds;
	}

	public String getSalesChannel() {
		return salesChannel;
	}

	public void setSalesChannel(String salesChannel) {
		this.salesChannel = salesChannel;
	}

	public String getQualityField() {
		return qualityField;
	}

	public void setQualityField(String qualityField) {
		this.qualityField = qualityField;
	}

	public String getPromotionCode() {
		return promotionCode;
	}

	public void setPromotionCode(String promotionCode) {
		this.promotionCode = promotionCode;
	}

	public String getPersonCode() {
		return personCode;
	}

	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getStateShow() {
		return stateShow;
	}

	public void setStateShow(String stateShow) {
		this.stateShow = stateShow;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}
}
