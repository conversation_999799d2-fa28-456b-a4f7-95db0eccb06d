package com.sgs.ecom.order.dto.portal;

import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderReportDTO;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.order.request.portal.*;

import java.util.ArrayList;
import java.util.List;

public class PortalOrderDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private PortalApplicationReq application=new PortalApplicationReq();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private PortalReportReq report=new PortalReportReq();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<PortalSampleReq> sampleList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<PortalItemReq> itemList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private PortalOrderBaseReq orderBase=new PortalOrderBaseReq();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private PortalIdReq portalIdReq=new PortalIdReq();

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public PortalOrderDTO() {
    }

    public PortalOrderDTO(BaseOrderDTO baseOrderDTO, OrderApplicationFormDTO orderApplicationFormDTO, OrderReportDTO orderReportDTO,
                          List<OrderDetailDTO> itemList, List<OrderSampleMoreDTO> sampleDTOList) {
        PortalApplicationReq applicationReq=new PortalApplicationReq();
        baseCopy.copy(applicationReq,orderApplicationFormDTO);
        PortalReportReq reportReq=new PortalReportReq();
        baseCopy.copy(reportReq,orderReportDTO);
        PortalOrderBaseReq portalOrderBaseReq=new PortalOrderBaseReq();
        if(!ValidationUtil.isEmpty(baseOrderDTO)){
            baseCopy.copy(portalOrderBaseReq,baseOrderDTO);
        }
        List<PortalItemReq> itemBaseList=new ArrayList<>();
        for(OrderDetailDTO orderDetailDTO:itemList){
            PortalItemReq portalItemReq=new PortalItemReq();
            baseCopy.copy(portalItemReq,orderDetailDTO);
            itemBaseList.add(portalItemReq);
        }
        List<PortalSampleReq> sampleBaseList=new ArrayList<>();
        for(OrderSampleMoreDTO orderSampleDTO:sampleDTOList){
            PortalSampleReq portalSampleReq=new PortalSampleReq();
            baseCopy.copy(portalSampleReq,orderSampleDTO);
            portalSampleReq.setSampleFromDTOList(orderSampleDTO.getSampleFromDTOList());
            sampleBaseList.add(portalSampleReq);
        }

        this.application=applicationReq;
        this.report=reportReq;
        this.orderBase=portalOrderBaseReq;
        this.itemList=itemBaseList;
        this.sampleList=sampleBaseList;
    }



    public PortalApplicationReq getApplication() {
        return application;
    }

    public void setApplication(PortalApplicationReq application) {
        this.application = application;
    }

    public PortalReportReq getReport() {
        return report;
    }

    public void setReport(PortalReportReq report) {
        this.report = report;
    }

    public List<PortalSampleReq> getSampleList() {
        return sampleList;
    }

    public void setSampleList(List<PortalSampleReq> sampleList) {
        this.sampleList = sampleList;
    }

    public List<PortalItemReq> getItemList() {
        return itemList;
    }

    public void setItemList(List<PortalItemReq> itemList) {
        this.itemList = itemList;
    }

    public PortalOrderBaseReq getOrderBase() {
        return orderBase;
    }

    public void setOrderBase(PortalOrderBaseReq orderBase) {
        this.orderBase = orderBase;
    }

    public PortalIdReq getPortalIdReq() {
        return portalIdReq;
    }

    public void setPortalIdReq(PortalIdReq portalIdReq) {
        this.portalIdReq = portalIdReq;
    }
}
