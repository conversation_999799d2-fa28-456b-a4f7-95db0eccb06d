package com.sgs.ecom.order.dto.platform;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;

import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;

/**
 * <AUTHOR>
 */
public class ExpressToOtherDTO {
    @ApiAnno(groups={BaseOrderFilter.ExpressToOther.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.ExpressToOther.class})
    private String platformOrder;
    @ApiAnno(groups={BaseOrderFilter.ExpressToOther.class})
    private String belongSystem;
    @ApiAnno(groups={BaseOrderFilter.ExpressToOther.class})
    @BeanAnno(dtocls = {OrderExpressDTO.class})
    private OrderExpressDTO deliver;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPlatformOrder() {
        return platformOrder;
    }

    public void setPlatformOrder(String platformOrder) {
        this.platformOrder = platformOrder;
    }

    public String getBelongSystem() {
        return belongSystem;
    }

    public void setBelongSystem(String belongSystem) {
        this.belongSystem = belongSystem;
    }

    public OrderExpressDTO getDeliver() {
        return deliver;
    }

    public void setDeliver(OrderExpressDTO deliver) {
        this.deliver = deliver;
    }
}
