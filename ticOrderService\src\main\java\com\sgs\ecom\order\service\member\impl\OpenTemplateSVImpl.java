package com.sgs.ecom.order.service.member.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationAttrDomainService;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.dml.DmlMainDTO;
import com.sgs.ecom.order.dto.dml.DmlMainReqDTO;
import com.sgs.ecom.order.dto.order.OrderApplicationAttrDTO;
import com.sgs.ecom.order.dto.pay.PayBaseDTO;
import com.sgs.ecom.order.dto.platform.ExpressToOtherDTO;
import com.sgs.ecom.order.dto.platform.OrderToOtherDTO;
import com.sgs.ecom.order.dto.rpc.PayToOtherDTO;
import com.sgs.ecom.order.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.order.enumtool.application.OiqFormEnum;
import com.sgs.ecom.order.request.oiq.SampleCategoryReq;
import com.sgs.ecom.order.service.member.interfaces.IOpenTemplateSV;
import com.sgs.ecom.order.service.rpc.OpenRpcService;
import com.sgs.ecom.order.service.util.interfaces.ICenterService;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.order.OrderToOtherUtil;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.util.select.RpcMapUtil;
import com.sgs.ecom.order.vo.VOOrderExpress;
import com.sgs.ecom.order.vo.VOOrderPay;
import com.sgs.util.json.JsonTransUtil;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;



/**
 * <AUTHOR>
 */
@Service
public class OpenTemplateSVImpl  implements IOpenTemplateSV {
	Logger logger = LoggerFactory.getLogger(OpenTemplateSVImpl.class);

	public static final String FILE_NAME = "fileName";
	public static final String TOKEN = "token";
	public static final String ORDER = "order";
	public static final String HISTORY_ID = "historyId";
	public static final String CONFIRM_AT = "confirmAt";
	public static final String MODIFY_AT = "modifyAt";
	public static final String INT_PLATFORM = "intPlatform";
	public static final String INT_NAME = "intName";
	public static final String ORDER_NO = "orderNo";

	@Resource
	private OpenRpcService openRpcService;
	@Resource
	private JsonTransUtil jsonTransUtil;
	@Resource
	private IOrderUtilService orderUtilService;
	@Resource
	private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;
	@Resource
	private ICenterService centerService;


	@Override
	public void createApplication(DmlMainDTO dmlMainDTO) throws Exception {
		String jsonString = JSON.toJSONString(dmlMainDTO);
		logger.info("createApplication==request==" + jsonString);
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("data",jsonString);
		ResultBody resultBody = openRpcService.createApplication(jsonObject.toString());
		logger.info("createApplication==resp==" + JSON.toJSONString(resultBody));
		if (!RpcMapUtil.ZERO.equals(resultBody.getResultCode())) {
			throw new BusinessException(resultBody);
		}
	}

	@Override
	public void updateApplication(DmlMainDTO dmlMainDTO) {
		String jsonString = JSON.toJSONString(dmlMainDTO);
		logger.info("updateApplication==request==" + jsonString);
		JSONObject jsonObject=new JSONObject();
		jsonObject.put("data",jsonString);
		ResultBody resultBody = openRpcService.updateApplication(jsonObject.toString());
		logger.info("updateApplication==resp==" + JSON.toJSONString(resultBody));
		if (!RpcMapUtil.ZERO.equals(resultBody.getResultCode())) {
			//暂定1天
			/*String key=dmlMainDTO.getOIQNo()+"-"+RedisKeyUtil.UPDATE_FORM;
			redisClient.setValue(key, String.valueOf(System.currentTimeMillis()), 172800);
			throw new BusinessException(ResultEnumCode.UPDATE_FORM_FAIL);*/
			throw new BusinessException(resultBody);
		}
	}





	@Override
	public List<OrderAttachmentDTO> getReport(String orderNo) throws Exception {

		JSONObject jsonObject = new JSONObject();
		jsonObject.put(ORDER, orderNo);
		String jsonString = jsonObject.toString();
		logger.info("getReport==request==" + jsonString);
		ResultBody restBody = openRpcService.getReport(jsonString);
		String json = JSON.toJSONString(restBody);
		logger.info("getReport==resp==" + json);
		Map mapReturn = JSON.parseObject(json, Map.class);
		if (!mapReturn.get(RpcMapUtil.RESULT_CODE).equals(RpcMapUtil.ZERO)) {
			throw new BusinessException(mapReturn.get(RpcMapUtil.RESULT_CODE).toString(), mapReturn.get(RpcMapUtil.RESULT_MSG).toString());
		}
		List<OrderAttachmentDTO> list = new ArrayList<>();
		if (!mapReturn.containsKey("data")) {
			return list;
		}

		Map dataMap = (Map) mapReturn.get("data");

		String listTxt = JSONArray.toJSONString(dataMap.get("reports"));
		List<Map> listMap = JSON.parseArray(listTxt, Map.class);
		if (ValidationUtil.isEmpty(listMap)) {
			return list;
		}
		for (Map map : listMap) {
			if (map.containsKey(FILE_NAME) && map.containsKey(TOKEN)) {
				OrderAttachmentDTO orderAttachmentDTO = new OrderAttachmentDTO();
				orderAttachmentDTO.setFileName(map.get(FILE_NAME).toString());
				orderAttachmentDTO.setFileUrl(map.get(TOKEN).toString());
				list.add(orderAttachmentDTO);
			}
		}
		return list;
	}

	@Override
	public JSONObject qryLastLog(String orderNo) throws Exception {


		JSONObject jsonObject = new JSONObject();
		jsonObject.put(INT_PLATFORM, "RSTS");
		jsonObject.put(INT_NAME, "createOrder");
		jsonObject.put(ORDER_NO, orderNo);
		String jsonString = jsonObject.toString();
		logger.info("qryLastLog==request==" + jsonString);
		JSONObject jsonObject1 = openRpcService.qryLastLog(jsonString);
		logger.info("qryLastLog==resp==" + JSON.toJSONString(jsonObject1));

		return jsonObject1;
	}

	@Override
	public void payToOther(BaseOrderDTO baseOrderDTO, VOOrderPay voOrderPay, String systemCode) throws Exception{
		PayToOtherDTO payToOtherDTO=new PayToOtherDTO();
		payToOtherDTO.setOrderNo(baseOrderDTO.getOrderNo());
		payToOtherDTO.setPlatformOrder(baseOrderDTO.getPlatformOrder());
		payToOtherDTO.setPayState(baseOrderDTO.getPayState());
		PayBaseDTO orderPay=new PayBaseDTO();
		BaseCopyObj baseCopyObj=new BaseCopyObj();
		baseCopyObj.copyWithNull(orderPay,voOrderPay);
		if(StringUtils.isBlank(orderPay.getPayDate())){
			orderPay.setPayDate(UseDateUtil.getDateString(new Date()));
		}
		payToOtherDTO.setOrderPay(orderPay);
		payToOtherDTO.setBelongSystem(systemCode);
		net.sf.json.JSONObject jsonObject=jsonTransUtil.transBoToJson(PayToOtherDTO.class, payToOtherDTO, BaseOrderFilter.PayToOther.class);
		logger.info("payToOther==request==" +JSON.toJSONString(jsonObject));
		ResultBody resultBody = openRpcService.payToOther(JSON.toJSONString(jsonObject));
		logger.info("payToOther==resp==" + JSON.toJSONString(resultBody));
		if (!RpcMapUtil.ZERO.equals(resultBody.getResultCode())) {
			throw new BusinessException(resultBody);
		}
	}

	@Override
	public void saveOrderToOther(DmlMainReqDTO dmlMainReqDTO, String systemCode) throws Exception{
		OrderToOtherDTO orderToOtherDTO= OrderToOtherUtil.mainDTOToOtherDTO(dmlMainReqDTO);
		SampleCategoryReq sampleCategoryReq=dmlMainReqDTO.getSampleCategoryReq();
		if(!ValidationUtil.isEmpty(sampleCategoryReq)){
			SampleCategoryDTO sampleCategoryDTO=new SampleCategoryDTO();
			sampleCategoryDTO.setSampleName(sampleCategoryReq.getSampleName());
			sampleCategoryDTO.setSampleCategoryCode(sampleCategoryReq.getSampleCategoryCode());
			sampleCategoryDTO.setSampleShapeCode(sampleCategoryReq.getSampleShapeCode());
			Map<String, OrderApplicationAttrDTO> attrDTOMap = orderApplicationAttrDomainService.getAttrDTOMap(dmlMainReqDTO.getOrderNo());
			String businessCode = "";
			if (!ValidationUtil.isEmpty(attrDTOMap.get(OiqFormEnum.SAMPLE_BUSINESS_CODE.getName()))) {
				businessCode = attrDTOMap.get(OiqFormEnum.SAMPLE_BUSINESS_CODE.getName()).getAttrValue();
			}
			JSONObject jsonObject = centerService.qrySampleCategoryByCode(
				dmlMainReqDTO.getBaseOrderDTO().getBu(),
				dmlMainReqDTO.getBaseOrderDTO().getApplicationLineId(),
				sampleCategoryReq.getSampleCategoryCode());
			if (!ValidationUtil.isEmpty(jsonObject)) {
				sampleCategoryDTO.setSampleCategoryName(jsonObject.optString("categoryName"));
				sampleCategoryDTO.setSampleCategoryRemark(jsonObject.optString("externalId"));
			}
			sampleCategoryDTO.setSampleShapeName(orderUtilService.getEnumStringByKey(RedisKeyUtil.MIN_SAMPLE_SHAPE + businessCode,sampleCategoryDTO.getSampleShapeCode()));
			orderToOtherDTO.setSampleCategory(sampleCategoryDTO);
		}
		orderToOtherDTO.setBelongSystem(systemCode);
		net.sf.json.JSONObject jsonObject=jsonTransUtil.transBoToJson(OrderToOtherDTO.class, orderToOtherDTO,BaseOrderFilter.OrderToOther.class);
		logger.info("saveOrderToOther==request==" +JSON.toJSONString(jsonObject));
		ResultBody resultBody = openRpcService.saveOrderToOther(JSON.toJSONString(jsonObject));
		logger.info("saveOrderToOther==resp==" + JSON.toJSONString(resultBody));
		if (!RpcMapUtil.ZERO.equals(resultBody.getResultCode())) {
			throw new BusinessException(resultBody);
		}
	}

	/**
	* 物流推送
	* @param baseOrderDTO
	* @param voOrderExpress
	* @param systemCode
	* @return void
	* <AUTHOR> || created at 2025/1/14 16:26
	* @throws Exception 抛出错误
	*/
	@Override
	public void sendExpressToOther(BaseOrderDTO baseOrderDTO, VOOrderExpress voOrderExpress, String systemCode) throws Exception  {
		ExpressToOtherDTO expressToOtherDTO=new ExpressToOtherDTO();
		expressToOtherDTO.setOrderNo(baseOrderDTO.getOrderNo());
		expressToOtherDTO.setBelongSystem(systemCode);
		expressToOtherDTO.setPlatformOrder(baseOrderDTO.getPlatformOrder());
		OrderExpressDTO orderExpress=new OrderExpressDTO();
		BaseCopyObj baseCopyObj=new BaseCopyObj();
		baseCopyObj.copyWithNull(orderExpress,voOrderExpress);
		expressToOtherDTO.setDeliver(orderExpress);

		net.sf.json.JSONObject jsonObject=jsonTransUtil.transBoToJson(ExpressToOtherDTO.class, expressToOtherDTO,BaseOrderFilter.ExpressToOther.class);
		logger.info("expressToOther==request==" +JSON.toJSONString(jsonObject));
		ResultBody resultBody = openRpcService.sendExpressToOther(JSON.toJSONString(jsonObject));
		logger.info("expressToOther==resp==" + JSON.toJSONString(resultBody));
		if (!RpcMapUtil.ZERO.equals(resultBody.getResultCode())) {
			throw new BusinessException(resultBody);
		}

	}
}
