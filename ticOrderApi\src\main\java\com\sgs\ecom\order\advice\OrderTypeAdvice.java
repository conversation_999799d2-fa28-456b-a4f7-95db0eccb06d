package com.sgs.ecom.order.advice;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderProductDomainService;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.dto.permission.OrderPermissionDTO;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.service.custom.interfaces.ICustomLimitService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.collection.MapUtil;
import com.sgs.ecom.order.util.order.OrderPermissionUtil;
import com.sgs.ecom.order.util.select.SelectBaseUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@Aspect
@Component
public class OrderTypeAdvice extends ControllerUtil {

	@Autowired
	private IOrderBaseInfoService orderBaseInfoService;
	@Autowired
	private ICustomLimitService customLimitService;
	@Autowired
	private OrderPermissionUtil orderPermissionUtil;
	@Resource
	private IOrderProductDomainService orderProductDomainService;

	@Pointcut("@annotation(com.sgs.ecom.order.advice.CheckType)")
	@Order(10)
	public void checkOrderType() {
	}

	@Before("checkOrderType()")
	public void doBefore(JoinPoint joinPoint) throws Exception {

		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		//第一个为token 第2个为对象 对象包含orderId
		Object[] args = joinPoint.getArgs();
		//当小于两个对象的时候不处理
		if (args.length < 2 && args[1] instanceof java.lang.String) {
			return;
		}
		Map<String, Object> map = MapUtil.objectToMap(args[1]);
		if (!map.containsKey(SelectBaseUtil.ORDER_ID)) {
			return;
		}
		String order=map.get(SelectBaseUtil.ORDER_ID).toString();
		if(order.contains(",")){
			return;
		}

		Long orderId = Long.parseLong(map.get(SelectBaseUtil.ORDER_ID).toString());
		OrderPermissionDTO orderPermissionDTO = orderBaseInfoService.selectPermission(orderId);
		if (ValidationUtil.isEmpty(orderPermissionDTO)) {
			throw new BusinessException(ResultEnumCode.ORDER_EDIT_UNABLE);
		}
		List<OrderPermissionDTO> list  = orderBaseInfoService.selectPermissionByRelateOrderNo(orderPermissionDTO.getOrderNo());
		orderPermissionDTO.setList(list);
		orderPermissionDTO.setBoSysPerson(getPersonInfo(request.getHeader("accessToken")));
		PrivilegeLevelDTO power = getPower(request.getHeader("accessToken"), request.getHeader("system"));
		orderPermissionDTO.setPrivilegeLevelDTO(power);
		Boolean flg = orderPermissionUtil.checkOrderPermission(orderPermissionDTO);
		if (!flg) {
			throw new BusinessException(ResultEnumCode.ORDER_EDIT_UNABLE);
		}
		//当是ro的情况下 需要额外处理
		if(String.valueOf(orderPermissionDTO.getOrderType()).equals(OrderTypeEnum.RSTS_ORDER.getIndex()) && !ValidationUtil.isEmpty(orderPermissionDTO.getRoPermission())){
			Long n=customLimitService.selectRoPermission(orderPermissionDTO.getRoPermission());
			if(n==0L){
				throw new BusinessException(ResultEnumCode.ORDER_EDIT_UNABLE);
			}
		}

	}


}
