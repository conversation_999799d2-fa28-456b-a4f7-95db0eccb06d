package com.sgs.ecom.order.controller.user;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.service.user.interfaces.IUserLabelSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOUserLabel;

@RestController
@RequestMapping("/business/api.v1.user/label")
public class UserLabelController extends ControllerUtil {

	@Resource
	private IUserLabelSV userLabelSV;
	
    /**   
	* @Function: setUserLabel
	* @Description: 查询用户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "set", method = { RequestMethod.POST })
    public ResultBody setUserLabel(@RequestBody VOUserLabel userLabel) throws Exception {
    	userLabelSV.setUserLabel(userLabel);
		return ResultBody.success();
	}
    
}
