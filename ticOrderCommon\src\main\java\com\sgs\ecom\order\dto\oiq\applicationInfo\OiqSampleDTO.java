package com.sgs.ecom.order.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.order.util.collection.StrUtil;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OiqSampleDTO {

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleName;
    //oiq使用
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String sampleNo;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleNameCn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleNameEn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Long sampleId;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderSampleFromDTO.class})
    private List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String row;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleNameStr;
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OrderToOther.class})
    @BeanAnno(dtocls = {OrderSampleFromDTO.class})
    private List<OrderSampleFromDTO> sampleAttr=new ArrayList<>();

    private String sampleShapeCode;

    private String sampleCategoryCode;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Map<String,Object> sampleFromDTOMap;


    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public List<OrderSampleFromDTO> getSampleFromDTOList() {
        return sampleFromDTOList;
    }

    public void setSampleFromDTOList(List<OrderSampleFromDTO> sampleFromDTOList) {
        this.sampleFromDTOList = sampleFromDTOList;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public Long getSampleId() {
        return sampleId;
    }

    public void setSampleId(Long sampleId) {
        this.sampleId = sampleId;
    }

    public String getRow() {
        return row;
    }

    public void setRow(String row) {
        this.row = row;
    }

    public String getSampleNameCn() {
        return sampleNameCn;
    }

    public void setSampleNameCn(String sampleNameCn) {
        this.sampleNameCn = sampleNameCn;
    }

    public String getSampleNameEn() {
        return sampleNameEn;
    }

    public void setSampleNameEn(String sampleNameEn) {
        this.sampleNameEn = sampleNameEn;
    }

    public String getSampleNameStr() {
        return StrUtil.strAddStr(sampleNameCn,sampleNameEn,'|');
    }

    public void setSampleNameStr(String sampleNameStr) {
        this.sampleNameStr = sampleNameStr;
    }

    public String getSampleShapeCode() {
        return sampleShapeCode;
    }

    public void setSampleShapeCode(String sampleShapeCode) {
        this.sampleShapeCode = sampleShapeCode;
    }

    public String getSampleCategoryCode() {
        return sampleCategoryCode;
    }

    public void setSampleCategoryCode(String sampleCategoryCode) {
        this.sampleCategoryCode = sampleCategoryCode;
    }

    public Map<String,Object> getSampleFromDTOMap() {
        if(ValidationUtil.isEmpty(sampleFromDTOList)){
            return new HashMap();
        }
        return sampleFromDTOList.stream().collect(Collectors.toMap(OrderSampleFromDTO::getSampleKey, OrderSampleFromDTO::getSampleValue, (key1, key2) -> key2));
    }

    public List<OrderSampleFromDTO> getSampleAttr() {
        return sampleAttr;
    }

    public void setSampleAttr(List<OrderSampleFromDTO> sampleAttr) {
        this.sampleAttr = sampleAttr;
    }
}
