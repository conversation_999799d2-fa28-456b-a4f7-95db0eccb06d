package com.sgs.ecom.order.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno; 

public class UserInfoDTO{
 
 	public static final String CREATE_SQL = "select USER_NICK,LEVEL_NAME,USER_EMAIL,STATE_DATE,USER_ID,STATE,REG_IP,USER_NAME,U_ID,LEVEL_ID,USER_PHONE,CREATE_DATE,BBC_PWD,USER_PWD from USER_INFO"; 
 
 
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="USER_NICK", getName="getUserNick", setName="setUserNick")
 	private String userNick;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="LEVEL_NAME", getName="getLevelName", setName="setLevelName")
 	private String levelName;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="USER_EMAIL", getName="getUserEmail", setName="setUserEmail")
 	private String userEmail;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@IsNullAnno(serviceName={"modUser"})
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private long state;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="REG_IP", getName="getRegIp", setName="setRegIp")
 	private String regIp;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@IsNullAnno(serviceName={"qryCustByPaycode","syncUserCompany"})
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="U_ID", getName="getUId", setName="setUId")
 	private long uId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="LEVEL_ID", getName="getLevelId", setName="setLevelId")
 	private long levelId;
 	@ApiAnno(serviceName={"qryBill","qryUser"})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="BBC_PWD", getName="getBbcPwd", setName="setBbcPwd")
 	private String bbcPwd;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="USER_PWD", getName="getUserPwd", setName="setUserPwd")
 	private String userPwd;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="ADDRESS", getName="getAddress", setName="setAddress")
 	private String address;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CUST_NAME", getName="getCustName", setName="setCustName")
 	private String custName;
 	@ApiAnno(serviceName={"qryUser"})
 	@BeanAnno(value="COMPANY_ADDR_EN", getName="getCompanyAddrEn", setName="setCompanyAddrEn")
 	private String companyAddrEn;
 	
 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	public void setLevelName(String levelName){
 		 this.levelName=levelName;
 	}
 	public String getLevelName(){
 		 return this.levelName;
 	}
 
 	 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	public void setRegIp(String regIp){
 		 this.regIp=regIp;
 	}
 	public String getRegIp(){
 		 return this.regIp;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setUId(long uId){
 		 this.uId=uId;
 	}
 	public long getUId(){
 		 return this.uId;
 	}
 
 	 
 	public void setLevelId(long levelId){
 		 this.levelId=levelId;
 	}
 	public long getLevelId(){
 		 return this.levelId;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBbcPwd(String bbcPwd){
 		 this.bbcPwd=bbcPwd;
 	}
 	public String getBbcPwd(){
 		 return this.bbcPwd;
 	}
 
 	 
 	public void setUserPwd(String userPwd){
 		 this.userPwd=userPwd;
 	}
 	public String getUserPwd(){
 		 return this.userPwd;
 	}
	public long getuId() {
		return uId;
	}
	public void setuId(long uId) {
		this.uId = uId;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyNameEn() {
		return companyNameEn;
	}
	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}
	public String getProvice() {
		return provice;
	}
	public void setProvice(String provice) {
		this.provice = provice;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getTown() {
		return town;
	}
	public void setTown(String town) {
		this.town = town;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCustCode() {
		return custCode;
	}
	public void setCustCode(String custCode) {
		this.custCode = custCode;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getCompanyAddrEn() {
		return companyAddrEn;
	}
	public void setCompanyAddrEn(String companyAddrEn) {
		this.companyAddrEn = companyAddrEn;
	}
 
 	 
}