package com.sgs.ecom.order.bill.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.order.bill.service.interfaces.IBillDtlSV;
import com.sgs.ecom.order.util.ResultBody;

//@RestController
//@RequestMapping("/business/api.v1.bill/billdtl")
public class BillDtlAction extends BaseAction {

	private static IBillDtlSV billDtlSV = CollectionService.getService(IBillDtlSV.class);
	
	/**   
	* @Function: qryBillDtl
	* @Description: 查询账单订单列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qry", method = { RequestMethod.POST })
    public ResultBody qryBillDtl(@RequestBody String data) throws Exception {
    	return ResultBody.newInstance(billDtlSV.qryBillDtl(data));
	}
    
    /**   
	* @Function: confirmTicket
	* @Description: 订单出票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "ticket", method = { RequestMethod.POST })
    public ResultBody confirmTicket(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlSV.confirmTicket(data, getUserInfo(token));
    	return ResultBody.success();
	}
    
    /**   
	* @Function: confirmPay
	* @Description: 订单支付
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(method = {RequestMethod.POST} , value = "pay")
    public ResultBody confirmPay(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlSV.confirmPay(data, getUserInfo(token));
    	return ResultBody.success();
	}
    
    /**   
	* @Function: ticketByBill
	* @Description: 根据账单出票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(method = {RequestMethod.POST} , value = "ticketByBill")
    public ResultBody ticketByBill(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlSV.ticketByBill(data, getUserInfo(token));
    	return ResultBody.success();
	}
    
    /**   
	* @Function: payByBill
	* @Description: 根据账单支付
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(method = {RequestMethod.POST} , value = "payByBill")
    public ResultBody payByBill(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlSV.payByBill(data, getUserInfo(token));
    	return ResultBody.success();
	}
}
