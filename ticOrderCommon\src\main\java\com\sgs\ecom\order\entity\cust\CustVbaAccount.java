package com.sgs.ecom.order.entity.cust;

import java.util.Date;

public class CustVbaAccount {
    private Long accountId;

    private Long custId;

    private String labCode;

    private String vbaAccountNo;

    private Date createDate;

    private Date stateDate;

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode == null ? null : labCode.trim();
    }

    public String getVbaAccountNo() {
        return vbaAccountNo;
    }

    public void setVbaAccountNo(String vbaAccountNo) {
        this.vbaAccountNo = vbaAccountNo == null ? null : vbaAccountNo.trim();
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getStateDate() {
        return stateDate;
    }

    public void setStateDate(Date stateDate) {
        this.stateDate = stateDate;
    }
}