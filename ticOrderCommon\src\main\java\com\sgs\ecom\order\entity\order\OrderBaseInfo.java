package com.sgs.ecom.order.entity.order;

import javax.persistence.Id;
import java.math.BigDecimal;

public class OrderBaseInfo {

    private Integer userSex;
    private String userEmail;
    private String payDate;
    private Long userId;
    private String recommendReason;
    private Integer hisState;
    private BigDecimal urgentAmount;
    private Integer isDelete;
    private String platform;

    private Long lineId;

    private Long applicationLineId;

    private Integer payState;

    private String sampleRequirements;

    private String businessLine;

    private String offerDate;

    private BigDecimal realAmount;

    private String relateOrderNo;

    private Integer isPayReceived;

    private Integer isRead;

    private String stateDate;

    private String tmpGroupNo;

    private Integer isInvoice;

    private String productName;

    private Integer isTest;

    private String companyName;

    private String reportForm;

    private String createDate;

    private Integer subState;

    private String csName;

    private String reportLuaCode;

    private String reportFormCode;

    private Integer state;

    private String orderNo;

    private String csBranch;

    private String csPhone;

    private String csEmail;

    private String businessPersonEmail;

    private String productImg;

    private Long labId;

    private String labName;

    private Integer isUrgent;

    private String bu;

    private String reportLua;

    private BigDecimal testCycle;

    private Long questionId;

    private String csCode;

    private Integer orderType;

    private Integer isRemind;

    private String csNameEn;

    private String userName;

    private String platformOrder;
    private String createCode;

    private BigDecimal orderAmount;

    private String userPhone;
    @Id
    private Long orderId;


    private String city;

    private String groupNo;

    private Integer totalNums;

    private String categoryPath;

    private String auditCode;

    private Long catagoryId;

    private BigDecimal discountAmount;

    private String province;

    private String orderExpDate;

    private Integer proState;

    private BigDecimal serviceAmount;
    private String confirmOrderDate;


    private String accountNo;
    private String orderSource;

    private String closeCode;
    private String closeReason;
    private Integer testLabel;
    private String lastResponseDate;
    private String orderSourceFrom;
    private Integer refundState;
    private Integer payMethod;
    private String operatorSource;

    private String salesCode;
    private String salesPhone;
    private String promoInfo;
    private String fromSource;
    private String fromUrl;
    private String bossNo;
    private Long custId;
    private Integer monthPay;
    private BigDecimal shopDisAmount;//店铺优惠金额
    private String abstractCustcode;
    private String currency;
    private BigDecimal exchangeRate;

    private String companyAddressEn;
    private String companyAddressCn;
    private String companyNameEn;
    private String town;



    private Integer isBill;

    private String salesEmail;


    private String testCycleMemo;

    private String recommendReasonImage;

    private String leadsCode;


    private String operatorCode;

    private String applySubmitDate;



    private String deadlineTime;

    private Integer noticeNum;


    private Integer isFinish;
    private String slimNo;

    private BigDecimal platformAmount;

    private BigDecimal taxRates;


    private String outOrderNo;


    private Integer isElectron; //是否是上海开票显示电子 0-否 1-是

    private BigDecimal csDiscountAmount;

    private String urgentName;//加急选项

    public String getUrgentName() {
        return urgentName;
    }

    public void setUrgentName(String urgentName) {
        this.urgentName = urgentName;
    }

    public BigDecimal getCsDiscountAmount() {
        return csDiscountAmount;
    }

    public void setCsDiscountAmount(BigDecimal csDiscountAmount) {
        this.csDiscountAmount = csDiscountAmount;
    }

    public Integer getIsElectron() {
        return isElectron;
    }

    public void setIsElectron(Integer isElectron) {
        this.isElectron = isElectron;
    }



    public String getFromUrl() {
        return fromUrl;
    }

    public void setFromUrl(String fromUrl) {
        this.fromUrl = fromUrl;
    }



    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }



    public Integer getUserSex() {
        return userSex;
    }

    public void setUserSex(Integer userSex) {
        this.userSex = userSex;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public Integer getHisState() {
        return hisState;
    }

    public void setHisState(Integer hisState) {
        this.hisState = hisState;
    }

    public BigDecimal getUrgentAmount() {
        return urgentAmount;
    }

    public void setUrgentAmount(BigDecimal urgentAmount) {
        this.urgentAmount = urgentAmount;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public Long getApplicationLineId() {
        return applicationLineId;
    }

    public void setApplicationLineId(Long applicationLineId) {
        this.applicationLineId = applicationLineId;
    }

    public Integer getPayState() {
        return payState;
    }

    public void setPayState(Integer payState) {
        this.payState = payState;
    }

    public String getSampleRequirements() {
        return sampleRequirements;
    }

    public void setSampleRequirements(String sampleRequirements) {
        this.sampleRequirements = sampleRequirements;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getOfferDate() {
        return offerDate;
    }

    public void setOfferDate(String offerDate) {
        this.offerDate = offerDate;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public Integer getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(Integer isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public Integer getIsRead() {
        return isRead;
    }

    public void setIsRead(Integer isRead) {
        this.isRead = isRead;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public String getTmpGroupNo() {
        return tmpGroupNo;
    }

    public void setTmpGroupNo(String tmpGroupNo) {
        this.tmpGroupNo = tmpGroupNo;
    }

    public Integer getIsInvoice() {
        return isInvoice;
    }

    public void setIsInvoice(Integer isInvoice) {
        this.isInvoice = isInvoice;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getIsTest() {
        return isTest;
    }

    public void setIsTest(Integer isTest) {
        this.isTest = isTest;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getReportForm() {
        return reportForm;
    }

    public void setReportForm(String reportForm) {
        this.reportForm = reportForm;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Integer getSubState() {
        return subState;
    }

    public void setSubState(Integer subState) {
        this.subState = subState;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public String getReportLuaCode() {
        return reportLuaCode;
    }

    public void setReportLuaCode(String reportLuaCode) {
        this.reportLuaCode = reportLuaCode;
    }

    public String getReportFormCode() {
        return reportFormCode;
    }

    public void setReportFormCode(String reportFormCode) {
        this.reportFormCode = reportFormCode;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCsBranch() {
        return csBranch;
    }

    public void setCsBranch(String csBranch) {
        this.csBranch = csBranch;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getProductImg() {
        return productImg;
    }

    public void setProductImg(String productImg) {
        this.productImg = productImg;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public Integer getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(Integer isUrgent) {
        this.isUrgent = isUrgent;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getReportLua() {
        return reportLua;
    }

    public void setReportLua(String reportLua) {
        this.reportLua = reportLua;
    }

    public BigDecimal getTestCycle() {
        return testCycle;
    }

    public void setTestCycle(BigDecimal testCycle) {
        this.testCycle = testCycle;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public Integer getIsRemind() {
        return isRemind;
    }

    public void setIsRemind(Integer isRemind) {
        this.isRemind = isRemind;
    }

    public String getCsNameEn() {
        return csNameEn;
    }

    public void setCsNameEn(String csNameEn) {
        this.csNameEn = csNameEn;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPlatformOrder() {
        return platformOrder;
    }

    public void setPlatformOrder(String platformOrder) {
        this.platformOrder = platformOrder;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Integer getTotalNums() {
        return totalNums;
    }

    public void setTotalNums(Integer totalNums) {
        this.totalNums = totalNums;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public void setAuditCode(String auditCode) {
        this.auditCode = auditCode;
    }

    public Long getCatagoryId() {
        return catagoryId;
    }

    public void setCatagoryId(Long catagoryId) {
        this.catagoryId = catagoryId;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getOrderExpDate() {
        return orderExpDate;
    }

    public void setOrderExpDate(String orderExpDate) {
        this.orderExpDate = orderExpDate;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public String getTestCycleMemo() {
        return testCycleMemo;
    }

    public void setTestCycleMemo(String testCycleMemo) {
        this.testCycleMemo = testCycleMemo;
    }

    public String getLeadsCode() {
        return leadsCode;
    }

    public void setLeadsCode(String leadsCode) {
        this.leadsCode = leadsCode;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getApplySubmitDate() {
        return applySubmitDate;
    }

    public void setApplySubmitDate(String applySubmitDate) {
        this.applySubmitDate = applySubmitDate;
    }

    public String getRecommendReasonImage() {
        return recommendReasonImage;
    }

    public void setRecommendReasonImage(String recommendReasonImage) {
        this.recommendReasonImage = recommendReasonImage;
    }

    public Integer getTestLabel() {
        return testLabel;
    }

    public void setTestLabel(Integer testLabel) {
        this.testLabel = testLabel;
    }

    public String getCloseCode() {
        return closeCode;
    }

    public void setCloseCode(String closeCode) {
        this.closeCode = closeCode;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public String getDeadlineTime() {
        return deadlineTime;
    }

    public void setDeadlineTime(String deadlineTime) {
        this.deadlineTime = deadlineTime;
    }

    public Integer getNoticeNum() {
        return noticeNum;
    }

    public void setNoticeNum(Integer noticeNum) {
        this.noticeNum = noticeNum;
    }

    public Integer getRefundState() {
        return refundState;
    }

    public void setRefundState(Integer refundState) {
        this.refundState = refundState;
    }

    public Integer getIsFinish() {
        return isFinish;
    }

    public void setIsFinish(Integer isFinish) {
        this.isFinish = isFinish;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getSlimNo() {
        return slimNo;
    }

    public void setSlimNo(String slimNo) {
        this.slimNo = slimNo;
    }



    public BigDecimal getPlatformAmount() {
        return platformAmount;
    }

    public void setPlatformAmount(BigDecimal platformAmount) {
        this.platformAmount = platformAmount;
    }

    public Integer getIsBill() {
        return isBill;
    }

    public void setIsBill(Integer isBill) {
        this.isBill = isBill;
    }

    public String getAbstractCustcode() {
        return abstractCustcode;
    }

    public void setAbstractCustcode(String abstractCustcode) {
        this.abstractCustcode = abstractCustcode;
    }

    public Integer getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(Integer monthPay) {
        this.monthPay = monthPay;
    }

    public String getLastResponseDate() {
        return lastResponseDate;
    }

    public void setLastResponseDate(String lastResponseDate) {
        this.lastResponseDate = lastResponseDate;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public BigDecimal getShopDisAmount() {
        return shopDisAmount;
    }

    public void setShopDisAmount(BigDecimal shopDisAmount) {
        this.shopDisAmount = shopDisAmount;
    }

    public String getCompanyAddressEn() {
        return companyAddressEn;
    }

    public void setCompanyAddressEn(String companyAddressEn) {
        this.companyAddressEn = companyAddressEn;
    }

    public String getCompanyAddressCn() {
        return companyAddressCn;
    }

    public void setCompanyAddressCn(String companyAddressCn) {
        this.companyAddressCn = companyAddressCn;
    }

    public String getCompanyNameEn() {
        return companyNameEn;
    }

    public void setCompanyNameEn(String companyNameEn) {
        this.companyNameEn = companyNameEn;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public Integer getProState() {
        return proState;
    }

    public void setProState(Integer proState) {
        this.proState = proState;
    }

    public String getConfirmOrderDate() {
        return confirmOrderDate;
    }

    public void setConfirmOrderDate(String confirmOrderDate) {
        this.confirmOrderDate = confirmOrderDate;
    }

    public String getOrderSourceFrom() {
        return orderSourceFrom;
    }

    public void setOrderSourceFrom(String orderSourceFrom) {
        this.orderSourceFrom = orderSourceFrom;
    }

    public String getOperatorSource() {
        return operatorSource;
    }

    public void setOperatorSource(String operatorSource) {
        this.operatorSource = operatorSource;
    }

    public String getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(String salesCode) {
        this.salesCode = salesCode;
    }

    public String getSalesPhone() {
        return salesPhone;
    }

    public void setSalesPhone(String salesPhone) {
        this.salesPhone = salesPhone;
    }

    public String getPromoInfo() {
        return promoInfo;
    }

    public void setPromoInfo(String promoInfo) {
        this.promoInfo = promoInfo;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getBossNo() {
        return bossNo;
    }

    public void setBossNo(String bossNo) {
        this.bossNo = bossNo;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getBusinessPersonEmail() {
        return businessPersonEmail;
    }

    public void setBusinessPersonEmail(String businessPersonEmail) {
        this.businessPersonEmail = businessPersonEmail;
    }



    public String getCsPhone() {
        return csPhone;
    }

    public void setCsPhone(String csPhone) {
        this.csPhone = csPhone;
    }

    public String getCreateCode() {
        return createCode;
    }
    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }
    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getSalesEmail() {
        return salesEmail;
    }

    public void setSalesEmail(String salesEmail) {
        this.salesEmail = salesEmail;
    }

    public BigDecimal getTaxRates() {
        return taxRates;
    }

    public void setTaxRates(BigDecimal taxRates) {
        this.taxRates = taxRates;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }
}
