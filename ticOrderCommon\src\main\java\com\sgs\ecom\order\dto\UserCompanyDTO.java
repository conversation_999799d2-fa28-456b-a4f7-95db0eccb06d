package com.sgs.ecom.order.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 

public class UserCompanyDTO{
 
 	public static final String CREATE_SQL = "select COMPANY_NAME,CREATE_DATE,PROVICE,COUNTRY,CITY,TOWN,ADDRESS,STATE_DATE,USER_ID,STATE,COMPANY_ID,COMPANY_NAME_EN from USER_COMPANY"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COUNTRY", getName="getCountry", setName="setCountry")
 	private String country;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="ADDRESS", getName="getAddress", setName="setAddress")
 	private String address;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COMPANY_ID", getName="getCompanyId", setName="setCompanyId")
 	private long companyId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;

 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setCompanyId(long companyId){
 		 this.companyId=companyId;
 	}
 	public long getCompanyId(){
 		 return this.companyId;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
}