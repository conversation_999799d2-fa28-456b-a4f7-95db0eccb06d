package com.sgs.ecom.order.entity.order;

import java.util.Date;

public class OrderSampleFrom {

    public static  final String SAMPLE_NAME="SAMPLE_NAME";
    public static  final String PRODUCT_INFO="PRODUCT_INFO";
    public static  final String PRODUCT_BATCH="PRODUCT_BATCH";
    public static  final String PRODUCTER_NAME="PRODUCTER_NAME";
    public static  final String MATERIAL_GRADE="MATERIAL_GRADE";
    public static  final String REMARK="REMARK";
    public static  final String BUYERS_NAME="BUYERS_NAME";
    public static  final String SUPPLIER_NAME="SUPPLIER_NAME";




    private Long formId;

    private String orderNo;

    private String sampleNo;

    private String groupNo;

    private String sampleKeyName;

    private String sampleKey;

    private String sampleValue;

    private int state;

    private Date createDate;

    private Date stateDate;

    private int sortShow;

    private Integer isReportShow;

    private String  remark;
    private String  lua;
    private String sampleGroup;

    private String remarkEnumConfig;

    private String enumConfig;
    public String getLua() {
        return lua;
    }

    public void setLua(String lua) {
        this.lua = lua;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getIsReportShow() {
        return isReportShow;
    }

    public void setIsReportShow(Integer isReportShow) {
        this.isReportShow = isReportShow;
    }

    public Long getFormId() {
        return formId;
    }

    public void setFormId(Long formId) {
        this.formId = formId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo == null ? null : sampleNo.trim();
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo == null ? null : groupNo.trim();
    }

    public String getSampleKeyName() {
        return sampleKeyName;
    }

    public void setSampleKeyName(String sampleKeyName) {
        this.sampleKeyName = sampleKeyName == null ? null : sampleKeyName.trim();
    }

    public String getSampleKey() {
        return sampleKey;
    }

    public void setSampleKey(String sampleKey) {
        this.sampleKey = sampleKey == null ? null : sampleKey.trim();
    }

    public String getSampleValue() {
        return sampleValue;
    }

    public void setSampleValue(String sampleValue) {
        this.sampleValue = sampleValue == null ? null : sampleValue.trim();
    }



    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getStateDate() {
        return stateDate;
    }

    public void setStateDate(Date stateDate) {
        this.stateDate = stateDate;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getSortShow() {
        return sortShow;
    }

    public void setSortShow(int sortShow) {
        this.sortShow = sortShow;
    }

    public String getSampleGroup() {
        return sampleGroup;
    }

    public void setSampleGroup(String sampleGroup) {
        this.sampleGroup = sampleGroup;
    }

    public String getRemarkEnumConfig() {
        return remarkEnumConfig;
    }

    public void setRemarkEnumConfig(String remarkEnumConfig) {
        this.remarkEnumConfig = remarkEnumConfig;
    }

    public String getEnumConfig() {
        return enumConfig;
    }

    public void setEnumConfig(String enumConfig) {
        this.enumConfig = enumConfig;
    }
}