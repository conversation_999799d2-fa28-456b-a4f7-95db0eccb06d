package com.sgs.ecom.order.dto.order;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;

public class OrderLockDTO {
    public static final String DELETE_FLG = "deleteFlg";
    public static final String PAY_FLG = "payFlg";
    public static final String MONTH_PAY_FLG = "monthPayFlg";
    public static final String RECEIVED_FLG = "receivedFlg";
    public static final String IS_TEST_FLG = "isTestFlg";


    private Long orderId;
    private int oldState;
    private int newState;
    private String closeCode;
    private String closeReason;
    private String payDate;

    private String deleteFlg;
    private int oldDelete;
    private int newDelete;

    private String payFlg;
    private int oldPayState;
    private int newPayState;

    private String monthPayFlg;
    private int oldMonthPay;
    private int newMonthPay;

    private String isPayReceivedFlg;
    private int oldReceived;
    private int newReceived;

    private String isTestFlg;
    private int oldIsTest;
    private int newIsTest;

    private Integer payMethod;

    private int payMethodIsNull;
    private int payDateIsNull;

    public OrderLockDTO() {
    }
    /**审核线下支付使用 这个业务场景不变主状态 加锁是 判断依据以支付状态*/
    public OrderLockDTO(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) {
        this.orderId = orderBaseInfoCheckDTO.getOrderId();
        this.oldState = orderBaseInfoCheckDTO.getState();
        this.newState = orderBaseInfoCheckDTO.getState();
        this.payFlg =OrderLockDTO.PAY_FLG;
        this.oldPayState = orderBaseInfoCheckDTO.getPayState();
    }

    public OrderLockDTO(BaseOrderDTO baseOrderDTO) {
        this.orderId = baseOrderDTO.getOrderId();
        this.oldState = baseOrderDTO.getState();
        this.newState = baseOrderDTO.getState();
        this.payFlg =OrderLockDTO.PAY_FLG;
        this.oldPayState = baseOrderDTO.getPayState();
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public int getOldState() {
        return oldState;
    }

    public void setOldState(int oldState) {
        this.oldState = oldState;
    }

    public int getNewState() {
        return newState;
    }

    public void setNewState(int newState) {
        this.newState = newState;
    }

    public String getCloseCode() {
        return closeCode;
    }

    public void setCloseCode(String closeCode) {
        this.closeCode = closeCode;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public int getOldDelete() {
        return oldDelete;
    }

    public void setOldDelete(int oldDelete) {
        this.oldDelete = oldDelete;
    }

    public int getNewDelete() {
        return newDelete;
    }

    public void setNewDelete(int newDelete) {
        this.newDelete = newDelete;
    }

    public String getDeleteFlg() {
        return deleteFlg;
    }

    public void setDeleteFlg(String deleteFlg) {
        this.deleteFlg = deleteFlg;
    }

    public String getPayFlg() {
        return payFlg;
    }

    public void setPayFlg(String payFlg) {
        this.payFlg = payFlg;
    }

    public int getOldPayState() {
        return oldPayState;
    }

    public void setOldPayState(int oldPayState) {
        this.oldPayState = oldPayState;
    }

    public int getNewPayState() {
        return newPayState;
    }

    public void setNewPayState(int newPayState) {
        this.newPayState = newPayState;
    }

    public String getMonthPayFlg() {
        return monthPayFlg;
    }

    public void setMonthPayFlg(String monthPayFlg) {
        this.monthPayFlg = monthPayFlg;
    }

    public int getOldMonthPay() {
        return oldMonthPay;
    }

    public void setOldMonthPay(int oldMonthPay) {
        this.oldMonthPay = oldMonthPay;
    }

    public int getNewMonthPay() {
        return newMonthPay;
    }

    public void setNewMonthPay(int newMonthPay) {
        this.newMonthPay = newMonthPay;
    }

    public static String getReceivedFlg() {
        return RECEIVED_FLG;
    }

    public String getIsPayReceivedFlg() {
        return isPayReceivedFlg;
    }

    public void setIsPayReceivedFlg(String isPayReceivedFlg) {
        this.isPayReceivedFlg = isPayReceivedFlg;
    }

    public int getOldReceived() {
        return oldReceived;
    }

    public void setOldReceived(int oldReceived) {
        this.oldReceived = oldReceived;
    }

    public int getNewReceived() {
        return newReceived;
    }

    public void setNewReceived(int newReceived) {
        this.newReceived = newReceived;
    }

    public String getIsTestFlg() {
        return isTestFlg;
    }

    public void setIsTestFlg(String isTestFlg) {
        this.isTestFlg = isTestFlg;
    }

    public int getOldIsTest() {
        return oldIsTest;
    }

    public void setOldIsTest(int oldIsTest) {
        this.oldIsTest = oldIsTest;
    }

    public int getNewIsTest() {
        return newIsTest;
    }

    public void setNewIsTest(int newIsTest) {
        this.newIsTest = newIsTest;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public int getPayMethodIsNull() {
        return payMethodIsNull;
    }

    public void setPayMethodIsNull(int payMethodIsNull) {
        this.payMethodIsNull = payMethodIsNull;
    }

    public int getPayDateIsNull() {
        return payDateIsNull;
    }

    public void setPayDateIsNull(int payDateIsNull) {
        this.payDateIsNull = payDateIsNull;
    }
}
