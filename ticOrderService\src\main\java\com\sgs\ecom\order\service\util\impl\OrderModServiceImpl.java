package com.sgs.ecom.order.service.util.impl;

import com.alibaba.fastjson.JSONObject;
import com.platform.annotation.Master;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.bo.UserInfo;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderAttributeDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderDetailDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderOperatorLogDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleFromDomainService;
import com.sgs.ecom.order.domain.service.user.interfaces.IUserSalesDomainService;
import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.center.CenterCategory;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.center.QuestionQryDtlDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.custom.OrderCheckStateDTO;
import com.sgs.ecom.order.dto.log.BaseLog;
import com.sgs.ecom.order.dto.order.OrderAttributeDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.order.OrderOperatorLogDTO;
import com.sgs.ecom.order.dto.order.OrderPriceDTO;
import com.sgs.ecom.order.dto.order.OrderSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleRelateDTO;
import com.sgs.ecom.order.dto.send.TicOtherMailDTO;
import com.sgs.ecom.order.dto.send.TicOtherSmsDTO;
import com.sgs.ecom.order.dto.user.UserEmailDTO;
import com.sgs.ecom.order.entity.UserQuestion;
import com.sgs.ecom.order.entity.order.OrderBaseInfo;
import com.sgs.ecom.order.entity.order.OrderSampleFrom;
import com.sgs.ecom.order.enumtool.CustomEnum;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.order.GroupTypeEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.user.UserLabelCodeEnum;
import com.sgs.ecom.order.event.EventMailUtil;
import com.sgs.ecom.order.event.EventSmsUtil;
import com.sgs.ecom.order.factory.OrderFactory;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.operator.OrderCloseReq;
import com.sgs.ecom.order.request.order.ConfirmOrderReq;
import com.sgs.ecom.order.request.order.InquiryReq;
import com.sgs.ecom.order.service.custom.interfaces.ICustomCodeService;
import com.sgs.ecom.order.service.custom.interfaces.ICustomLimitService;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.IMemberRestTemplateService;
import com.sgs.ecom.order.service.member.interfaces.ISSOTemplateSV;
import com.sgs.ecom.order.service.order.interfaces.IOrderAttributeService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.order.interfaces.IOrderDetailService;
import com.sgs.ecom.order.service.order.interfaces.IOrderGroupService;
import com.sgs.ecom.order.service.order.interfaces.IOrderOperatorLogService;
import com.sgs.ecom.order.service.order.interfaces.IOrderSampleFromService;
import com.sgs.ecom.order.service.order.interfaces.IOrderSampleRelateService;
import com.sgs.ecom.order.service.order.interfaces.IOrderSampleService;
import com.sgs.ecom.order.service.order.interfaces.ISubOrderService;
import com.sgs.ecom.order.service.user.interfaces.IUserInfoSV;
import com.sgs.ecom.order.service.user.interfaces.IUserLabelSV;
import com.sgs.ecom.order.service.user.interfaces.IUserQuestionSV;
import com.sgs.ecom.order.service.util.interfaces.IOrderModService;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.check.OrderCheckUtil;
import com.sgs.ecom.order.util.collection.ListUtil;
import com.sgs.ecom.order.util.collection.NumUtil;
import com.sgs.ecom.order.util.order.OrderUtil;
import com.sgs.ecom.order.util.select.AttributeUtil;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.util.select.SelectListUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.vo.VOOrderAttribute;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import com.sgs.ecom.order.vo.VOOrderDetail;
import com.sgs.ecom.order.vo.VOOrderOperatorLog;
import com.sgs.ecom.order.vo.VOOrderSample;
import com.sgs.ecom.order.vo.VOOrderSampleRelate;
import com.sgs.ecom.order.vo.VOSubOrder;
import com.sgs.redis.RedisClient;
import com.sgs.util.calendar.LunarWorkday;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class OrderModServiceImpl extends BaseService implements IOrderModService {



	@Autowired
	private IOrderBaseInfoService orderBaseInfoService;
	@Autowired
	private IOrderBaseInfoCustomService orderBaseInfoCustomService;
	@Autowired
	private IOrderOperatorLogService orderOperatorLogService;
	@Autowired
	private ICustomCodeService customCodeService;
	@Autowired
	private ISubOrderService subOrderService;
	@Autowired
	private OrderFactory orderFactory;
	@Autowired
	private IOrderDetailService orderDetailService;
	@Autowired
	private IOrderAttributeService orderAttributeService;
	@Autowired
	private ICenterTemplateSV centerTemplateSV;
	@Autowired
	private LunarWorkday lunarWorkday;
	@Autowired
	private IUserQuestionSV userQuestionSV;
	@Autowired
	private RedisClient redisClient;
	@Autowired
	private IUserInfoSV userInfoSV;
	@Autowired
	private IOrderGroupService orderGroupService;
	@Autowired
	private IOrderSampleRelateService orderSampleRelateService;
	@Autowired
	private IOrderSampleService orderSampleService;
	@Autowired
	private IOrderUtilService orderUtilService;
	@Autowired
	private ISSOTemplateSV issoTemplateSV;
	@Autowired
	private IMemberRestTemplateService memberRestTemplateService;
	@Autowired
	private IOrderSampleFromService orderSampleFromService;
	@Autowired
	private IUserLabelSV userLabelSV;
	@Autowired
	private ICustomLimitService customLimitService;
	@Autowired
	private EventMailUtil eventMailUtil;
	@Autowired
	private EventSmsUtil eventSmsUtil;
	@Autowired
	private IOrderBaseInfoDomainService orderBaseInfoDomainService;
	@Autowired
	private IOrderDetailDomainService orderDetailDomainService;
	@Autowired
	private IOrderSampleDomainService orderSampleDomainService;
	@Autowired
	private IUserSalesDomainService userSalesDomainService;
	@Autowired
	private IOrderSampleFromDomainService orderSampleFromDomainService;
	@Autowired
	private IOrderAttributeDomainService orderAttributeDomainService;
	@Autowired
	private IOrderOperatorLogDomainService orderOperatorLogDomainService;


	@Transactional
	public void createSubOrder(OrderPriceReq orderPriceReq, BOSysPerson boSysPerson) throws Exception {
		String dateStr = UseDateUtil.getDateString(new Date());
		if (StringUtils.isBlank(orderPriceReq.getPrice())) {
			throw new BusinessException("7006", "请填写说明或价格");
		}

		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.qryBase(Long.parseLong(orderPriceReq.getOrderId()));

		//已完成的无法创建补差价
		if (orderBaseInfoCheckDTO.getState() == 80 && OrderTypeEnum.TIC.getIndex().equals(String.valueOf(orderBaseInfoCheckDTO.getOrderType()))) {
			throw new BusinessException(ResultEnumCode.ORDER_STATE_CHANGE);
		}
		BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(orderBaseInfoCheckDTO.getOrderNo());

		if(PayMethodEnum.MONTH.getIndex().equals(String.valueOf(orderBaseInfoCheckDTO.getPayMethod()))){
			throw new BusinessException(ResultEnumCode.MONTH_PAY_IS_NOT);
		}

		String newOrderNo = customCodeService.getNewOrderNo(orderBaseInfoCheckDTO.getBu());
		VOSubOrder voSubOrder = subOrderService.getVOSubOrder(orderBaseInfoCheckDTO, orderPriceReq, boSysPerson, newOrderNo);
		//
		voSubOrder.setGroupNo(customCodeService.getNewGroupNo());
		if (!ValidationUtil.isEmpty(orderPriceReq.getOrderDetailAddList())) {
			//加税率和原价
			voSubOrder.setOrderAmount(orderPriceReq.getOrderAmount());
			orderDetailService.addSubOrderDetail(voSubOrder, orderPriceReq.getOrderDetailAddList());
			VOOrderAttribute voOrderAttribute = orderAttributeService.getVOOrderAttribute(voSubOrder.getOrderNo(), voSubOrder.getGroupNo(),
				AttributeUtil.TAX_RATES, orderPriceReq.getTaxRates().toString(), "", 0, dateStr);
			orderAttributeService.insertSelective(voOrderAttribute);
			voSubOrder.setIsRemind(orderPriceReq.getIsRemind());
			voSubOrder.setCurrency(baseOrderDTO.getCurrency());
			voSubOrder.setExchangeRate(baseOrderDTO.getExchangeRate());
		}

		orderFactory.createEditService(orderBaseInfoCheckDTO).createSubOrder(voSubOrder);
		subOrderService.insertSelective(voSubOrder);
		BigDecimal price=voSubOrder.getRealAmount();
		DecimalFormat decimalFormat = new DecimalFormat("0.00");
		String priceStr = decimalFormat.format(price);

		Map<String,String> currencyMarkMap= orderUtilService.getCurrencyMark(RedisKeyUtil.OIQ_CURRENCY);
		//主订单日志  补差价日志
		String operatorText = newOrderNo + " 金额："+currencyMarkMap.getOrDefault(voSubOrder.getCurrency(),"") + priceStr + " 补差价说明：" + orderPriceReq.getMemo();
		BaseLog baseLog = new BaseLog(orderBaseInfoCheckDTO, OrderOperatorTypeEnum.CREATE_SUB_ORDER, operatorText, orderPriceReq.getMemo(), boSysPerson.getPersonCode(), 1);
		orderOperatorLogService.addLogByBase(baseLog);
		// 补差价日志
		BaseLog baseSubLog = new BaseLog(voSubOrder.getOrderNo(), voSubOrder.getOrderType().toString(), OrderOperatorTypeEnum.CREATE_SUB_ORDER, operatorText, orderPriceReq.getMemo(), boSysPerson.getPersonCode(), 1);
		orderOperatorLogService.addLogByBase(baseSubLog);


		if( OrderTypeEnum.TIC.getIndex().equals(String.valueOf(orderBaseInfoCheckDTO.getOrderType()))){
			OiqMailEnum oiqMailEnum = OiqMailEnum.TIC_CREATE_SUB_ORDER;
			OiqSmsEnum oiqSmsEnum = OiqSmsEnum.TIC_CREATE_SUB_ORDER;
			TicOtherMailDTO otherMailDTO = new TicOtherMailDTO();
			otherMailDTO.setOrderNo(newOrderNo);
			otherMailDTO.setUserName(orderBaseInfoCheckDTO.getUserName());
			TicOtherSmsDTO otherSmsDTO = new TicOtherSmsDTO();
			otherSmsDTO.setOrderNo(newOrderNo);
			otherSmsDTO.setUserName(orderBaseInfoCheckDTO.getUserName());
			//发送邮件短信
			eventMailUtil.sendTicMail(orderBaseInfoCheckDTO.getOrderId(), oiqMailEnum, 1L,otherMailDTO,true);
			eventSmsUtil.sendSMS(orderBaseInfoCheckDTO.getOrderId(), oiqSmsEnum,otherSmsDTO,true);
		}
		if( OrderTypeEnum.OIQ_ORDER.getIndex().equals(String.valueOf(orderBaseInfoCheckDTO.getOrderType()))){
			orderPriceReq.setEventFlg(1);
			orderPriceReq.setEventOrderNo(newOrderNo);
			orderPriceReq.setEventOrderType(OrderTypeEnum.OIQ_SUB_ORDER.getIndex());
		}

	}

	@Transactional
	@Master
	public void batchInvoice(OrderIdReq orderIdReq, BOSysPerson boSysPerson) throws Exception {
		List<Long> orderIdList = ListUtil.stringToLongList(orderIdReq.getOrderId());
		//查询出所有的订单
		Map<String, Object> map = new HashMap<>();
		map.put(SelectListUtil.ORDER_ID_LIST, orderIdList);
		List<OrderCheckStateDTO> orderCheckStateDTOList = orderBaseInfoCustomService.selectOrderCheckStateDTO(map);
		OrderCheckUtil.checkBatchInvoice(orderCheckStateDTOList);
		orderBaseInfoCustomService.batchUpdateInvoice(orderIdList);
		for (OrderCheckStateDTO orderCheckStateDTO : orderCheckStateDTOList) {
			orderOperatorLogService.addLogByBase(new BaseLog(orderCheckStateDTO.getOrderNo(), String.valueOf(orderCheckStateDTO.getOrderType()), OrderOperatorTypeEnum.IS_INVOICE, null, null, boSysPerson.getPersonCode(), 1));
		}

	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public JSONObject createInquiryOrder(InquiryReq inquiryReq, BOSysPerson personInfo) throws Exception {


		QuestionQryDtlDTO questionQryDtlDTO = centerTemplateSV.qryByQuestion(inquiryReq.getQuestionId());
		String bu = questionQryDtlDTO.getBu();
		String orderNo = customCodeService.getInquiryOrderNo(inquiryReq.getUserPhone(), bu);

		Date date = new Date();
		String dateStr = UseDateUtil.getDateString(date);
		VOOrderBaseInfo voOrderBaseInfoAdd = new VOOrderBaseInfo();
		baseCopyObj.copyWithNull(voOrderBaseInfoAdd, inquiryReq);
		voOrderBaseInfoAdd.setOrderNo(orderNo);
		voOrderBaseInfoAdd.setIsTest(0);
		voOrderBaseInfoAdd.setIsDelete(0);
		voOrderBaseInfoAdd.setCreateDate(dateStr);
		voOrderBaseInfoAdd.setConfirmOrderDate(dateStr);
		voOrderBaseInfoAdd.setStateDate(dateStr);
		voOrderBaseInfoAdd.setOrderType(Integer.parseInt(OrderTypeEnum.OIQ_INQUIRY.getIndex()));

		voOrderBaseInfoAdd.setQuestionId(Long.parseLong(inquiryReq.getQuestionId()));
		voOrderBaseInfoAdd.setLineId(Long.parseLong(questionQryDtlDTO.getItemId()));
		voOrderBaseInfoAdd.setApplicationLineId(Long.parseLong(questionQryDtlDTO.getItemId()));
		voOrderBaseInfoAdd.setBusinessLine(questionQryDtlDTO.getItemName());

		voOrderBaseInfoAdd.setBu(bu);
		voOrderBaseInfoAdd.setCatagoryId(Long.parseLong(inquiryReq.getCategoryId()));
		voOrderBaseInfoAdd.setTestLabel(1);
		String timeDateShow = UseDateUtil.getOrderDate(lunarWorkday, date, UseDateUtil.getOrderDateNum(date));
		voOrderBaseInfoAdd.setLastResponseDate(timeDateShow);
		voOrderBaseInfoAdd.setState(Integer.parseInt(OrderStateEnum.WAITDISTRIBUTION.getIndex()));
		voOrderBaseInfoAdd.setFromSource("orderCreate");
		voOrderBaseInfoAdd.setCreateCode(personInfo.getPersonCode());
		voOrderBaseInfoAdd.setOrderSource(OrderSourceEnum.N.getIndex());

		SysPersonDTO sysPersonDTO = null;
		UserEmailDTO userEmailDTO=userInfoSV.qryUserByPhone(inquiryReq.getUserPhone());
		if (!ValidationUtil.isEmpty(userEmailDTO)) {
			Long userId=userEmailDTO.getUserId();
			UserInfo userInfo=userInfoSV.selectByPrimaryKey(userId);
			if(StringUtils.isBlank(userInfo.getUserNick()) ){
				UserInfo updateUser=new UserInfo();
				updateUser.setUserId(userInfo.getUserId());
				updateUser.setUserNick(inquiryReq.getUserName());
				userInfoSV.updateByPrimaryKeySelective(updateUser);
			}
			voOrderBaseInfoAdd.setUserId(userId);
			voOrderBaseInfoAdd.setIsTest(userEmailDTO.getIsTest());
			//第一次下单
			if(userLabelSV.qryUserLabelFlg(userId, UserLabelCodeEnum.INQUIRY_REPURCHASE)==0 &&
					customLimitService.selectRepurchaseCount(userId,voOrderBaseInfoAdd.getOrderType())>0) {
				userLabelSV.saveUserLabel(userId,UserLabelCodeEnum.INQUIRY_REPURCHASE);
			}

			sysPersonDTO = orderUtilService.distributionByLineId(voOrderBaseInfoAdd.getLineId(),
				OrderTypeEnum.OIQ_INQUIRY.getIndex(), voOrderBaseInfoAdd.getUserId());
			if (sysPersonDTO != null) {
				voOrderBaseInfoAdd.setCsCode(sysPersonDTO.getPersonCode());
				voOrderBaseInfoAdd.setCsEmail(sysPersonDTO.getPersonMail());
				voOrderBaseInfoAdd.setState(Integer.parseInt(OrderStateEnum.distribution.getIndex()));
				inquiryReq.setEventFlg(1);
				inquiryReq.setEventOrderNo(orderNo);
			}

			String userSalesStr = userSalesDomainService.qryLastSalesByUser(userId, bu,voOrderBaseInfoAdd.getLineId());
			if (StringUtils.isNotBlank(userSalesStr)) {
				voOrderBaseInfoAdd.setSalesCode(userSalesStr);
			}
		} else {//新用户
			SysPersonDTO qryPerson = issoTemplateSV.qryPersonByUser(personInfo.getPersonCode());
			//新用户注册一个
			Long userId=issoTemplateSV.getUserId(inquiryReq.getUserPhone(),"","");
			voOrderBaseInfoAdd.setUserId(userId);
			if (OrderCheckUtil.checkLine(qryPerson.getLineItem(), voOrderBaseInfoAdd.getLineId())) {
				sysPersonDTO = new SysPersonDTO();
				sysPersonDTO.setPersonCode(personInfo.getPersonCode());
				sysPersonDTO.setPersonMail(personInfo.getPersonMail());
				voOrderBaseInfoAdd.setCsCode(sysPersonDTO.getPersonCode());
				voOrderBaseInfoAdd.setCsEmail(sysPersonDTO.getPersonMail());
				voOrderBaseInfoAdd.setState(Integer.parseInt(OrderStateEnum.distribution.getIndex()));
			}
		}
		orderBaseInfoService.insertSelective(voOrderBaseInfoAdd);

		//申请表订单的虚拟询价单的业务线
		String code=questionQryDtlDTO.getLineCode();
		if(StringUtils.isNotBlank(code) && voOrderBaseInfoAdd.getUserId()!=null){
			userLabelSV.saveUserLabel(voOrderBaseInfoAdd.getUserId(), ListUtil.add(code));
		}

		userQuestionSV.insertByInquiry(inquiryReq, orderNo, questionQryDtlDTO);
		List<OrderSampleFrom> list=orderSampleFromService.getBaseFromListByInquiry(orderNo,voOrderBaseInfoAdd.getApplicationLineId());
		orderSampleFromDomainService.insertForeachAddCenter(list, voOrderBaseInfoAdd.getApplicationLineId());

		VOOrderAttribute voOrderAttribute = orderAttributeService.getVOOrderAttribute(orderNo, OrderUtil.CUSTOM_GROUP,
				AttributeUtil.ORDER_CREATE_CS_CODE,personInfo.getPersonCode(), "", 1, dateStr);
		orderAttributeService.insertSelective(voOrderAttribute);


		String operatorText = "";
		orderOperatorLogService.addLogByBase(new BaseLog(orderNo, OrderTypeEnum.OIQ_INQUIRY.getIndex(), OrderOperatorTypeEnum.CRM_DEMAND, operatorText, null, personInfo.getPersonCode(), 1));


		String operatorTextTwo = "";
		String csCode = CustomEnum.SYSTEM.getNameCh();
		orderOperatorLogService.addLogByBase(new BaseLog(orderNo, OrderTypeEnum.OIQ_INQUIRY.getIndex(), OrderOperatorTypeEnum.WAITDISTRIBUTION, operatorTextTwo, null, csCode, 1));

		redisClient.setValue(RedisKeyUtil.getUrgeRedisKey(voOrderBaseInfoAdd.getOrderNo()), String.valueOf(System.currentTimeMillis()), RedisKeyUtil.URGE_MAX_TIME.intValue());
		if (sysPersonDTO != null) {
			String operatorText3 = "<span style=\"color: #2B02FE; text-decoration: underline\">" + sysPersonDTO.getPersonCode() + "</span>";

			orderOperatorLogService.addLogByBase(new BaseLog(orderNo, OrderTypeEnum.OIQ_INQUIRY.getIndex(), OrderOperatorTypeEnum.DISTRIBUTION, operatorText3, null, sysPersonDTO.getPersonCode(), 1));
		}


		JSONObject jsonObject = new JSONObject();
		jsonObject.put(VOOrderBaseInfo.ORDER_NO, voOrderBaseInfoAdd.getOrderNo());
		jsonObject.put(VOOrderBaseInfo.ORDER_ID, voOrderBaseInfoAdd.getOrderId());
		jsonObject.put("userFlg", 1);
		//生产订单异步同步leads
		inquiryReq.setSaveOrderFlg(1);
		inquiryReq.setEventOrderNo(orderNo);
		return jsonObject;

	}


	@Transactional(rollbackFor = Throwable.class)
	public void updateConfirmOrder(ConfirmOrderReq confirmOrderReq, BOSysPerson personInfo) throws Exception {
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.qryBase(confirmOrderReq.getOrderId());
		extUpdateConfirmOrder(confirmOrderReq, personInfo, orderBaseInfoCheckDTO);
	}

	private void extUpdateConfirmOrder(ConfirmOrderReq confirmOrderReq, BOSysPerson personInfo, OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) throws Exception {
		confirmOrderReq.setOrderNo(orderBaseInfoCheckDTO.getOrderNo());

		String orderNo = orderBaseInfoCheckDTO.getOrderNo();
		OrderCheckStateDTO orderCheckStateDTO = orderBaseInfoCustomService.selectOrderCheckStateDTO(confirmOrderReq.getOrderId());

		VOOrderBaseInfo voOrderBaseInfoBase = orderBaseInfoService.selectByPrimaryKey(orderBaseInfoCheckDTO.getOrderId());
		if (orderCheckStateDTO.getState() != 3) {
			throw new BusinessException(ResultEnumCode.ORDER_STATE_ERROR);
		}

		if (!voOrderBaseInfoBase.getGroupNo().equals(confirmOrderReq.getGroupNo())) {
			throw new BusinessException(ResultEnumCode.ORDER_STATE_ERROR);
		}

		VOOrderBaseInfo voOrderBaseInfoUpdate = new VOOrderBaseInfo();
		voOrderBaseInfoUpdate.setOrderId(voOrderBaseInfoBase.getOrderId());
		voOrderBaseInfoUpdate.setState(Integer.parseInt(OrderStateEnum.CONFIRM.getIndex()));
		voOrderBaseInfoUpdate.setStateDate(UseDateUtil.getDateString(new Date()));


		String oldGroup = orderBaseInfoCheckDTO.getGroupNo();
		String newGroup = customCodeService.getNewGroupNo();

		orderGroupService.insert(orderGroupService.getVOOrderGroup(orderNo, newGroup, 0, GroupTypeEnum.CRM_CONFIRM));
		voOrderBaseInfoUpdate.setGroupNo(newGroup);
		voOrderBaseInfoUpdate.setTmpGroupNo("");

		OrderPriceDTO orderPriceDTO = new OrderPriceDTO(orderNo, oldGroup, newGroup);
		orderPriceDTO.setTotalUrgentAmount(voOrderBaseInfoBase.getUrgentAmount());
		orderPriceDTO.setOldRealAmount(voOrderBaseInfoBase.getRealAmount());
		OrderAttributeDTO orderAttributeDTO=orderAttributeService.selectCustom(orderNo,oldGroup,AttributeUtil.PRICE_TYPE);
		if(ValidationUtil.isEmpty(orderAttributeDTO)){
			orderPriceDTO.setPriceType(Integer.parseInt(orderAttributeDTO.getAttrValue()));
		}
		//统计测试项目 计算orderAmount价格
		addOrderDetail(confirmOrderReq, orderPriceDTO);

		//获取attribute
		List<VOOrderAttribute> list = orderAttributeService.confirmAttribute(confirmOrderReq, orderPriceDTO, true);
		//计算价格
		calculatePrice(orderPriceDTO, voOrderBaseInfoUpdate);
		//价格的值添加attrbute
		list = priceToAttribute(voOrderBaseInfoUpdate, list, orderNo, newGroup,orderPriceDTO);


		if (!ValidationUtil.isEmpty(list)) {
			orderAttributeService.insertForeach(list);
		}

		voOrderBaseInfoUpdate.setCsName(personInfo.getPersonName());
		voOrderBaseInfoUpdate.setCsNameEn(personInfo.getPersonNameEn());

		voOrderBaseInfoUpdate.setStateDate(UseDateUtil.getDateString(new Date()));
		orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfoUpdate);

		VOOrderOperatorLog voOrderOperatorLog=new VOOrderOperatorLog();
		voOrderOperatorLog.setFileUrl(confirmOrderReq.getFileUrl());
		String operatorText= StringUtils.isBlank(confirmOrderReq.getMemo())?null:"确认备注："+ confirmOrderReq.getMemo();
		//添加一个日志 日志为
		orderOperatorLogService.addLogByBase(voOrderOperatorLog,new BaseLog(orderCheckStateDTO.getOrderNo(),String.valueOf(orderCheckStateDTO.getOrderType()),
			OrderOperatorTypeEnum.CRM_CONFIRM_ORDER, operatorText, confirmOrderReq.getMemo(), personInfo.getPersonCode(), 1));
	}

	@Override
	public String copyOrder(String orderNo,String csCode) throws Exception{
		String orderNoReturn=memberRestTemplateService.copyOrder(orderNo,csCode);
		return orderNoReturn;
	}


	public void editCloseMemo(OrderCloseReq orderCloseReq, BOSysPerson personInfo) {
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.qryBase(Long.parseLong(orderCloseReq.getOrderId()));

		VOOrderBaseInfo voOrderBaseInfoUpdate=new VOOrderBaseInfo();
		voOrderBaseInfoUpdate.setOrderId(orderBaseInfoCheckDTO.getOrderId());
		voOrderBaseInfoUpdate.setCloseCode(orderCloseReq.getCloseCode());

		String enumStr=orderUtilService.getEnumStringByKey(RedisKeyUtil.CRM_CLOSE,orderCloseReq.getCloseCode());
		Map<String,Object> map=new HashMap<>();
		map.put(VOOrderOperatorLog.ORDER_NO,orderBaseInfoCheckDTO.getOrderNo());
		map.put(SelectMapUtil.ORDERBY,"LOG_ID desc");
		map.put(SelectMapUtil.OPERATOR_TYPE,OrderOperatorTypeEnum.SYS_CLOSE.getIndex());


		map.put(SelectListUtil.OPERATOR_TYPE_LIST,Arrays.asList(
						OrderOperatorTypeEnum.USER_CLOSE.getIndex(),
				OrderOperatorTypeEnum.CRM_CLOSE.getIndex(),
				OrderOperatorTypeEnum.SYS_CLOSE.getIndex()));
		OrderOperatorLogDTO orderOperatorLogDTO=orderOperatorLogService.getLastOrderLog(map);
		voOrderBaseInfoUpdate.setCloseReason(enumStr);
		if(!ValidationUtil.isEmpty(orderOperatorLogDTO) &&orderOperatorLogDTO.getOperatorType().equals(OrderOperatorTypeEnum.SYS_CLOSE.getIndex())){
			voOrderBaseInfoUpdate.setCloseReason("询价单超时自动关闭;"+enumStr);
		}

		voOrderBaseInfoUpdate.setState(Integer.parseInt(OrderStateEnum.CLOSE.getIndex()));
		voOrderBaseInfoUpdate.setStateDate(UseDateUtil.getDateString(new Date()));
		orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfoUpdate);




		String orderNo=orderBaseInfoCheckDTO.getOrderNo();
		String csCode=personInfo.getPersonCode();
		String memo=orderCloseReq.getMemo();

		String  operatorText=enumStr+" "+memo;
		//记录日志
		orderOperatorLogService.addLogByBase(null, new BaseLog(orderNo, String.valueOf(orderBaseInfoCheckDTO.getOrderType()),
				OrderOperatorTypeEnum.SYS_EDIT_CLOSE, operatorText, memo, csCode, 0));
	}

	@Override
	@Transactional(rollbackFor = Throwable.class)
	public String copyInquiryOrder(OrderNoReq orderNoReq, BOSysPerson personInfo) throws Exception{
		OrderBaseInfo old=orderBaseInfoDomainService.selectEntityByOrderNo(orderNoReq.getOrderNo());
		if(StringUtils.isBlank(old.getGroupNo())){
			return "";
		}

		//获取原始问卷id
		UserQuestion userQuestion=userQuestionSV.selectByOrderNo(old.getOrderNo(),1);
		UserQuestion userQuestion2=userQuestionSV.selectByOrderNo(old.getOrderNo(),0);
		QuestionQryDtlDTO questionQryDtlDTO=null;
		QuestionQryDtlDTO oldQuestion=null;
		if(ValidationUtil.isEmpty(userQuestion2)){
			oldQuestion=checkQuestion(userQuestion,old);
		}else{
			oldQuestion=centerTemplateSV.qryByQuestion(String.valueOf(userQuestion.getQuestionId()));
			oldQuestion.setUseCategoryPath(userQuestion.getCategoryPath());
			try {
				Map<Long,CenterCategory> map=oldQuestion.getCatagoryItem().stream().collect(Collectors.toMap(E->E.getCatagoryId(), Function.identity(), (key1, key2) -> key2));
				if(map.containsKey(userQuestion.getCategoryId())){
					oldQuestion.setUseCategoryPath(map.get(userQuestion.getCategoryId()).getCatagoryPath());
				}
			} catch (Exception e) {

			}
			 questionQryDtlDTO=checkQuestion(userQuestion2,old);
		}


		OrderBaseInfo newOrder=orderBaseInfoDomainService.copyInquiryOrderByOrderNo(old,personInfo);
		Map<Long,Long> detailMap=orderDetailDomainService.copyInquiryDetailByOld(old,newOrder);
		orderSampleDomainService.copyInquirySampleByOld(old,newOrder,detailMap);
		//拷贝附件，拷贝问卷等数据
		orderAttributeDomainService.copyInquiryAttrByOld(old,newOrder);

		//
		userQuestionSV.copyInquiryQuestionByOld(old,newOrder,oldQuestion,1);
		userQuestionSV.copyInquiryQuestionByOld(old,newOrder,questionQryDtlDTO,0);
		//记录日志

		orderOperatorLogDomainService.addLog(new BaseOrderDTO(newOrder.getOrderNo(),String.valueOf(newOrder.getOrderType())),
				OrderOperatorTypeEnum.CRM_COPY_INQUIRY,personInfo.getPersonCode());

		return newOrder.getOrderNo();
	}

	private QuestionQryDtlDTO checkQuestion(UserQuestion userQuestion,OrderBaseInfo old) {

		QuestionQryDtlDTO questionQryDtlDTO=null;
		try{
			questionQryDtlDTO = centerTemplateSV.qryByQuestion(String.valueOf(userQuestion.getQuestionId()));
		}catch (Exception e){
			throw new BusinessException(ResultEnumCode.BUSINESS_LINE_ERROR);
		}
		if(questionQryDtlDTO.getIsDelete()==1){
			throw new BusinessException(ResultEnumCode.BUSINESS_LINE_ERROR);
		}

		Long lineId=Long.parseLong(questionQryDtlDTO.getItemId());
		if(lineId!=old.getLineId()){
			throw new BusinessException(ResultEnumCode.BUSINESS_LINE_ERROR);
		}
		if(ValidationUtil.isEmpty(questionQryDtlDTO.getCatagoryItem())){
			throw new BusinessException(ResultEnumCode.BUSINESS_LINE_ERROR);
		}

		if(questionQryDtlDTO.getCatagoryItem().size()>0){
			List<CenterCategory> list=questionQryDtlDTO.getCatagoryItem();
			Map<Long,CenterCategory> map=list.stream().collect(Collectors.toMap(E->E.getCatagoryId(), Function.identity(), (key1, key2) -> key2));
			if(!map.containsKey(userQuestion.getCategoryId())){
				throw new BusinessException(ResultEnumCode.BUSINESS_LINE_ERROR);
			}
			questionQryDtlDTO.setUseCategoryPath(map.get(userQuestion.getCategoryId()).getCatagoryPath());
		}
		return questionQryDtlDTO;


	}


	private List<VOOrderAttribute> priceToAttribute(VOOrderBaseInfo voOrderBaseInfoUpdate, List<VOOrderAttribute> baseList, String orderNo, String groupNo,OrderPriceDTO orderPriceDTO) {
		String realAmount = OrderUtil.toZero(voOrderBaseInfoUpdate.getRealAmount()).toString();
		String orderAmount = OrderUtil.toZero(voOrderBaseInfoUpdate.getOrderAmount()).toString();
		String urgentAmount = OrderUtil.toZero(voOrderBaseInfoUpdate.getUrgentAmount()).toString();
		String serviceAmount = OrderUtil.toZero(voOrderBaseInfoUpdate.getServiceAmount()).toString();
		String discountAmount = OrderUtil.toZero(voOrderBaseInfoUpdate.getDiscountAmount()).toString();
		baseList = orderAttributeService.attributeToVOList(baseList, orderNo, groupNo, AttributeUtil.REAL_AMOUNT, realAmount, true);
		baseList = orderAttributeService.attributeToVOList(baseList, orderNo, groupNo, AttributeUtil.ORDER_AMOUNT, orderAmount, true);
		baseList = orderAttributeService.attributeToVOList(baseList, orderNo, groupNo, AttributeUtil.URGENT_AMOUNT, urgentAmount, true);
		baseList = orderAttributeService.attributeToVOList(baseList, orderNo, groupNo, AttributeUtil.SERVICE_AMOUNT, serviceAmount, true);
		baseList = orderAttributeService.attributeToVOList(baseList, orderNo, groupNo, AttributeUtil.DISCOUNT_AMOUNT, discountAmount, true);
		baseList = orderAttributeService.attributeToVOList(baseList, orderNo, groupNo, AttributeUtil.DISCOUNT_AMOUNT_NUM, orderPriceDTO.getDiscount().toString(), true);
		if(orderPriceDTO.getNewDiscount()!=null){
			baseList = orderAttributeService.attributeToVOList(baseList, orderNo, groupNo, AttributeUtil.NEW_DISCOUNT_AMOUNT_NUM, orderPriceDTO.getNewDiscount().toString(), true);
		}


		return baseList;
	}


	//根据用户的选中生成detail sample relate
	private void addOrderDetail(ConfirmOrderReq confirmOrderReq, OrderPriceDTO orderPriceDTO) {
		String dateStr = UseDateUtil.getDateString(new Date());
		String orderNo = orderPriceDTO.getOrderNo();
		String oldGroup = orderPriceDTO.getOldGroup();
		String newGroup = orderPriceDTO.getNewGroup();
		BigDecimal orderAmount = BigDecimal.ZERO;

		Map map = new HashMap();//确认只算加入的
		map.put(SelectMapUtil.ORDER_NO, orderPriceDTO.getOrderNo());
		map.put(SelectMapUtil.GROUP_NO, oldGroup);
		map.put(SelectMapUtil.IS_DEFAULT, 1);
		List<OrderDetailDTO> orderDetailDTOList = orderDetailService.selectListByMap(map);


		List<OrderSampleRelateDTO> orderSampleRelateDTOList = orderSampleRelateService.selectListByMap(map);
		Map<String, List<OrderSampleRelateDTO>> groupBySex = orderSampleRelateDTOList.stream()
			.collect(Collectors.groupingBy(OrderSampleRelateDTO::getDetailId));

		List<OrderSampleDTO> orderSampleDTOList = orderSampleService.selectListByMap(map);
		Map<String, OrderSampleDTO> sampleMap = new HashMap();
		for (OrderSampleDTO sampleDTO : orderSampleDTOList) {
			sampleDTO.setState(0);//样品状态全改成0
			sampleMap.put(sampleDTO.getSampleNo(), sampleDTO);
		}


		List<VOOrderSampleRelate> voOrderSampleRelateList = new ArrayList<>();
		for (int n = 0; n < orderDetailDTOList.size(); n++) {
			{
				OrderDetailDTO orderDetailDTO = orderDetailDTOList.get(n);
				if (orderDetailDTO.getPrice() == null) {
					throw new BusinessException("3111", "有价格待定的项目，请联系工程师修改后再确认");
				}
				VOOrderDetail addDetail = new VOOrderDetail();
				baseCopyObj.copyWithNull(addDetail, orderDetailDTO);
				addDetail.setGroupNo(newGroup);
				addDetail.setIsOptional(0);
				addDetail.setIsDefault(1);
				addDetail.setState(1);
				Long newDetailId = orderDetailService.insertSelective(addDetail);
				orderAmount = orderAmount.add(OrderUtil.toZero(addDetail.getTotalPrice()));

				List<OrderSampleRelateDTO> useRelate = groupBySex.get(String.valueOf(orderDetailDTO.getDetailId()));
				if (!ValidationUtil.isEmpty(useRelate) && !ValidationUtil.isEmpty(sampleMap)) {
					for (OrderSampleRelateDTO orderSampleRelateDTO : useRelate) {
						String sampleNo = orderSampleRelateDTO.getSampleNo();
						OrderSampleDTO orderSampleDTO = sampleMap.get(sampleNo);
						orderSampleDTO.setState(1);
						sampleMap.put(sampleNo, orderSampleDTO);

						VOOrderSampleRelate voOrderSampleRelate = orderSampleRelateService.getVOOrderSampleRelate(newDetailId, orderNo, newGroup, sampleNo);
						voOrderSampleRelateList.add(voOrderSampleRelate);
					}
				}
			}
		}


		List<VOOrderSample> voOrderSampleList = new ArrayList<>();
		for (String sampleNo : sampleMap.keySet()) {
			OrderSampleDTO sampleDTO = sampleMap.get(sampleNo);
			VOOrderSample voOrderSample = new VOOrderSample();
			baseCopyObj.copyWithNull(voOrderSample, sampleDTO);
			voOrderSample.setGroupNo(newGroup);
			voOrderSampleList.add(voOrderSample);
		}


		if (!ValidationUtil.isEmpty(voOrderSampleList)) {
			List<VOOrderSample> newList = voOrderSampleList.stream().sorted(Comparator.comparing(E -> E.getSampleId()))
				.collect(Collectors.toList());
			orderSampleService.insertForeach(newList);
			//复制sampleForm
			orderSampleFromService.copySampleFormBySql(orderNo,orderNo,oldGroup,newGroup);
		}


		orderSampleRelateService.insertForeach(voOrderSampleRelateList);
		if (orderAmount != null && orderAmount.doubleValue() == 0L) {
			throw new BusinessException("3111", "没有测试项目无法确认报价单");
		}
		orderPriceDTO.setOrderAmount(orderAmount);
	}


	//计算价格
	public void calculatePrice(OrderPriceDTO orderPriceDTO, VOOrderBaseInfo voOrderBaseInfoUpdate) throws Exception {
		VOOrderAttribute voOrderAttributeLua = orderPriceDTO.getVoOrderAttributeLua();
		VOOrderAttribute voOrderAttributeFrom = orderPriceDTO.getVoOrderAttributeFrom();
		VOOrderAttribute voOrderAttributeLab = orderPriceDTO.getVoOrderAttributeLab();

		BigDecimal orderAmountBase = orderPriceDTO.getOrderAmount();//订单费用
		//总加急费
		BigDecimal urgentAmount = orderPriceDTO.getTotalUrgentAmount();
		//订单小计包含加急费
		BigDecimal totalAmount=orderAmountBase.add(urgentAmount);


		voOrderBaseInfoUpdate.setOrderAmount(orderAmountBase);
		BigDecimal serviceAmount = BigDecimal.ZERO;
		if (voOrderAttributeLab != null) {
			voOrderBaseInfoUpdate.setLabId(Long.parseLong(voOrderAttributeLab.getAttrCode()));
			voOrderBaseInfoUpdate.setLabName(voOrderAttributeLab.getAttrName());
			LabDTO labDTO = centerTemplateSV.labQryByUser(voOrderAttributeLab.getAttrCode());
			if (labDTO == null) {
				throw new BusinessException(ResultEnumCode.LAB_ERROR);
			}
			if (StringUtils.isBlank(labDTO.getAccountNo())) {
				throw new BusinessException(ResultEnumCode.LAB_ACCOUNT_ERROR);
			}

			voOrderBaseInfoUpdate.setAccountNo(labDTO.getAccountNo());
			serviceAmount = serviceAmount.add(voOrderAttributeLab.getAttrAmount());
		}

		if (voOrderAttributeFrom != null) {
			voOrderBaseInfoUpdate.setReportForm(voOrderAttributeFrom.getAttrName());
			voOrderBaseInfoUpdate.setReportFormCode(voOrderAttributeFrom.getAttrCode());
			serviceAmount = serviceAmount.add(voOrderAttributeFrom.getAttrAmount());
		}
		if (voOrderAttributeLua != null) {
			voOrderBaseInfoUpdate.setReportLua(voOrderAttributeLua.getAttrName());
			voOrderBaseInfoUpdate.setReportLuaCode(voOrderAttributeLua.getAttrCode());
			serviceAmount = serviceAmount.add(voOrderAttributeLua.getAttrAmount());
		}



		//获取报价金额类型
		int priceType=0;
		OrderAttributeDTO priceTypeAttr=orderAttributeService.selectCustom(orderPriceDTO.getOrderNo(),orderPriceDTO.getOldGroup(),AttributeUtil.PRICE_TYPE);
		if(!ValidationUtil.isEmpty(priceTypeAttr)){
			priceType=Integer.parseInt(priceTypeAttr.getAttrName());
		}

		//
		BigDecimal discountAmountNum=BigDecimal.ZERO;
		OrderAttributeDTO discountAmountNumAttr=orderAttributeService.selectCustom(orderPriceDTO.getOrderNo(),orderPriceDTO.getOldGroup(),AttributeUtil.DISCOUNT_AMOUNT_NUM);
		if(!ValidationUtil.isEmpty(discountAmountNumAttr)){
			discountAmountNum=new BigDecimal(discountAmountNumAttr.getAttrName());
		}


		BigDecimal disCountReq=priceType==0? NumUtil.toZero(discountAmountNum): BigDecimal.ZERO;


		//优惠金额
		BigDecimal disCountAmount = orderAmountBase.multiply(disCountReq.movePointLeft(2)).setScale(2, BigDecimal.ROUND_HALF_UP);

		//实际支付金额未算费率
		BigDecimal finalBig = orderAmountBase.subtract(disCountAmount).add(serviceAmount);

		BigDecimal tax=new BigDecimal(0.06);
		OrderAttributeDTO taxAttr=orderAttributeService.selectCustom(orderPriceDTO.getOrderNo(),orderPriceDTO.getOldGroup(),AttributeUtil.TAX_RATES);
		if(!ValidationUtil.isEmpty(taxAttr)){
			tax=new BigDecimal(taxAttr.getAttrName()).divide(new BigDecimal(100));
		}

		BigDecimal otherAmount = finalBig.multiply(tax).setScale(2, BigDecimal.ROUND_HALF_UP);
		//计算已选项目税后总价
		BigDecimal total=finalBig.add(otherAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
		BigDecimal realAmount=null;

		if(priceType==0){
			realAmount=total;
		}

		if(priceType==1){
			realAmount=orderPriceDTO.getOldRealAmount();
			disCountAmount=total.subtract(realAmount);
			orderPriceDTO.setNewDiscount(realAmount.divide(total,4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)));
		}
		voOrderBaseInfoUpdate.setDiscountAmount(disCountAmount);
		orderPriceDTO.setDiscount(disCountReq);



		//计算出其他费用
		voOrderBaseInfoUpdate.setServiceAmount(serviceAmount);
		voOrderBaseInfoUpdate.setRealAmount(realAmount);

		OrderAttributeDTO testCycleAttr=orderAttributeService.selectCustom(orderPriceDTO.getOrderNo(),orderPriceDTO.getOldGroup(),AttributeUtil.TEST_CYCLE_NUM);
		if(!ValidationUtil.isEmpty(testCycleAttr)){
			voOrderBaseInfoUpdate.setTestCycle(new BigDecimal(testCycleAttr.getAttrName()));
		}

	}


}
