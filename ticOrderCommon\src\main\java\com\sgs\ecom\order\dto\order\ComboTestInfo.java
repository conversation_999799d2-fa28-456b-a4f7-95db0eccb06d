package com.sgs.ecom.order.dto.order;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class ComboTestInfo {


    //type = 2
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testItem;//检测项目
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testingStandard;//检测标准

    public String getTestItem() {
        return testItem;
    }

    public void setTestItem(String testItem) {
        this.testItem = testItem;
    }

    public String getTestingStandard() {
        return testingStandard;
    }

    public void setTestingStandard(String testingStandard) {
        this.testingStandard = testingStandard;
    }
}
