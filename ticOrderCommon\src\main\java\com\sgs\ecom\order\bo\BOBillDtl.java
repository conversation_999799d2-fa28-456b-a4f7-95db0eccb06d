package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp;
import java.util.List;

import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="账单明细")
public class BOBillDtl{
 
 	public static final String SEQUENCE = "DTL_ID"; 
  
 	public static final String BO_SQL = "TB_BILL_DTL"; 
 
 	public static final String OWNER ="member";

 	public static final String REFERENCE_INFO="referenceInfo";
 	public static final String STATE_DATE="stateDate";
 	public static final String PAY_DATE="payDate";
 	public static final String STATE="state";
 	public static final String ORDER_NO="orderNo";
 	public static final String USER_NAME="userName";
 	public static final String INVOICE_NO="invoiceNo";
 	public static final String IS_OTHER="isOther";
 	public static final String ORDER_AMOUNT="orderAmount";
 	public static final String USER_PHONE="userPhone";
 	public static final String CREATE_DATE="createDate";
 	public static final String DTL_ID="dtlId";
 	public static final String BILL_ID="billId";
 	public static final String BU="bu";
 	public static final String PAY_STATE="payState";
 	public static final String BILL_CYCLE="billCycle";
 	public static final String REPORT_NAME="reportName";

 	@BeanAnno("REFERENCE_INFO")
 	private String referenceInfo;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("PAY_DATE")
 	private Timestamp payDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("ORDER_NO")
 	private String orderNo;
 	@BeanAnno("USER_NAME")
 	private String userName;
 	@BeanAnno("INVOICE_NO")
 	private String invoiceNo;
 	@BeanAnno("IS_OTHER")
 	private int isOther;
 	@BeanAnno("ORDER_AMOUNT")
 	private double orderAmount;
 	@BeanAnno("USER_PHONE")
 	private String userPhone;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("DTL_ID")
 	private long dtlId;
 	@BeanAnno("BILL_ID")
 	private long billId;
 	@BeanAnno("bu")
 	private String bu;
 	@BeanAnno("PAY_STATE")
 	private int payState;
 	@BeanAnno("BILL_CYCLE")
 	private String billCycle;
 	@BeanAnno("REPORT_NAME")
 	private String reportName;

 	private List<String> reportNameList;

 	private int maxSize;

 	@CharacterVaild(len = 500) 
 	public void setReferenceInfo(String referenceInfo){
 		 this.referenceInfo=referenceInfo;
 	}
 	@CheckAnno(len = 500) 
 	public String getReferenceInfo(){
 		 return this.referenceInfo;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setPayDate(Timestamp payDate){
 		 this.payDate=payDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getPayDate(){
 		 return this.payDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	@CheckAnno(len = 50) 
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	@CheckAnno(len = 100) 
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setInvoiceNo(String invoiceNo){
 		 this.invoiceNo=invoiceNo;
 	}
 	@CheckAnno(len = 50) 
 	public String getInvoiceNo(){
 		 return this.invoiceNo;
 	}
 
 	 
 	public void setIsOther(int isOther){
 		 this.isOther=isOther;
 	}
 	public int getIsOther(){
 		 return this.isOther;
 	}
 
 	 
 	public void setOrderAmount(double orderAmount){
 		 this.orderAmount=orderAmount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getOrderAmount(){
 		 return this.orderAmount;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	@CheckAnno(len = 20) 
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setDtlId(long dtlId){
 		 this.dtlId=dtlId;
 	}
 	public long getDtlId(){
 		 return this.dtlId;
 	}
 
 	 
 	public void setBillId(long billId){
 		 this.billId=billId;
 	}
 	public long getBillId(){
 		 return this.billId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	@CheckAnno(len = 50) 
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setPayState(int payState){
 		 this.payState=payState;
 	}
 	public int getPayState(){
 		 return this.payState;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setBillCycle(String billCycle){
 		 this.billCycle=billCycle;
 	}
 	@CheckAnno(len = 20) 
 	public String getBillCycle(){
 		 return this.billCycle;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setReportName(String reportName){
 		 this.reportName=reportName;
 	}
 	@CheckAnno(len = 500) 
 	public String getReportName(){
 		 return this.reportName;
 	}

	public List<String> getReportNameList() {
		return reportNameList;
	}

	public void setReportNameList(List<String> reportNameList) {
		this.reportNameList = reportNameList;
	}

	public int getMaxSize() {
		return maxSize;
	}

	public void setMaxSize(int maxSize) {
		this.maxSize = maxSize;
	}
}