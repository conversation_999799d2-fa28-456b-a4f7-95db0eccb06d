package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqAddressDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqUserAddressDTO;
import com.sgs.ecom.order.dto.user.UserAddressDTO;
import com.sgs.ecom.order.dto.user.UserInvoiceDTO;
import com.sgs.ecom.order.entity.order.OrderExpress;
import com.sgs.ecom.order.entity.order.OrderInvoice;
import com.sgs.ecom.order.enumtool.application.DeliverTypeEnum;
import com.sgs.ecom.order.enumtool.user.UserInvoiceEnum;
import com.sgs.ecom.order.request.detail.ExpressReq;
import com.sgs.ecom.order.request.oiq.OiqAddressReq;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.collection.StrUtil;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.StringJoiner;

public class OrderExpressDO {

    private BaseCopyObj baseCopy = new BaseCopyObj();


    public OrderExpress getOrderExpress(UserAddressDTO backAddress, DeliverTypeEnum deliverTypeEnum, ExpressReq expressReq, LabDTO labDTO, String orderNo, Long userId) {
        OrderExpress orderExpress =  init(deliverTypeEnum,orderNo,userId);
        if (expressReq != null) {
            orderExpress.setGoodsName(expressReq.getGoodsName());
            orderExpress.setGoodsPrice(expressReq.getGoodsPrice());
            orderExpress.setExpressCode(expressReq.getExpressCode());
            orderExpress.setPackageNums(expressReq.getPackageNums());
            orderExpress.setPreExpressDate(expressReq.getPreExpressDate());
            if (StringUtils.isNotBlank(expressReq.getExpressNo())) {
                orderExpress.setExpressNo(expressReq.getExpressNo().toUpperCase());
            }
            orderExpress.setPayMethond(StringUtils.isBlank(expressReq.getPayMethond()) ? 0 : Integer.parseInt(expressReq.getPayMethond()));
            orderExpress.setExpressType(StringUtils.isBlank(expressReq.getExpressType()) ? "0" : expressReq.getExpressType());
            orderExpress.setMonthlyCard(StringUtils.isBlank(expressReq.getMonthlyCard()) ? "" : expressReq.getMonthlyCard());
        }

        if (deliverTypeEnum.getIndex().equals(DeliverTypeEnum.REPORT.getIndex()) ||
                deliverTypeEnum.getIndex().equals(DeliverTypeEnum.BACK.getIndex()) ||
                deliverTypeEnum.getIndex().equals(DeliverTypeEnum.DETECTION.getIndex()) ||
                deliverTypeEnum.getIndex().equals(DeliverTypeEnum.TEACHING_MATERIAL.getIndex()) ||
                deliverTypeEnum.getIndex().equals(DeliverTypeEnum.CERTIFICATE.getIndex()) ) {
            if (!ValidationUtil.isEmpty(backAddress)) {
                orderExpress.setReceiptProvice(backAddress.getProvince());
                orderExpress.setReceiptCity(backAddress.getCity());
                orderExpress.setReceiptTown(backAddress.getTown());
                orderExpress.setReceiptPerson(backAddress.getUserName());
                orderExpress.setReceiptPhone(backAddress.getUserPhone());
                orderExpress.setReceiptAddr(backAddress.getCompanyAddress());
                orderExpress.setReceiptCompany(backAddress.getCompanyName());
                orderExpress.setAddressId(backAddress.getAddressId());
                orderExpress.setReceiptEmail(backAddress.getUserMail());
            }


        } else {
            if (!ValidationUtil.isEmpty(backAddress)) {
                orderExpress.setSendPerson(backAddress.getUserName());
                orderExpress.setSendPhone(backAddress.getUserPhone());
                orderExpress.setSendTown(backAddress.getTown());
                orderExpress.setSendProvice(backAddress.getProvince());
                orderExpress.setSendCity(backAddress.getCity());
                orderExpress.setSendAddr(backAddress.getCompanyAddress());
                orderExpress.setSendCompany(backAddress.getCompanyName());
                orderExpress.setAddressId(backAddress.getAddressId());
            }

            if (labDTO != null) {
                orderExpress.setReceiptProvice(labDTO.getProvince());
                orderExpress.setReceiptCity(labDTO.getCity());
                orderExpress.setReceiptTown(labDTO.getTown());
                orderExpress.setReceiptPerson(labDTO.getLinkPerson());
                orderExpress.setReceiptPhone(labDTO.getLinkPhone());
                orderExpress.setReceiptAddr(labDTO.getLabAddress());
                orderExpress.setLabId(labDTO.getLabId());
                orderExpress.setLabName(labDTO.getLabName());
            }

        }

        return orderExpress;
    }

    public static  String getAddressByExpressDTO(OrderExpressDTO orderExpressDTO){
        StringJoiner stringJoiner=new StringJoiner("");
        stringJoiner.add(StrUtil.toStr(orderExpressDTO.getReceiptProvice()));
        stringJoiner.add(StrUtil.toStr(orderExpressDTO.getReceiptCity()));
        stringJoiner.add(StrUtil.toStr(orderExpressDTO.getReceiptTown()));
        stringJoiner.add(StrUtil.toStr(orderExpressDTO.getReceiptAddr()));
        return stringJoiner.toString();
    }

    private OrderExpress init( DeliverTypeEnum deliverTypeEnum, String orderNo, Long userId) {
        OrderExpress orderExpress = new OrderExpress();
        orderExpress.setStateDate(UseDateUtil.getDateString(new Date()));
        orderExpress.setState(1);
        orderExpress.setDeliverType(Integer.parseInt(deliverTypeEnum.getIndex()));
        orderExpress.setUserId(userId);
        orderExpress.setCreateDate(UseDateUtil.getDateString(new Date()));
        orderExpress.setOrderNo(orderNo);
        return orderExpress;
    }
    public static List<OiqAddressDTO> expressListToAddressList(List<OrderExpressDTO> orderExpressDTOList) {
        if(ValidationUtil.isEmpty(orderExpressDTOList)){
            return new ArrayList<>();
        }
        List<OiqAddressDTO> list=new ArrayList<>();
        for(int n=0;n<orderExpressDTOList.size();n++){
            OrderExpressDTO orderExpressDTO=orderExpressDTOList.get(n);
            OiqUserAddressDTO userAddressDTO=new OiqUserAddressDTO();
            userAddressDTO.setAddressId(orderExpressDTO.getAddressId());
            userAddressDTO.setCity(orderExpressDTO.getReceiptCity());
            userAddressDTO.setProvince(orderExpressDTO.getReceiptProvice());
            userAddressDTO.setTown(orderExpressDTO.getReceiptTown());
            userAddressDTO.setUserName(orderExpressDTO.getReceiptPerson());
            userAddressDTO.setUserPhone(orderExpressDTO.getReceiptPhone());
            userAddressDTO.setCompanyAddress(orderExpressDTO.getReceiptAddr());
            userAddressDTO.setCompanyName(orderExpressDTO.getReceiptCompany());
            userAddressDTO.setUserMail(orderExpressDTO.getReceiptEmail());
            list.add(new OiqAddressDTO(orderExpressDTO.getAddressId(),userAddressDTO));
        }
        return list;
    }
}
