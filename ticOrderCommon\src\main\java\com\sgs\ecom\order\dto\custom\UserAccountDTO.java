package com.sgs.ecom.order.dto.custom;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import java.sql.Timestamp;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserAccountDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,ACCOUNT_TYPE,ACCOUNT_ID,USER_ID,STATE,ACCOUNT_NO from USER_ACCOUNT"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ACCOUNT_TYPE", getName="getAccountType", setName="setAccountType")
 	private String accountType;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ACCOUNT_ID", getName="getAccountId", setName="setAccountId")
 	private long accountId;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="ACCOUNT_NO", getName="getAccountNo", setName="setAccountNo")
 	private String accountNo;

	private String appId;
 	
 	@ApiAnno(groups={QueryList.class})
 	private String verifyCode;
 	@ApiAnno(groups={QueryList.class})
 	private String verifyNumber;
 	@ApiAnno(groups={QueryList.class})
 	private String verifyMode;
 	@ApiAnno(groups={QueryList.class})
 	private String verifyType;
 	@ApiAnno(groups={QuerySummary.class})
 	private Long margeUserId;
 	@ApiAnno(groups={QuerySummary.class})
 	private String margeAccountNo;

 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}

}