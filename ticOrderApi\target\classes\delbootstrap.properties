#spring.cloud.nacos.config.server-addr=10.168.129.79:6701
#uat
#spring.cloud.nacos.config.namespace=6130b322-3b22-4696-8577-09df5f997576
#test
#spring.cloud.nacos.config.namespace=7f5dd9ae-1ae0-4430-8c17-d1d8cde069c3
#spring.cloud.nacos.config.prefix=public



#\u03a2\ufffd\ufffd\ufffd\ufffd\ufffd\ufffdnacos
spring.cloud.nacos.config.server-addr=http://10.168.129.144:8848
spring.cloud.nacos.config.namespace=6e84c8b4-b800-4220-b0ec-1923e3ecab0d
spring.cloud.nacos.config.prefix=public




#\u516c\u5171\u914d\u7f6e
#spring.cloud.nacos.config.extension-configs[0].data-id=public.properties
#spring.cloud.nacos.config.extension-configs[0].group=DEFAULT_GROUP
#spring.cloud.nacos.config.extension-configs[0].refresh=true

#\u591a\u914d\u7f6e
spring.cloud.nacos.config.shared-dataids=tic-order-basic.properties,tic-order-db.properties,tic-order-url.properties
spring.cloud.nacos.config.refreshable-dataids=tic-order-basic.properties,tic-order-db.properties,tic-order-url.properties

#\u7aef\u53e3
server.port=8005
# \u5e94\u7528\u540d\u79f0
spring.application.name=tic-order
spring.cloud.nacos.config.group=DEFAULT_GROUP
spring.profiles.active=Aprod

