package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.OrderRelateExternalReq;
import com.sgs.ecom.order.request.OtsReq;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderRelateExternalService;
import com.sgs.ecom.order.service.util.interfaces.IOrderApplicationService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/external")
public class OrderRelateExternalController extends ControllerUtil {


    @Autowired
    private IOrderApplicationService orderApplicationService;
    @Autowired
    private IOrderRelateExternalService orderRelateExternalService;

    /**
    *@Function: saveExternal
    *@Description  保存执行系统单号
    *@param: [orderRelateExternalReq, token]
    *@author: Xiwei_Qiu @date: 2021/5/17 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4524" })
    @RequestMapping(value = "save",method = { RequestMethod.POST })
    public ResultBody saveExternal(
            @RequestBody OrderRelateExternalReq orderRelateExternalReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        orderApplicationService.saveExternal(orderRelateExternalReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }

    /**
     *@Function: saveExternal
     *@Description  保存执行系统单号 （只新增）
     *@param: [orderRelateExternalReq, token]
     *@author: Xiwei_Qiu @date: 2021/5/17 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4524" })
    @RequestMapping(value = "saveOts",method = { RequestMethod.POST })
    public ResultBody saveOts(
        @RequestBody OrderRelateExternalReq orderRelateExternalReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        orderRelateExternalService.saveOts(orderRelateExternalReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }

    /**
     *@Function: saveExternal
     *@Description 修改执行单号
     *@param: [orderRelateExternalReq, token]
     *@author: Xiwei_Qiu @date: 2021/5/17 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4524" })
    @RequestMapping(value = "modOts",method = { RequestMethod.POST })
    public ResultBody modOts(
        @RequestBody OtsReq otsReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        orderRelateExternalService.modOts(otsReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }

    /**
     *@Function: saveExternal
     *@Description  删除执行单号
     *@param: [orderRelateExternalReq, token]
     *@author: Xiwei_Qiu @date: 2021/5/17 @version:
     **/

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4524" })
    @RequestMapping(value = "delOts",method = { RequestMethod.POST })
    public ResultBody delOts(
        @RequestBody OtsReq otsReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        orderRelateExternalService.delOts(otsReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }






    /**
    *@Function: list
    *@Description 执行系统列表
    *@param: [token, orderIdReq]
    *@author: Xiwei_Qiu @date: 2021/12/6 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "list", method = { RequestMethod.POST })
    public ResultBody list(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderIdReq orderIdReq) throws Exception {
        return ResultBody.newInstance(orderApplicationService.selectExternal(orderIdReq.getOrderId(),getPersonInfo(token),getPower(token)));
    }
}
