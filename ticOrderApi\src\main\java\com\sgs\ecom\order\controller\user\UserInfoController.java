package com.sgs.ecom.order.controller.user;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.service.user.interfaces.IUserInfoSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOUserInfo;

@RestController
@RequestMapping("/business/api.v1.user/user")
public class UserInfoController extends ControllerUtil {

    @Autowired
	private IUserInfoSV userInfoSV;
	
    /**   
	* @Function: qryUser
	* @Description: 查询用户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qry", method = { RequestMethod.POST })
    public ResultBody qryUser(@RequestBody VOUserInfo userInfo) throws Exception {
		return ResultBody.newInstance(userInfoSV.qryUser(userInfo));
	}
    
    /**   
	* @Function: modUser
	* @Description: 修改月结用户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "mod", method = { RequestMethod.POST })
    public ResultBody modUser(@Validated(BaseBean.Update.class)
    	@RequestBody VOUserInfo userInfo) throws Exception {
		userInfoSV.modUser(userInfo);
		return ResultBody.success();
	}

    /**   
	* @Function: addUser
	* @Description: 增加用户信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2018-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2018-08-30  shenyi    v1.0                 新增
	*/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "add", method = { RequestMethod.POST })
    public ResultBody addUser(@RequestBody VOUserInfo userInfo) throws Exception {
    	userInfoSV.addUser(userInfo);
    	return ResultBody.success();
	}
    
    /**   
	* @Function: qryUserList
	* @Description: 查询用户列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryList", method = { RequestMethod.POST })
    public ResultBody qryUserList(@RequestBody VOUserInfo userInfo) throws Exception {
		return ResultBody.newInstance(userInfoSV.qryUserList(userInfo));
	}
    
    /**   
	* @Function: qryByCust
	* @Description: 查询用户列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryByCust", method = { RequestMethod.POST })
    public ResultBody qryByCust(@RequestBody VOUserInfo userInfo) throws Exception {
		return ResultBody.newInstance(userInfoSV.qryByCust(userInfo));
	}

    /**   
	* @Function: relateCompany
	* @Description: 关联企业
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "relateCompany", method = { RequestMethod.POST })
    public ResultBody relateCompany(@RequestBody VOUserInfo userInfo) throws Exception {
    	userInfoSV.relateCompany(userInfo);
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "checkRelate", method = { RequestMethod.POST })
	public ResultBody checkRelate(@RequestBody VOUserInfo userInfo) throws Exception {
		return ResultBody.success(userInfoSV.checkRelate(userInfo));
	}


	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "addRelateCompany", method = { RequestMethod.POST })
	public ResultBody addRelateCompany(@RequestBody VOUserInfo userInfo) throws Exception {
		userInfoSV.addRelateCompany(userInfo);
		return ResultBody.success();
	}



	/**
	* @Function: setTest
	* @Description: 设置\关闭测试账号
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-10-28
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-10-28  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "setTest", method = { RequestMethod.POST })
    public ResultBody setTest(@RequestBody VOUserInfo userInfo) throws Exception {
    	userInfoSV.setTest(userInfo);
		return ResultBody.success();
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryOiqUserInfo", method = { RequestMethod.POST })
	public ResultBody qryOiqUserInfo(@RequestBody VOUserInfo userInfo) throws Exception {
		return ResultBody.newInstance(userInfoSV.qryOiqUserInfo(userInfo.getUserPhone()));
	}
}
