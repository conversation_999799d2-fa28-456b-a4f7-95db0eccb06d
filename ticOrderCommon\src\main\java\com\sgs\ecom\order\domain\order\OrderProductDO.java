package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.order.OrderProductDTO;
import com.sgs.ecom.order.entity.order.OrderProduct;
import com.sgs.ecom.order.util.order.OrderUtil;
import com.sgs.ecom.order.vo.VOOrderDetail;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName OrderProductDO
 * <AUTHOR>
 * @Date 2024-06-23 17:32
 */
public class OrderProductDO extends OrderProduct {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public OrderProductDO() {
        super();
    }


    public OrderProduct copyProduct(OrderProductDTO orderProductDTO) {
        OrderProduct orderProduct = new OrderProduct();
        baseCopy.copy(orderProduct,orderProductDTO);
        return orderProduct;
    }

    public void updatePrice(OrderProduct orderProduct, BigDecimal bigDecimal, OrderDetailDTO orderDetailDTO) {
        orderProduct.setQuantity(orderDetailDTO.getBuyNums());
        orderProduct.setPrice(orderDetailDTO.getPrice());
        orderProduct.setTotalPrice(orderDetailDTO.getPrice().multiply(new BigDecimal(orderDetailDTO.getBuyNums())));
        orderProduct.setRealTotalPrice(bigDecimal);
    }

    public OrderProduct initAndCopy(OrderProductDTO orderProduct, VOOrderDetail orderDetailDTO, BigDecimal bigDecimal, String dateStr, List<OrderProductDTO> orderProductDTOS) {
        OrderProduct orderProduct1 = copyProduct(orderProduct);
        orderProduct1.setQuantity(orderDetailDTO.getBuyNums());
        orderProduct1.setPrice(orderDetailDTO.getPrice());
        orderProduct1.setTotalPrice(orderDetailDTO.getPrice().multiply(new BigDecimal(orderDetailDTO.getBuyNums())));
        orderProduct1.setRealTotalPrice(bigDecimal);
        orderProduct1.setCreateDate(dateStr);
        orderProduct1.setState(1);
        if(!ValidationUtil.isEmpty(orderProductDTOS)){
            orderProduct1.setShopDisAmount(ValidationUtil.isEmpty(orderProductDTOS.get(0).getShopDisAmount())?BigDecimal.ZERO:orderProductDTOS.get(0).getShopDisAmount());
        }

        return orderProduct1;
    }

    public void initProductAmount(OrderProduct product, BigDecimal totalAmount, String dateStr, VOOrderDetail detail) {
        product.setTotalPrice(detail.getTotalPrice());
        product.setProductId(null);
        product.setCreateDate(dateStr);
        product.setShopDisAmount(OrderUtil.toZero(product.getShopDisAmount()));
        product.setSubDiscountAmount(OrderUtil.toZero(detail.getSubDiscountAmount()));
        product.setSubCsDiscountAmount(OrderUtil.toZero(detail.getSubCsDiscountAmount()));
        product.setSubServiceAmount(OrderUtil.toZero(detail.getSubServiceAmount()));
        product.setRealTotalPrice(product.getTotalPrice()
                .subtract(product.getShopDisAmount())
                .subtract(detail.getSubDiscountAmount())
                .subtract(detail.getSubCsDiscountAmount())
                .add(detail.getSubServiceAmount()));

        active(product);
    }

    public OrderProduct initDetailToProduct(VOOrderDetail detail, OrderProduct oldOrderProduct) {
        OrderProduct orderProduct = new OrderProduct();
        baseCopy.copy(orderProduct,oldOrderProduct);
        orderProduct.setOrderNo(oldOrderProduct.getOrderNo());
        orderProduct.setShopDisAmount(null);
        orderProduct.setQuantity(detail.getBuyNums());
        orderProduct.setPrice(detail.getPrice());
        orderProduct.setTotalPrice(detail.getPrice().multiply(new BigDecimal(detail.getBuyNums())));
        orderProduct.setShopDisAmount(new BigDecimal(0));
        // 最低售价差额
        orderProduct.setLowestPriceMargin(new BigDecimal(0));
        active(orderProduct);
        return orderProduct;
    }

    private void active(OrderProduct orderProduct) {
        orderProduct.setState(1);
    }
}
