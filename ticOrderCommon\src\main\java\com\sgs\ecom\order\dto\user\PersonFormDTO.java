package com.sgs.ecom.order.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class PersonFormDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String colName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer colLength;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formCode;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String colKey;

	public String getFormName() {
		return formName;
	}

	public void setFormName(String formName) {
		this.formName = formName;
	}

	public String getColName() {
		return colName;
	}

	public void setColName(String colName) {
		this.colName = colName;
	}

	public Integer getColLength() {
		return colLength;
	}

	public void setColLength(Integer colLength) {
		this.colLength = colLength;
	}

	public String getFormCode() {
		return formCode;
	}

	public void setFormCode(String formCode) {
		this.formCode = formCode;
	}

	public String getColKey() {
		return colKey;
	}

	public void setColKey(String colKey) {
		this.colKey = colKey;
	}
}
