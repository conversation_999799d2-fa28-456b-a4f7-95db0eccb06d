package com.sgs.ecom.order.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class UserAccountManagerDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,BU,PERSON_CODE,STATE_DATE,USER_ID,STATE,MANAGER_ID from USER_ACCOUNT_MANAGER"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MANAGER_ID", getName="getManagerId", setName="setManagerId")
 	private long managerId;

 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setPersonCode(String personCode){
 		 this.personCode=personCode;
 	}
 	public String getPersonCode(){
 		 return this.personCode;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setManagerId(long managerId){
 		 this.managerId=managerId;
 	}
 	public long getManagerId(){
 		 return this.managerId;
 	}
 
 	 
}