package com.sgs.ecom.order.service.util.impl;

import com.alibaba.fastjson.JSON;
import com.platform.complex.util.UUID;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.domain.service.order.interfaces.*;
import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.center.EnumDTO;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.dto.detail.OrderReportDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.*;
import com.sgs.ecom.order.dto.order.OrderApplicationAttrDTO;
import com.sgs.ecom.order.dto.order.OrderLinkDTO;
import com.sgs.ecom.order.dto.platform.OrderToOtherDTO;
import com.sgs.ecom.order.enumtool.application.OiqFormEnum;
import com.sgs.ecom.order.enumtool.oiq.OiqReportMethodEnum;
import com.sgs.ecom.order.infrastructure.handle.OrderUtilHandle;
import com.sgs.ecom.order.infrastructure.rpc.template.CenterRespository;
import com.sgs.ecom.order.request.OrderAttributeReq;
import com.sgs.ecom.order.service.member.interfaces.ISSOTemplateSV;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.check.OrderCheckUtil;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.util.select.SelectBaseUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.util.time.TimeCalendarUtil;
import com.sgs.encrypt.Rc4Encrypt;
import com.sgs.redis.RedisClient;
import com.sgs.util.RandomUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class OrderUtilServiceImpl extends BaseService implements IOrderUtilService {
    @Resource
    private Rc4Encrypt rc4Encrypt;
    @Resource
    private RandomUtil randomUtil;
    @Resource
    private RedisClient redisClient;
    @Resource
    private CenterRespository centerRespository;
    @Resource
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Resource
    private ISSOTemplateSV issoTemplateSV;
    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Resource
    private IOrderApplicationFormDomainService orderApplicationFormDomainService;
    @Resource
    private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;
    @Resource
    private IOrderExpressDomainService orderExpressDomainService;
    @Resource
    private IOrderInvoiceDomainService orderInvoiceDomainService;
    @Resource
    private IOrderSampleDomainService orderSampleDomainService;
    @Resource
    private IOrderDetailDomainService orderDetailDomainService;
    @Resource
    private IOrderLinkDomainService orderLinkDomainService;
    @Resource
    private IOrderReportDomainService orderReportDomainService;
    @Resource
    private IOrderShippingDomainService orderShippingDomainService;
    @Resource
    private IOrderShippingContactDomainService orderShippingContactDomainService;
    @Resource
    private OrderUtilHandle orderUtilHandle;


    public String getNewOrderNo(String buCode) {
        return "TIC"+buCode+ UseDateUtil.getDateTimeNum(new Date())+randomUtil.randomString(4).toUpperCase();
    }

    public String getOrderNoByRedis(String userPhone, String buCode) {
        String firstTime=UseDateUtil.getDateNum(new Date());
        String key="OIQ"+firstTime;
        String num=String.valueOf(redisClient.incrNum(key,1));
        redisClient.expire(key,86400);
        DecimalFormat df=new DecimalFormat("0000");
        String str2=df.format(Integer.parseInt(num));

        if(userPhone.length()>4){
            userPhone=userPhone.substring(userPhone.length()-4,userPhone.length());
        }
        if(StringUtils.isBlank(userPhone)){
            int i=(int) (Math.random()*9000+1000);//随机生成一个四位整数
            userPhone= i+"";
        }

        String value=firstTime+userPhone+buCode+str2;
        return value;
    }

    @Override
    public String getTfsPlatFormOrder(String destinationCountry) {
        String year = DateFormatUtils.format(new Date(), "yyyy");
        String key = destinationCountry + "-" + year;
        String num = String.valueOf(redisClient.incrNum(key, 1));
        String formatNum = StringUtils.leftPad(num, 5, "0");
        return key + "-" + formatNum;
    }

    @Override
    public String getNewGroupNo() {
        return  rc4Encrypt.encryRcString(randomUtil.randomString(8), UUID.getID());
    }

    @Override
    public String getNewSampleNo() {
        return  rc4Encrypt.encryRcString(randomUtil.randomString(6), UUID.getID());
    }

    @Override
    public String getAddressInfo(OrderExpressDTO orderExpressDTO) {
        if(orderExpressDTO==null){
            return "";
        }
        StringBuilder stringBuilder=new StringBuilder();
        if(StringUtils.isNotBlank(orderExpressDTO.getReceiptPerson())){
            stringBuilder.append(orderExpressDTO.getReceiptPerson());
            stringBuilder.append(" ");
        }
        if(StringUtils.isNotBlank(orderExpressDTO.getReceiptPhone())){
            stringBuilder.append(orderExpressDTO.getReceiptPhone());
            stringBuilder.append(" ");
        }
        if(StringUtils.isNotBlank(orderExpressDTO.getReceiptCompany())){
            stringBuilder.append(orderExpressDTO.getReceiptCompany());
            stringBuilder.append(",");
        }
        if(!ValidationUtil.isEmpty(orderExpressDTO.getReceiptProvice())){
            stringBuilder.append(orderExpressDTO.getReceiptProvice());

        }
        //北京、上海、天津、重庆
        if(!ValidationUtil.isEmpty(orderExpressDTO) && !ValidationUtil.isEmpty(orderExpressDTO.getReceiptCity()) &&  (orderExpressDTO.getReceiptCity().equals("北京市") ||
                orderExpressDTO.getReceiptCity().equals("上海市") ||
                orderExpressDTO.getReceiptCity().equals("天津市") ||
                orderExpressDTO.getReceiptCity().equals("重庆市"))
        ){
            orderExpressDTO.setReceiptCity("");
        }

        if(!ValidationUtil.isEmpty(orderExpressDTO.getReceiptCity())){
            stringBuilder.append(orderExpressDTO.getReceiptCity());

        }
        if(!ValidationUtil.isEmpty(orderExpressDTO.getReceiptTown())){
            stringBuilder.append(orderExpressDTO.getReceiptTown());

        }
        if(!ValidationUtil.isEmpty(orderExpressDTO.getReceiptAddr())){
            stringBuilder.append(orderExpressDTO.getReceiptAddr());

        }
        stringBuilder.append(" ");

        String str=stringBuilder.toString();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);
        }
        return str;
    }

    @Override
    public Map<String, EnumDTO> getEnumByRedisKey(String redisKey) {
        String items = redisClient.getValue(RedisKeyUtil.ENUM_KEY, redisKey);
        if(StringUtils.isBlank(items)){
            return new HashMap<String, EnumDTO>();
        }
        List<EnumDTO> enumDTOList= JSON.parseArray(items,EnumDTO.class);
        return enumDTOList.stream().collect(Collectors.toMap(EnumDTO::getEnumCode, Function.identity(), (key1, key2) -> key2));
    }

    public List<EnumDTO> getEnumListByRedisKey(String redisKey) {
        String items = redisClient.getValue(RedisKeyUtil.ENUM_KEY, redisKey);
        if(StringUtils.isBlank(items)){
            return new ArrayList<>();
        }
        return JSON.parseArray(items,EnumDTO.class);
    }
    @Override
    public EnumDTO getEnumKey(String redisKey, String key) {
        return getEnumByRedisKey(redisKey).get(key);
    }
    @Override
    public String getEnumStringByKey(String redisKey,String key) {
        String enumStr="";
        EnumDTO enumDTO=getEnumByRedisKey(redisKey).get(key);
        if(enumDTO!=null){
            enumStr=enumDTO.getEnumName();
        }
        return enumStr;
    }
    @Override
    public Map<String,String> getCurrencyMark(String redisKey){
        List<EnumDTO> list=getEnumListByRedisKey(redisKey);
        if(ValidationUtil.isEmpty(list)){
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(EnumDTO::getEnumCode, EnumDTO::getEnumExtend, (key1, key2) -> key2));

    }



    @Override
    public Boolean checkLab(String bu,Long lineId, List<OrderAttributeReq> labNameList) {
        try {
            List<LabDTO> labDTOList = centerRespository.qryLabListByLineId(bu, lineId);
            if (labDTOList == null || labDTOList.isEmpty()) {
                return false;
            }
            Map<String, Object> labMap = new HashMap<>();
            for (LabDTO labDTO : labDTOList) {
                labMap.put(String.valueOf(labDTO.getLabId()), labDTO);
            }
            for (OrderAttributeReq orderAttributeReq : labNameList) {
                if (!labMap.containsKey(orderAttributeReq.getAttrCode())) {
                    return false;
                }

            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    public Map<String, String> getBuNameByRedis() {
        Map <String, String>map=new HashMap<>();
        String json=redisClient.getValue(RedisKeyUtil.TIC_ORDER_BU);
        List<Map> mapList=JSON.parseArray(json,Map.class);
        if(!ValidationUtil.isEmpty(mapList)){
            for (Map m:mapList){
                if(m.containsKey(SelectBaseUtil.BU_NAME) && m.containsKey(SelectBaseUtil.BU)){
                    map.put(m.get(SelectBaseUtil.BU).toString(),m.get(SelectBaseUtil.BU_NAME).toString());
                }
            }
        }
        return map;
    }




    //查询出业务线下的单子
    @Override
    public SysPersonDTO distributionByLineId(Long lineId, String orderType, Long userId) throws Exception{
        Map map=new HashMap<>();
        map.put(SelectMapUtil.USER_ID,userId);
        map.put(SelectMapUtil.LINE_ID,lineId);
        map.put(SelectMapUtil.ORDER_TYPE,orderType);
        SysPersonDTO sysPersonDTO=orderBaseInfoCustomService.qryPersonByLineId(map);
        if(ValidationUtil.isEmpty(sysPersonDTO)){
            return null;
        }
        if(StringUtils.isBlank(sysPersonDTO.getPersonCode())){
            return null;
        }
        SysPersonDTO qryPerson = issoTemplateSV.qryPersonByUser(sysPersonDTO.getPersonCode());
        return OrderCheckUtil.checkLine(qryPerson.getLineItem(),lineId)?sysPersonDTO:null;
    }

    @Override
    public int getNewTemplateTemp(String dateStr) {
        EnumDTO enumDTO=getEnumKey(RedisKeyUtil.OIQ_NEW_TEMPLATE_DATE,RedisKeyUtil.NEW_TEMPLATE);
        if(!ValidationUtil.isEmpty(enumDTO)){
            Date pdfDate= TimeCalendarUtil.getStringToDate(enumDTO.getEnumName());
            Date orderDate=TimeCalendarUtil.getStringToDate(dateStr);
            if(orderDate.before(pdfDate)){
                return 0;
            }
        }
        return 1;
    }

    @Override
    public String getEnumStringByKeys(String redisKey, String key) {
        Map<String, EnumDTO> enumDTOMap = getEnumByRedisKey(redisKey);
        String[] split = key.split(",");
        if(ValidationUtil.isEmpty(split))
            return "";

        StringJoiner stringJoiner = new StringJoiner(",");
        for (String keys :split){
            EnumDTO enumDTO = enumDTOMap.get(keys);
            if(!ValidationUtil.isEmpty(enumDTO)){
                stringJoiner.add(enumDTO.getEnumName());
            }

        }
        return stringJoiner.toString();
    }

    @Override
    public String getLineEnumByKey(String redisKey,String key) {
        String line=getEnumStringByKey(redisKey,key);
        return StringUtils.isBlank(line)?"IND-ML":line;
    }

    @Override
    public OrderToOtherDTO qryOrderToOther(String orderNo)throws Exception{
        OrderToOtherDTO orderToOtherDTO=new OrderToOtherDTO();
        orderToOtherDTO.setOrderNo(orderNo);
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(orderNo);
        orderToOtherDTO.setBaseInfo(baseOrderDTO);
        OrderApplicationFormDTO orderApplicationFormDTO=orderApplicationFormDomainService.selectDTOByOrderNo(orderNo);
        orderToOtherDTO.setApplicationForm(orderApplicationFormDTO);
        List<OrderApplicationAttrDTO> attrDTOList=orderApplicationAttrDomainService.selectListByOrderNo(orderNo);
        orderToOtherDTO.setApplicationAttr(attrDTOList.stream().filter(a->!a.getAttrCode().equals(OiqFormEnum.TEST_MEMO_IMG.getName())).collect(Collectors.toList()));

        List<OrderExpressDTO> expressDTOList=orderExpressDomainService.selectExpress(orderNo,null);
        orderToOtherDTO.setDelivers(expressDTOList);

        OrderInvoiceDTO orderInvoiceDTO=orderInvoiceDomainService.qryOneOrderInvoiceByOrder(orderNo);
        orderToOtherDTO.setInvoice(orderInvoiceDTO);
        List<OiqSampleDTO> oiqSampleDTOList=orderSampleDomainService.getOiqOrderSample(orderNo,baseOrderDTO.getGroupNo(),baseOrderDTO.getApplicationLineId(),true);


        for(int n=0;n<oiqSampleDTOList.size();n++){
            oiqSampleDTOList.get(n).setSampleAttr(oiqSampleDTOList.get(n).getSampleFromDTOList());
        }
        orderToOtherDTO.setSamples(oiqSampleDTOList);
        List<OiqItemDTO> itemDTOList=orderDetailDomainService.getItemByOrderNo(orderNo,baseOrderDTO.getGroupNo(),oiqSampleDTOList);
        orderToOtherDTO.setItems(itemDTOList);

        List<OrderLinkDTO> linkDTOList=orderLinkDomainService.selectListByOrderNo(orderNo);
        orderToOtherDTO.setOrderLink(linkDTOList);
        OrderReportDTO orderReportDTO=orderReportDomainService.qryOrderReport(orderNo);
        if(!ValidationUtil.isEmpty(orderReportDTO)){
            OiqReportDTO oiqReportDTO=new OiqReportDTO();
            baseCopyObj.copyWithNull(oiqReportDTO,orderReportDTO);
            if(OiqReportMethodEnum.OTHER.getIndex()==oiqReportDTO.getReportMethod()){
                oiqReportDTO.setReportMethodShow("报告出具要求："+oiqReportDTO.getReportMethodMemo());
            }
            orderToOtherDTO.setReport(oiqReportDTO);
        }

        OrderShippingDTO orderShippingDTO=orderShippingDomainService.qryOrderShippingByOrder(orderNo);
        //航运枚举单独出来
        Map<String, EnumDTO> enumDTOMap=orderUtilHandle.getEnumByRedisKey(RedisKeyUtil.FREIGHT_PAYMENT_TYPE);
        if(!ValidationUtil.isEmpty(orderShippingDTO) &&enumDTOMap.containsKey(orderShippingDTO.getShippingPaymentMethod())){
            orderShippingDTO.setShippingPaymentMethod(enumDTOMap.get(orderShippingDTO.getShippingPaymentMethod()).getEnumName());
        }
        orderToOtherDTO.setOrderShipping(orderShippingDTO);
        List<OrderShippingContactDTO> contactDTOList=orderShippingContactDomainService.qryOrderShippingContactListByOrderNo(orderNo);
        orderToOtherDTO.setContactDTOList(contactDTOList);
        System.out.println("orderToBase="+JSON.toJSONString(orderToOtherDTO));
        return orderToOtherDTO;
    }
}
