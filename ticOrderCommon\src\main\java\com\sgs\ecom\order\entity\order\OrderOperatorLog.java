package com.sgs.ecom.order.entity.order;

import java.math.BigDecimal;

public class OrderOperatorLog {

    private BigDecimal originalPrice;

    private String detailNo;

    private String csCode;

    private BigDecimal currentPrice;

    private String operatorText;

    private Integer orderType;

    private String orderNo;

    private Long logId;

    private String operatorDate;

    private Integer operatorType;

    private String memo;

    private String fileUrl;

    private int isShow;

    private int isComfirm;
    private String currency;



    public void setDetailNo(String detailNo){
        this.detailNo=detailNo;
    }
    public String getDetailNo(){
        return this.detailNo;
    }


    public void setCsCode(String csCode){
        this.csCode=csCode;
    }
    public String getCsCode(){
        return this.csCode;
    }




    public void setOperatorText(String operatorText){
        this.operatorText=operatorText;
    }
    public String getOperatorText(){
        return this.operatorText;
    }


    public void setOrderType(Integer orderType){
        this.orderType=orderType;
    }
    public Integer getOrderType(){
        return this.orderType;
    }


    public void setOrderNo(String orderNo){
        this.orderNo=orderNo;
    }
    public String getOrderNo(){
        return this.orderNo;
    }


    public void setLogId(Long logId){
        this.logId=logId;
    }
    public Long getLogId(){
        return this.logId;
    }


    public void setOperatorDate(String operatorDate){
        this.operatorDate=operatorDate;
    }
    public String getOperatorDate(){
        return this.operatorDate;
    }


    public void setOperatorType(Integer operatorType){
        this.operatorType=operatorType;
    }
    public Integer getOperatorType(){
        return this.operatorType;
    }


    public void setMemo(String memo){
        this.memo=memo;
    }
    public String getMemo(){
        return this.memo;
    }


    public void setFileUrl(String fileUrl){
        this.fileUrl=fileUrl;
    }
    public String getFileUrl(){
        return this.fileUrl;
    }

    public int getIsShow() {
        return isShow;
    }

    public void setIsShow(int isShow) {
        this.isShow = isShow;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getCurrentPrice() {
        return currentPrice;
    }

    public void setCurrentPrice(BigDecimal currentPrice) {
        this.currentPrice = currentPrice;
    }

    public int getIsComfirm() {
        return isComfirm;
    }

    public void setIsComfirm(int isComfirm) {
        this.isComfirm = isComfirm;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
