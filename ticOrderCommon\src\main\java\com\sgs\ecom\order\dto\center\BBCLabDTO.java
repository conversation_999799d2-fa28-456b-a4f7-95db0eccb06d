package com.sgs.ecom.order.dto.center;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import org.apache.commons.lang.StringUtils;

public class BBCLabDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long labId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long labCategoryId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String province;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String city;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String town;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String address;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String person;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String phone;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String zipCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createTime;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String updateTime;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String accountNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String weichatNo;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int isDefault;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labAddressShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryName;

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public int getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(int isDefault) {
        this.isDefault = isDefault;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public Long getLabCategoryId() {
        return labCategoryId;
    }

    public void setLabCategoryId(Long labCategoryId) {
        this.labCategoryId = labCategoryId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }



    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getPerson() {
        return person;
    }

    public void setPerson(String person) {
        this.person = person;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getZipCode() {
        return zipCode;
    }

    public void setZipCode(String zipCode) {
        this.zipCode = zipCode;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getWeichatNo() {
        return weichatNo;
    }

    public void setWeichatNo(String weichatNo) {
        this.weichatNo = weichatNo;
    }

    public String getLabAddressShow() {

        StringBuilder stringBuilder=new StringBuilder();
        if(StringUtils.isNotBlank(province)){
            stringBuilder.append(province);
        }
        if(StringUtils.isNotBlank(city)){
            stringBuilder.append(city);
        }
        if(StringUtils.isNotBlank(town)){
            stringBuilder.append(town);
        }
        if(StringUtils.isNotBlank(address)){
            stringBuilder.append(address);
        }

        return stringBuilder.toString();
    }

}
