package com.sgs.ecom.order.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno; 

public class BillLogDTO{
 
 	public static final String CREATE_SQL = "select CS_CODE,CREATE_DATE,BILL_ID,BILL_CYCLE,STATE_DATE,STATE,LOG_ID,REMARK,CS_ID from TB_BILL_LOG"; 
 
 
 	@ApiAnno(serviceName={"qryBillLog"})
 	@BeanAnno(value="CS_CODE", getName="getCsCode", setName="setCsCode")
 	private String csCode;
 	@ApiAnno(serviceName={"qryBillLog"})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@IsNullAnno(serviceName={"qryBillLog"})
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="BILL_ID", getName="getBillId", setName="setBillId")
 	private long billId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="BILL_CYCLE", getName="getBillCycle", setName="setBillCycle")
 	private long billCycle;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@IsNullAnno(serviceName={"delBillLog"})
 	@ApiAnno(serviceName={"qryBillLog"})
 	@BeanAnno(value="LOG_ID", getName="getLogId", setName="setLogId")
 	private long logId;
 	@ApiAnno(serviceName={"qryBillLog"})
 	@BeanAnno(value="REMARK", getName="getRemark", setName="setRemark")
 	private String remark;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CS_ID", getName="getCsId", setName="setCsId")
 	private long csId;

 	public void setCsCode(String csCode){
 		 this.csCode=csCode;
 	}
 	public String getCsCode(){
 		 return this.csCode;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBillId(long billId){
 		 this.billId=billId;
 	}
 	public long getBillId(){
 		 return this.billId;
 	}
 
 	 
 	public void setBillCycle(long billCycle){
 		 this.billCycle=billCycle;
 	}
 	public long getBillCycle(){
 		 return this.billCycle;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setLogId(long logId){
 		 this.logId=logId;
 	}
 	public long getLogId(){
 		 return this.logId;
 	}
 
 	 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
 	public void setCsId(long csId){
 		 this.csId=csId;
 	}
 	public long getCsId(){
 		 return this.csId;
 	}
 
 	 
}