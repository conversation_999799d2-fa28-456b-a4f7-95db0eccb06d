package com.sgs.ecom.order.dto.center;

public class EnumDTO {
    private int configId;
    private String colName;
    private String enumCode;
    private String enumExtend;
    private String enumName;
    private int isFill;
    private String remark;
    private String showSort;
    private String tableName;
    private String labAccount;//实验室才需要查询对应的银行账号

    public int getConfigId() {
        return configId;
    }

    public void setConfigId(int configId) {
        this.configId = configId;
    }

    public String getColName() {
        return colName;
    }

    public void setColName(String colName) {
        this.colName = colName;
    }

    public String getEnumCode() {
        return enumCode;
    }

    public void setEnumCode(String enumCode) {
        this.enumCode = enumCode;
    }

    public String getEnumName() {
        return enumName;
    }

    public void setEnumName(String enumName) {
        this.enumName = enumName;
    }

    public int getIsFill() {
        return isFill;
    }

    public void setIsFill(int isFill) {
        this.isFill = isFill;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getShowSort() {
        return showSort;
    }

    public void setShowSort(String showSort) {
        this.showSort = showSort;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getLabAccount() {
        return labAccount;
    }

    public void setLabAccount(String labAccount) {
        this.labAccount = labAccount;
    }

    public String getEnumExtend() {
        return enumExtend;
    }

    public void setEnumExtend(String enumExtend) {
        this.enumExtend = enumExtend;
    }
}
