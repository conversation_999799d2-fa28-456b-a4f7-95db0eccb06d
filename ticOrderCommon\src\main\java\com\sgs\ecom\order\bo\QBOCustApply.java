package com.sgs.ecom.order.bo; 
 
import java.sql.Timestamp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.CharacterVaild;
import com.platform.util.json.TimeFormatSerializer; 

public class QBOCustApply{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.CUST_ID,a.CUST_CODE,a.COMPANY_NAME,a.COMPANY_NAME_EN,a.COUNTRY,a.PROVICE,a.CITY,a.TOWN,a.ADDRESS,a.CREDIT_AMOUNT,a.STATE,a.CREATE_DATE,a.STATE_DATE,a.IS_FREEZE,a.MEMO,b.user_id,c.user_name,c.user_phone,c.user_nick from TB_CUST_INFO a,TB_CUST_APPLY_RELATE b,USER_INFO c where a.cust_id=b.cust_id and b.user_id=c.user_id and a.state=1 and c.state=1"; 
 
 	public static final String OWNER ="member";

 	public static final String IS_FREEZE="isFreeze";
 	public static final String CUST_ID="custId";
 	public static final String PROVICE="provice";
 	public static final String CREDIT_AMOUNT="creditAmount";
 	public static final String USER_NAME="userName";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String USER_NICK="userNick";
 	public static final String COMPANY_NAME="companyName";
 	public static final String CREATE_DATE="createDate";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String USER_ID="userId";
 	public static final String ADDRESS="address";
 	public static final String USER_PHONE="userPhone";
 	public static final String CUST_CODE="custCode";
 	public static final String COMPANY_NAME_EN="companyNameEn";
 	public static final String MEMO="memo";

 	@BeanAnno(value="IS_FREEZE",table="a")
 	private int isFreeze;
 	@BeanAnno(value="CUST_ID",table="a")
 	private long custId;
 	@BeanAnno(value="PROVICE",table="a")
 	private String provice;
 	@BeanAnno(value="CREDIT_AMOUNT",table="a")
 	private double creditAmount;
 	@BeanAnno(value="user_name",table="c")
 	private String userName;
 	@BeanAnno(value="STATE_DATE",table="a")
 	private Timestamp stateDate;
 	@BeanAnno(value="STATE",table="a")
 	private int state;
 	@BeanAnno(value="user_nick",table="c")
 	private String userNick;
 	@BeanAnno(value="COMPANY_NAME",table="a")
 	private String companyName;
 	@BeanAnno(value="CREATE_DATE",table="a")
 	private Timestamp createDate;
 	@BeanAnno(value="COUNTRY",table="a")
 	private String country;
 	@BeanAnno(value="CITY",table="a")
 	private String city;
 	@BeanAnno(value="TOWN",table="a")
 	private String town;
 	@BeanAnno(value="user_id",table="b")
 	private long userId;
 	@BeanAnno(value="ADDRESS",table="a")
 	private String address;
 	@BeanAnno(value="user_phone",table="c")
 	private String userPhone;
 	@BeanAnno(value="CUST_CODE",table="a")
 	private String custCode;
 	@BeanAnno(value="COMPANY_NAME_EN",table="a")
 	private String companyNameEn;
 	@BeanAnno(value="MEMO",table="a")
 	private String memo;

 	public void setIsFreeze(int isFreeze){
 		 this.isFreeze=isFreeze;
 	}
 	public int getIsFreeze(){
 		 return this.isFreeze;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCreditAmount(double creditAmount){
 		 this.creditAmount=creditAmount;
 	}
 	public double getCreditAmount(){
 		 return this.creditAmount;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	@CharacterVaild(len = 11) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
}