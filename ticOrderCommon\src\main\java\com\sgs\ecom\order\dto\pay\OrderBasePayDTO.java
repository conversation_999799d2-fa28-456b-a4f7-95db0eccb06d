package com.sgs.ecom.order.dto.pay;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;
import java.util.List;

public class OrderBasePayDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private  Long orderId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderType;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int payState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int subState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long userId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportForm;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportLua;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int refundState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String groupNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long labId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String relateOrderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payMethod;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bu;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String toOrderNo;
//    @ApiAnno(groups={BaseQryFilter.Default.class})
//    private List<OrderProductDTO> orderProductDTOList;
    private List<Long> labIdList;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int monthPay;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int isPayReceived;

    public int getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(int isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}

	public List<Long> getLabIdList() {
        return labIdList;
    }

    public void setLabIdList(List<Long> labIdList) {
        this.labIdList = labIdList;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getReportForm() {
        return reportForm;
    }

    public void setReportForm(String reportForm) {
        this.reportForm = reportForm;
    }

    public String getReportLua() {
        return reportLua;
    }

    public void setReportLua(String reportLua) {
        this.reportLua = reportLua;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }

    public int getRefundState() {
        return refundState;
    }

    public void setRefundState(int refundState) {
        this.refundState = refundState;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getToOrderNo() {
        return toOrderNo;
    }

    public void setToOrderNo(String toOrderNo) {
        this.toOrderNo = toOrderNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

//    public List<OrderProductDTO> getOrderProductDTOList() {
//        return orderProductDTOList;
//    }
//
//    public void setOrderProductDTOList(List<OrderProductDTO> orderProductDTOList) {
//        this.orderProductDTOList = orderProductDTOList;
//    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }
}
