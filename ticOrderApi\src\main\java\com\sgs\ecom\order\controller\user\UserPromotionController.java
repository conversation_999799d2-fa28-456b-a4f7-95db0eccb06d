package com.sgs.ecom.order.controller.user;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.base.EditReq;
import com.sgs.ecom.order.request.user.UserPromotionReq;
import com.sgs.ecom.order.service.user.interfaces.IUserPromotionSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/business/api.v1.user/promotion")
public class UserPromotionController extends ControllerUtil {
	@Autowired
	private IUserPromotionSV userPromotionSV;
	/**
	*@Function: qryUser
	*@Description
	*@param: [userInfo]
	*@author: Xiwei_Qiu @date: 2022/3/18 @version:
	**/
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryList", method = { RequestMethod.POST })
	public ResultBody qryList(@RequestBody UserPromotionReq userPromotionReq) throws Exception {
		return ResultBody.newInstance(userPromotionSV.getPageList(userPromotionReq));
	}

	/**
	 * @Description: 用户活动列表:统计个数
	 * @Author: bowen zhang
	 * @Date: 2022/5/31
	 * @param userPromotionReq:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryCountList", method = { RequestMethod.POST })
	public ResultBody qryCountList(@RequestBody UserPromotionReq userPromotionReq) throws Exception {
		return ResultBody.newInstance(userPromotionSV.qryCountList(userPromotionReq));
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "submit", method = { RequestMethod.POST })
	public ResultBody submit(@RequestHeader(value="accessToken") String token,
							 @RequestBody EditReq editReq) throws Exception {
		userPromotionSV.submit(editReq,getPersonInfo(token));
		return ResultBody.success();
	}

	/**
	 * 导出excel
	 * @param
	 * @throws Exception
	 */
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "expActivityList", method = {RequestMethod.POST})
	public void expOrderList(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody UserPromotionReq userPromotionReq,
		HttpServletResponse res
	) throws Exception {
		userPromotionSV.expActivityList(userPromotionReq, getPersonInfo(token), res);
	}



}
