package com.sgs.ecom.order.controller.user;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.service.user.interfaces.IUserInfoSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOUserInfo;

@RestController
@RequestMapping("/business/api.v1.user/company")
public class UserCompanyController extends ControllerUtil {

	@Autowired
	private IUserInfoSV userInfoSV;
	
    /**   
	* @Function: syncUserCompany
	* @Description: 同步用户公司
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "sync", method = { RequestMethod.POST })
    public ResultBody syncUserCompany(@RequestBody VOUserInfo userInfo) throws Exception {
    	userInfoSV.syncUserCompany(userInfo);
		return ResultBody.success();
	}
}
