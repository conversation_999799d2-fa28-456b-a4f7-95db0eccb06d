package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderShareService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/order/share")
public class OrderShareController extends ControllerUtil {

	@Autowired
	private IOrderShareService orderShareService;


	/**
	 * @Function: getShareUrl
	 * @Description 不校验权限
	 * @param: [orderNo, httpServletResponse]
	 * @author: Xiwei_Qiu @date: 2021/6/3 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "getShare", method = {RequestMethod.POST})
	public ResultBody createShare(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderNoReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderShareService.createShare(orderNoReq));
	}





}
