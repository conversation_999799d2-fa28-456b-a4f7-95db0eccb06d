package com.sgs.ecom.order.entity;

import javax.persistence.Id;
import java.math.BigDecimal;
import java.util.Date;

public class CustInfo {
    @Id
    private Long custId;

    private String custCode;

    private String companyName;

    private String companyNameEn;

    private String country;

    private String provice;

    private String city;

    private String town;

    private String address;

    private String addressEn;

    private BigDecimal creditAmount;

    private Integer state;

    private Date createDate;

    private Date stateDate;

    private Integer isFreeze;

    private String memo;

    private String bossNo;

    private String vbaAccount;

    private Integer settleType;

    private String belongArea;

    private String busiCode;

    private BigDecimal reportPrice;

    private String belongLabCode;

    private Integer isDelete;

    private String linkEmail;
    
    private String currency;
    
    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustCode() {
        return custCode;
    }

    public void setCustCode(String custCode) {
        this.custCode = custCode == null ? null : custCode.trim();
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }

    public String getCompanyNameEn() {
        return companyNameEn;
    }

    public void setCompanyNameEn(String companyNameEn) {
        this.companyNameEn = companyNameEn == null ? null : companyNameEn.trim();
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country == null ? null : country.trim();
    }

    public String getProvice() {
        return provice;
    }

    public void setProvice(String provice) {
        this.provice = provice == null ? null : provice.trim();
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city == null ? null : city.trim();
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town == null ? null : town.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getAddressEn() {
        return addressEn;
    }

    public void setAddressEn(String addressEn) {
        this.addressEn = addressEn == null ? null : addressEn.trim();
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Date getStateDate() {
        return stateDate;
    }

    public void setStateDate(Date stateDate) {
        this.stateDate = stateDate;
    }

    public Integer getIsFreeze() {
        return isFreeze;
    }

    public void setIsFreeze(Integer isFreeze) {
        this.isFreeze = isFreeze;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public String getBossNo() {
        return bossNo;
    }

    public void setBossNo(String bossNo) {
        this.bossNo = bossNo == null ? null : bossNo.trim();
    }

    public String getVbaAccount() {
        return vbaAccount;
    }

    public void setVbaAccount(String vbaAccount) {
        this.vbaAccount = vbaAccount == null ? null : vbaAccount.trim();
    }

    public Integer getSettleType() {
        return settleType;
    }

    public void setSettleType(Integer settleType) {
        this.settleType = settleType;
    }

    public String getBelongArea() {
        return belongArea;
    }

    public void setBelongArea(String belongArea) {
        this.belongArea = belongArea == null ? null : belongArea.trim();
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode == null ? null : busiCode.trim();
    }

    public BigDecimal getReportPrice() {
        return reportPrice;
    }

    public void setReportPrice(BigDecimal reportPrice) {
        this.reportPrice = reportPrice;
    }

    public String getBelongLabCode() {
        return belongLabCode;
    }

    public void setBelongLabCode(String belongLabCode) {
        this.belongLabCode = belongLabCode == null ? null : belongLabCode.trim();
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

	public String getLinkEmail() {
		return linkEmail;
	}

	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail == null ? null : linkEmail.trim();
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency == null ? null : currency.trim();
	}
    
}