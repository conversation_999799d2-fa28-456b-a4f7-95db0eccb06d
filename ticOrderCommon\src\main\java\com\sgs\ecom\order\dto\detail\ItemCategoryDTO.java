package com.sgs.ecom.order.dto.detail;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;

/**
 * <AUTHOR>
 */
public class ItemCategoryDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String categoryPath;

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }
}
