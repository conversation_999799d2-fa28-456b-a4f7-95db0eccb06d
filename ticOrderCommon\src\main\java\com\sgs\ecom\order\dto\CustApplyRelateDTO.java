package com.sgs.ecom.order.dto; 
 
import java.sql.Timestamp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

public class CustApplyRelateDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select IS_FREEZE,CUST_ID,PROVICE,CREDIT_AMOUNT,user_name,STATE_DATE,STATE,user_nick,COMPANY_NAME,CREATE_DATE,COUNTRY,CITY,TOWN,user_id,ADDRESS,user_phone,CUST_CODE,COMPANY_NAME_EN,MEMO from TB_CUST_INFO a,TB_CUST_APPLY_RELATE b,USER_INFO c where a.cust_id=b.cust_id and b.user_id=c.user_id and a.state=1 and c.state=1"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="IS_FREEZE", getName="getIsFreeze", setName="setIsFreeze")
 	private int isFreeze;
 	@IsNullAnno(serviceName={"relateUser"})
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class}, serviceName={"qryRelate"})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREDIT_AMOUNT", getName="getCreditAmount", setName="setCreditAmount")
 	private double creditAmount;
 	@ApiAnno(serviceName={"qryCust","qryRelate"})
 	@BeanAnno(value="user_name", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(serviceName={"qryCust","qryRelate"})
 	@BeanAnno(value="user_nick", getName="getUserNick", setName="setUserNick")
 	private String userNick;
 	@ApiAnno(serviceName={"qryRelate","qryUser","qryCust"})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COUNTRY", getName="getCountry", setName="setCountry")
 	private String country;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@IsNullAnno(serviceName={"relateUser"})
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class}, serviceName={"qryRelate"})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="ADDRESS", getName="getAddress", setName="setAddress")
 	private String address;
 	@ApiAnno(serviceName={"qryCust","qryRelate"})
 	@BeanAnno(value="user_phone", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(serviceName={"qryUser","qryCust","qryRelate"})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="TYPE", getName="getType", setName="setType")
 	private String type;

 	public void setIsFreeze(int isFreeze){
 		 this.isFreeze=isFreeze;
 	}
 	public int getIsFreeze(){
 		 return this.isFreeze;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCreditAmount(double creditAmount){
 		 this.creditAmount=creditAmount;
 	}
 	public double getCreditAmount(){
 		 return this.creditAmount;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
 
 	 
}