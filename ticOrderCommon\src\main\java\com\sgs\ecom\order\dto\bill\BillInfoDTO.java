package com.sgs.ecom.order.dto.bill;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.util.json.DateFormatSerializer;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.enumtool.bill.BillBuEnum;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class BillInfoDTO{
 
 	public static final String CREATE_SQL = "select CUST_ID,ORDER_NUM,bill_start_date,CUSTOMER_NUMBER,STATE_DATE,STATE,COMPANY_NAME,INVOICE_ID,CREATE_DATE,BILL_ID,STORE_NAME,PAY_STATE,BILL_CYCLE,BILL_DATE,CUST_CODE,INVOICE_TITLE,PAYMENT_CODE,BILL_AMOUNT,bill_end_date,LAB_NAME from TB_BILL_INFO";

 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="ORDER_NUM", getName="getOrderNum", setName="setOrderNum")
 	private long orderNum;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="bill_start_date", getName="getBillStartDate", setName="setBillStartDate")
 	private Date billStartDate;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="CUSTOMER_NUMBER", getName="getCustomerNumber", setName="setCustomerNumber")
 	private String customerNumber;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(serviceName={},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="INVOICE_ID", getName="getInvoiceId", setName="setInvoiceId")
 	private long invoiceId;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@IsNullAnno(serviceName={"expBill","ticketByBill","payByBill"})
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="BILL_ID", getName="getBillId", setName="setBillId")
 	private long billId;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="STORE_NAME", getName="getStoreName", setName="setStoreName")
 	private String storeName;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="PAY_STATE", getName="getPayState", setName="setPayState")
 	private int payState;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="BILL_CYCLE", getName="getBillCycle", setName="setBillCycle")
 	private String billCycle;
 	@ApiAnno(serviceName={},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="BILL_DATE", getName="getBillDate", setName="setBillDate")
 	private Timestamp billDate;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="INVOICE_TITLE", getName="getInvoiceTitle", setName="setInvoiceTitle")
 	private String invoiceTitle;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="PAYMENT_CODE", getName="getPaymentCode", setName="setPaymentCode")
 	private String paymentCode;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="BILL_AMOUNT", getName="getBillAmount", setName="setBillAmount")
 	private double billAmount;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="bill_end_date", getName="getBillEndDate", setName="setBillEndDate")
 	private Date billEndDate;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
	@ApiAnno(serviceName={"qryBill"},groups={BaseQryFilter.Default.class})
	@BeanAnno(value="bu", getName="getBu", setName="setBu")
	private String bu;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<OrderExpressDTO> orderExpressDTOList;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<Map> userList=new ArrayList<>();
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<OrderAttachmentDTO> fileReqList=new ArrayList<>();

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String categoryName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int sendMailFlg;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Long labId;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Long categoryId;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String labName;

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public List<Map> getUserList() {
		return userList;
	}

	public void setUserList(List<Map> userList) {
		this.userList = userList;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setOrderNum(long orderNum){
 		 this.orderNum=orderNum;
 	}
 	public long getOrderNum(){
 		 return this.orderNum;
 	}
 
 	 
 	public void setBillStartDate(Date billStartDate){
 		 this.billStartDate=billStartDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getBillStartDate(){
 		 return this.billStartDate;
 	}
 
 	 
 	public void setCustomerNumber(String customerNumber){
 		 this.customerNumber=customerNumber;
 	}
 	public String getCustomerNumber(){
 		 return this.customerNumber;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBillId(long billId){
 		 this.billId=billId;
 	}
 	public long getBillId(){
 		 return this.billId;
 	}
 
 	 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		return BillBuEnum.getNameCh(bu);
 	}
 
 	 
 	public void setPayState(int payState){
 		 this.payState=payState;
 	}
 	public int getPayState(){
 		 return this.payState;
 	}
 
 	 
 	public void setBillCycle(String billCycle){
 		 this.billCycle=billCycle;
 	}
 	public String getBillCycle(){
 		 return this.billCycle;
 	}
 
 	 
 	public void setBillDate(Timestamp billDate){
 		 this.billDate=billDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getBillDate(){
 		 return this.billDate;
 	}
 
 	 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setPaymentCode(String paymentCode){
 		 this.paymentCode=paymentCode;
 	}
 	public String getPaymentCode(){
 		 return this.paymentCode;
 	}
 
 	 
 	public void setBillAmount(double billAmount){
 		 this.billAmount=billAmount;
 	}
 	public double getBillAmount(){
 		 return this.billAmount;
 	}
 
 	 
 	public void setBillEndDate(Date billEndDate){
 		 this.billEndDate=billEndDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getBillEndDate(){
 		 return this.billEndDate;
 	}
 	
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserPhone() {
		return userPhone;
	}
	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public List<OrderExpressDTO> getOrderExpressDTOList() {
		return orderExpressDTOList;
	}

	public void setOrderExpressDTOList(List<OrderExpressDTO> orderExpressDTOList) {
		this.orderExpressDTOList = orderExpressDTOList;
	}

	public List<OrderAttachmentDTO> getFileReqList() {
		return fileReqList;
	}

	public void setFileReqList(List<OrderAttachmentDTO> fileReqList) {
		this.fileReqList = fileReqList;
	}

	public int getSendMailFlg() {
		return sendMailFlg;
	}

	public void setSendMailFlg(int sendMailFlg) {
		this.sendMailFlg = sendMailFlg;
	}
}