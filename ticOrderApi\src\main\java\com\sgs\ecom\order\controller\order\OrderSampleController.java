package com.sgs.ecom.order.controller.order;


import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.OrderSampleReq;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.rpc.CenterBusinessReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderDetailService;
import com.sgs.ecom.order.service.order.interfaces.IOrderSampleService;
import com.sgs.ecom.order.service.util.interfaces.IDetailService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/sample")
public class OrderSampleController extends ControllerUtil {

    @Autowired
    private IOrderDetailService orderDetailService;
    @Autowired
    private IOrderSampleService orderSampleService;
    @Autowired
    private IDetailService detailService;



    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "saveSample", method = { RequestMethod.POST })
    public ResultBody saveSample(
            @RequestBody OrderSampleReq orderSampleReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.success(orderDetailService.saveOrderSample(orderSampleReq,getPersonInfo(token),getPower(token)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "saveQuestionSample", method = { RequestMethod.POST })
    public ResultBody saveQuestionSample(
        @RequestBody OrderSampleReq orderSampleReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(detailService.saveQuestionSample(orderSampleReq,getPersonInfo(token),getPower(token)));
    }



    /**
    *@Function: delSample
    *@Description 删除样品信息
    *@param: [orderSampleReq, token]
    *@author: Xiwei_Qiu @date: 2021/11/24 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "delSample", method = { RequestMethod.POST })
    public ResultBody delSample(
            @RequestBody OrderSampleReq orderSampleReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        orderSampleService.deleteByPrimaryKey(orderSampleReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }


    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qrySampleList", method = { RequestMethod.POST })
    public ResultBody qrySampleList(
            @RequestBody OrderSampleReq orderSampleReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderSampleService.qrySampleList(orderSampleReq,getPersonInfo(token),getPower(token)));
    }

    /**
    *@Function: orderList（样品属性修改）
    *@Description 查询订单的样品（）
    *@param: [orderSampleReq, token]
    *@author: Xiwei_Qiu @date: 2022/5/7 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "orderSampleList", method = { RequestMethod.POST })
    public ResultBody orderSampleList(
        @RequestBody OrderIdReq orderIdReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderSampleService.orderSampleList(orderIdReq,getPersonInfo(token),getPower(token)));
    }

    /**
    *@Function: modSampleRequirements
    *@Description 修改样品说明
    *@param: [orderSampleReq, token]
    *@author: Xiwei_Qiu @date: 2022/3/8 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "modSampleRequirements", method = { RequestMethod.POST })
    public ResultBody modSampleRequirements(
        @RequestBody OrderSampleReq orderSampleReq,
        @RequestHeader(value="accessToken") String token) throws Exception {
        orderDetailService.modSampleRequirements(orderSampleReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }


    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryBaseSample", method = { RequestMethod.POST })
    public ResultBody qryBaseSample(
            @RequestBody CenterBusinessReq centerBusinessReq) throws Exception {
        return ResultBody.newInstance(orderSampleService.qryBaseSample(centerBusinessReq));
    }

}
