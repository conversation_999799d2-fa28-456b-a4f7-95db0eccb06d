package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

@ExplainAnno(value="enum",name="报告语言")
public enum TransReportFormEnum implements EnumMessage {


    ONE("纸质报告", 1),
    ELECTRON("电子报告", 2),
    ONE_AND_ELECTRON("电子报告+纸质报告", 3);

    // 成员变量
    private String name;
    private int index;
    // 构造方法electron
    private TransReportFormEnum(String name, int index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(int index) {
        for (TransReportFormEnum c : TransReportFormEnum.values()) {
            if (c.getIndex()== index ) {
                return c.name;  
            }  
        }  
        return null;  
    }

    public static int getIndex(String name) {
        for (TransReportFormEnum c : TransReportFormEnum.values()) {
            if (c.getName().equals(name)) {
                return c.index;
            }
        }
        return 4;
    }
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public int getIndex() {
        return index;
    }

	@Override
	public String getKey() {
		return String.valueOf(index);
	}

	@Override
	public String getValue() {
		return name;
	}  
}
