package com.sgs.ecom.order.dto.mq;

public class MqUserChangeDTO {
    //原始
    private Long margeUserId;
    private String margeAccountNo;
    //新
    private Long userId;
    private String accountNo;

    private int isUnactivity;//0不合

    public Long getMargeUserId() {
        return margeUserId;
    }

    public void setMargeUserId(Long margeUserId) {
        this.margeUserId = margeUserId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getMargeAccountNo() {
        return margeAccountNo;
    }

    public void setMargeAccountNo(String margeAccountNo) {
        this.margeAccountNo = margeAccountNo;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public int getIsUnactivity() {
        return isUnactivity;
    }

    public void setIsUnactivity(int isUnactivity) {
        this.isUnactivity = isUnactivity;
    }
}
