package com.sgs.ecom.order.dto.pdf;

import com.sgs.ecom.order.dto.order.OrderBaseInfoMoreDTO;
import com.sgs.ecom.order.enumtool.application.TestCycleEnum;

public class QuotationOtherDTO {

	private String testCycleName;

	private String testCycleMemo;

	private String testCycle;

	private String stateDate;

	private String recommendReason;


	public QuotationOtherDTO() {
	}

	public QuotationOtherDTO(OrderBaseInfoMoreDTO orderBaseInfoMoreDTO) {
		this.testCycleName =  TestCycleEnum.getNameCh(String.valueOf(orderBaseInfoMoreDTO.getIsUrgent()));
	}

	public String getTestCycleName() {
		return testCycleName;
	}

	public void setTestCycleName(String testCycleName) {
		this.testCycleName = testCycleName;
	}

	public String getTestCycleMemo() {
		return testCycleMemo;
	}

	public void setTestCycleMemo(String testCycleMemo) {
		this.testCycleMemo = testCycleMemo;
	}

	public String getTestCycle() {
		return testCycle;
	}

	public void setTestCycle(String testCycle) {
		this.testCycle = testCycle;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getRecommendReason() {
		return recommendReason;
	}

	public void setRecommendReason(String recommendReason) {
		this.recommendReason = recommendReason;
	}
}
