package com.sgs.ecom.order.enums;


import com.sgs.ecom.order.dto.order.OrderStateNumDTO;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public enum QryOrderStateNumSortEnum {
    NO_PAY("待付款", 10,0),
    USED_UPLOAD_VOUCHER("已上传支付凭证",51,1),
    WAITAPPLY("待提交申请表", 11,2),
    WAITEXAMINE("待审核", 13,3),
    SERVICE("服务中", 14,4),
    REPORT_DELAY("报告Delay", 73,5),
    REFUND_CONFIRMED("退款确认中", 53,6);


    // 成员变量
    private String name;
    private int index;
    private int sortShow;
    // 构造方法
    private QryOrderStateNumSortEnum(String name, int index, int sortShow) {
        this.name = name;
        this.index = index;
        this.sortShow = sortShow;
    }

    // 普通方法
    public static String getName(int index) {
        for (QryOrderStateNumSortEnum c : QryOrderStateNumSortEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }
    public static List<OrderStateNumDTO>  sortShowList() {
        List<OrderStateNumDTO> list = new ArrayList<>();
        for (QryOrderStateNumSortEnum c : QryOrderStateNumSortEnum.values()) {
            OrderStateNumDTO orderStateNumDTO = new OrderStateNumDTO();
            orderStateNumDTO.setState(c.getIndex());
            orderStateNumDTO.setSortShow(c.getSortShow());
            list.add(orderStateNumDTO);
        }
        return list.stream().sorted(Comparator.comparing(OrderStateNumDTO::getSortShow)).collect(Collectors.toList());
    }


    // get set 方法
    public String getName() {
        return name;
    }

    public int getIndex() {
        return index;
    }

    public int getSortShow() {
        return sortShow;
    }
}
