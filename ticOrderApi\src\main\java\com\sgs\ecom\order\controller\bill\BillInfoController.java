package com.sgs.ecom.order.controller.bill;

import com.platform.annotation.AuthRequired;
import com.platform.util.SysCommonConstant;
import com.platform.util.date.DateFormat;
import com.sgs.ecom.order.request.bill.BillListReq;
import com.sgs.ecom.order.request.bill.BillReq;
import com.sgs.ecom.order.service.bill.interfaces.IBillInfoService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.util.date.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;

@RestController
@RequestMapping("/business/api.v2.bill/bill")
public class BillInfoController extends ControllerUtil {

	@Autowired
	private IBillInfoService billInfoService;
	private static DateUtil dateUtil=new DateUtil();

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryList", method = { RequestMethod.POST })
	public ResultBody qryList(
			@RequestHeader(value="system") String system,
			@RequestBody BillListReq billListReq,
			@RequestHeader(value = "accessToken") String token
			) throws Exception {
		return ResultBody.newInstance(billInfoService.getPageList(billListReq, getPersonInfo(token), getPower(token,system)));
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "sendBill", method = { RequestMethod.POST })
	public ResultBody sendBill(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody BillReq billReq) throws Exception {
		billInfoService.sendBill(billReq,getPersonInfo(token));
		return ResultBody.success();
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "getBillPDF", method = { RequestMethod.POST })
	public void getBillPDF(@RequestBody BillReq billReq,
							   HttpServletResponse httpServletResponse) throws Exception {
		billInfoService.getBillPDF(billReq,httpServletResponse);
	}


	/**
	 * @Description: 订单导出
	 * @Author: bowen zhang
	 * @Date: 2023/2/3
	 * @param token:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "expBillList", method = {RequestMethod.POST})
	public void expBillList(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody BillListReq billListReq, HttpServletResponse res) throws Exception {
		billInfoService.expBillList(billListReq, getPersonInfo(token), getPower(token), res);
	}

	@RequestMapping(value = "test", method = {RequestMethod.POST})
	public void test(
			@RequestHeader(value = "accessToken") String token) throws Exception {
		Date date = DateFormat.addMonths(new Date(), -0);

		Date startDate = dateUtil.getMonthFirst(date);
		Date endDate = dateUtil.getMonthLast(date);
		String billCycle = DateFormat.dateToString(startDate, SysCommonConstant.DateFormat.YmFormat);
		billInfoService.createBill(DateFormat.dateToString(startDate, SysCommonConstant.DateFormat.DatetimeFormat),
				DateFormat.dateToString(endDate, SysCommonConstant.DateFormat.DatetimeFormat), billCycle);
	}
}
