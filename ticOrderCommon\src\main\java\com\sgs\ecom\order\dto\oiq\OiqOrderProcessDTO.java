package com.sgs.ecom.order.dto.oiq;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.request.oiq.OiqOrderReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: OIQ保存申请表传递参数对象
 * @date 2024/12/10 17:13
 */
@Data
public class OiqOrderProcessDTO {
    /**
     * 入参请求对象
     */
    private OiqOrderReq oiqOrderReq;
    /**
     * 订单对象
     */
    private BaseOrderDTO orderDTO;
    /**
     * 询价单对象
     */
    private BaseOrderDTO inquiryDTO;
    /**
     *
     */
    private String originalOrderInfo;
}
