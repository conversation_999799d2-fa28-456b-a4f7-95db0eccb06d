<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  
  <parent>
    <groupId>com.sgs.ecom</groupId>
    <artifactId>ticOrder</artifactId>
    <version>1.1</version>
  </parent>
  <artifactId>ticOrderApi</artifactId>
  <version>${ticOrderApi.version}</version>
  <packaging>jar</packaging>
  <name>ticOrder<PERSON>pi</name>
  <url>http://maven.apache.org</url>
  
  <dependencies>
    
	<!-- https://mvnrepository.com/artifact/org.springframework/spring-web 
	<dependency>
	    <groupId>org.springframework</groupId>
	    <artifactId>spring-web</artifactId>
	    <version>3.1.0.RELEASE</version>
	</dependency>-->
    
	<!-- https://mvnrepository.com/artifact/org.quartz-scheduler/quartz 
	<dependency>
	    <groupId>org.quartz-scheduler</groupId>
	    <artifactId>quartz</artifactId>
	    <version>1.7.2</version>
	</dependency>-->
	

    <dependency>
	    <groupId>com.sgs.ecom</groupId>
	    <artifactId>ticOrderService</artifactId>
	    <version>${ticOrderService.version}</version>
	</dependency>
    <dependency>
	    <groupId>com.sgs.ecom</groupId>
	    <artifactId>ticOrderCommon</artifactId>
	    <version>${ticOrderCommon.version}</version>
	</dependency>
  </dependencies>

    <build>
        <!-- <finalName>ticOrder</finalName>-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- mybatis generator 自动生成代码插件 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
