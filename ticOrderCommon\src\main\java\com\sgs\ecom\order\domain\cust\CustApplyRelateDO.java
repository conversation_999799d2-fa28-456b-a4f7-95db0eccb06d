package com.sgs.ecom.order.domain.cust;

import com.sgs.ecom.order.entity.cust.CustApplyRelate;
import com.sgs.ecom.order.vo.VOUserInfo;

import java.sql.Timestamp;

/**
 * @Function: CustApplyRelateDO
 * @Description: 方法名
 *
 * @version: 1.0
 * @author: <PERSON><PERSON>_<PERSON>
 * @throws Exception
 * @date: 2023-11-06
 *
 * Modification History:
 * Date         Author          Version            Description
 *---------------------------------------------------------*
 * 修改时间     修改人           版本               修改原因
 * 2023-11-06      Kilian_Shen         v1.0               新增
 */
public class CustApplyRelateDO extends CustApplyRelate {

    public CustApplyRelate init(Long custId,Long userId,String bu,Integer state) {
        CustApplyRelate relate = new CustApplyRelate();
        relate.setBu(bu);
        relate.setCustId(custId);
        relate.setUserId(userId);
        relate.setState(state);
        relate.setCreateDate(new Timestamp(System.currentTimeMillis()));
        return relate;
    }
}
