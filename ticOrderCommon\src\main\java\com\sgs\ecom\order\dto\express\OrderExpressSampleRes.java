package com.sgs.ecom.order.dto.express;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @Description :
 * @date 2023/2/14
 */
public class OrderExpressSampleRes {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long orderId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String companyName;

    private Long labId;
    private String labName;
    private String province;
    private String city;
    private String town;
    private String labAddress;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String linkPerson;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String linkPhone;
    private String postcode;
    private String accountNo;
    private String subBu;
    private String businessLineId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labAddressShow;
    private String alipayChannel;
    private String wxpayChannel;
    private String catName;


    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }



    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getLabAddress() {
        return labAddress;
    }

    public void setLabAddress(String labAddress) {
        this.labAddress = labAddress;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getSubBu() {
        return subBu;
    }

    public void setSubBu(String subBu) {
        this.subBu = subBu;
    }

    public String getBusinessLineId() {
        return businessLineId;
    }

    public void setBusinessLineId(String businessLineId) {
        this.businessLineId = businessLineId;
    }

    public String getAlipayChannel() {
        return alipayChannel;
    }

    public void setAlipayChannel(String alipayChannel) {
        this.alipayChannel = alipayChannel;
    }

    public String getWxpayChannel() {
        return wxpayChannel;
    }

    public void setWxpayChannel(String wxpayChannel) {
        this.wxpayChannel = wxpayChannel;
    }

    public String getCatName() {
        return catName;
    }

    public void setCatName(String catName) {
        this.catName = catName;
    }

    public String getLabAddressShow() {

        StringBuilder stringBuilder=new StringBuilder();
        if(StringUtils.isNotBlank(province)){
            stringBuilder.append(province);
        }
        if(StringUtils.isNotBlank(city)){
            stringBuilder.append(city);
        }
        if(StringUtils.isNotBlank(town)){
            stringBuilder.append(town);
        }
        if(StringUtils.isNotBlank(labAddress)){
            stringBuilder.append(labAddress);
        }

        return stringBuilder.toString();
    }
}
