package com.sgs.ecom.order.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import java.sql.Timestamp;
import java.util.List;
import net.sf.json.JSONArray; 

public class UserInfoDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select USER_NICK,LEVEL_NAME,USER_SEX,USER_EMAIL,STATE_DATE,USER_ID,STATE,REG_IP,USER_NAME,U_ID,LEVEL_ID,USER_PHONE,CREATE_DATE,BBC_PWD,IS_BLACK,USER_PWD from USER_INFO"; 
 
 
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="USER_NICK", getName="getUserNick", setName="setUserNick")
 	private String userNick;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LEVEL_NAME", getName="getLevelName", setName="setLevelName")
 	private String levelName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_SEX", getName="getUserSex", setName="setUserSex")
 	private int userSex;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="USER_EMAIL", getName="getUserEmail", setName="setUserEmail")
 	private String userEmail;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="REG_IP", getName="getRegIp", setName="setRegIp")
 	private String regIp;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="U_ID", getName="getUId", setName="setUId")
 	private long uId;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="LEVEL_ID", getName="getLevelId", setName="setLevelId")
 	private int levelId;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BBC_PWD", getName="getBbcPwd", setName="setBbcPwd")
 	private String bbcPwd;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_BLACK", getName="getIsBlack", setName="setIsBlack")
 	private int isBlack;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_PWD", getName="getUserPwd", setName="setUserPwd")
 	private String userPwd;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="REG_MODE", getName="getRegMode", setName="setRegMode")
 	private String regMode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="REG_SOURCE", getName="getRegSource", setName="setRegSource")
 	private String regSource;

 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="COMPANY_ADDR_EN", getName="getCompanyAddrEn", setName="setCompanyAddrEn")
 	private String companyAddrEn;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="ADDRESS", getName="getAddress", setName="setAddress")
 	private String address;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CUST_NAME", getName="getCustName", setName="setCustName")
 	private String custName;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CUST_Para", getName="getCustPara", setName="setCustPara")
 	private String custPara;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private JSONArray custCode;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
	@ApiAnno(groups={Default.class,QueryList.class})
	private  int couponNum;
	@ApiAnno(groups={Default.class,QueryList.class})
	private  int isImportant;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="USER_WX", getName="getUserWx", setName="setUserWx")
 	private String userWx;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private int isTest;
 	

	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private int noticeState;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String bu;

	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String industry;//行业信息
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String mainProduct;//主营产品

	//订单相关
	@ApiAnno(groups={Default.class,QueryList.class})
	private long orderNum;
	@ApiAnno(groups={Default.class,QueryList.class})
	private double orderAmount;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private int relateState;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String custIdStr;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private int isBind;
	
	
	public String getIndustry() {
		return industry;
	}

	public void setIndustry(String industry) {
		this.industry = industry;
	}

	public String getMainProduct() {
		return mainProduct;
	}

	public void setMainProduct(String mainProduct) {
		this.mainProduct = mainProduct;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	@ApiAnno(groups={Default.class,QueryList.class})
	private List<UserAccountManagerDTO> userAccountManagerDTOList;

	public List<UserAccountManagerDTO> getUserAccountManagerDTOList() {
		return userAccountManagerDTOList;
	}

	public void setUserAccountManagerDTOList(List<UserAccountManagerDTO> userAccountManagerDTOList) {
		this.userAccountManagerDTOList = userAccountManagerDTOList;
	}

	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	public void setLevelName(String levelName){
 		 this.levelName=levelName;
 	}
 	public String getLevelName(){
 		 return this.levelName;
 	}
 
 	 
 	public void setUserSex(int userSex){
 		 this.userSex=userSex;
 	}
 	public int getUserSex(){
 		 return this.userSex;
 	}
 
 	 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setRegIp(String regIp){
 		 this.regIp=regIp;
 	}
 	public String getRegIp(){
 		 return this.regIp;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setUId(long uId){
 		 this.uId=uId;
 	}
 	public long getUId(){
 		 return this.uId;
 	}
 
 	 
 	public void setLevelId(int levelId){
 		 this.levelId=levelId;
 	}
 	public int getLevelId(){
 		 return this.levelId;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBbcPwd(String bbcPwd){
 		 this.bbcPwd=bbcPwd;
 	}
 	public String getBbcPwd(){
 		 return this.bbcPwd;
 	}
 
 	 
 	public void setIsBlack(int isBlack){
 		 this.isBlack=isBlack;
 	}
 	public int getIsBlack(){
 		 return this.isBlack;
 	}
 
 	 
 	public void setUserPwd(String userPwd){
 		 this.userPwd=userPwd;
 	}
 	public String getUserPwd(){
 		 return this.userPwd;
 	}
	public long getuId() {
		return uId;
	}
	public void setuId(long uId) {
		this.uId = uId;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
	public String getCompanyNameEn() {
		return companyNameEn;
	}
	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}
	public String getCompanyAddrEn() {
		return companyAddrEn;
	}
	public void setCompanyAddrEn(String companyAddrEn) {
		this.companyAddrEn = companyAddrEn;
	}
	public String getProvice() {
		return provice;
	}
	public void setProvice(String provice) {
		this.provice = provice;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public String getTown() {
		return town;
	}
	public void setTown(String town) {
		this.town = town;
	}
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	public String getCustName() {
		return custName;
	}
	public void setCustName(String custName) {
		this.custName = custName;
	}
	public String getCustPara() {
		return custPara;
	}
	public void setCustPara(String custPara) {
		this.custPara = custPara;
	}
	public JSONArray getCustCode() {
		return custCode;
	}
	public void setCustCode(JSONArray custCode) {
		this.custCode = custCode;
	}

	public int getCouponNum() {
		return couponNum;
	}

	public void setCouponNum(int couponNum) {
		this.couponNum = couponNum;
	}
	public String getPersonCode() {
		return personCode;
	}
	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}
	public String getRegMode() {
		return regMode;
	}
	public void setRegMode(String regMode) {
		this.regMode = regMode;
	}
	public String getRegSource() {
		return regSource;
	}
	public void setRegSource(String regSource) {
		this.regSource = regSource;
	}
	public long getCustId() {
		return custId;
	}
	public void setCustId(long custId) {
		this.custId = custId;
	}
	public int getIsImportant() {
		return isImportant;
	}
	public void setIsImportant(int isImportant) {
		this.isImportant = isImportant;
	}

	public long getOrderNum() {
		return orderNum;
	}

	public void setOrderNum(long orderNum) {
		this.orderNum = orderNum;
	}

	public double getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(double orderAmount) {
		this.orderAmount = orderAmount;
	}

	public String getUserWx() {
		return userWx;
	}

	public void setUserWx(String userWx) {
		this.userWx = userWx;
	}

	public int getNoticeState() {
		return noticeState;
	}

	public void setNoticeState(int noticeState) {
		this.noticeState = noticeState;
	}

	public int getIsTest() {
		return isTest;
	}

	public void setIsTest(int isTest) {
		this.isTest = isTest;
	}

	public int getRelateState() {
		return relateState;
	}

	public void setRelateState(int relateState) {
		this.relateState = relateState;
	}

	public String getCustIdStr() {
		return custIdStr;
	}

	public void setCustIdStr(String custIdStr) {
		this.custIdStr = custIdStr;
	}

	public int getIsBind() {
		return isBind;
	}

	public void setIsBind(int isBind) {
		this.isBind = isBind;
	}
}