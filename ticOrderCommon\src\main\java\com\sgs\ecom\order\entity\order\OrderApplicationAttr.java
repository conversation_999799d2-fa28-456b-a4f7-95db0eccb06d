package com.sgs.ecom.order.entity.order;

/**
 * <AUTHOR>
 */
public class OrderApplicationAttr {


 	private String attrValue;

 	private String createDate;

 	private String attrName;

 	private String orderNo;

 	private Long attrId;

 	private String areaCode;

 	private String attrCode;

	private String attrText;

 	private int state;

	private String classifyType;

	private String enumConfig;

	public void setAttrValue(String attrValue){
 		 this.attrValue=attrValue;
 	}
 	public String getAttrValue(){
 		 return this.attrValue;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setAttrName(String attrName){
 		 this.attrName=attrName;
 	}
 	public String getAttrName(){
 		 return this.attrName;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setAttrId(Long attrId){
 		 this.attrId=attrId;
 	}
 	public Long getAttrId(){
 		 return this.attrId;
 	}
 
 	 
 	public void setAreaCode(String areaCode){
 		 this.areaCode=areaCode;
 	}
 	public String getAreaCode(){
 		 return this.areaCode;
 	}
 
 	 
 	public void setAttrCode(String attrCode){
 		 this.attrCode=attrCode;
 	}
 	public String getAttrCode(){
 		 return this.attrCode;
 	}

	public String getAttrText() {
		return attrText;
	}

	public void setAttrText(String attrText) {
		this.attrText = attrText;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getClassifyType() {
		return classifyType;
	}

	public void setClassifyType(String classifyType) {
		this.classifyType = classifyType;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}
}