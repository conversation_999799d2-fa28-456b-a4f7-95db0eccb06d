package com.sgs.ecom.order.dto.dml;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.dto.detail.OrderReportDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqItemDTO;
import com.sgs.ecom.order.dto.order.OrderAttributeDTO;
import com.sgs.ecom.order.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.order.entity.OrderLink;
import com.sgs.ecom.order.entity.order.OrderApplicationAttr;
import com.sgs.ecom.order.request.oiq.SampleCategoryReq;

import java.util.ArrayList;
import java.util.List;

public class DmlMainReqDTO {

    private OrderApplicationFormDTO formDTO=new OrderApplicationFormDTO();
    private OrderInvoiceDTO orderInvoiceDTO=new OrderInvoiceDTO();
    private OrderReportDTO orderReportDTO=new OrderReportDTO();
    private BaseOrderDTO baseOrderDTO;
    private List<OrderSampleMoreDTO> orderSampleDTOList=new ArrayList<>();
    private List<OiqItemDTO> itemDTOList=new ArrayList<>();

    private String orderNo;
    private String line;
    private String ownerEmail;
    private int type;


    private OrderAttributeDTO imgAttr;


    private OrderExpressDTO report;
    private OrderExpressDTO back;

    private DmlOtherDTO dmlOther;

    private List<OrderApplicationAttr> attrList;
    private List<OrderLink> orderLinkList;
    private SampleCategoryReq sampleCategoryReq;


    public OrderApplicationFormDTO getFormDTO() {
        return formDTO;
    }

    public void setFormDTO(OrderApplicationFormDTO formDTO) {
        this.formDTO = formDTO;
    }

    public OrderInvoiceDTO getOrderInvoiceDTO() {
        return orderInvoiceDTO;
    }

    public void setOrderInvoiceDTO(OrderInvoiceDTO orderInvoiceDTO) {
        this.orderInvoiceDTO = orderInvoiceDTO;
    }

    public OrderReportDTO getOrderReportDTO() {
        return orderReportDTO;
    }

    public void setOrderReportDTO(OrderReportDTO orderReportDTO) {
        this.orderReportDTO = orderReportDTO;
    }



    public List<OrderSampleMoreDTO> getOrderSampleDTOList() {
        return orderSampleDTOList;
    }

    public void setOrderSampleDTOList(List<OrderSampleMoreDTO> orderSampleDTOList) {
        this.orderSampleDTOList = orderSampleDTOList;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getLine() {
        return line;
    }

    public void setLine(String line) {
        this.line = line;
    }

    public OrderAttributeDTO getImgAttr() {
        return imgAttr;
    }

    public void setImgAttr(OrderAttributeDTO imgAttr) {
        this.imgAttr = imgAttr;
    }


    public List<OiqItemDTO> getItemDTOList() {
        return itemDTOList;
    }

    public void setItemDTOList(List<OiqItemDTO> itemDTOList) {
        this.itemDTOList = itemDTOList;
    }

    public OrderExpressDTO getReport() {
        return report;
    }

    public void setReport(OrderExpressDTO report) {
        this.report = report;
    }

    public OrderExpressDTO getBack() {
        return back;
    }

    public void setBack(OrderExpressDTO back) {
        this.back = back;
    }

    public String getOwnerEmail() {
        return ownerEmail;
    }

    public void setOwnerEmail(String ownerEmail) {
        this.ownerEmail = ownerEmail;
    }

    public DmlOtherDTO getDmlOther() {
        return dmlOther;
    }

    public void setDmlOther(DmlOtherDTO dmlOther) {
        this.dmlOther = dmlOther;
    }



    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public BaseOrderDTO getBaseOrderDTO() {
        return baseOrderDTO;
    }

    public void setBaseOrderDTO(BaseOrderDTO baseOrderDTO) {
        this.baseOrderDTO = baseOrderDTO;
    }

    public List<OrderApplicationAttr> getAttrList() {
        return attrList;
    }

    public void setAttrList(List<OrderApplicationAttr> attrList) {
        this.attrList = attrList;
    }

    public List<OrderLink> getOrderLinkList() {
        return orderLinkList;
    }

    public void setOrderLinkList(List<OrderLink> orderLinkList) {
        this.orderLinkList = orderLinkList;
    }

    public SampleCategoryReq getSampleCategoryReq() {
        return sampleCategoryReq;
    }

    public void setSampleCategoryReq(SampleCategoryReq sampleCategoryReq) {
        this.sampleCategoryReq = sampleCategoryReq;
    }
}
