package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.detail.OrderReportDTO;
import com.sgs.ecom.order.dto.portal.PortalInquiryBaseDTO;
import com.sgs.ecom.order.enumtool.application.TestCycleEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.pay.MonthPayEnum;
import com.sgs.ecom.order.util.serializer.DateStringFormatSerializer;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;
import net.sf.json.JSONObject;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


public class OrderBaseInfoDTO  extends OrderBaseInfoCheckDTO {


 	public static final String CREATE_SQL = "select COMPANY_NAME,CREATE_DATE,USER_PHONE,CS_CODE,ORDER_ID,USER_ID,STATE,ORDER_NO,PROVINCE,USER_NAME from ORDER_BASE_INFO";


	//时间相关数据
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class,RstsList.class,})
	private String createDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String stateDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String offerDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String orderExpDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private String applySubmitDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryList.class})
	public String timeDateShow;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String orderShow;
	
 	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;

 	@ApiAnno(groups={Default.class,InquiryList.class})
 	@BeanAnno(value="PROVINCE", getName="getProvince", setName="setProvince")
 	private String province;
 	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String categoryPath;

	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String labName;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String userSex;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String userEmail;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String city;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String businessLine;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private List<JSONObject> userAnswerDTOList;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String productName;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String orderSource;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String orderSourceShow;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String leadsCode;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String orderSourceFrom;




	//订单新加参数
	@ApiAnno(groups={Default.class,InquiryList.class})
	private int totalNums;

	@ApiAnno(groups={Default.class,InquiryList.class})
	private String productImg;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String relateOrderNo;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String reportForm;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String reportLua;

	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private BigDecimal orderAmount;

	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private int isTest;
	@ApiAnno(groups={Default.class,TicList.class})
	private int isInvoice;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String auditCode;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private List<OrderAttachmentDTO> fileList=new ArrayList<>() ;
	@ApiAnno(groups={Default.class})
	private List<OrderAttachmentDTO> invoiceFileList=new ArrayList<>() ;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class,RstsList.class})
	private List<SubOrderDTO> subOrderDTOList =new ArrayList<>();
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={TicList.class})
	private BigDecimal subAmount=BigDecimal.ZERO ;
	@ApiAnno(groups={TicList.class})
	private String subPayStr;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String operatorCode;

	@ApiAnno(groups={Default.class,InquiryList.class})
	private int isUrgent;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String sampleNameStr;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String testCycle;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private OrderOperatorLogDTO lastLog;



	@JsonSerialize(using = DateStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String deadlineTime;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String deadlineMemo;

	//增加俩个字段
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String itemName;

	@ApiAnno(groups={Default.class,InquiryList.class})
	private Long relateOrderId;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private String buName;
	@ApiAnno(groups={TicList.class})
	private List<OrderProductDTO> orderProductDTOList=new ArrayList<>();
	@ApiAnno(groups={TicList.class})
	private List<OrderAttachmentDTO> reportList=new ArrayList<>();
	@ApiAnno(groups={TicList.class})
	private List<OrderAttachmentDTO> formFileList=new ArrayList<>();
	@ApiAnno(groups={TicList.class})
	private Map<String, List<OrderExpressDTO>> groupByOrderAndTypeExpress=new HashMap<>();//根据类型分组数据
	@ApiAnno(groups={TicList.class})
	private String expressNoStrs;
	@ApiAnno(groups={TicList.class})
	private String storeId;
	@ApiAnno(groups={TicList.class})
	private int levelId;
	@ApiAnno(groups={TicList.class,RstsList.class})
	private int monthPay;
	@ApiAnno(groups={TicList.class})
	private int expressStateFlg;//1表示能推送
	@ApiAnno(groups={TicList.class})
	private List<OrderRelateExternalDTO> otsList=new ArrayList<>();
	@ApiAnno(groups={TicList.class})
	private String monthPayShow;
	//复购和复询相关功能
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private int userLabelFlg;
	@ApiAnno(groups={Default.class,InquiryList.class})
	private int firstOrderFlg;
	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private int reFlg;//0新用户 1复购用户

	//小门户 orderBaseInfoDTO
	@ApiAnno(groups={Default.class,InquiryList.class})
	private List<OrderAttachmentDTO> quotationFileList=new ArrayList<>();

	private Integer isBill;
	private String abstractCustcode;

	@ApiAnno(groups={Default.class,RstsDetail.class,RstsList.class})
	private int payMethod;


	@ApiAnno(groups={RstsList.class,RstsDetail.class,RstsList.class})
	private BigDecimal platformAmount;

	@ApiAnno(groups={RstsList.class})
	private List<OrderDetailDTO> orderDetailDTOList;
	@ApiAnno(groups={RstsList.class,TicList.class})
	private List<OrderSampleDTO> orderSampleDTOList;
	@ApiAnno(groups={RstsList.class})
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String confirmOrderDate;
	@ApiAnno(groups={RstsList.class,InquiryList.class,Default.class})
	private String currency;
	@ApiAnno(groups={InquiryList.class,Default.class})
	private String currencyMark;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private BigDecimal rstsAmount;
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private Long custId;
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private int industryField;
	@ApiAnno(groups={RstsList.class})
	private String toPayAmount;
	@ApiAnno(groups={RstsList.class})
	private OrderOperatorLogDTO orderOperatorLogDTO;


	@ApiAnno(groups={TicList.class})
	private BigDecimal discountAmount;
	@ApiAnno(groups={TicList.class})
	private BigDecimal urgentAmount;
	@ApiAnno(groups={TicList.class})
	private String sampleRequirements;
	@ApiAnno(groups={Default.class})
	private String platformOrder;

	@ApiAnno(groups={Default.class,TicList.class})
	private Long categoryId;//实验室类别id
	@ApiAnno(groups={Default.class,TicList.class})
	private String model;

	@ApiAnno(groups={Default.class,TicList.class})
	private String urgentName;

	@ApiAnno(groups={Default.class,TicList.class})
	private OrderReportDTO orderReport;

	@ApiAnno(groups={Default.class})
	private String ladingNo;
	@ApiAnno(groups={Default.class})
	private String outOrderNo;
	@ApiAnno(groups={Default.class})
	private String destinationCountry;
	@ApiAnno(groups={Default.class})
	private Integer etaRemainingDays;
	@ApiAnno(groups={Default.class})
	private String businessPersonEmail;
	@ApiAnno(groups={Default.class})
	private String salesEmail;


	public String getUrgentName() {
		return urgentName;
	}

	public void setUrgentName(String urgentName) {
		this.urgentName = urgentName;
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	@Override
	public Long getCategoryId() {
		return categoryId;
	}

	@Override
	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getPlatformOrder() {
		return platformOrder;
	}

	public void setPlatformOrder(String platformOrder) {
		this.platformOrder = platformOrder;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	@ApiAnno(groups={TicList.class})
	private int isRefundAll;//发起的退款是全额还是部分退款  0-无退款信息 1-全部  2-部分

	@ApiAnno(groups={Default.class,InquiryList.class,TicList.class})
	private String fromSource;


	@ApiAnno(groups={OrderDetail.class,Default.class})
	private PortalInquiryBaseDTO inquiryBaseDTO;
	@ApiAnno(groups={OrderDetail.class,Default.class,TicList.class})
	private String categoryName;
	@ApiAnno(groups={OrderDetail.class,Default.class})
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getFromSource() {
		return fromSource;
	}

	public void setFromSource(String fromSource) {
		this.fromSource = fromSource;
	}

	public PortalInquiryBaseDTO getInquiryBaseDTO() {
		return inquiryBaseDTO;
	}

	public void setInquiryBaseDTO(PortalInquiryBaseDTO inquiryBaseDTO) {
		this.inquiryBaseDTO = inquiryBaseDTO;
	}

	public int getIsRefundAll() {
		return isRefundAll;
	}

	public void setIsRefundAll(int isRefundAll) {
		this.isRefundAll = isRefundAll;
	}

	public Map<String, List<OrderExpressDTO>> getGroupByOrderAndTypeExpress() {
		return groupByOrderAndTypeExpress;
	}

	public void setGroupByOrderAndTypeExpress(Map<String, List<OrderExpressDTO>> groupByOrderAndTypeExpress) {
		this.groupByOrderAndTypeExpress = groupByOrderAndTypeExpress;
	}

	public String getExpressNoStrs() {
		return expressNoStrs;
	}

	public void setExpressNoStrs(String expressNoStrs) {
		this.expressNoStrs = expressNoStrs;
	}

	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 




	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}


 

 	 
 	public void setProvince(String province){
 		 this.province=province;
 	}
 	public String getProvince(){
 		 return this.province;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}





	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getUserSex() {
		return userSex;
	}

	public void setUserSex(String userSex) {
		this.userSex = userSex;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}



	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}







	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}


	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public void setUserAnswerDTOList(List<JSONObject> userAnswerDTOList) {
		this.userAnswerDTOList = userAnswerDTOList;
	}

	public List<JSONObject> getUserAnswerDTOList() {
		return userAnswerDTOList;
	}

	public int getTotalNums() {
		return totalNums;
	}

	public void setTotalNums(int totalNums) {
		this.totalNums = totalNums;
	}


	public String getProductImg() {
		return productImg;
	}

	public void setProductImg(String productImg) {
		this.productImg = productImg;
	}

	public String getRelateOrderNo() {
		return relateOrderNo;
	}

	public void setRelateOrderNo(String relateOrderNo) {
		this.relateOrderNo = relateOrderNo;
	}

	public String getReportForm() {
		return reportForm;
	}

	public void setReportForm(String reportForm) {
		this.reportForm = reportForm;
	}

	public String getReportLua() {
		return reportLua;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}



	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}



	public int getIsTest() {
		return isTest;
	}

	public void setIsTest(int isTest) {
		this.isTest = isTest;
	}

	public int getIsInvoice() {
		return isInvoice;
	}

	public void setIsInvoice(int isInvoice) {
		this.isInvoice = isInvoice;
	}

	public String getAuditCode() {
		return auditCode;
	}

	public void setAuditCode(String auditCode) {
		this.auditCode = auditCode;
	}

	public List<OrderAttachmentDTO> getFileList() {
		return fileList;
	}

	public void setFileList(List<OrderAttachmentDTO> fileList) {
		this.fileList = fileList;
	}



	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public String getLeadsCode() {
		return leadsCode;
	}

	public void setLeadsCode(String leadsCode) {
		this.leadsCode = leadsCode;
	}

	public int getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(int isUrgent) {
		this.isUrgent = isUrgent;
	}

	public String getSampleNameStr() {
		return sampleNameStr;
	}

	public void setSampleNameStr(String sampleNameStr) {
		this.sampleNameStr = sampleNameStr;
	}


	public String getStateDate() {
		return stateDate;
	}






	public String getIsUrgentShow() {
		return TestCycleEnum.getNameCh(String.valueOf(isUrgent));
	}

	public String getTestCycle() {
		return testCycle;
	}

	public void setTestCycle(String testCycle) {
		this.testCycle = testCycle;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getOfferDate() {
		return offerDate;
	}

	public void setOfferDate(String offerDate) {
		this.offerDate = offerDate;
	}

	public String getOrderExpDate() {
		return orderExpDate;
	}

	public void setOrderExpDate(String orderExpDate) {
		this.orderExpDate = orderExpDate;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public String getTimeDateShow() {
		return timeDateShow;
	}

	public void setTimeDateShow(String timeDateShow) {
		this.timeDateShow = timeDateShow;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public List<SubOrderDTO> getSubOrderDTOList() {
		return subOrderDTOList;
	}

	public void setSubOrderDTOList(List<SubOrderDTO> subOrderDTOList) {
		this.subOrderDTOList = subOrderDTOList;
	}

	@Override
	public String getOperatorCode() {
		return operatorCode;
	}

	@Override
	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public String getOrderSourceFrom() {
		return orderSourceFrom;
	}

	public void setOrderSourceFrom(String orderSourceFrom) {
		this.orderSourceFrom = orderSourceFrom;
	}

	public Long getRelateOrderId() {
		return relateOrderId;
	}

	public void setRelateOrderId(Long relateOrderId) {
		this.relateOrderId = relateOrderId;
	}

	public List<OrderProductDTO> getOrderProductDTOList() {
		return orderProductDTOList;
	}

	public void setOrderProductDTOList(List<OrderProductDTO> orderProductDTOList) {
		this.orderProductDTOList = orderProductDTOList;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public int getLevelId() {
		return levelId;
	}

	public void setLevelId(int levelId) {
		this.levelId = levelId;
	}

	public String getBuName() {
		return buName;
	}

	public void setBuName(String buName) {
		this.buName = buName;
	}

	public OrderOperatorLogDTO getLastLog() {
		return lastLog;
	}

	public void setLastLog(OrderOperatorLogDTO lastLog) {
		this.lastLog = lastLog;
	}

	public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}

	public String getMonthPayShow() {
		return MonthPayEnum.getNameCh(monthPay);
	}

	public List<OrderRelateExternalDTO> getOtsList() {
		return otsList;
	}

	public void setOtsList(List<OrderRelateExternalDTO> otsList) {
		this.otsList = otsList;
	}

	public List<OrderAttachmentDTO> getReportList() {
		return reportList;
	}

	public void setReportList(List<OrderAttachmentDTO> reportList) {
		this.reportList = reportList;
	}

	public String getOrderShow() {
		return orderShow;
	}

	public void setOrderShow(String orderShow) {
		this.orderShow = orderShow;
	}
	public Integer getIsBill() {
		return isBill;
	}
	public void setIsBill(Integer isBill) {
		this.isBill = isBill;
	}
	public String getAbstractCustcode() {
		return abstractCustcode;
	}
	public void setAbstractCustcode(String abstractCustcode) {
		this.abstractCustcode = abstractCustcode;
	}

	public int getExpressStateFlg() {
		return expressStateFlg;
	}

	public void setExpressStateFlg(int expressStateFlg) {
		this.expressStateFlg = expressStateFlg;
	}

	public List<OrderAttachmentDTO> getFormFileList() {
		return formFileList;
	}

	public void setFormFileList(List<OrderAttachmentDTO> formFileList) {
		this.formFileList = formFileList;
	}

	public int getUserLabelFlg() {
		return userLabelFlg;
	}

	public void setUserLabelFlg(int userLabelFlg) {
		this.userLabelFlg = userLabelFlg;
	}

	public String getOrderSourceShow() {
		return OrderSourceEnum.getNameCh(orderSource);
	}

	public String getDeadlineMemo() {
		return deadlineMemo;
	}

	public void setDeadlineMemo(String deadlineMemo) {
		this.deadlineMemo = deadlineMemo;
	}

	public BigDecimal getSubAmount() {
		return subAmount;
	}

	public void setSubAmount(BigDecimal subAmount) {
		this.subAmount = subAmount;
	}

	public String getSubPayStr() {
		return subPayStr;
	}

	public void setSubPayStr(String subPayStr) {
		this.subPayStr = subPayStr;
	}

	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public List<OrderDetailDTO> getOrderDetailDTOList() {
		return orderDetailDTOList;
	}

	public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
		this.orderDetailDTOList = orderDetailDTOList;
	}

	public List<OrderSampleDTO> getOrderSampleDTOList() {
		return orderSampleDTOList;
	}

	public void setOrderSampleDTOList(List<OrderSampleDTO> orderSampleDTOList) {
		this.orderSampleDTOList = orderSampleDTOList;
	}

	@Override
	public String getConfirmOrderDate() {
		return confirmOrderDate;
	}

	@Override
	public void setConfirmOrderDate(String confirmOrderDate) {
		this.confirmOrderDate = confirmOrderDate;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public BigDecimal getRstsAmount() {
		return rstsAmount;
	}

	public void setRstsAmount(BigDecimal rstsAmount) {
		this.rstsAmount = rstsAmount;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public int getIndustryField() {
		return industryField;
	}

	public void setIndustryField(int industryField) {
		this.industryField = industryField;
	}

	public int getReFlg() {
		return reFlg;
	}

	public void setReFlg(int reFlg) {
		this.reFlg = reFlg;
	}

	public List<OrderAttachmentDTO> getQuotationFileList() {
		return quotationFileList;
	}

	public void setQuotationFileList(List<OrderAttachmentDTO> quotationFileList) {
		this.quotationFileList = quotationFileList;
	}

	public List<OrderAttachmentDTO> getInvoiceFileList() {
		return invoiceFileList;
	}

	public void setInvoiceFileList(List<OrderAttachmentDTO> invoiceFileList) {
		this.invoiceFileList = invoiceFileList;
	}

	public String getToPayAmount() {
		return toPayAmount;
	}

	public void setToPayAmount(String toPayAmount) {
		this.toPayAmount = toPayAmount;
	}




	public OrderOperatorLogDTO getOrderOperatorLogDTO() {
		return orderOperatorLogDTO;
	}

	public void setOrderOperatorLogDTO(OrderOperatorLogDTO orderOperatorLogDTO) {
		this.orderOperatorLogDTO = orderOperatorLogDTO;
	}

	@Override
	public int getPayMethod() {
		return payMethod;
	}

	@Override
	public void setPayMethod(int payMethod) {
		this.payMethod = payMethod;
	}

	public String getCurrencyMark() {
		return currencyMark;
	}

	public void setCurrencyMark(String currencyMark) {
		this.currencyMark = currencyMark;
	}

	public int getFirstOrderFlg() {
		return firstOrderFlg;
	}

	public void setFirstOrderFlg(int firstOrderFlg) {
		this.firstOrderFlg = firstOrderFlg;
	}

	public OrderReportDTO getOrderReport() {
		return orderReport;
	}

	public void setOrderReport(OrderReportDTO orderReport) {
		this.orderReport = orderReport;
	}

	public String getLadingNo() {
		return ladingNo;
	}

	public void setLadingNo(String ladingNo) {
		this.ladingNo = ladingNo;
	}

	public String getOutOrderNo() {
		return outOrderNo;
	}

	public void setOutOrderNo(String outOrderNo) {
		this.outOrderNo = outOrderNo;
	}

	public String getDestinationCountry() {
		return destinationCountry;
	}

	public void setDestinationCountry(String destinationCountry) {
		this.destinationCountry = destinationCountry;
	}

	public Integer getEtaRemainingDays() {
		return etaRemainingDays;
	}

	public void setEtaRemainingDays(Integer etaRemainingDays) {
		this.etaRemainingDays = etaRemainingDays;
	}

	public String getBusinessPersonEmail() {
		return businessPersonEmail;
	}

	public void setBusinessPersonEmail(String businessPersonEmail) {
		this.businessPersonEmail = businessPersonEmail;
	}

	public String getSalesEmail() {
		return salesEmail;
	}

	public void setSalesEmail(String salesEmail) {
		this.salesEmail = salesEmail;
	}
}