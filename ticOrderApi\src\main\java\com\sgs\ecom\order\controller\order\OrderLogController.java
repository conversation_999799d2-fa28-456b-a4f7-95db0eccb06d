package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.request.log.ConfirmLogReq;
import com.sgs.ecom.order.request.log.LogAddReq;
import com.sgs.ecom.order.request.log.LogListReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderLogService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/log")
public class OrderLogController extends ControllerUtil {

    @Resource
    private IOrderLogService orderLogService;


    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "addMsg", method = { RequestMethod.POST })
    public ResultBody addMsg(
            @Validated(BaseBean.Insert.class)
            @RequestBody LogAddReq logAddReq,
            @RequestHeader(value="accessToken") String token
    ) throws Exception {
        orderLogService.addMsg(logAddReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }


    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryList", method = { RequestMethod.POST })
    public ResultBody qryList(
            @RequestHeader(value="accessToken") String token,
            @RequestBody LogListReq logListReq) throws Exception {
        return ResultBody.newInstance(orderLogService.qryLogList(logListReq,getPersonInfo(token)));
    }


    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "addConfirmLog", method = { RequestMethod.POST })
    public ResultBody addConfirmLog(
            @RequestHeader(value="accessToken") String token,
            @RequestBody ConfirmLogReq confirmLogReq) throws Exception {
        orderLogService.addConfirmLog(confirmLogReq,getPersonInfo(token));
        return ResultBody.success();
    }




}
