package com.sgs.ecom.order.dto.send;

import com.sgs.ecom.order.request.FileReq;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.request.express.OrderTicExpress;
import com.sgs.ecom.order.request.operator.OrderServiceRefundReq;

import java.util.List;
import java.util.Map;

public class TicOtherMailDTO {
    private String sendCC;
    private String checkDate;
    private String orderNo;
    private String userName;//客户名称
    private List<FileReq> fileReqList;
    private List<OrderTicExpress> orderTicExpressList;// 快递信息
    private String orderNoExp;
    private OrderServiceRefundReq orderServiceRefundReq;
    private OrderPriceReq orderPriceReq;
	private int ehsFlg;
	private Map<String,Object> paramMap ;

    private int memberCc;//发客户发的时候添加的cc
	private int orderCc;//往客服发的时候添加的cc

	private String sendMail;

	private PaymentDTO paymentDTO;
	private String auditCode;

	public String getSendMail() {
		return sendMail;
	}

	public void setSendMail(String sendMail) {
		this.sendMail = sendMail;
	}

	public Map<String, Object> getParamMap() {
		return paramMap;
	}

	public void setParamMap(Map<String, Object> paramMap) {
		this.paramMap = paramMap;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	private Integer state;

	public int getEhsFlg() {
		return ehsFlg;
	}

	public void setEhsFlg(int ehsFlg) {
		this.ehsFlg = ehsFlg;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public OrderPriceReq getOrderPriceReq() {
		return orderPriceReq;
	}

	public void setOrderPriceReq(OrderPriceReq orderPriceReq) {
		this.orderPriceReq = orderPriceReq;
	}

	public OrderServiceRefundReq getOrderServiceRefundReq() {
		return orderServiceRefundReq;
	}

	public void setOrderServiceRefundReq(OrderServiceRefundReq orderServiceRefundReq) {
		this.orderServiceRefundReq = orderServiceRefundReq;
	}

	public String getOrderNoExp() {
		return orderNoExp;
	}

	public void setOrderNoExp(String orderNoExp) {
		this.orderNoExp = orderNoExp;
	}

	public List<OrderTicExpress> getOrderTicExpressList() {
		return orderTicExpressList;
	}

	public void setOrderTicExpressList(List<OrderTicExpress> orderTicExpressList) {
		this.orderTicExpressList = orderTicExpressList;
	}

	public List<FileReq> getFileReqList() {
        return fileReqList;
    }

    public void setFileReqList(List<FileReq> fileReqList) {
        this.fileReqList = fileReqList;
    }

    public String getSendCC() {
        return sendCC;
    }

    public void setSendCC(String sendCC) {
        this.sendCC = sendCC;
    }

    public String getCheckDate() {
        return checkDate;
    }

    public void setCheckDate(String checkDate) {
        this.checkDate = checkDate;
    }

	public int getMemberCc() {
		return memberCc;
	}

	public void setMemberCc(int memberCc) {
		this.memberCc = memberCc;
	}

	public int getOrderCc() {
		return orderCc;
	}

	public void setOrderCc(int orderCc) {
		this.orderCc = orderCc;
	}

	public PaymentDTO getPaymentDTO() {
		return paymentDTO;
	}

	public void setPaymentDTO(PaymentDTO paymentDTO) {
		this.paymentDTO = paymentDTO;
	}

	public String getAuditCode() {
		return auditCode;
	}

	public void setAuditCode(String auditCode) {
		this.auditCode = auditCode;
	}
}
