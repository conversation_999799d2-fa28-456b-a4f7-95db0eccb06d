package com.sgs.ecom.order.dto.leads;

import java.math.BigDecimal;

public class LeadsSyncDTO {


    private String companyName;
    private Long lineId;
    private String itemNameStr;
    private String inquiryOrderNo;
    private String orderNo;
    private String currency;
    private String inquiryCreateDate;
    private String bu;
    private String lineName;
    private String csCode;
    private String csEmail;
    private String orderCsCode;
    private Integer payState;
    private BigDecimal amount;
    private BigDecimal subAmount;

    //处理参数
    private String ticketId;
    private String buName;
    private String lineCode;
    private String emailCode;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }



    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public String getItemNameStr() {
        return itemNameStr;
    }

    public void setItemNameStr(String itemNameStr) {
        this.itemNameStr = itemNameStr;
    }

    public String getInquiryOrderNo() {
        return inquiryOrderNo;
    }

    public void setInquiryOrderNo(String inquiryOrderNo) {
        this.inquiryOrderNo = inquiryOrderNo;
    }

    public String getInquiryCreateDate() {
        return inquiryCreateDate;
    }

    public void setInquiryCreateDate(String inquiryCreateDate) {
        this.inquiryCreateDate = inquiryCreateDate;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getLineName() {
        return lineName;
    }

    public void setLineName(String lineName) {
        this.lineName = lineName;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getOrderCsCode() {
        return orderCsCode;
    }

    public void setOrderCsCode(String orderCsCode) {
        this.orderCsCode = orderCsCode;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getSubAmount() {
        return subAmount;
    }

    public void setSubAmount(BigDecimal subAmount) {
        this.subAmount = subAmount;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBuName() {
        return buName;
    }

    public void setBuName(String buName) {
        this.buName = buName;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }

    public Integer getPayState() {
        return payState;
    }

    public void setPayState(Integer payState) {
        this.payState = payState;
    }

    public String getEmailCode() {
        return emailCode;
    }

    public void setEmailCode(String emailCode) {
        this.emailCode = emailCode;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
