package com.sgs.ecom.order.tic.controller;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.order.service.interfaces.ITicOrderSV;
import com.sgs.ecom.order.util.ResultBody;

@RestController
@RequestMapping("/business/api.v1.order/order")
public class TicOrderController extends BaseAction {

	private static ITicOrderSV ticOrderSV = CollectionService.getService(ITicOrderSV.class);
	
	/**   
	* @Function: qryOrder
	* @Description: 查询订单列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-11-06
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-11-06  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryOrder", method = { RequestMethod.POST })
    public ResultBody qryOrder(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(ticOrderSV.qryOrder(data));
	}
    
    /**   
	* @Function: expOrder
	* @Description: 导出订单列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-11-06
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-11-06  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "expOrder", method = { RequestMethod.POST })
    public void expOrder(@RequestBody String data, HttpServletResponse res) throws Exception {
		ticOrderSV.expOrder(data, res);
	}
	
    /**   
	* @Function: qryDtl
	* @Description: 查询订单信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-11-06
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-11-06  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryDtl", method = { RequestMethod.POST })
    public ResultBody qryTicOrderDtl(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(ticOrderSV.qryTicOrderDtl(data));
	}
    
    /**   
	* @Function: saveReportSend
	* @Description: 保存报告邮件收件人邮箱
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-11-06
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-11-06  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "saveSend", method = { RequestMethod.POST })
    public ResultBody saveReportSend(@RequestBody String data) throws Exception {
		ticOrderSV.saveReportSend(data);
		return ResultBody.success();
	}
    
    /**   
	* @Function: batchTicket
	* @Description: 批量出票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-11-06
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-11-06  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "batchTicket", method = { RequestMethod.POST })
    public ResultBody batchTicket(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
		ticOrderSV.batchTicket(data, getUserInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: batchMonthPay
	* @Description: 设置月结支付
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-11-06
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-11-06  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "batchMonthPay", method = { RequestMethod.POST })
    public ResultBody batchMonthPay(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
		ticOrderSV.batchMonthPay(data, getUserInfo(token));
		return ResultBody.success();
	}
}
