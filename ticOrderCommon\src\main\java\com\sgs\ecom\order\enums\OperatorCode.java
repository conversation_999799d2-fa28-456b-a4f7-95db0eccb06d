package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

@ExplainAnno(value="enum",name="订单状态变更操作人")
public enum OperatorCode implements EnumMessage {

    PRE_ORDER("pre-order", 1),
    ;

    // 成员变量
    private String name;
    private int index;
    // 构造方法
    private OperatorCode(String name, int index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(int index) {  
        for (OperatorCode c : OperatorCode.values()) {
            if (c.getIndex() == index) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public int getIndex() {  
        return index;  
    }  

	@Override
	public String getKey() {
		return String.valueOf(index);
	}

	@Override
	public String getValue() {
		return name;
	}  
}
