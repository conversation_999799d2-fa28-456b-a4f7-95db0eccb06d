package com.sgs.ecom.order.dto.bill;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.dto.BillDtlDTO;
import com.sgs.ecom.order.dto.pay.BankDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @Description :月结账单导出信息新的结构
 * @date 2024/7/22
 */
public class MonthBillDTO {
    //银行信息
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    private BankDTO bankDTO;
    //账单信息
    @ApiAnno(groups={BaseQryFilter.QueryDtl.class})
    @BeanAnno(dtocls = {BillDtlDTO.class})
    private List<BillDtlDTO> items;

    public BankDTO getBankDTO() {
        return bankDTO;
    }

    public void setBankDTO(BankDTO bankDTO) {
        this.bankDTO = bankDTO;
    }

    public List<BillDtlDTO> getItems() {
        return items;
    }

    public void setItems(List<BillDtlDTO> items) {
        this.items = items;
    }
}
