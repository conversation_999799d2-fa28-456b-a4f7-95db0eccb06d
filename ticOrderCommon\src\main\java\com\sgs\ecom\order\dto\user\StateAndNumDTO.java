package com.sgs.ecom.order.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class StateAndNumDTO {
    /**
     * 活动编号
     */
    private String  activityCode;
    /**
     * 状态
     */
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    /**
     * 数量
     */
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int num;


    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }
}
