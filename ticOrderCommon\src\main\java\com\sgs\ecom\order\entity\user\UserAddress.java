package com.sgs.ecom.order.entity.user;

public class UserAddress {

    private String stateDate;

    private Long userId;

    private Integer state;

    private String userName;

    private String companyAddress;

    private String companyName;

    private String userPhone;

    private String createDate;

    private String city;

    private String town;

    private String province;

    private Integer isDefault;

    private Long addressId;

    private String userMail;

    private String addressUuid;



    public void setStateDate(String stateDate){
        this.stateDate=stateDate;
    }
    public String getStateDate(){
        return this.stateDate;
    }


    public void setUserId(Long userId){
        this.userId=userId;
    }
    public Long getUserId(){
        return this.userId;
    }


    public void setState(Integer state){
        this.state=state;
    }
    public Integer getState(){
        return this.state;
    }


    public void setUserName(String userName){
        this.userName=userName;
    }
    public String getUserName(){
        return this.userName;
    }


    public void setCompanyAddress(String companyAddress){
        this.companyAddress=companyAddress;
    }
    public String getCompanyAddress(){
        return this.companyAddress;
    }


    public void setCompanyName(String companyName){
        this.companyName=companyName;
    }
    public String getCompanyName(){
        return this.companyName;
    }


    public void setUserPhone(String userPhone){
        this.userPhone=userPhone;
    }
    public String getUserPhone(){
        return this.userPhone;
    }


    public void setCreateDate(String createDate){
        this.createDate=createDate;
    }
    public String getCreateDate(){
        return this.createDate;
    }


    public void setCity(String city){
        this.city=city;
    }
    public String getCity(){
        return this.city;
    }


    public void setTown(String town){
        this.town=town;
    }
    public String getTown(){
        return this.town;
    }


    public void setProvince(String province){
        this.province=province;
    }
    public String getProvince(){
        return this.province;
    }


    public void setIsDefault(Integer isDefault){
        this.isDefault=isDefault;
    }
    public Integer getIsDefault(){
        return this.isDefault;
    }


    public void setAddressId(Long addressId){
        this.addressId=addressId;
    }
    public Long getAddressId(){
        return this.addressId;
    }


    public void setUserMail(String userMail){
        this.userMail=userMail;
    }
    public String getUserMail(){
        return this.userMail;
    }

    public String getAddressUuid() {
        return addressUuid;
    }

    public void setAddressUuid(String addressUuid) {
        this.addressUuid = addressUuid;
    }
}
