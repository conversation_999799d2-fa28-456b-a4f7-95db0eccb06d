package com.sgs.ecom.order.dto.send;

import com.sgs.ecom.order.request.FileReq;

import java.util.List;

public class TicOtherApiDTO {

	private List<FileReq> fileReqList;

	private String orderNo;
	private String operatorCode;

	private boolean operatorCodeFlag;//当前这笔订单的订单操作人时空的为true


	private boolean updateFlag;

	public boolean isOperatorCodeFlag() {
		return operatorCodeFlag;
	}

	public void setOperatorCodeFlag(boolean operatorCodeFlag) {
		this.operatorCodeFlag = operatorCodeFlag;
	}

	public boolean isUpdateFlag() {
		return updateFlag;
	}

	public void setUpdateFlag(boolean updateFlag) {
		this.updateFlag = updateFlag;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public TicOtherApiDTO() {
	}

	public TicOtherApiDTO(List<FileReq> fileReqList) {
		this.fileReqList = fileReqList;
	}

	public List<FileReq> getFileReqList() {
		return fileReqList;
	}

	public void setFileReqList(List<FileReq> fileReqList) {
		this.fileReqList = fileReqList;
	}
}
