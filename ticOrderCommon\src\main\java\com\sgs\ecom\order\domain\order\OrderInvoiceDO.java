package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.user.UserAddressDTO;
import com.sgs.ecom.order.dto.user.UserInvoiceDTO;
import com.sgs.ecom.order.entity.order.OrderInvoice;
import com.sgs.ecom.order.entity.user.UserInvoice;
import com.sgs.ecom.order.enumtool.user.UserInvoiceEnum;
import com.sgs.ecom.order.request.oiq.OiqUserInvoiceReq;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;

public class OrderInvoiceDO {

    private BaseCopyObj baseCopy = new BaseCopyObj();



    private  OrderInvoice initOrderInvoice(String orderNo, Long userId, OrderInvoice orderInvoice) {
        orderInvoice.setOrderNo(orderNo);
        orderInvoice.setUserId(userId);
        orderInvoice.setState(1);
        orderInvoice.setCreateDate(UseDateUtil.getDateString(new Date()));
        orderInvoice.setStateDate(UseDateUtil.getDateString(new Date()));
        return orderInvoice;
    }

    public OrderInvoice getOrderInvoiceByUserInvoiceDTO(UserInvoiceDTO userInvoiceDTO, OrderInvoice orderInvoice) {
        baseCopy.copyWithNull(orderInvoice,userInvoiceDTO);
        orderInvoice.setBankAddr(userInvoiceDTO.getBankName());
        orderInvoice.setRegisterPhone(userInvoiceDTO.getRegPhone());
        orderInvoice.setRegisterAddr(userInvoiceDTO.getRegAddress());
        return orderInvoice;
    }

    public OrderInvoice getOrderInvoice(String orderNo, Long userId, UserAddressDTO userAddressDTO, OrderInvoice orderInvoice) {
        orderInvoice= initOrderInvoice(orderNo, userId,orderInvoice);
        if (!ValidationUtil.isEmpty(userAddressDTO) && !ValidationUtil.isEmpty(orderInvoice)) {
            orderInvoice.setDeliveryName(userAddressDTO.getUserName());
            orderInvoice.setDeliveryPhone(userAddressDTO.getUserPhone());
            orderInvoice.setDeliveryProvince(userAddressDTO.getProvince());
            orderInvoice.setDeliveryCity(userAddressDTO.getCity());
            orderInvoice.setDeliveryTown(userAddressDTO.getTown());
            orderInvoice.setDeliveryAddr(userAddressDTO.getCompanyAddress());
            orderInvoice.setAddressId(userAddressDTO.getAddressId());
            orderInvoice.setDeliverMail(userAddressDTO.getUserMail());
            orderInvoice.setDeliverCompany(userAddressDTO.getCompanyName());
            if (!ValidationUtil.isEmpty(orderInvoice) && !ValidationUtil.isEmpty(orderInvoice.getInvoiceType())
                    && orderInvoice.getInvoiceType() == UserInvoiceEnum.ONLY.getIndex()) {
                orderInvoice.setLinkPerson(userAddressDTO.getUserName());
                orderInvoice.setLinkPhone(userAddressDTO.getUserPhone());
                orderInvoice.setLinkEmail(userAddressDTO.getUserMail());
            }

        }
        return orderInvoice;

    }

    public OrderInvoice getInvoiceByUserInvoiceDTO(OrderInvoice orderInvoice, OiqUserInvoiceReq userInvoiceDTO){
        if(ValidationUtil.isEmpty(userInvoiceDTO)){
            return orderInvoice;
        }
        baseCopy.copy(orderInvoice,userInvoiceDTO);
        orderInvoice.setBankAddr(userInvoiceDTO.getBankName());
        orderInvoice.setRegisterPhone(userInvoiceDTO.getRegPhone());
        orderInvoice.setRegisterAddr(userInvoiceDTO.getRegAddress());
        return orderInvoice;
    }

}
