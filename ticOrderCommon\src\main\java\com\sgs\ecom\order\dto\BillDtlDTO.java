package com.sgs.ecom.order.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class BillDtlDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select REFERENCE_INFO,STATE_DATE,PAY_DATE,STATE,ORDER_NO,USER_NAME,INVOICE_NO,IS_OTHER,ORDER_AMOUNT,USER_PHONE,CREATE_DATE,DTL_ID,BILL_ID,PAY_STATE,BILL_CYCLE,REPORT_NAME from TB_BILL_DTL"; 
 
 
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="REFERENCE_INFO", getName="getReferenceInfo", setName="setReferenceInfo")
 	private String referenceInfo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="PAY_DATE", getName="getPayDate", setName="setPayDate")
 	private Timestamp payDate;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={QueryDtl.class},serviceName={"qryBill"})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="INVOICE_NO", getName="getInvoiceNo", setName="setInvoiceNo")
 	private String invoiceNo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="IS_OTHER", getName="getIsOther", setName="setIsOther")
 	private int isOther;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="ORDER_AMOUNT", getName="getOrderAmount", setName="setOrderAmount")
 	private BigDecimal orderAmount;
 	@ApiAnno(groups={QueryDtl.class},serviceName={"qryBill"})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="DTL_ID", getName="getDtlId", setName="setDtlId")
 	private long dtlId;
 	@IsNullAnno(groups={QueryDtl.class})
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="BILL_ID", getName="getBillId", setName="setBillId")
 	private long billId;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="PAY_STATE", getName="getPayState", setName="setPayState")
 	private int payState;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="BILL_CYCLE", getName="getBillCycle", setName="setBillCycle")
 	private String billCycle;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="REPORT_NAME", getName="getReportName", setName="setReportName")
 	private String reportName;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;

 	@ApiAnno(groups={QueryDtl.class})
 	private List<String> reportNameList=new ArrayList<>();
 	@ApiAnno(groups={QueryDtl.class})
	private int maxSize;
	@ApiAnno(groups={QueryDtl.class})
	private String categoryName;
	@ApiAnno(groups={QueryDtl.class})
	private String labName;

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public void setReferenceInfo(String referenceInfo){
 		 this.referenceInfo=referenceInfo;
 	}
 	public String getReferenceInfo(){
 		 return this.referenceInfo;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setPayDate(Timestamp payDate){
 		 this.payDate=payDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getPayDate(){
 		 return this.payDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setInvoiceNo(String invoiceNo){
 		 this.invoiceNo=invoiceNo;
 	}
 	public String getInvoiceNo(){
 		 return this.invoiceNo;
 	}
 
 	 
 	public void setIsOther(int isOther){
 		 this.isOther=isOther;
 	}
 	public int getIsOther(){
 		 return this.isOther;
 	}
 
 	 
 	public void setOrderAmount(BigDecimal orderAmount){
 		 this.orderAmount=orderAmount;
 	}
 	public BigDecimal getOrderAmount(){
 		 return this.orderAmount;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setDtlId(long dtlId){
 		 this.dtlId=dtlId;
 	}
 	public long getDtlId(){
 		 return this.dtlId;
 	}
 
 	 
 	public void setBillId(long billId){
 		 this.billId=billId;
 	}
 	public long getBillId(){
 		 return this.billId;
 	}
 
 	 
 	public void setPayState(int payState){
 		 this.payState=payState;
 	}
 	public int getPayState(){
 		 return this.payState;
 	}
 
 	 
 	public void setBillCycle(String billCycle){
 		 this.billCycle=billCycle;
 	}
 	public String getBillCycle(){
 		 return this.billCycle;
 	}
 
 	 
 	public void setReportName(String reportName){
 		 this.reportName=reportName;
 	}
 	public String getReportName(){
 		 return this.reportName;
 	}
	public String getBu() {
		return bu;
	}
	public void setBu(String bu) {
		this.bu = bu;
	}
	public List<String> getReportNameList() {
		return reportNameList;
	}
	public void setReportNameList(List<String> reportNameList) {
		this.reportNameList = reportNameList;
	}
	public int getMaxSize() {
		return maxSize;
	}
	public void setMaxSize(int maxSize) {
		this.maxSize = maxSize;
	}
}