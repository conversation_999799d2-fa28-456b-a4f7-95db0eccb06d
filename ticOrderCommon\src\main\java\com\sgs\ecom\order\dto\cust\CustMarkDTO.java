package com.sgs.ecom.order.dto.cust;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;

import java.util.ArrayList;
import java.util.List;

public class CustMarkDTO {
    @ApiAnno(groups={BaseOrderFilter.CustMarkList.class})
    private Long custId;
    @ApiAnno(groups={BaseOrderFilter.CustMarkList.class})
    private String companyName;
    @ApiAnno(groups={BaseOrderFilter.CustMarkList.class})
    private List<CustUserMarkDTO> userList=new ArrayList<>();

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public List<CustUserMarkDTO> getUserList() {
        return userList;
    }

    public void setUserList(List<CustUserMarkDTO> userList) {
        this.userList = userList;
    }


}
