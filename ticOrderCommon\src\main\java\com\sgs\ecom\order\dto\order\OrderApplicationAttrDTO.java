package com.sgs.ecom.order.dto.order;

import com.alibaba.fastjson.JSON;
import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.enumtool.bbc.AppFormEnum;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class OrderApplicationAttrDTO {

	private String attrKey;
	@ApiAnno(groups={BaseOrderFilter.OrderQryList.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String attrValue;
	@ApiAnno(groups={BaseOrderFilter.OrderQryList.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String attrName;
	private String orderNo;
	@ApiAnno(groups={BaseOrderFilter.OrderQryList.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String areaCode;
	@ApiAnno(groups={BaseOrderFilter.OrderQryList.class,BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String attrCode;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String value;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class,BaseOrderFilter.OiqFormInfo.class})
	private String enumConfig;

	private String  attrTextString;
	@ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
	private Object attrText;

	private int isFile;
	private int keyShow=1;
	private int sortShow;//通过枚举获取

	public OrderApplicationAttrDTO() {
	}
	public OrderApplicationAttrDTO(String attrValue) {
		this.attrValue = attrValue;
	}

	public OrderApplicationAttrDTO(String attrCode,String attrName) {
		this.attrCode=attrCode;
		this.attrName = attrName;
	}

	public OrderApplicationAttrDTO(String attrCode, String attrValue, String areaCode, int keyShow) {
		this.attrValue = attrValue;
		this.keyShow = keyShow;
	}

	private List<OrderAttachmentDTO> fileList=new ArrayList<>();

	public String getAttrValue() {
		return attrValue;
	}

	public void setAttrValue(String attrValue) {
		this.attrValue = attrValue;
	}

	public String getAttrName() {
		return attrName;
	}

	public void setAttrName(String attrName) {
		this.attrName = attrName;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getAttrCode() {
		return attrCode;
	}

	public void setAttrCode(String attrCode) {
		this.attrCode = attrCode;
	}

	public int getIsFile() {
		return isFile;
	}

	public void setIsFile(int isFile) {
		this.isFile = isFile;
	}

	public List<OrderAttachmentDTO> getFileList() {
		return fileList;
	}

	public void setFileList(List<OrderAttachmentDTO> fileList) {
		this.fileList = fileList;
	}

	public int getSortShow() {
		return AppFormEnum.getSortShow(attrCode);
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}

	public int getKeyShow() {
		return keyShow;
	}

	public void setKeyShow(int keyShow) {
		this.keyShow = keyShow;
	}

	public String getAttrKey() {
		return attrKey;
	}

	public void setAttrKey(String attrKey) {
		this.attrKey = attrKey;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}

	public String getAttrTextString() {
		return attrTextString;
	}

	public void setAttrTextString(String attrTextString) {
		this.attrTextString = attrTextString;
	}

	public void setAttrText(Object attrText) {
		this.attrText = attrText;
	}

	public Object getAttrText() {
		if(StringUtils.isNotBlank(attrTextString)){
			return JSON.parseObject(attrTextString, Object.class);
		}

		return attrText;
	}
}
