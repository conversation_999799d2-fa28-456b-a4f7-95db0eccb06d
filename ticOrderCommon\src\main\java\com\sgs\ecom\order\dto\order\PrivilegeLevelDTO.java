package com.sgs.ecom.order.dto.order;



import com.sgs.ecom.order.dto.permission.AuthGroupDTO;
import com.sgs.ecom.order.dto.permission.AuthOrderStateDTO;
import com.sgs.ecom.order.dto.permission.RoAuthDTO;

import java.util.List;
import java.util.Map;

public class PrivilegeLevelDTO {
    //1自己的数据 3全部数据
    private String privilegeLevel;

    private List<Map> buItem;

    private List<Map> lineItem;

    //ro使用
    private List<RoAuthDTO> privilegeItem;

    public List<AuthGroupDTO> authProductItem;
    // attrCode:ORDER attrValue;
    private List<AuthOrderStateDTO> roleAttr;

    private List<Long> labIds;

    public List<Long> getLabIds() {
        return labIds;
    }

    public void setLabIds(List<Long> labIds) {
        this.labIds = labIds;
    }

    public String getPrivilegeLevel() {
        return privilegeLevel;
    }

    public void setPrivilegeLevel(String privilegeLevel) {
        this.privilegeLevel = privilegeLevel;
    }

    public List<Map> getBuItem() {
        return buItem;
    }

    public void setBuItem(List<Map> buItem) {
        this.buItem = buItem;
    }

    public List<Map> getLineItem() {
        return lineItem;
    }

    public void setLineItem(List<Map> lineItem) {
        this.lineItem = lineItem;
    }

    public List<AuthGroupDTO> getAuthProductItem() {
        return authProductItem;
    }

    public void setAuthProductItem(List<AuthGroupDTO> authProductItem) {
        this.authProductItem = authProductItem;
    }

    public List<AuthOrderStateDTO> getRoleAttr() {
        return roleAttr;
    }

    public void setRoleAttr(List<AuthOrderStateDTO> roleAttr) {
        this.roleAttr = roleAttr;
    }

    public List<RoAuthDTO> getPrivilegeItem() {
        return privilegeItem;
    }

    public void setPrivilegeItem(List<RoAuthDTO> privilegeItem) {
        this.privilegeItem = privilegeItem;
    }
}
