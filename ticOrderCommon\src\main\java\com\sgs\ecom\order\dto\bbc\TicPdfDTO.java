package com.sgs.ecom.order.dto.bbc;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

public class TicPdfDTO {
	private String orderNo;
	private String to;
	private String attn;
	private String add;
	private String tel;
	private String email;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String date;
	private String from;
	private String contact;
	private String sgsEmail;

	public TicPdfDTO() {
	}

	public TicPdfDTO(String orderNo) {
		this.orderNo = orderNo;
		this.from = "通标标准技术服务有限公司 ";
		this.contact = "************";
		this.sgsEmail = "<EMAIL>";
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getTo() {
		return to;
	}

	public void setTo(String to) {
		this.to = to;
	}

	public String getAttn() {
		return attn;
	}

	public void setAttn(String attn) {
		this.attn = attn;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getDate() {
		return date.split("\\.")[0];
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getSgsEmail() {
		return sgsEmail;
	}

	public void setSgsEmail(String sgsEmail) {
		this.sgsEmail = sgsEmail;
	}

	public String getAdd() {
		return add;
	}

	public void setAdd(String add) {
		this.add = add;
	}
}
