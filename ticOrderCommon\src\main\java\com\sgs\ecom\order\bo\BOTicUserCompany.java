package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="用户公司")
public class BOTicUserCompany{
 
 	public static final String SEQUENCE = "ID"; 
  
 	public static final String BO_SQL = "TIC_UserCompany"; 
 
 	public static final String OWNER ="bbc";

 	public static final String PAY_TYPE="payType";
 	public static final String CREATEBY="createby";
 	public static final String COMPANYNAMEEN="companynameen";
 	public static final String COMPANYNAMECN="companynamecn";
 	public static final String CITY="city";
 	public static final String MODIFIEDBY="modifiedby";
 	public static final String MODIFIEDDATE="modifieddate";
 	public static final String CREATEDATE="createdate";
 	public static final String PROVINCE="province";
 	public static final String COMPANYADDRESS="companyaddress";
 	public static final String COMPANY_ADDR_EN="companyAddrEn";
 	public static final String USERID="userid";
 	public static final String ID="id";
 	public static final String DISTRICT="district";

 	@BeanAnno("Pay_Type")
 	private String payType;
 	@BeanAnno("CreateBy")
 	private String createby;
 	@BeanAnno("CompanyNameEN")
 	private String companynameen;
 	@BeanAnno("CompanyNameCN")
 	private String companynamecn;
 	@BeanAnno("City")
 	private String city;
 	@BeanAnno("ModifiedBy")
 	private String modifiedby;
 	@BeanAnno("ModifiedDate")
 	private Timestamp modifieddate;
 	@BeanAnno("CreateDate")
 	private Timestamp createdate;
 	@BeanAnno("Province")
 	private String province;
 	@BeanAnno("CompanyAddress")
 	private String companyaddress;
 	@BeanAnno("Company_Addr_En")
 	private String companyAddrEn;
 	@BeanAnno("UserID")
 	private String userid;
 	@BeanAnno("ID")
 	private String id;
 	@BeanAnno("District")
 	private String district;

 	@CharacterVaild(len = 10) 
 	public void setPayType(String payType){
 		 this.payType=payType;
 	}
 	@CheckAnno(len = 10) 
 	public String getPayType(){
 		 return this.payType;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCreateby(String createby){
 		 this.createby=createby;
 	}
 	@CheckAnno(len = 50) 
 	public String getCreateby(){
 		 return this.createby;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanynameen(String companynameen){
 		 this.companynameen=companynameen;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanynameen(){
 		 return this.companynameen;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanynamecn(String companynamecn){
 		 this.companynamecn=companynamecn;
 	}
 	@CheckAnno(len = 500) 
 	public String getCompanynamecn(){
 		 return this.companynamecn;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	@CheckAnno(len = 100) 
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setModifiedby(String modifiedby){
 		 this.modifiedby=modifiedby;
 	}
 	@CheckAnno(len = 50) 
 	public String getModifiedby(){
 		 return this.modifiedby;
 	}
 
 	 
 	public void setModifieddate(Timestamp modifieddate){
 		 this.modifieddate=modifieddate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getModifieddate(){
 		 return this.modifieddate;
 	}
 
 	 
 	public void setCreatedate(Timestamp createdate){
 		 this.createdate=createdate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreatedate(){
 		 return this.createdate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProvince(String province){
 		 this.province=province;
 	}
 	@CheckAnno(len = 100) 
 	public String getProvince(){
 		 return this.province;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyaddress(String companyaddress){
 		 this.companyaddress=companyaddress;
 	}
 	@CheckAnno(len = 500) 
 	public String getCompanyaddress(){
 		 return this.companyaddress;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyAddrEn(String companyAddrEn){
 		 this.companyAddrEn=companyAddrEn;
 	}
 	@CheckAnno(len = 500) 
 	public String getCompanyAddrEn(){
 		 return this.companyAddrEn;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserid(String userid){
 		 this.userid=userid;
 	}
 	@CheckAnno(len = 50) 
 	public String getUserid(){
 		 return this.userid;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	@CheckAnno(len = 50) 
 	public String getId(){
 		 return this.id;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDistrict(String district){
 		 this.district=district;
 	}
 	@CheckAnno(len = 100) 
 	public String getDistrict(){
 		 return this.district;
 	}
 
 	 
}