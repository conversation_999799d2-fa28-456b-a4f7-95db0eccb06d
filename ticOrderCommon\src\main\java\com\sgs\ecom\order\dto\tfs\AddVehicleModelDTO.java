package com.sgs.ecom.order.dto.tfs;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class AddVehicleModelDTO {
    /**
     * 海关编码
     */
    private String hsCode;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 车辆类型显示值
     */
    private String vehicleTypeShow;
    /**
     * 车辆品牌
     */
    private String vehicleBrand;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 车架号
     */
    private String vehicleChassisNo;
    /**
     * FOB价
     */
    private BigDecimal fobValue;
    /**
     * 海运费
     */
    private BigDecimal oceanFreight;
    /**
     * 品名
     */
    private String goodsName;
    /**
     * 车辆型号
     */
    private String vehicleModel;
    /**
     * 货物件数
     */
    private String packagesNumber;
    /**
     * 包装种类
     */
    private String packageType;
    /**
     * 总毛重
     */
    private String totalGrossWeight;
    /**
     * 车毛重
     */
    private String vehicleGrossWeight;
    /**
     * 车辆制造日期
     */
    private String manufactureDate;
    /**
     * 是否为二手车
     */
    private String isUsedVehicle;
}
