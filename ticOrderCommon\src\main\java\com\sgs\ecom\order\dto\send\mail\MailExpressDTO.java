package com.sgs.ecom.order.dto.send.mail;


import com.sgs.ecom.order.enumtool.application.ExpressCodeEnum;
import com.sgs.ecom.order.vo.VOOrderExpress;

public class MailExpressDTO {
    private String expressNo;
    private String expressCodeShow;
    private String goodsName;

    public MailExpressDTO() {
    }



    public MailExpressDTO(VOOrderExpress voOrderExpress) {
        this.expressNo = voOrderExpress.getExpressNo();
        this.expressCodeShow = ExpressCodeEnum.getNameCh(voOrderExpress.getExpressCode());
        this.goodsName=voOrderExpress.getGoodsName();

    }
    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }

    public String getExpressCodeShow() {
        return expressCodeShow;
    }

    public void setExpressCodeShow(String expressCodeShow) {
        this.expressCodeShow = expressCodeShow;
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }
}
