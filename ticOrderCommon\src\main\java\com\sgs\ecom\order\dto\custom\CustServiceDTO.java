package com.sgs.ecom.order.dto.custom; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.sql.Timestamp;

public class CustServiceDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select PERSON_PHONE,CREATE_DATE,BU,PERSON_MAIL,PERSON_CODE,PERSON_ID,STATE_DATE,STATE,CUST_CODE,BUSI_CODE,ID from TB_CUST_SERVICE"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PERSON_PHONE", getName="getPersonPhone", setName="setPersonPhone")
 	private String personPhone;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PERSON_MAIL", getName="getPersonMail", setName="setPersonMail")
 	private String personMail;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PERSON_ID", getName="getPersonId", setName="setPersonId")
 	private long personId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BUSI_CODE", getName="getBusiCode", setName="setBusiCode")
 	private String busiCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private long id;

	private Long custId;

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public void setPersonPhone(String personPhone){
 		 this.personPhone=personPhone;
 	}
 	public String getPersonPhone(){
 		 return this.personPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setPersonMail(String personMail){
 		 this.personMail=personMail;
 	}
 	public String getPersonMail(){
 		 return this.personMail;
 	}
 
 	 
 	public void setPersonCode(String personCode){
 		 this.personCode=personCode;
 	}
 	public String getPersonCode(){
 		 return this.personCode;
 	}
 
 	 
 	public void setPersonId(long personId){
 		 this.personId=personId;
 	}
 	public long getPersonId(){
 		 return this.personId;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	public void setBusiCode(String busiCode){
 		 this.busiCode=busiCode;
 	}
 	public String getBusiCode(){
 		 return this.busiCode;
 	}
 
 	 
 	public void setId(long id){
 		 this.id=id;
 	}
 	public long getId(){
 		 return this.id;
 	}
 
 	 
}