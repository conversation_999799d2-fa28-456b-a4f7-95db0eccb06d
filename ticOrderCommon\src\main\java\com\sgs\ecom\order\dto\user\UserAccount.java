package com.sgs.ecom.order.dto.user;

import java.util.Date;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 用户账户
 *
 * <AUTHOR>
 * @since 2025-06-27 08:50
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAccount {

    /**
     * 账号ID
     */
    private Long accountId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 账号类型，account用户名，phone手机，email邮箱，wechat微信，zfb支付宝
     */
    private String accountType;

    /**
     * 账号
     */
    private String accountNo;

    /**
     * 状态,1有效，0无效
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 外部应用ID
     */
    private String appId;

    /**
     * 微信unionId
     */
    private String unionId;
}
