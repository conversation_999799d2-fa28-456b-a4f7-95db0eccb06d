package com.sgs.ecom.order.dto.export;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.order.FromSourceEnum;
import com.sgs.ecom.order.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.order.TestLabelEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.util.collection.StrUtil;
import org.apache.commons.lang.StringUtils;

/**
* @params
* @return 询价和订单的合集
* @description
* <AUTHOR> || created at 2023/6/14 9:20
*/
public class ExpInquiryOrderDTO {
    //询价单号
    private String inquiryOrderNo;
    //订单编号
    private String orderNo;
    //BU信息
    private String bu;
    //业务线
    private String businessLine;
    //执行实验室
    private String labName;
    //首次询价
    private String inquiryFlg;
    //询价单创建时间
    private String createDate;
    //询价单状态
    private String state;
    //询价单来源
    private String orderSource;
    //leads单号
    private String leadsCode;
    //询价单创建来源
    private String fromSource;
    private String fromSourceShow;
    //产品类目
    private String categoryPath;
    //测试类型标签
    private String testLabel;
    //问卷名称
    private String questionName;
    //测试服务项目
    private String itemNameAll;
    //当前报价金额
    private String inquiryRealAmount;
    //订单总金额
    private String realAmountTotal;
    //支付状态
    private String payState;
    //支付方式
    private String payMethod;
    //客户ID
    private Long userId;
    //询价单公司名称
    private String companyName;
    //订单公司名称
    private String orderCompanyName;
    //复购客户
    private String reOrder;
    //所在地区
    private String provinceCity;
    //首次支付OIQ订单时间
    private String firstPayTime;
    //当前跟进人
    private String csCode;
    //首次认领/分配时间
    private String firstDistributionTime;
    //首次报价时间
    private String firstWaitConfirmTime;
    //预期报价时间
    private String lastResponseDate;
    //最近报价时间
    private String lastWaitConfirmTime;
    //报价确认时间
    private String confirmTime;
    //询价单关闭时间
    private String closeTime;
    //询价单关闭理由
    private String closeMemo;
    //询价单关闭理由备注(和业务确认取最新一条)
    private String closeReasonMemo;
    //订单创建时间
    private String orderCreateDate;
    //订单状态
    private String orderState;
    //订单关闭理由
    private String orderCloseReason;
    //订单关闭备注
    private String orderCloseMemo;

    private String createCode;

    private String qryDetailTime;

    private String currency;

    //销售员
    private String salesCode;
    //引导员
    private String promoInfo;

    private String userPhone;

    private String userEmail;

    public String getCloseReasonMemo() {
        return closeReasonMemo;
    }

    public void setCloseReasonMemo(String closeReasonMemo) {
        this.closeReasonMemo = closeReasonMemo;
    }

    public String getInquiryOrderNo() {
        return inquiryOrderNo;
    }

    public void setInquiryOrderNo(String inquiryOrderNo) {
        this.inquiryOrderNo = inquiryOrderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public String getInquiryFlg() {
        return inquiryFlg;
    }

    public void setInquiryFlg(String inquiryFlg) {
        this.inquiryFlg = inquiryFlg;
    }

    public String getCreateDate() {
        return StrUtil.isTime(createDate);
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getState() {
        return OrderStateEnum.getNameCh(state);
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getOrderSource() {
        if(StringUtils.isNotBlank(orderSource)){
            return OrderSourceEnum.getNameCh(orderSource);
        }
        return "";
    }


    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getLeadsCode() {
        return leadsCode;
    }

    public void setLeadsCode(String leadsCode) {
        this.leadsCode = leadsCode;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getFromSourceShow() {
        return FromSourceEnum.getNameCh(fromSource);
    }

    public void setFromSourceShow(String fromSourceShow) {
        this.fromSourceShow = fromSourceShow;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getTestLabel() {
        if(StringUtils.isNotBlank(testLabel)){
            return TestLabelEnum.getNameCh(Integer.parseInt(testLabel));
        }
        return "";
    }

    public void setTestLabel(String testLabel) {
        this.testLabel = testLabel;
    }

    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public String getItemNameAll() {
        return itemNameAll;
    }

    public void setItemNameAll(String itemNameAll) {
        this.itemNameAll = itemNameAll;
    }

    public String getInquiryRealAmount() {
        return inquiryRealAmount;
    }

    public void setInquiryRealAmount(String inquiryRealAmount) {
        this.inquiryRealAmount = inquiryRealAmount;
    }

    public String getRealAmountTotal() {
        return realAmountTotal;
    }

    public void setRealAmountTotal(String realAmountTotal) {
        this.realAmountTotal = realAmountTotal;
    }

    public String getPayState() {
        if(StringUtils.isNotBlank(payState)){
            return OrderPayStateEnum.getNameCh(Integer.parseInt(payState));
        }
        return "";
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public String getPayMethod() {
        return PayMethodEnum.getNameCh(payMethod);
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getOrderCompanyName() {
        return orderCompanyName;
    }

    public void setOrderCompanyName(String orderCompanyName) {
        this.orderCompanyName = orderCompanyName;
    }

    public String getProvinceCity() {
        return provinceCity;
    }

    public void setProvinceCity(String provinceCity) {
        this.provinceCity = provinceCity;
    }

    public String getFirstPayTime() {
        return StrUtil.isTime(firstPayTime);
    }

    public void setFirstPayTime(String firstPayTime) {
        this.firstPayTime = firstPayTime;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getFirstDistributionTime() {
        return StrUtil.isTime(firstDistributionTime);
    }

    public void setFirstDistributionTime(String firstDistributionTime) {
        this.firstDistributionTime = firstDistributionTime;
    }

    public String getFirstWaitConfirmTime() {
        return StrUtil.isTime(firstWaitConfirmTime);
    }

    public void setFirstWaitConfirmTime(String firstWaitConfirmTime) {
        this.firstWaitConfirmTime = firstWaitConfirmTime;
    }

    public String getLastResponseDate() {
        return StrUtil.isTime(lastResponseDate);
    }

    public void setLastResponseDate(String lastResponseDate) {
        this.lastResponseDate = lastResponseDate;
    }

    public String getLastWaitConfirmTime() {
        return StrUtil.isTime(lastWaitConfirmTime);
    }

    public void setLastWaitConfirmTime(String lastWaitConfirmTime) {
        this.lastWaitConfirmTime = lastWaitConfirmTime;
    }

    public String getConfirmTime() {
        return StrUtil.isTime(confirmTime);
    }

    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getCloseTime() {
        return StrUtil.isTime(closeTime);
    }

    public void setCloseTime(String closeTime) {
        this.closeTime = closeTime;
    }

    public String getCloseMemo() {
        return closeMemo;
    }

    public void setCloseMemo(String closeMemo) {
        this.closeMemo = closeMemo;
    }

    public String getOrderCreateDate() {
        return orderCreateDate;
    }

    public void setOrderCreateDate(String orderCreateDate) {
        this.orderCreateDate = orderCreateDate;
    }

    public String getOrderState() {
        return OrderStateEnum.getNameCh(orderState);
    }


    public void setOrderState(String orderState) {
        this.orderState = orderState;
    }



    public String getOrderCloseMemo() {
        return orderCloseMemo;
    }

    public void setOrderCloseMemo(String orderCloseMemo) {
        this.orderCloseMemo = orderCloseMemo;
    }

    public String getReOrder() {
        return reOrder;
    }

    public void setReOrder(String reOrder) {
        this.reOrder = reOrder;
    }

    public String getOrderCloseReason() {
        return orderCloseReason;
    }

    public void setOrderCloseReason(String orderCloseReason) {
        this.orderCloseReason = orderCloseReason;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public String getQryDetailTime() {
        return StrUtil.isTime(qryDetailTime);
    }

    public void setQryDetailTime(String qryDetailTime) {
        this.qryDetailTime = qryDetailTime;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(String salesCode) {
        this.salesCode = salesCode;
    }

    public String getPromoInfo() {
        return promoInfo;
    }

    public void setPromoInfo(String promoInfo) {
        this.promoInfo = promoInfo;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }
}
