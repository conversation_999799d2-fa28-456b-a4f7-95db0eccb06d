package com.sgs.ecom.order.domain.order;

import com.sgs.ecom.order.entity.order.OrderCustomer;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;

public class OrderCustomerDO extends OrderCustomer {


    public static OrderCustomer getBaseOrderCustomer(String orderNo){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderCustomer orderCustomer=new OrderCustomer();
        orderCustomer.setState(1);
        orderCustomer.setOrderNo(orderNo);
        return orderCustomer;
    }
}
