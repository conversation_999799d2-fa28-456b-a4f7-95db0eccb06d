package com.sgs.ecom.order.dto.order;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class OrderIndexDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int num;

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }
}
