package com.sgs.ecom.order.dto.question;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class OrderQuestionDTO {

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long replyId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long catagoryId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long questionId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int questionType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String questionName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String questionNameEn;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bu;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String confirmOrderDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long lineId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String businessLine;


    public Long getCatagoryId() {
        return catagoryId;
    }

    public void setCatagoryId(Long catagoryId) {
        this.catagoryId = catagoryId;
    }

    public Long getQuestionId() {
        return questionId;
    }

    public void setQuestionId(Long questionId) {
        this.questionId = questionId;
    }

    public int getQuestionType() {
        return questionType;
    }

    public void setQuestionType(int questionType) {
        this.questionType = questionType;
    }

    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public String getQuestionNameEn() {
        return questionNameEn;
    }

    public void setQuestionNameEn(String questionNameEn) {
        this.questionNameEn = questionNameEn;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Long getReplyId() {
        return replyId;
    }

    public void setReplyId(Long replyId) {
        this.replyId = replyId;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getConfirmOrderDate() {
        return confirmOrderDate;
    }

    public void setConfirmOrderDate(String confirmOrderDate) {
        this.confirmOrderDate = confirmOrderDate;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }
}
