package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="")
public class QBOCustInfo{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.CUST_ID,a.address_en,a.CUST_CODE,a.COMPANY_NAME,a.COMPANY_NAME_EN,a.COUNTRY,a.PROVICE,a.CITY,a.TOWN,a.ADDRESS,a.CREDIT_AMOUNT,a.STATE,a.CREATE_DATE,a.STATE_DATE,a.IS_FREEZE,a.MEMO,c.user_name,c.user_nick,c.user_phone,d.invoice_title,a.BUSI_CODE from TB_CUST_INFO a LEFT JOIN TB_CUST_APPLY_RELATE b ON a.cust_id=b.cust_id LEFT JOIN USER_INFO c ON b.USER_ID=c.USER_ID LEFT JOIN TB_CUST_INVOICE d ON a.CUST_ID=d.CUST_ID"; 
 
 	public static final String OWNER ="member";

 	public static final String IS_FREEZE="isFreeze";
 	public static final String CUST_ID="custId";
 	public static final String PROVICE="provice";
 	public static final String CREDIT_AMOUNT="creditAmount";
 	public static final String USER_NAME="userName";
 	public static final String INVOICE_TITLE="invoiceTitle";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String USER_NICK="userNick";
 	public static final String COMPANY_NAME="companyName";
 	public static final String CREATE_DATE="createDate";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String ADDRESS="address";
 	public static final String USER_PHONE="userPhone";
 	public static final String ADDRESS_EN="addressEn";
 	public static final String CUST_CODE="custCode";
 	public static final String COMPANY_NAME_EN="companyNameEn";
 	public static final String MEMO="memo";
 	public static final String BUSI_CODE="busiCode";

 	@BeanAnno(value="IS_FREEZE",table="a")
 	private int isFreeze;
 	@BeanAnno(value="CUST_ID",table="a")
 	private long custId;
 	@BeanAnno(value="PROVICE",table="a")
 	private String provice;
 	@BeanAnno(value="CREDIT_AMOUNT",table="a")
 	private double creditAmount;
 	@BeanAnno(value="user_name",table="c")
 	private String userName;
 	@BeanAnno(value="invoice_title",table="d")
 	private String invoiceTitle;
 	@BeanAnno(value="STATE_DATE",table="a")
 	private Timestamp stateDate;
 	@BeanAnno(value="STATE",table="a")
 	private int state;
 	@BeanAnno(value="user_nick",table="c")
 	private String userNick;
 	@BeanAnno(value="COMPANY_NAME",table="a")
 	private String companyName;
 	@BeanAnno(value="CREATE_DATE",table="a")
 	private Timestamp createDate;
 	@BeanAnno(value="COUNTRY",table="a")
 	private String country;
 	@BeanAnno(value="CITY",table="a")
 	private String city;
 	@BeanAnno(value="TOWN",table="a")
 	private String town;
 	@BeanAnno(value="ADDRESS",table="a")
 	private String address;
 	@BeanAnno(value="user_phone",table="c")
 	private String userPhone;
 	@BeanAnno(value="address_en",table="a")
 	private String addressEn;
 	@BeanAnno(value="CUST_CODE",table="a")
 	private String custCode;
 	@BeanAnno(value="COMPANY_NAME_EN",table="a")
 	private String companyNameEn;
 	@BeanAnno(value="MEMO",table="a")
 	private String memo;
 	@BeanAnno(value="BUSI_CODE",table="a")
 	private String busiCode;

 	public void setIsFreeze(int isFreeze){
 		 this.isFreeze=isFreeze;
 	}
 	public int getIsFreeze(){
 		 return this.isFreeze;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	@CheckAnno(len = 50) 
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCreditAmount(double creditAmount){
 		 this.creditAmount=creditAmount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getCreditAmount(){
 		 return this.creditAmount;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	@CheckAnno(len = 50) 
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	@CheckAnno(len = 500) 
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	@CheckAnno(len = 30) 
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	@CheckAnno(len = 30) 
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	@CheckAnno(len = 30) 
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	@CheckAnno(len = 30) 
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	@CheckAnno(len = 500) 
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	@CharacterVaild(len = 11) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	@CheckAnno(len = 11) 
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setAddressEn(String addressEn){
 		 this.addressEn=addressEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getAddressEn(){
 		 return this.addressEn;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	@CheckAnno(len = 50) 
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	@CheckAnno(len = 2000) 
 	public String getMemo(){
 		 return this.memo;
 	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
 
 	 
}