package com.sgs.ecom.order.dto.detail;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class OrderInvoiceDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select TAX_NO,REGISTER_PHONE,BANK_NUMBER,DELIVERY_NAME,STATE_DATE,USER_ID,DELIVERY_TOWN,STATE,ORDER_NO,DELIVERY_PROVINCE,DETAIL_NO,INVOICE_ID,REGISTER_ADDR,CREATE_DATE,INVOICE_TYPE,DELIVERY_ADDR,DELIVERY_CITY,ID,INVOICE_TITLE,BANK_ADDR,DELIVERY_PHONE from ORDER_INVOICE"; 
 
 
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="TAX_NO", getName="getTaxNo", setName="setTaxNo")
 	private String taxNo;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="REGISTER_PHONE", getName="getRegisterPhone", setName="setRegisterPhone")
 	private String registerPhone;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="BANK_NUMBER", getName="getBankNumber", setName="setBankNumber")
 	private String bankNumber;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DELIVERY_NAME", getName="getDeliveryName", setName="setDeliveryName")
 	private String deliveryName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private Long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DELIVERY_TOWN", getName="getDeliveryTown", setName="setDeliveryTown")
 	private String deliveryTown;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DELIVERY_PROVINCE", getName="getDeliveryProvince", setName="setDeliveryProvince")
 	private String deliveryProvince;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DETAIL_NO", getName="getDetailNo", setName="setDetailNo")
 	private String detailNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="INVOICE_ID", getName="getInvoiceId", setName="setInvoiceId")
 	private Long invoiceId;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="REGISTER_ADDR", getName="getRegisterAddr", setName="setRegisterAddr")
 	private String registerAddr;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="INVOICE_TYPE", getName="getInvoiceType", setName="setInvoiceType")
 	private int invoiceType;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DELIVERY_ADDR", getName="getDeliveryAddr", setName="setDeliveryAddr")
 	private String deliveryAddr;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DELIVERY_CITY", getName="getDeliveryCity", setName="setDeliveryCity")
 	private String deliveryCity;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private long id;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="INVOICE_TITLE", getName="getInvoiceTitle", setName="setInvoiceTitle")
 	private String invoiceTitle;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="BANK_ADDR", getName="getBankAddr", setName="setBankAddr")
 	private String bankAddr;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DELIVERY_PHONE", getName="getDeliveryPhone", setName="setDeliveryPhone")
 	private String deliveryPhone;

	@ApiAnno(groups={Default.class})
	private String backImg;
	@ApiAnno(groups={Default.class})
	private String frontImg;
	@ApiAnno(groups={Default.class})
	private String deliverCompany;
	@ApiAnno(groups={Default.class})
	private String deliverMail;
	@ApiAnno(groups={Default.class})
	private String linkPhone;
	@ApiAnno(groups={Default.class})
	private String linkPerson;
	@ApiAnno(groups={Default.class})
	private String linkEmail;
	@ApiAnno(groups={Default.class})
	private String bossTag;
	@ApiAnno(groups={Default.class})
	private List<OrderAttachmentDTO> otherFileList=new ArrayList<>();
	private String monthCompanyName;
	private String monthCompanyNameEn;
	private String monthAddress;
	private String monthAddressEn;
	@ApiAnno(groups={Default.class})
	private int isForeign;



	@ApiAnno(groups={Default.class})
	private String bankName;
	@ApiAnno(groups={Default.class})
	private String regPhone;
	@ApiAnno(groups={Default.class})
	private String regAddress;

	@ApiAnno(groups={Default.class})
	private String province;
	@ApiAnno(groups={Default.class})
	private String town;
	@ApiAnno(groups={Default.class})
	private String city;
	@ApiAnno(groups={Default.class})
	private String companyAddress;
	@ApiAnno(groups={Default.class})
	private String userName;
	@ApiAnno(groups={Default.class})
	private String userPhone;
	@ApiAnno(groups={Default.class})
	private Long addressId;

	@ApiAnno(groups={Default.class})
	private int bossFlg;
	@ApiAnno(groups={Default.class})
	private String country;

	@ApiAnno(groups={Default.class})
	private String postCode;

	@ApiAnno(groups={Default.class})
	private String contact;

	@ApiAnno(groups={Default.class})
	private String foreignCity;

	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String payerName;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String payerPhone;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String payerEmail;

	public String getForeignCity() {
		return foreignCity;
	}

	public void setForeignCity(String foreignCity) {
		this.foreignCity = foreignCity;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getRegPhone() {
		return regPhone;
	}

	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}

	public String getRegAddress() {
		return regAddress;
	}

	public void setRegAddress(String regAddress) {
		this.regAddress = regAddress;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCompanyAddress() {
		return companyAddress;
	}

	public void setCompanyAddress(String companyAddress) {
		this.companyAddress = companyAddress;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public Long getAddressId() {
		return addressId;
	}

	public void setAddressId(Long addressId) {
		this.addressId = addressId;
	}

	public int getBossFlg() {
		return bossFlg;
	}

	public void setBossFlg(int bossFlg) {
		this.bossFlg = bossFlg;
	}

	public String getBossTag() {
		return bossTag;
	}

	public void setBossTag(String bossTag) {
		this.bossTag = bossTag;
	}

	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}
 
 	 
 	public void setRegisterPhone(String registerPhone){
 		 this.registerPhone=registerPhone;
 	}
 	public String getRegisterPhone(){
 		 return this.registerPhone;
 	}
 
 	 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 
 	public void setDeliveryName(String deliveryName){
 		 this.deliveryName=deliveryName;
 	}
 	public String getDeliveryName(){
 		 return this.deliveryName;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class)
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(Long userId){
 		 this.userId=userId;
 	}
 	public Long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setDeliveryTown(String deliveryTown){
 		 this.deliveryTown=deliveryTown;
 	}
 	public String getDeliveryTown(){
 		 return this.deliveryTown;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setDeliveryProvince(String deliveryProvince){
 		 this.deliveryProvince=deliveryProvince;
 	}
 	public String getDeliveryProvince(){
 		 return this.deliveryProvince;
 	}
 
 	 
 	public void setDetailNo(String detailNo){
 		 this.detailNo=detailNo;
 	}
 	public String getDetailNo(){
 		 return this.detailNo;
 	}
 
 	 
 	public void setInvoiceId(Long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public Long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setRegisterAddr(String registerAddr){
 		 this.registerAddr=registerAddr;
 	}
 	public String getRegisterAddr(){
 		 return this.registerAddr;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class)
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 
 	public void setDeliveryAddr(String deliveryAddr){
 		 this.deliveryAddr=deliveryAddr;
 	}
 	public String getDeliveryAddr(){
 		 return this.deliveryAddr;
 	}
 
 	 
 	public void setDeliveryCity(String deliveryCity){
 		 this.deliveryCity=deliveryCity;
 	}
 	public String getDeliveryCity(){
 		 return this.deliveryCity;
 	}
 
 	 
 	public void setId(long id){
 		 this.id=id;
 	}
 	public long getId(){
 		 return this.id;
 	}
 
 	 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setBankAddr(String bankAddr){
 		 this.bankAddr=bankAddr;
 	}
 	public String getBankAddr(){
 		 return this.bankAddr;
 	}
 
 	 
 	public void setDeliveryPhone(String deliveryPhone){
 		 this.deliveryPhone=deliveryPhone;
 	}
 	public String getDeliveryPhone(){
 		 return this.deliveryPhone;
 	}

	public String getBackImg() {
		return backImg;
	}

	public void setBackImg(String backImg) {
		this.backImg = backImg;
	}

	public String getFrontImg() {
		return frontImg;
	}

	public void setFrontImg(String frontImg) {
		this.frontImg = frontImg;
	}

	public String getDeliverCompany() {
		return deliverCompany;
	}

	public void setDeliverCompany(String deliverCompany) {
		this.deliverCompany = deliverCompany;
	}

	public String getDeliverMail() {
		return deliverMail;
	}

	public void setDeliverMail(String deliverMail) {
		this.deliverMail = deliverMail;
	}

	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}

	public String getLinkPerson() {
		return linkPerson;
	}

	public void setLinkPerson(String linkPerson) {
		this.linkPerson = linkPerson;
	}

	public String getLinkEmail() {
		return linkEmail;
	}

	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail;
	}

	public List<OrderAttachmentDTO> getOtherFileList() {
		return otherFileList;
	}

	public void setOtherFileList(List<OrderAttachmentDTO> otherFileList) {
		this.otherFileList = otherFileList;
	}

	public String getMonthCompanyName() {
		return monthCompanyName;
	}

	public void setMonthCompanyName(String monthCompanyName) {
		this.monthCompanyName = monthCompanyName;
	}

	public String getMonthCompanyNameEn() {
		return monthCompanyNameEn;
	}

	public void setMonthCompanyNameEn(String monthCompanyNameEn) {
		this.monthCompanyNameEn = monthCompanyNameEn;
	}

	public String getMonthAddress() {
		return monthAddress;
	}

	public void setMonthAddress(String monthAddress) {
		this.monthAddress = monthAddress;
	}

	public String getMonthAddressEn() {
		return monthAddressEn;
	}

	public void setMonthAddressEn(String monthAddressEn) {
		this.monthAddressEn = monthAddressEn;
	}

	public int getIsForeign() {
		return isForeign;
	}

	public void setIsForeign(int isForeign) {
		this.isForeign = isForeign;
	}



	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	public String getPayerPhone() {
		return payerPhone;
	}

	public void setPayerPhone(String payerPhone) {
		this.payerPhone = payerPhone;
	}

	public String getPayerEmail() {
		return payerEmail;
	}

	public void setPayerEmail(String payerEmail) {
		this.payerEmail = payerEmail;
	}
}