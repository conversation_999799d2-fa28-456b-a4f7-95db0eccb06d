package com.sgs.ecom.order.entity.order;

import javax.persistence.Id;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderShipping {
    /**
     * 主键ID
     */
    @Id
    private Long shippingId;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 到港日期
     */
    private String arrivalDate;
    /**
     * 装船日期
     */
    private String shipmentDate;
    /**
     * 提单签发日期
     */
    private String issueDate;
    /**
     * 提单签发地点
     */
    private String issuePlace;
    /**
     * 目的港船代
     */
    private String shippingAgent;
    /**
     * 提单号码
     */
    private String ladingNo;
    /**
     * 船名
     */
    private String vesselName;
    /**
     * 航次
     */
    private String vesselNumber;
    /**
     * 船公司
     */
    private String shippingLine;
    /**
     * 装运国
     */
    private String originalCountry;
    /**
     * 装运港
     */
    private String loadingPort;
    /**
     * 卸货港
     */
    private String dischargePort;
    /**
     * 最终目的国家
     */
    private String deliveryCountry;
    /**
     * 最终目的城市
     */
    private String deliveryCity;
    /**
     * 状态
     */
    private Integer state;
    /**
     * 创建时间
     */
    private String createDate;
    /**
     * 状态更新时间
     */
    private String stateDate;
    /**
     * 支付方式
     */
    private String shippingPaymentMethod;
    /**
     * 原产国
     */
    private String countryOfOrigin;
}
