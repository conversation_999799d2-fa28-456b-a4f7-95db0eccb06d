package com.sgs.ecom.order.domain.order;

import com.sgs.ecom.order.dto.mq.MqLeadsSyncDTO;
import com.sgs.ecom.order.entity.order.OrderPlatformData;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;

public class OrderPlatformDataDO extends OrderPlatformData {


    public OrderPlatformData getEntityByLeadsMatch(MqLeadsSyncDTO mqLeadsSyncDTO,String createDate,String stateDate,String orderNo){
        OrderPlatformData orderPlatformData=getEntityByLeads(mqLeadsSyncDTO,createDate,stateDate);
        orderPlatformData.setOrderNo(orderNo);
        orderPlatformData.setMatchDate(UseDateUtil.getDateString(new Date()));
        orderPlatformData.setState(1);
        return orderPlatformData;
    }


    public OrderPlatformData getEntityByLeads(MqLeadsSyncDTO mqLeadsSyncDTO,String createDate,String stateDate){
        OrderPlatformData orderPlatformData=new OrderPlatformData();
        orderPlatformData.setPlatform("LEADS");
        orderPlatformData.setPlatformOrder(mqLeadsSyncDTO.getTicketId());
        orderPlatformData.setUserPhone(mqLeadsSyncDTO.getPersonPhone());
        orderPlatformData.setUserEmail(mqLeadsSyncDTO.getPersonEmail());
        orderPlatformData.setCreateDate(createDate);
        orderPlatformData.setStateDate(stateDate);
        orderPlatformData.setState(0);
        orderPlatformData.setEmailCode(mqLeadsSyncDTO.getEmailCode());
        return orderPlatformData;
    }


}
