package com.sgs.ecom.order.dto; 
 
import java.sql.Timestamp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.annotation.UpdateAnno;
import com.platform.util.json.TimeFormatSerializer; 

public class TicOrderDTO{
 
 	public static final String CREATE_SQL = "select order_no,status_wait_payment,confirm_app_by,fileName,status_check_app_creat_time,SkuAttr,user_name,status_cancel,contact_tel,status_refund,cancel_flg,change_status,status_return_creat_time,company_address,operator,contact_email,Remark,is_ticket,status_check_app,status_payment_document_creatTime,store_name,company_name_cn,company_name_en,status_cancel_app_creat_time,status_finish_app_creat_time,store_id,status_submit_app_creat_time,contact_name,total_price,payment_status,is_bill,status_return,Quantity,ProductID,product_name,status_service_app_creatTime,status_payment_document,is_month_pay,status_refund_creat_time,status_finish,user_id,status_submit_app,Price,created_date,status_wait_payment_creat_time,status_service,status from VW_TIC_ORDER"; 
 

 	@IsNullAnno(serviceName={"ticketByOrder","payByOrder","sendReportMail","saveReportSend","qryTicOrderDtl"})
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_WAIT_PAYMENT", getName="getStatusWaitPayment", setName="setStatusWaitPayment")
 	private String statusWaitPayment;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="CONFIRM_APP_BY", getName="getConfirmAppBy", setName="setConfirmAppBy")
 	private String confirmAppBy;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="FILE_NAME", getName="getFileName", setName="setFileName")
 	private String fileName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_CHECK_APP_CREAT_TIME", getName="getStatusCheckAppCreatTime", setName="setStatusCheckAppCreatTime")
 	private Timestamp statusCheckAppCreatTime;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="SKU_ATTR", getName="getSkuAttr", setName="setSkuAttr")
 	private String skuAttr;
 	@ApiAnno(serviceName={"qryTicOrderDtl","qryOrder"})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_CANCEL", getName="getStatusCancel", setName="setStatusCancel")
 	private String statusCancel;
 	@ApiAnno(serviceName={"qryTicOrderDtl","qryOrder"})
 	@BeanAnno(value="CONTACT_TEL", getName="getContactTel", setName="setContactTel")
 	private String contactTel;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_REFUND", getName="getStatusRefund", setName="setStatusRefund")
 	private String statusRefund;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CANCEL_FLG", getName="getCancelFlg", setName="setCancelFlg")
 	private String cancelFlg;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CHANGE_STATUS", getName="getChangeStatus", setName="setChangeStatus")
 	private String changeStatus;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_RETURN_CREAT_TIME", getName="getStatusReturnCreatTime", setName="setStatusReturnCreatTime")
 	private Timestamp statusReturnCreatTime;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="COMPANY_ADDRESS", getName="getCompanyAddress", setName="setCompanyAddress")
 	private String companyAddress;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="OPERATOR", getName="getOperator", setName="setOperator")
 	private String operator;
 	@ApiAnno(serviceName={"sendReportMail","qryTicOrderDtl","qryOrder"})
 	@BeanAnno(value="CONTACT_EMAIL", getName="getContactEmail", setName="setContactEmail")
 	private String contactEmail;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="REMARK", getName="getRemark", setName="setRemark")
 	private String remark;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="IS_TICKET", getName="getIsTicket", setName="setIsTicket")
 	private int isTicket;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_CHECK_APP", getName="getStatusCheckApp", setName="setStatusCheckApp")
 	private String statusCheckApp;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_PAYMENT_DOCUMENT_CREATTIME", getName="getStatusPaymentDocumentCreattime", setName="setStatusPaymentDocumentCreattime")
 	private Timestamp statusPaymentDocumentCreattime;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="STORE_NAME", getName="getStoreName", setName="setStoreName")
 	private String storeName;
 	@ApiAnno(serviceName={"qryTicOrderDtl","qryOrder"})
 	@BeanAnno(value="COMPANY_NAME_CN", getName="getCompanyNameCn", setName="setCompanyNameCn")
 	private String companyNameCn;
 	@ApiAnno(serviceName={"qryTicOrderDtl","qryOrder"})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_CANCEL_APP_CREAT_TIME", getName="getStatusCancelAppCreatTime", setName="setStatusCancelAppCreatTime")
 	private Timestamp statusCancelAppCreatTime;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_FINISH_APP_CREAT_TIME", getName="getStatusFinishAppCreatTime", setName="setStatusFinishAppCreatTime")
 	private Timestamp statusFinishAppCreatTime;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STORE_ID", getName="getStoreId", setName="setStoreId")
 	private String storeId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_SUBMIT_APP_CREAT_TIME", getName="getStatusSubmitAppCreatTime", setName="setStatusSubmitAppCreatTime")
 	private Timestamp statusSubmitAppCreatTime;
 	@ApiAnno(serviceName={"qryTicOrderDtl","qryOrder"})
 	@BeanAnno(value="CONTACT_NAME", getName="getContactName", setName="setContactName")
 	private String contactName;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="TOTAL_PRICE", getName="getTotalPrice", setName="setTotalPrice")
 	private double totalPrice;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="PAYMENT_STATUS", getName="getPaymentStatus", setName="setPaymentStatus")
 	private String paymentStatus;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="IS_BILL", getName="getIsBill", setName="setIsBill")
 	private int isBill;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_RETURN", getName="getStatusReturn", setName="setStatusReturn")
 	private String statusReturn;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="QUANTITY", getName="getQuantity", setName="setQuantity")
 	private long quantity;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="PRODUCT_ID", getName="getProductId", setName="setProductId")
 	private String productId;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="PRODUCT_NAME", getName="getProductName", setName="setProductName")
 	private String productName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_SERVICE_APP_CREATTIME", getName="getStatusServiceAppCreattime", setName="setStatusServiceAppCreattime")
 	private Timestamp statusServiceAppCreattime;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_PAYMENT_DOCUMENT", getName="getStatusPaymentDocument", setName="setStatusPaymentDocument")
 	private String statusPaymentDocument;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="IS_MONTH_PAY", getName="getIsMonthPay", setName="setIsMonthPay")
 	private int isMonthPay;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_REFUND_CREAT_TIME", getName="getStatusRefundCreatTime", setName="setStatusRefundCreatTime")
 	private Timestamp statusRefundCreatTime;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_FINISH", getName="getStatusFinish", setName="setStatusFinish")
 	private String statusFinish;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private String userId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_SUBMIT_APP", getName="getStatusSubmitApp", setName="setStatusSubmitApp")
 	private String statusSubmitApp;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="PRICE", getName="getPrice", setName="setPrice")
 	private double price;
 	@IsNullAnno(serviceName={"expOrder"})
 	@ApiAnno(serviceName={"qryTicOrderDtl","qryOrder"})
 	@BeanAnno(value="CREATED_DATE", getName="getCreatedDate", setName="setCreatedDate")
 	private Timestamp createdDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_WAIT_PAYMENT_CREAT_TIME", getName="getStatusWaitPaymentCreatTime", setName="setStatusWaitPaymentCreatTime")
 	private Timestamp statusWaitPaymentCreatTime;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS_SERVICE", getName="getStatusService", setName="setStatusService")
 	private String statusService;
 	@ApiAnno(serviceName={"qryOrder"})
 	@BeanAnno(value="STATUS", getName="getStatus", setName="setStatus")
 	private String status;
 	@UpdateAnno(serviceName={"saveReportSend"})
 	@ApiAnno(serviceName={"sendReportMail","qryTicOrderDtl"})
 	@BeanAnno(value="report_send_cc", getName="getReportSendCc", setName="setReportSendCc")
 	private String reportSendCc;
 	@IsNullAnno(serviceName={"expOrder"})
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="HIS_STATE", getName="getHisState", setName="setHisState")
 	private String hisState;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="IS_CANCEL", getName="getIsCancel", setName="setIsCancel")
 	private String isCancel;

 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setStatusWaitPayment(String statusWaitPayment){
 		 this.statusWaitPayment=statusWaitPayment;
 	}
 	public String getStatusWaitPayment(){
 		 return this.statusWaitPayment;
 	}
 
 	 
 	public void setConfirmAppBy(String confirmAppBy){
 		 this.confirmAppBy=confirmAppBy;
 	}
 	public String getConfirmAppBy(){
 		 return this.confirmAppBy;
 	}
 
 	 
 	public void setFilename(String fileName){
 		 this.fileName=fileName;
 	}
 	public String getFileName(){
 		 return this.fileName;
 	}
 
 	 
 	public void setStatusCheckAppCreatTime(Timestamp statusCheckAppCreatTime){
 		 this.statusCheckAppCreatTime=statusCheckAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusCheckAppCreatTime(){
 		 return this.statusCheckAppCreatTime;
 	}
 
 	 
 	public void setSkuattr(String skuAttr){
 		 this.skuAttr=skuAttr;
 	}
 	public String getSkuAttr(){
 		 return this.skuAttr;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setStatusCancel(String statusCancel){
 		 this.statusCancel=statusCancel;
 	}
 	public String getStatusCancel(){
 		 return this.statusCancel;
 	}
 
 	 
 	public void setContactTel(String contactTel){
 		 this.contactTel=contactTel;
 	}
 	public String getContactTel(){
 		 return this.contactTel;
 	}
 
 	 
 	public void setStatusRefund(String statusRefund){
 		 this.statusRefund=statusRefund;
 	}
 	public String getStatusRefund(){
 		 return this.statusRefund;
 	}
 
 	 
 	public void setCancelFlg(String cancelFlg){
 		 this.cancelFlg=cancelFlg;
 	}
 	public String getCancelFlg(){
 		 return this.cancelFlg;
 	}
 
 	 
 	public void setChangeStatus(String changeStatus){
 		 this.changeStatus=changeStatus;
 	}
 	public String getChangeStatus(){
 		 return this.changeStatus;
 	}
 
 	 
 	public void setStatusReturnCreatTime(Timestamp statusReturnCreatTime){
 		 this.statusReturnCreatTime=statusReturnCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusReturnCreatTime(){
 		 return this.statusReturnCreatTime;
 	}
 
 	 
 	public void setCompanyAddress(String companyAddress){
 		 this.companyAddress=companyAddress;
 	}
 	public String getCompanyAddress(){
 		 return this.companyAddress;
 	}
 
 	 
 	public void setOperator(String operator){
 		 this.operator=operator;
 	}
 	public String getOperator(){
 		 return this.operator;
 	}
 
 	 
 	public void setContactEmail(String contactEmail){
 		 this.contactEmail=contactEmail;
 	}
 	public String getContactEmail(){
 		 return this.contactEmail;
 	}
 
 	 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
 	public void setIsTicket(int isTicket){
 		 this.isTicket=isTicket;
 	}
 	public int getIsTicket(){
 		 return this.isTicket;
 	}
 
 	 
 	public void setStatusCheckApp(String statusCheckApp){
 		 this.statusCheckApp=statusCheckApp;
 	}
 	public String getStatusCheckApp(){
 		 return this.statusCheckApp;
 	}
 
 	 
 	public void setStatusPaymentDocumentCreattime(Timestamp statusPaymentDocumentCreattime){
 		 this.statusPaymentDocumentCreattime=statusPaymentDocumentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusPaymentDocumentCreattime(){
 		 return this.statusPaymentDocumentCreattime;
 	}
 
 	 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	public void setCompanyNameCn(String companyNameCn){
 		 this.companyNameCn=companyNameCn;
 	}
 	public String getCompanyNameCn(){
 		 return this.companyNameCn;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
 	public void setStatusCancelAppCreatTime(Timestamp statusCancelAppCreatTime){
 		 this.statusCancelAppCreatTime=statusCancelAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusCancelAppCreatTime(){
 		 return this.statusCancelAppCreatTime;
 	}
 
 	 
 	public void setStatusFinishAppCreatTime(Timestamp statusFinishAppCreatTime){
 		 this.statusFinishAppCreatTime=statusFinishAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusFinishAppCreatTime(){
 		 return this.statusFinishAppCreatTime;
 	}
 
 	 
 	public void setStoreId(String storeId){
 		 this.storeId=storeId;
 	}
 	public String getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	public void setStatusSubmitAppCreatTime(Timestamp statusSubmitAppCreatTime){
 		 this.statusSubmitAppCreatTime=statusSubmitAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusSubmitAppCreatTime(){
 		 return this.statusSubmitAppCreatTime;
 	}
 
 	 
 	public void setContactName(String contactName){
 		 this.contactName=contactName;
 	}
 	public String getContactName(){
 		 return this.contactName;
 	}
 
 	 
 	public void setTotalPrice(double totalPrice){
 		 this.totalPrice=totalPrice;
 	}
 	public double getTotalPrice(){
 		 return this.totalPrice;
 	}
 
 	 
 	public void setPaymentStatus(String paymentStatus){
 		 this.paymentStatus=paymentStatus;
 	}
 	public String getPaymentStatus(){
 		 return this.paymentStatus;
 	}
 
 	 
 	public void setIsBill(int isBill){
 		 this.isBill=isBill;
 	}
 	public int getIsBill(){
 		 return this.isBill;
 	}
 
 	 
 	public void setStatusReturn(String statusReturn){
 		 this.statusReturn=statusReturn;
 	}
 	public String getStatusReturn(){
 		 return this.statusReturn;
 	}
 
 	 
 	public void setQuantity(long quantity){
 		 this.quantity=quantity;
 	}
 	public long getQuantity(){
 		 return this.quantity;
 	}
 
 	 
 	public void setProductid(String productId){
 		 this.productId=productId;
 	}
 	public String getProductId(){
 		 return this.productId;
 	}
 
 	 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 
 	 
 	public void setStatusServiceAppCreattime(Timestamp statusServiceAppCreattime){
 		 this.statusServiceAppCreattime=statusServiceAppCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusServiceAppCreattime(){
 		 return this.statusServiceAppCreattime;
 	}
 
 	 
 	public void setStatusPaymentDocument(String statusPaymentDocument){
 		 this.statusPaymentDocument=statusPaymentDocument;
 	}
 	public String getStatusPaymentDocument(){
 		 return this.statusPaymentDocument;
 	}
 
 	 
 	public void setIsMonthPay(int isMonthPay){
 		 this.isMonthPay=isMonthPay;
 	}
 	public int getIsMonthPay(){
 		 return this.isMonthPay;
 	}
 
 	 
 	public void setStatusRefundCreatTime(Timestamp statusRefundCreatTime){
 		 this.statusRefundCreatTime=statusRefundCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusRefundCreatTime(){
 		 return this.statusRefundCreatTime;
 	}
 
 	 
 	public void setStatusFinish(String statusFinish){
 		 this.statusFinish=statusFinish;
 	}
 	public String getStatusFinish(){
 		 return this.statusFinish;
 	}
 
 	 
 	public void setUserId(String userId){
 		 this.userId=userId;
 	}
 	public String getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setStatusSubmitApp(String statusSubmitApp){
 		 this.statusSubmitApp=statusSubmitApp;
 	}
 	public String getStatusSubmitApp(){
 		 return this.statusSubmitApp;
 	}
 
 	 
 	public void setPrice(double price){
 		 this.price=price;
 	}
 	public double getPrice(){
 		 return this.price;
 	}
 
 	 
 	public void setCreatedDate(Timestamp createdDate){
 		 this.createdDate=createdDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreatedDate(){
 		 return this.createdDate;
 	}
 
 	 
 	public void setStatusWaitPaymentCreatTime(Timestamp statusWaitPaymentCreatTime){
 		 this.statusWaitPaymentCreatTime=statusWaitPaymentCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusWaitPaymentCreatTime(){
 		 return this.statusWaitPaymentCreatTime;
 	}
 
 	 
 	public void setStatusService(String statusService){
 		 this.statusService=statusService;
 	}
 	public String getStatusService(){
 		 return this.statusService;
 	}
 
 	 
 	public void setStatus(String status){
 		 this.status=status;
 	}
 	public String getStatus(){
 		 return this.status;
 	}
	public String getReportSendCc() {
		return reportSendCc;
	}
	public void setReportSendCc(String reportSendCc) {
		this.reportSendCc = reportSendCc;
	}
	public String getHisState() {
		return hisState;
	}
	public void setHisState(String hisState) {
		this.hisState = hisState;
	}
	public String getIsCancel() {
		return isCancel;
	}
	public void setIsCancel(String isCancel) {
		this.isCancel = isCancel;
	}
 
 	 
}