package com.sgs.ecom.order.domain.order;


import com.sgs.ecom.order.util.collection.NumUtil;

import java.math.BigDecimal;
import java.util.Map;

public class OrderPayDO {

    public static  String getOffPayAmount(Map<String,Integer> map,String orderNo){
        Integer amount=map.getOrDefault(orderNo+"310000",0);
        if(amount!=null&&amount>0){
            return  NumUtil.toFormat(new BigDecimal(amount.doubleValue()/100));
        }
        amount=map.getOrDefault(orderNo+"320000",0);
        if(amount!=null&&amount>0){
            return  NumUtil.toFormat(new BigDecimal(amount.doubleValue()/100));
        }

        return "";
    }

    public static  String getOnlinePayAmount(Map<String,Integer> map,String orderNo){
        Integer amount=map.getOrDefault(orderNo+"100000",0);
        if(amount!=null&&amount>0){
            return  NumUtil.toFormat(new BigDecimal(amount.doubleValue()/100));
        }
        amount=map.getOrDefault(orderNo+"100001",0);
        if(amount!=null&&amount>0){
            return  NumUtil.toFormat(new BigDecimal(amount.doubleValue()/100));
        }

        return "";
    }
}
