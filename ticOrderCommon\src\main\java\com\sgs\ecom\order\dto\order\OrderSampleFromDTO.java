package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.bbc.TicSampleJOSNDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.sys.SysEnumConfigDTO;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class OrderSampleFromDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,GROUP_NO,STATE_DATE,SAMPLE_NO,STATE,ORDER_NO,SAMPLE_KEY_NAME,FORM_ID,SAMPLE_VALUE,SAMPLE_KEY from ORDER_SAMPLE_FROM"; 
 
 

 	@ApiAnno(groups={Default.class,BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="GROUP_NO", getName="getGroupNo", setName="setGroupNo")
 	private String groupNo;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="SAMPLE_NO", getName="getSampleNo", setName="setSampleNo")
 	private String sampleNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderQryList.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="SAMPLE_KEY_NAME", getName="getSampleKeyName", setName="setSampleKeyName")
 	private String sampleKeyName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="FORM_ID", getName="getFormId", setName="setFormId")
 	private long formId;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderQryList.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="SAMPLE_VALUE", getName="getSampleValue", setName="setSampleValue")
 	private String sampleValue;
 	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderQryList.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="SAMPLE_KEY", getName="getSampleKey", setName="setSampleKey")
 	private String sampleKey;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
	private int sortShow;

	private TicSampleJOSNDTO ticSampleJOSNDTO = new TicSampleJOSNDTO();
	private int ticSampleFlg;



	private Integer isReportShow;

	@ApiAnno(groups={Default.class})
	private String  sampleExplain;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String  remark;
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private List<SysEnumConfigDTO>  enums = new ArrayList<>();
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private List<SysEnumConfigDTO>  remarkEnums = new ArrayList<>();

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String enumConfig;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String remarkEnumConfig;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
	private String sampleValueRemark;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int type;

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String keys;//值为json的枚举选项

	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
	private String  lua;
	@ApiAnno(groups={Default.class})
	private String showLua;
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String showAttr;
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String regexRule;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private Integer  isMust;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String  sampleGroup;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int fillLen;
	@ApiAnno(groups={Default.class})
	private ShowAttrDTO showAttrDTO = new ShowAttrDTO();
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private int rowNum;
	@JsonProperty(access = JsonProperty.Access.READ_ONLY)
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String attrRules;
	@ApiAnno(groups={Default.class,BaseOrderFilter.OiqFormInfo.class})
	private String fillNotice;

	public ShowAttrDTO getShowAttrDTO() {
		return showAttrDTO;
	}

	public void setShowAttrDTO(ShowAttrDTO showAttrDTO) {
		this.showAttrDTO = showAttrDTO;
	}

	public String getShowAttr() {
		return showAttr;
	}

	public void setShowAttr(String showAttr) {
		this.showAttr = showAttr;
	}

	public String getShowLua() {
		return showLua;
	}

	public void setShowLua(String showLua) {
		this.showLua = showLua;
	}

	public String getLua() {
		return lua;
	}

	public void setLua(String lua) {
		this.lua = lua;
	}

	public String getKeys() {
		return keys;
	}

	public void setKeys(String keys) {
		this.keys = keys;
	}

	public List<SysEnumConfigDTO> getEnums() {
		return enums;
	}

	public void setEnums(List<SysEnumConfigDTO> enums) {
		this.enums = enums;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}



	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@ApiAnno(groups={Default.class})
	private List<OrderAttachmentDTO> rstsSDSFileList=new ArrayList<>();//RSTS专属附件
	private List<OrderAttachmentDTO> orderAttachmentFileList =new ArrayList<>();//对应申请表信息字段的相关附件

	public List<OrderAttachmentDTO> getOrderAttachmentFileList() {
		return orderAttachmentFileList;
	}

	public void setOrderAttachmentFileList(List<OrderAttachmentDTO> orderAttachmentFileList) {
		this.orderAttachmentFileList = orderAttachmentFileList;
	}

	public List<OrderAttachmentDTO> getRstsSDSFileList() {
		return rstsSDSFileList;
	}

	public void setRstsSDSFileList(List<OrderAttachmentDTO> rstsSDSFileList) {
		this.rstsSDSFileList = rstsSDSFileList;
	}

	public Integer getIsReportShow() {
		return isReportShow;
	}

	public void setIsReportShow(Integer isReportShow) {
		this.isReportShow = isReportShow;
	}

	public void setGroupNo(String groupNo){
 		 this.groupNo=groupNo;
 	}
 	public String getGroupNo(){
 		 return this.groupNo;
 	}

 
 	 
 	public void setSampleNo(String sampleNo){
 		 this.sampleNo=sampleNo;
 	}
 	public String getSampleNo(){
 		 return this.sampleNo;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setSampleKeyName(String sampleKeyName){
 		 this.sampleKeyName=sampleKeyName;
 	}
 	public String getSampleKeyName(){
 		 return this.sampleKeyName;
 	}
 
 	 
 	public void setFormId(long formId){
 		 this.formId=formId;
 	}
 	public long getFormId(){
 		 return this.formId;
 	}
 
 	 
 	public void setSampleValue(String sampleValue){
 		 this.sampleValue=sampleValue;
 	}
 	public String getSampleValue(){
 		if(StringUtils.isBlank(sampleValue)){
			 return "";
		}
		 return sampleValue;
 	}
 
 	 
 	public void setSampleKey(String sampleKey){
 		 this.sampleKey=sampleKey;
 	}
 	public String getSampleKey(){
 		 return this.sampleKey;
 	}

	public int getSortShow() {
		return sortShow;
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}

	public TicSampleJOSNDTO getTicSampleJOSNDTO() {
		return ticSampleJOSNDTO;
	}

	public void setTicSampleJOSNDTO(TicSampleJOSNDTO ticSampleJOSNDTO) {
		this.ticSampleJOSNDTO = ticSampleJOSNDTO;
	}

	public int getTicSampleFlg() {
		return ticSampleFlg;
	}

	public void setTicSampleFlg(int ticSampleFlg) {
		this.ticSampleFlg = ticSampleFlg;
	}

	public String getSampleExplain() {
		return sampleExplain;
	}

	public void setSampleExplain(String sampleExplain) {
		this.sampleExplain = sampleExplain;
	}

	public Integer getIsMust() {
		return isMust;
	}

	public void setIsMust(Integer isMust) {
		this.isMust = isMust;
	}

	public String getSampleGroup() {
		return sampleGroup;
	}

	public void setSampleGroup(String sampleGroup) {
		this.sampleGroup = sampleGroup;
	}

	public int getFillLen() {
		return fillLen;
	}

	public void setFillLen(int fillLen) {
		this.fillLen = fillLen;
	}

	public List<SysEnumConfigDTO> getRemarkEnums() {
		return remarkEnums;
	}

	public void setRemarkEnums(List<SysEnumConfigDTO> remarkEnums) {
		this.remarkEnums = remarkEnums;
	}

	public String getRemarkEnumConfig() {
		return remarkEnumConfig;
	}

	public void setRemarkEnumConfig(String remarkEnumConfig) {
		this.remarkEnumConfig = remarkEnumConfig;
	}

	public String getSampleValueRemark() {
		return sampleValueRemark;
	}

	public void setSampleValueRemark(String sampleValueRemark) {
		this.sampleValueRemark = sampleValueRemark;
	}



	public int getRowNum() {
		return rowNum;
	}

	public void setRowNum(int rowNum) {
		this.rowNum = rowNum;
	}

	public String getRegexRule() {
		return regexRule;
	}

	public void setRegexRule(String regexRule) {
		this.regexRule = regexRule;
	}

	public String getAttrRules() {
		return attrRules;
	}

	public void setAttrRules(String attrRules) {
		this.attrRules = attrRules;
	}

	public String getFillNotice() {
		return fillNotice;
	}

	public void setFillNotice(String fillNotice) {
		this.fillNotice = fillNotice;
	}
}