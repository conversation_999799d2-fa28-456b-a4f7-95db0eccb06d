package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

@ExplainAnno(value="enum",name="订单状态")
public enum TicOrderState implements EnumMessage {

	CREATE("待付款", "M"), UPLOAD_PAY("上传支付凭证", "G"), PAY("待提交申请表", "A"),  
	APPLY("待审核", "Q"), AUDIT("服务中", "I"), FINISH("服务完成", "F"), APPLY_REFUND("申请表退回", "R"),
	UNAPPLY_REFUND("退款确认中", "R-B"), UNAPPLY_COMFIRM("退款确认中", "Q-B"), UNAUDIT_COMFIRM("退款确认中", "A-B"),
	CANCEL("已取消(全部)", "X"), CANCEL_REFUND("已取消(退款)", "XF"),
	ALL("全部订单", "-1");
	
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private TicOrderState(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (TicOrderState c : TicOrderState.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  

	@Override
	public String getKey() {
		return index;
	}

	@Override
	public String getValue() {
		return name;
	}  
}
