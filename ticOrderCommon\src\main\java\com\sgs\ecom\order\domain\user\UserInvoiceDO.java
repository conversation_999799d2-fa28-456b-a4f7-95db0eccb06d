package com.sgs.ecom.order.domain.user;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.user.UserInvoiceDTO;
import com.sgs.ecom.order.entity.user.UserInvoice;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;

public class UserInvoiceDO {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public UserInvoice getUserInvoiceByDTO(UserInvoiceDTO userInvoiceDTO, Long userId){
        UserInvoice userInvoice=new UserInvoice();
        baseCopy.copy(userInvoice,userInvoiceDTO);
        userInvoice.setState(1);
        userInvoice.setUserId(userId);
        String dateStr= UseDateUtil.getDateString(new Date());
        userInvoice.setCreateDate(dateStr);
        userInvoice.setStateDate(dateStr);
        return userInvoice;
    }
}
