package com.sgs.ecom.order.dto.order;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class CustomerIdDTO {
    @ApiAnno(groups={BaseQryFilter.QueryCustom.class})
    private Long userId;
    @ApiAnno(groups={BaseQryFilter.QueryCustom.class})
    private Long custId;
    private int phoneFlg;
    @ApiAnno(groups={BaseQryFilter.QueryCustom.class})
    private String customer;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getCustId() {
        return custId;
    }

    public void setCustId(Long custId) {
        this.custId = custId;
    }

    public String getCustomer() {
        return customer;
    }

    public void setCustomer(String customer) {
        this.customer = customer;
    }

    public int getPhoneFlg() {
        return phoneFlg;
    }

    public void setPhoneFlg(int phoneFlg) {
        this.phoneFlg = phoneFlg;
    }
}
