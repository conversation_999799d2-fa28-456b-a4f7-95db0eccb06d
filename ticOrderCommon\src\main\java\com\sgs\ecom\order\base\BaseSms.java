package com.sgs.ecom.order.base;

import com.sgs.ecom.order.dto.send.TicOtherSmsDTO;
import com.sgs.ecom.order.dto.send.mail.OiqOtherDTO;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;

import java.util.HashMap;
import java.util.Map;

public class BaseSms {
	private Long orderId;
	private String orderNo;
	private String sendPhone;
	private OiqSmsEnum oiqSmsEnum;
	private String buType;
	private String smsEnum;
	private Boolean useOrderId;
	private Map<String,Object> mapData=new HashMap<>();
	private TicOtherSmsDTO ticOtherSmsDTO;
	private OiqOtherDTO oiqOtherDTO;

	private Long userId;
	private Integer orderType;
	private Boolean isSub;//是否是子单
	public BaseSms() {
	}
	public BaseSms(Long orderId, OiqSmsEnum oiqSmsEnum){
		this.orderId = orderId;
		this.smsEnum = oiqSmsEnum.getIndex();
		this.useOrderId=true;
	}

	public BaseSms(Long orderId, OiqSmsEnum oiqSmsEnum, OiqOtherDTO oiqOtherDTO){
		this.orderId = orderId;
		this.smsEnum = oiqSmsEnum.getIndex();
		this.useOrderId=true;
		this.oiqOtherDTO=oiqOtherDTO;
	}
	public BaseSms(Long orderId, OiqSmsEnum oiqSmsEnum,TicOtherSmsDTO ticOtherSmsDTO){
		this.orderId = orderId;
		this.smsEnum = oiqSmsEnum.getIndex();
		this.useOrderId=true;
		this.ticOtherSmsDTO=ticOtherSmsDTO;
	}

	public BaseSms(Long orderId, OiqSmsEnum oiqSmsEnum, Boolean isSub) {
		this.orderId = orderId;
		this.smsEnum = oiqSmsEnum.getIndex();
		this.useOrderId=true;
		this.isSub = isSub;
	}

	public BaseSms(Long orderId, OiqSmsEnum oiqSmsEnum, TicOtherSmsDTO ticOtherSmsDTO, Boolean isSub) {
		this.orderId = orderId;
		this.smsEnum = oiqSmsEnum.getIndex();
		this.useOrderId=true;
		this.ticOtherSmsDTO=ticOtherSmsDTO;
		this.isSub = isSub;
	}

	public Boolean getSub() {
		return isSub;
	}

	public void setSub(Boolean sub) {
		isSub = sub;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public TicOtherSmsDTO getTicOtherSmsDTO() {
		return ticOtherSmsDTO;
	}

	public void setTicOtherSmsDTO(TicOtherSmsDTO ticOtherSmsDTO) {
		this.ticOtherSmsDTO = ticOtherSmsDTO;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getSmsEnum() {
		return smsEnum;
	}

	public void setSmsEnum(String smsEnum) {
		this.smsEnum = smsEnum;
	}

	public Boolean getUseOrderId() {
		return useOrderId;
	}

	public void setUseOrderId(Boolean useOrderId) {
		this.useOrderId = useOrderId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSendPhone() {
		return sendPhone;
	}

	public void setSendPhone(String sendPhone) {
		this.sendPhone = sendPhone;
	}

	public Map<String, Object> getMapData() {
		return mapData;
	}

	public void setMapData(Map<String, Object> mapData) {
		this.mapData = mapData;
	}

	public OiqSmsEnum getOiqSmsEnum() {
		return oiqSmsEnum;
	}

	public void setOiqSmsEnum(OiqSmsEnum oiqSmsEnum) {
		this.oiqSmsEnum = oiqSmsEnum;
	}

	public String getBuType() {
		return buType;
	}

	public void setBuType(String buType) {
		this.buType = buType;
	}

	public OiqOtherDTO getOiqOtherDTO() {
		return oiqOtherDTO;
	}

	public void setOiqOtherDTO(OiqOtherDTO oiqOtherDTO) {
		this.oiqOtherDTO = oiqOtherDTO;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
}
