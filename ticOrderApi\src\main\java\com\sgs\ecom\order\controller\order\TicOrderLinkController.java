package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.request.order.TicOrderLinkReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderLinkSV;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.tic/orderLink")
public class TicOrderLinkController extends ControllerUtil {

	@Autowired
	private IOrderLinkSV iOrderLinkSV;


	/**
	 * @Description: 学员信息查询
	 * @Author: bowen zhang
	 * @Date: 2022/8/30
	 * @param token:
	 * @param ticOrderLinkReq:
	 * @return: com.sgs.ecom.member.util.ResultBody
	 **/
	@AuthRequired(login = "SSO", sign = true)
	@RequestMapping(value = "qry", method = {RequestMethod.POST})
	public ResultBody qry(
			@RequestHeader(value = "accessToken") String token,
			@Validated(BaseBean.Query.class)
			@RequestBody TicOrderLinkReq ticOrderLinkReq)throws Exception {
		return ResultBody.success(iOrderLinkSV.qry(ticOrderLinkReq, getPersonInfo(token)));
	}


}
