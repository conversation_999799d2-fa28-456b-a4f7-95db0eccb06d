package com.sgs.ecom.order.dto.detail;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

public class OrderAttachmentDTO extends BaseOrderFilter{
    @ApiAnno(groups={Default.class, SampleAndReportList.class, OrderQryList.class})
    private String fileId;
    @ApiAnno(groups={Default.class, SampleAndReportList.class, OrderQryList.class})
    private String cloudId;
    @ApiAnno(groups={Default.class, SampleAndReportList.class, OrderQryList.class})
    private String fileName;
    @ApiAnno(groups={Default.class, SampleAndReportList.class, OrderQryList.class})
    private String attType;
    @ApiAnno(groups={Default.class, SampleAndReportList.class})
    private Long attachmentId;
    @ApiAnno(groups={Default.class, SampleAndReportList.class})
    private String fileUrl;
    @ApiAnno(groups={Default.class, SampleAndReportList.class})
    private String createDate;
    @ApiAnno(groups={Default.class, SampleAndReportList.class, OrderQryList.class})
    private String orderNo;
    @ApiAnno(groups={Default.class, SampleAndReportList.class})
    private String groupNo;
    @ApiAnno(groups={Default.class, SampleAndReportList.class, OrderQryList.class})
    private String reportNo;
    @ApiAnno(groups={ SampleAndReportList.class})
    private String sampleName;

    private String invoiceNumber;

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }
    @ApiAnno(groups={Default.class, SampleAndReportList.class, OrderQryList.class})
    private int  uploadType;

    public int getUploadType() {
        return uploadType;
    }

    public void setUploadType(int uploadType) {
        this.uploadType = uploadType;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getCloudId() {
        return cloudId;
    }

    public void setCloudId(String cloudId) {
        this.cloudId = cloudId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getAttachmentId() {
        return attachmentId;
    }

    public void setAttachmentId(Long attachmentId) {
        this.attachmentId = attachmentId;
    }

    public String getAttType() {
        return attType;
    }

    public void setAttType(String attType) {
        this.attType = attType;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getReportNo() {
        return reportNo;
    }

    public void setReportNo(String reportNo) {
        this.reportNo = reportNo;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }
}
