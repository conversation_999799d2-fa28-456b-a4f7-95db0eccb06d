package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="月结客户")
public class BOCustInfo{
 
 	public static final String SEQUENCE = "CUST_ID"; 
  
 	public static final String BO_SQL = "TB_CUST_INFO"; 
 
 	public static final String OWNER ="member";

 	public static final String IS_FREEZE="isFreeze";
 	public static final String CUST_ID="custId";
 	public static final String PROVICE="provice";
 	public static final String CREDIT_AMOUNT="creditAmount";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String MEMO="memo";
 	public static final String COMPANY_NAME="companyName";
 	public static final String CREATE_DATE="createDate";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String ADDRESS="address";
 	public static final String CUST_CODE="custCode";
 	public static final String ADDRESS_EN="addressEn";
 	public static final String COMPANY_NAME_EN="companyNameEn";
 	public static final String BUSI_CODE="busiCode";
 	public static final String LINK_EMAIL="linkEmail";

 	@BeanAnno("IS_FREEZE")
 	private int isFreeze;
 	@BeanAnno("CUST_ID")
 	private long custId;
 	@BeanAnno("PROVICE")
 	private String provice;
 	@BeanAnno("CREDIT_AMOUNT")
 	private double creditAmount;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("memo")
 	private String memo;
 	@BeanAnno("COMPANY_NAME")
 	private String companyName;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("COUNTRY")
 	private String country;
 	@BeanAnno("CITY")
 	private String city;
 	@BeanAnno("TOWN")
 	private String town;
 	@BeanAnno("ADDRESS")
 	private String address;
 	@BeanAnno("CUST_CODE")
 	private String custCode;
 	@BeanAnno("address_en")
 	private String addressEn;
 	@BeanAnno("COMPANY_NAME_EN")
 	private String companyNameEn;
 	@BeanAnno("BUSI_CODE")
 	private String busiCode;
 	@BeanAnno("LINK_EMAIL")
 	private String linkEmail;

 	public void setIsFreeze(int isFreeze){
 		 this.isFreeze=isFreeze;
 	}
 	public int getIsFreeze(){
 		 return this.isFreeze;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	@CheckAnno(len = 50) 
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCreditAmount(double creditAmount){
 		 this.creditAmount=creditAmount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getCreditAmount(){
 		 return this.creditAmount;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	@CheckAnno(len = 2000) 
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	@CheckAnno(len = 30) 
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	@CheckAnno(len = 30) 
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	@CheckAnno(len = 30) 
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	@CheckAnno(len = 500) 
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	@CheckAnno(len = 50) 
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setAddressEn(String addressEn){
 		 this.addressEn=addressEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getAddressEn(){
 		 return this.addressEn;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
	public String getLinkEmail() {
		return linkEmail;
	}
	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail;
	}
 
 	 
}