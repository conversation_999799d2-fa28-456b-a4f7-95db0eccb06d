package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOTicAttachment{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "TIC_Attachment"; 
 
 	public static final String OWNER ="bbc";

 	public static final String CREATEDBY="createdby";
 	public static final String FILENAME="filename";
 	public static final String SYSTEMID="systemid";
 	public static final String ATTTYPE="atttype";
 	public static final String ORDERID="orderid";
 	public static final String MODIFIEDBY="modifiedby";
 	public static final String MODIFIEDDATE="modifieddate";
 	public static final String CLOUDID="cloudid";
 	public static final String FILEURL="fileurl";
 	public static final String BUID="buid";
 	public static final String CREATEDDATE="createddate";
 	public static final String FILEID="fileid";
 	public static final String ID="id";

 	@BeanAnno("CreatedBy")
 	private String createdby;
 	@BeanAnno("FileName")
 	private String filename;
 	@BeanAnno("SystemID")
 	private String systemid;
 	@BeanAnno("AttType")
 	private String atttype;
 	@BeanAnno("OrderID")
 	private String orderid;
 	@BeanAnno("ModifiedBy")
 	private String modifiedby;
 	@BeanAnno("ModifiedDate")
 	private Timestamp modifieddate;
 	@BeanAnno("CloudID")
 	private String cloudid;
 	@BeanAnno("FileUrl")
 	private String fileurl;
 	@BeanAnno("BUID")
 	private String buid;
 	@BeanAnno("CreatedDate")
 	private Timestamp createddate;
 	@BeanAnno("FileID")
 	private String fileid;
 	@BeanAnno("ID")
 	private String id;

 	@CharacterVaild(len = 50) 
 	public void setCreatedby(String createdby){
 		 this.createdby=createdby;
 	}
 	public String getCreatedby(){
 		 return this.createdby;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setFilename(String filename){
 		 this.filename=filename;
 	}
 	public String getFilename(){
 		 return this.filename;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSystemid(String systemid){
 		 this.systemid=systemid;
 	}
 	public String getSystemid(){
 		 return this.systemid;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setAtttype(String atttype){
 		 this.atttype=atttype;
 	}
 	public String getAtttype(){
 		 return this.atttype;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderid(String orderid){
 		 this.orderid=orderid;
 	}
 	public String getOrderid(){
 		 return this.orderid;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setModifiedby(String modifiedby){
 		 this.modifiedby=modifiedby;
 	}
 	public String getModifiedby(){
 		 return this.modifiedby;
 	}
 
 	 
 	public void setModifieddate(Timestamp modifieddate){
 		 this.modifieddate=modifieddate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getModifieddate(){
 		 return this.modifieddate;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCloudid(String cloudid){
 		 this.cloudid=cloudid;
 	}
 	public String getCloudid(){
 		 return this.cloudid;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setFileurl(String fileurl){
 		 this.fileurl=fileurl;
 	}
 	public String getFileurl(){
 		 return this.fileurl;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setBuid(String buid){
 		 this.buid=buid;
 	}
 	public String getBuid(){
 		 return this.buid;
 	}
 
 	 
 	public void setCreateddate(Timestamp createddate){
 		 this.createddate=createddate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateddate(){
 		 return this.createddate;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setFileid(String fileid){
 		 this.fileid=fileid;
 	}
 	public String getFileid(){
 		 return this.fileid;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	public String getId(){
 		 return this.id;
 	}
 
 	 
}