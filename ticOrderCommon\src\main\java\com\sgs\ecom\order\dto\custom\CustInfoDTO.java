package com.sgs.ecom.order.dto.custom; 
 
import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;

import com.sgs.ecom.order.dto.CustInvoiceDTO;
import com.sgs.ecom.order.dto.order.OrderMemoDTO;
import net.sf.json.JSONArray;

public class CustInfoDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select IS_FREEZE,CUST_ID,PROVICE,CREDIT_AMOUNT,STATE_DATE,STATE,memo,COMPANY_NAME,CREATE_DATE,COUNTRY,CITY,TOWN,ADDRESS,address_en,CUST_CODE,COMPANY_NAME_EN from TB_CUST_INFO";
 
 
 	@ApiAnno(groups={QueryList.class})
 	@BeanAnno(value="IS_FREEZE", getName="getIsFreeze", setName="setIsFreeze")
 	private int isFreeze;
 	@IsNullAnno(serviceName={"modCust","freezeCust","delCust","qryCustDtl"})
 	@ApiAnno(groups={Default.class,QueryDtl.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="CREDIT_AMOUNT", getName="getCreditAmount", setName="setCreditAmount")
 	private double creditAmount;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={QueryDtl.class,QueryList.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryList.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={})
 	@BeanAnno(value="COUNTRY", getName="getCountry", setName="setCountry")
 	private String country;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="ADDRESS", getName="getAddress", setName="setAddress")
 	private String address;

	@IsNullAnno(serviceName={"addCust","checkCredit"})
	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
	@BeanAnno(value="address_en", getName="getAddressEn", setName="setAddressEn")
	private String addressEn;
 	@BeanAnno(value="BUSI_CODE", getName="getBusiCode", setName="setBusiCode")
 	private String busiCode;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="REPORT_PRICE", getName="getReportPrice", setName="setReportPrice")
 	private double reportPrice;
 	@ApiAnno(groups={QueryDtl.class})
 	@BeanAnno(value="BELONG_LAB_CODE", getName="getBelongLabCode", setName="setBelongLabCode")
 	private String belongLabCode;
 	@BeanAnno(value="IS_DELETE", getName="getIsDelete", setName="setIsDelete")
 	private int isDelete;
 	@ApiAnno(groups={QueryDtl.class,QueryList.class})
 	@BeanAnno(value="LINK_EMAIL", getName="getLinkEmail", setName="setLinkEmail")
 	private String linkEmail;
 	
	@IsNullAnno(serviceName={"addCust","checkCredit"})
	@ApiAnno(groups={QueryDtl.class,QueryList.class,QuerySummary.class})
	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QueryList.class})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
 	@ApiAnno(groups={})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="USER_NICK", getName="getUserNick", setName="setUserNick")
 	private String userNick;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="BANK_NAME", getName="getBankName", setName="setBankName")
 	private String bankName;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="BANK_NUMBER", getName="getBankNumber", setName="setBankNumber")
 	private String bankNumber;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="REG_PHONE", getName="getRegPhone", setName="setRegPhone")
 	private String regPhone;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="REG_ADDRESS", getName="getRegAddress", setName="setRegAddress")
 	private String regAddress;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="INVOICE_TITLE", getName="getInvoiceTitle", setName="setInvoiceTitle")
 	private String invoiceTitle;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QuerySummary.class})
 	@BeanAnno(value="TAX_NO", getName="getTaxNo", setName="setTaxNo")
 	private String taxNo;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private Long userId;
 	@ApiAnno(groups={Default.class,QuerySummary.class,QueryDtl.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={Default.class,QueryDtl.class,QuerySummary.class})
 	@BeanAnno(value="BOSS_NO", getName="getBossNo", setName="setBossNo")
 	private String bossNo;
 	@ApiAnno(groups={Default.class,QuerySummary.class})
 	@BeanAnno(value="RELATE_ID", getName="getRelateId", setName="setRelateId")
 	private long relateId;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="SETTLE_TYPE", getName="getSettleType", setName="setSettleType")
 	private Integer settleType;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="BELONG_AREA", getName="getBelongArea", setName="setBelongArea")
 	private String belongArea;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="VBA_ACCOUNT", getName="getVbaAccount", setName="setVbaAccount")
 	private String vbaAccount;

 	@ApiAnno(groups={Default.class,QueryList.class})
 	private JSONArray users;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private JSONArray invoices;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	private double useAmount;
 	@ApiAnno(groups={Default.class,QueryList.class,QueryDtl.class})
 	private String currency;

 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	private List<CustReportDTO> reports;

	@ApiAnno(groups={Default.class,QueryDtl.class})
	@BeanAnno(dtocls = {CustInvoiceDTO.class})
	private List<CustInvoiceDTO> invoiceList;

	public List<CustInvoiceDTO> getInvoiceList() {
		return invoiceList;
	}

	public void setInvoiceList(List<CustInvoiceDTO> invoiceList) {
		this.invoiceList = invoiceList;
	}

	public void setIsFreeze(int isFreeze){
 		 this.isFreeze=isFreeze;
 	}
 	public int getIsFreeze(){
 		 return this.isFreeze;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setCreditAmount(double creditAmount){
 		 this.creditAmount=creditAmount;
 	}
 	public double getCreditAmount(){
 		 return this.creditAmount;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserNick() {
		return userNick;
	}
	public void setUserNick(String userNick) {
		this.userNick = userNick;
	}
	public String getUserPhone() {
		return userPhone;
	}
	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}
	public String getInvoiceTitle() {
		return invoiceTitle;
	}
	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getAddressEn() {
		return addressEn;
	}

	public void setAddressEn(String addressEn) {
		this.addressEn = addressEn;
	}
	public String getPersonCode() {
		return personCode;
	}
	public Integer getSettleType() {
		return settleType;
	}
	public void setSettleType(Integer settleType) {
		this.settleType = settleType;
	}
	public String getBelongArea() {
		return belongArea;
	}
	public void setBelongArea(String belongArea) {
		this.belongArea = belongArea;
	}
	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}
	public String getBossNo() {
		return bossNo;
	}
	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}
	public long getRelateId() {
		return relateId;
	}
	public void setRelateId(long relateId) {
		this.relateId = relateId;
	}
	public String getBankName() {
		return bankName;
	}
	public void setBankName(String bankName) {
		this.bankName = bankName;
	}
	public String getBankNumber() {
		return bankNumber;
	}
	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}
	public String getRegPhone() {
		return regPhone;
	}
	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}
	public String getRegAddress() {
		return regAddress;
	}
	public void setRegAddress(String regAddress) {
		this.regAddress = regAddress;
	}
	public String getTaxNo() {
		return taxNo;
	}
	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}
	public String getVbaAccount() {
		return vbaAccount;
	}
	public void setVbaAccount(String vbaAccount) {
		this.vbaAccount = vbaAccount;
	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
	public double getReportPrice() {
		return reportPrice;
	}
	public void setReportPrice(double reportPrice) {
		this.reportPrice = reportPrice;
	}
	public String getBelongLabCode() {
		return belongLabCode;
	}
	public void setBelongLabCode(String belongLabCode) {
		this.belongLabCode = belongLabCode;
	}
	public int getIsDelete() {
		return isDelete;
	}
	public void setIsDelete(int isDelete) {
		this.isDelete = isDelete;
	}
	public String getLinkEmail() {
		return linkEmail;
	}
	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail;
	}

	public JSONArray getUsers() {
		return users;
	}
	public void setUsers(JSONArray users) {
		this.users = users;
	}
	public JSONArray getInvoices() {
		return invoices;
	}
	public void setInvoices(JSONArray invoices) {
		this.invoices = invoices;
	}
	public double getUseAmount() {
		return useAmount;
	}
	public void setUseAmount(double useAmount) {
		this.useAmount = useAmount;
	}
	public String getCurrency() {
		return currency;
	}
	public void setCurrency(String currency) {
		this.currency = currency;
	}
	public List<CustReportDTO> getReports() {
		return reports;
	}
	public void setReports(List<CustReportDTO> reports) {
		this.reports = reports;
	}
	
}