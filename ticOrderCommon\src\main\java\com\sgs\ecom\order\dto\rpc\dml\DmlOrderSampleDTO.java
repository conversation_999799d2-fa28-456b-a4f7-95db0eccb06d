package com.sgs.ecom.order.dto.rpc.dml;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.order.request.oiq.OiqOrderSampleFromReq;

import java.util.ArrayList;
import java.util.List;

public class DmlOrderSampleDTO {

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleNo;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    @BeanAnno(dtocls = {OrderSampleFromDTO.class})
    private List<OiqOrderSampleFromReq> sampleAttr=new ArrayList<>();

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public List<OiqOrderSampleFromReq> getSampleAttr() {
        return sampleAttr;
    }

    public void setSampleAttr(List<OiqOrderSampleFromReq> sampleAttr) {
        this.sampleAttr = sampleAttr;
    }
}
