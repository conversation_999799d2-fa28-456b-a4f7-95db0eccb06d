package com.sgs.ecom.order.dto.send;

import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.request.FileReq;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 操作请求对象
 * @date 2025/6/3 13:35
 */
@Data
public class OperatorReqDTO {
    private String orderNo;
    private String platformOrder;
    private String csCode;
    private String csEmail;
    private int orderState;
    private String operatorDate;//记录操作时间
    private String preReportDate;//用于SODA回传TAT时间
    private BigDecimal orderAmount;
    private String memo;
    private String slimNo;
    private String platform;
    private List<FileReq> reports;
    private List<String> urls;
    private Integer type;  //需要异步处理的类型 1-订单完成 2-订单取消
    private EventEnum eventEnum;
    private TicOtherApiDTO ticOtherApiDTO;
    private Integer pendingState;//1、pending  0、unPending为空时则不是操作pending/unpending动作
}
