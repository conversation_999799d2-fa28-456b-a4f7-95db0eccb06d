package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOCustApplyRelate{
 
 	public static final String SEQUENCE = "RELATE_ID"; 
  
 	public static final String BO_SQL = "TB_CUST_APPLY_RELATE"; 
 
 	public static final String OWNER ="member";

 	public static final String RELATE_ID="relateId";
 	public static final String CUST_ID="custId";
 	public static final String CREATE_DATE="createDate";
 	public static final String USER_ID="userId";

 	@BeanAnno("RELATE_ID")
 	private long relateId;
 	@BeanAnno("CUST_ID")
 	private long custId;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("USER_ID")
 	private long userId;

 	public void setRelateId(long relateId){
 		 this.relateId=relateId;
 	}
 	public long getRelateId(){
 		 return this.relateId;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
}