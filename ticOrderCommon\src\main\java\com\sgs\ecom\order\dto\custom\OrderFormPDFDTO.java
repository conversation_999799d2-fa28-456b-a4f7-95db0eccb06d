package com.sgs.ecom.order.dto.custom;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.math.BigDecimal;

public class OrderFormPDFDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private  Long orderId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportLua;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportForm;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportLuaCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportFormCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal testCycle;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long labId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String csEmail;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String csCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testCycleMemo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String relateOrderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String businessLine;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String isUrgent;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer isDetermine=0;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String determine;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String groupNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currency;



    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getReportLua() {
        return reportLua;
    }

    public void setReportLua(String reportLua) {
        this.reportLua = reportLua;
    }

    public String getReportForm() {
        return reportForm;
    }

    public void setReportForm(String reportForm) {
        this.reportForm = reportForm;
    }

    public BigDecimal getTestCycle() {
        return testCycle;
    }

    public void setTestCycle(BigDecimal testCycle) {
        this.testCycle = testCycle;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getReportLuaCode() {
        return reportLuaCode;
    }

    public void setReportLuaCode(String reportLuaCode) {
        this.reportLuaCode = reportLuaCode;
    }

    public String getReportFormCode() {
        return reportFormCode;
    }

    public void setReportFormCode(String reportFormCode) {
        this.reportFormCode = reportFormCode;
    }

    public String getTestCycleMemo() {
        return testCycleMemo;
    }

    public void setTestCycleMemo(String testCycleMemo) {
        this.testCycleMemo = testCycleMemo;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(String isUrgent) {
        this.isUrgent = isUrgent;
    }

    public Integer getIsDetermine() {
        return isDetermine;
    }

    public void setIsDetermine(Integer isDetermine) {
        this.isDetermine = isDetermine;
    }

    public String getDetermine() {
        return determine;
    }

    public void setDetermine(String determine) {
        this.determine = determine;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
