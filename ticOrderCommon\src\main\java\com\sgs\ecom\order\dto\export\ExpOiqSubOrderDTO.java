package com.sgs.ecom.order.dto.export;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.order.util.serializer.ExpTimeFormatSerializer;

public class ExpOiqSubOrderDTO {
    //子订单编号
    private String orderNo;

    //主订单编号
    private String relateOrderNo;

    //操作人
    private String operatorCode;

    //支付状态
    private String payState;
    //订单价格
    private String realAmount;

    private int state;

    //订单创建时间
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String createDate;
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String payDate;

    //订单关闭时间
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String closeTime;

    //订单关闭理由
    private String closeReason;
    //订单关闭备注
    private String closeMemo;
    //订单关闭人
    private String closePerson;
    //开户银行
    private String bankAddr;
    //开户行号
    private String bankNumber;
    //是否月结
    private String monthPay;
    //支付方式
    private String payMethod;
    //商户订单号
    private String paymentNo;
    //交易流水号
    private String transNo;


    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public String getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(String realAmount) {
        this.realAmount = realAmount;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(String closeTime) {
        this.closeTime = closeTime;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public String getCloseMemo() {
        return closeMemo;
    }

    public void setCloseMemo(String closeMemo) {
        this.closeMemo = closeMemo;
    }

    public String getClosePerson() {
        return closePerson;
    }

    public void setClosePerson(String closePerson) {
        this.closePerson = closePerson;
    }

    public String getBankAddr() {
        return bankAddr;
    }

    public void setBankAddr(String bankAddr) {
        this.bankAddr = bankAddr;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(String monthPay) {
        this.monthPay = monthPay;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getPaymentNo() {
        return paymentNo;
    }

    public void setPaymentNo(String paymentNo) {
        this.paymentNo = paymentNo;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }
}
