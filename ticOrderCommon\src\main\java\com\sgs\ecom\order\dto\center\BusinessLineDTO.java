package com.sgs.ecom.order.dto.center;

public class BusinessLineDTO {


    public static final String CATEGORY="CATEGORY";
    public static final String LINE="LINE";

    private String bu;
    private String configName;
    private Long configId;
    private String configCode;
    private String platformCode;
    private int closeDays;




    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public String getConfigCode() {
        return configCode;
    }

    public void setConfigCode(String configCode) {
        this.configCode = configCode;
    }

    public String getPlatformCode() {
        return platformCode;
    }

    public void setPlatformCode(String platformCode) {
        this.platformCode = platformCode;
    }

    public int getCloseDays() {
        return closeDays;
    }

    public void setCloseDays(int closeDays) {
        this.closeDays = closeDays;
    }
}
