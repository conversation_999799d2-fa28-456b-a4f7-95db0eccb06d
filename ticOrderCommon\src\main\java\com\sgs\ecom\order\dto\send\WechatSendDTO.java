package com.sgs.ecom.order.dto.send;

import com.sgs.ecom.order.enumtool.send.WechatEnum;

import java.util.Map;

public class WechatSendDTO {

    public static final String ORDER_NO="orderNo";
    public static final String SUB_ORDER_NO="subOrderNo";
    public static final String CATEGORY_PATH="categoryPath";
    public static final String COMPANY_NAME="companyName";
    public static final String ORDER_EXP_DATE="orderExpDate";
    public static final String OFFER_DATE="offerDate";
    public static final String CREATE_DATE="createDate";
    public static final String CREATE_DATE_TEMPLATE="dateTemp";
    public static final String OPERATOR_DATE="operatorDate";
    public static final String MEMO="memo";
    public static final String STATE_SHOW="stateShow";
    public static final String REAL_AMOUNT="realAmount";
    public static final String ORDER_TYPE="orderType";
    public static final String CURRENCY="currency";
    public static final String FILE_NAME="fileName";
    public static final String SAMPLE_NAME="sampleName";


    private String orderNo;
    private Map<String,Object> item;

    private Long userId;

    private String businessType;
    private String buType;
    private String appId;
    private String openId;
    private String wechatMsgType;
    private String wechatTemplateId;



    public WechatSendDTO() {
    }

    public WechatSendDTO(String orderNo,Long userId, WechatEnum typeEnum) {
        this.userId=userId;
        this.businessType=typeEnum.getIndex();
        this.orderNo = orderNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Map<String, Object> getItem() {
        return item;
    }

    public void setItem(Map<String, Object> item) {
        this.item = item;
    }



    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getBuType() {
        return buType;
    }

    public void setBuType(String buType) {
        this.buType = buType;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getWechatMsgType() {
        return wechatMsgType;
    }

    public void setWechatMsgType(String wechatMsgType) {
        this.wechatMsgType = wechatMsgType;
    }

    public String getWechatTemplateId() {
        return wechatTemplateId;
    }

    public void setWechatTemplateId(String wechatTemplateId) {
        this.wechatTemplateId = wechatTemplateId;
    }
}
