package com.sgs.ecom.order.dto.tfs;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddContainerDTO {
    /**
     * 货柜号码
     */
    private String containerNumber;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 毛重
     */
    private BigDecimal grossWeight;
    /**
     * 海关编码
     */
    private String hsCode;
    /**
     * 货物危险品编码
     */
    private String dangerousGoodsCode;
    /**
     * FOB价
     */
    private BigDecimal fobValue;
    /**
     * 海运费
     */
    private BigDecimal oceanFreight;
    /**
     * 品名
     */
    private String goodsName;
    /**
     * 货柜尺寸
     */
    private String containerSize;
    /**
     * 货柜类型
     */
    private String containerType;
    /**
     * 封条号
     */
    private String sealNumber;
    /**
     * 运输方式
     */
    private String transport;
    /**
     * 核实集装箱总重
     */
    private BigDecimal checkContainerTotalWeight;
    /**
     * 箱内货物总体积
     */
    private BigDecimal goodsVolume;
    /**
     * 车辆类型
     */
    private String vehicleType;
    /**
     * 车辆品牌
     */
    private String vehicleBrand;
    /**
     * 车架号
     */
    private String vehicleChassisNo;
    /**
     * 货物件数
     */
    private String packagesNumber;
    /**
     * 包装种类
     */
    private String packageType;
    /**
     * 总体积
     */
    private String totalVolume;
    /**
     * 总重量
     */
    private String totalGrossWeight;
    /**
     * 20尺货柜数量
     */
    private String container20Quantity;
    /**
     * 40尺货柜数量
     */
    private String container40Quantity;
    /**
     * 20尺货柜内货物总体积
     */
    private String container20TotalVolume;
    /**
     * 20尺货柜内货物总重量
     */
    private String container20TotalGrossWeight;
    /**
     * 40尺货柜内货物总体积
     */
    private String container40TotalVolume;
    /**
     * 40尺货柜内货物总重量
     */
    private String container40TotalGrossWeight;
    /**
     * 是否为自备箱
     */
    private String shipperOwned;
    /**
     * 箱内货物净重
     */
    private String netWeight;
    /**
     * 货物件数
     */
    private String packagesQuantity;
    /**
     * 产品类别
     */
    private String productsType;
    /**
     * 是否属于危险品
     */
    private String isDangerousGoods;
    /**
     * 自动计算集装箱内货物总毛重
     */
    private BigDecimal goodsTotalGrossWeight;
    /**
     * 货物件数
     */
    private String goodsNumber;
    /**
     * IMO CLASS
     */
    private String imoClass;
}
