package com.sgs.ecom.order.dto.oiq;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.tfs.DynamicAttrDTO;
import lombok.Data;

import java.util.List;

@Data
public class TfsOrderDTO {
    /**
     * 跟踪单号
     */
    @ApiAnno(groups={ BaseOrderFilter.OrderDetail.class, BaseOrderFilter.InquiryDetail.class})
    private String outOrderNo;
    /**
     * 目的国code
     */
    private String destinationCountryCode;
    /**
     * 目的国
     */
    @ApiAnno(groups={ BaseOrderFilter.OrderDetail.class, BaseOrderFilter.InquiryDetail.class})
    private String destinationCountry;
    /**
     * 提单号码
     */
    @ApiAnno(groups={ BaseOrderFilter.OrderDetail.class, BaseOrderFilter.InquiryDetail.class})
    private String ladingNo;
    /**
     * 申请方公司名称
     */
    @ApiAnno(groups={ BaseOrderFilter.OrderDetail.class, BaseOrderFilter.InquiryDetail.class})
    private String companyName;
    /**
     * 运输方式
     */
    @ApiAnno(groups={ BaseOrderFilter.OrderDetail.class, BaseOrderFilter.InquiryDetail.class})
    private String transportModeShow;
    /**
     * 动态字段
     */
    @ApiAnno(groups={ BaseOrderFilter.OrderDetail.class, BaseOrderFilter.InquiryDetail.class})
    private List<DynamicAttrDTO> dynamicAttrDTOList;
}
