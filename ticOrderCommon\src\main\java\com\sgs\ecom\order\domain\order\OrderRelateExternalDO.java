package com.sgs.ecom.order.domain.order;

import com.sgs.ecom.order.entity.order.OrderRelateExternal;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class OrderRelateExternalDO extends OrderRelateExternal {

    public OrderRelateExternal getOrderRelateExternal(String orderNo,String platform,String platformOrder){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderRelateExternal orderRelateExternal=new OrderRelateExternal();
        orderRelateExternal.setCreateDate(dateStr);
        orderRelateExternal.setStateDate(dateStr);
        orderRelateExternal.setState(1);
        orderRelateExternal.setExternalSystem(platform);
        orderRelateExternal.setExternalNo(platformOrder);
        orderRelateExternal.setOrderNo(orderNo);
        return orderRelateExternal;
    }
}
