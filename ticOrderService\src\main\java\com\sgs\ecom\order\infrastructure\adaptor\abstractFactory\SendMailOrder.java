package com.sgs.ecom.order.infrastructure.adaptor.abstractFactory;


import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.base.BaseMailSend;
import com.sgs.ecom.order.domain.order.OrderAttachmentDO;
import com.sgs.ecom.order.domain.order.OrderSampleDO;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationAttrDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationFormDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderAttachmentDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderInvoiceDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderLinkDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleDomainService;
import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderLinkDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.order.dto.order.OrderLinkDTO;
import com.sgs.ecom.order.dto.order.OrderSampleDTO;
import com.sgs.ecom.order.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.order.dto.send.BaseSendDTO;
import com.sgs.ecom.order.dto.send.mail.MailExpressDTO;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.user.UserInfoSexEnum;
import com.sgs.ecom.order.infrastructure.event.MailEvent;
import com.sgs.ecom.order.request.base.BaseFileReq;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.pdf.OrderWordPrintReq;
import com.sgs.ecom.order.service.member.interfaces.IMemberRestTemplateService;
import com.sgs.ecom.order.service.member.interfaces.ISSOTemplateSV;
import com.sgs.ecom.order.service.oiq.interfaces.IOiqOrderApplicationService;
import com.sgs.ecom.order.service.order.interfaces.IOrderSampleService;
import com.sgs.ecom.order.service.util.interfaces.ICenterService;
import com.sgs.ecom.order.service.util.interfaces.IOrderService;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.MailSendUtil;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.collection.NumUtil;
import com.sgs.ecom.order.util.collection.StrUtil;
import com.sgs.ecom.order.util.file.MyFileUtils;
import com.sgs.ecom.order.util.order.EmailUtil;
import com.sgs.ecom.order.util.select.SelectBaseUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.util.time.TimeCalendarUtil;
import com.sgs.util.ShieldUtil;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;
import javax.annotation.Resource;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class SendMailOrder extends AbstractSend{

    Logger logger = LoggerFactory.getLogger(SendMailOrder.class);

    static{
        MailSendUtil.put(OrderTypeEnum.OIQ_ORDER.getIndex(), SendMailOrder.class);
        MailSendUtil.put(OrderTypeEnum.OIQ_SMALL_ORDER.getIndex(), SendMailOrder.class);
    }

    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Resource
    private IOrderApplicationFormDomainService orderApplicationFormDomainService;
    @Resource
    private IOrderLinkDomainService orderLinkDomainService;
    @Resource
    private ISSOTemplateSV ssoTemplateSV;
    @Resource
    private IOrderInvoiceDomainService orderInvoiceDomainService;
    @Resource
    private IMemberRestTemplateService memberRestTemplateService;
    @Resource
    private IOrderService orderService;
    @Resource
    private IOiqOrderApplicationService oiqOrderApplicationService;
    @Resource
    private IOrderSampleService orderSampleService;
    @Resource
    private IOrderAttachmentDomainService orderAttachmentDomainService;
    @Resource
    private IOrderSampleDomainService orderSampleDomainService;
    @Resource
    private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;

    @Value("${mail.member.host}")
    String memberHost;
    @Value("${mail.order.host}")
    String orderHost;
    @Value("${mail.ind.host}")
    private String indHost;
    @Value("${mail.min.host}")
    private String minHost;
    @Value("${mail.tfs.host}")
    private String tfsHost;
    @Resource
    private IOrderUtilService orderUtilService;
    @Resource
    private ICenterService centerService;


    @Override
    public BaseSendDTO sendEmail(MailEvent sendEvent) {
        BaseMailSend baseMailSend = sendEvent.getBaseMailSend();
        BaseSendDTO baseSendDTO=new BaseSendDTO(baseMailSend);
        try {
            Thread.sleep(2000);
            BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(baseMailSend.getOrderNo());
            BaseOrderDTO sub=null;
            if(OrderTypeEnum.getSub(baseOrderDTO.getOrderType())){
                sub=baseOrderDTO;
                baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());
            }
            Map<String, Object> mapDate = getBaseMap(baseOrderDTO,sub);
            addOtherDate(mapDate,baseMailSend);
            getQuotationFile(mapDate,baseMailSend.getOiqMailEnum(),baseOrderDTO);

            baseSendDTO.setItem(mapDate);
            baseSendDTO.setBuType(baseOrderDTO.getBu());

            if(baseMailSend.getMailType()==1L){
                //发给用户的先获取询价单里面的link数据 如果没有 取订单上的
                String sendMail=baseOrderDTO.getUserEmail();
                OrderApplicationFormDTO orderApplicationFormDTO=orderApplicationFormDomainService.selectDTOByOrderNo(baseOrderDTO.getOrderNo());
                if(!ValidationUtil.isEmpty(orderApplicationFormDTO)){
                    sendMail=orderApplicationFormDTO.getLinkEmail();
                    //to 增加付款方邮箱
                    if(OiqMailEnum.REFUND_SUCCESS==baseMailSend.getOiqMailEnum() | OiqMailEnum.SERVICE_NO_PAY==baseMailSend.getOiqMailEnum() ){
                        OrderInvoiceDTO orderInvoiceDTO=orderInvoiceDomainService.qryOneOrderInvoiceByOrder(baseOrderDTO.getOrderNo());
                        if(!ValidationUtil.isEmpty(orderInvoiceDTO)){
                            sendMail=StrUtil.emailAddStr(sendMail,orderInvoiceDTO.getPayerEmail());
                        }
                    }
                }
                StringJoiner stringJoiner=new StringJoiner(",");
                Map<String, List<OrderLinkDTO>> linkMap=orderLinkDomainService.qryOiqOrderLinkMap(baseOrderDTO.getOrderNo());
                //添加link数据
                stringJoiner.add(OiqOrderLinkDTO.listToStr(linkMap,"1",","));

                if(baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_INVOICE || baseMailSend.getOiqMailEnum()== OiqMailEnum.INVOICE ){
                    stringJoiner.add(OiqOrderLinkDTO.listToStr(linkMap,"3",","));
                }


                //询报价添加cc
                if(baseOrderDTO.getOrderType().equals(OrderTypeEnum.OIQ_ORDER.getIndex())){
                    stringJoiner.add(getSendCcByOrder(baseMailSend,baseOrderDTO));
                }
                //申请表加cc
                if(baseOrderDTO.getOrderType().equals(OrderTypeEnum.OIQ_SMALL_ORDER.getIndex())){
                    stringJoiner.add(getSendCcByPortal(baseMailSend,baseOrderDTO));
                }

                //报告 不管询报价或申请表都添加报告cc
                if(OiqMailEnum.SERVICE_SUCCESS==baseMailSend.getOiqMailEnum() ||OiqMailEnum.UPDATE_REPORT==baseMailSend.getOiqMailEnum()||
                        OiqMailEnum.ADD_REPORT==baseMailSend.getOiqMailEnum()||baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_SERVICE_SUCCESS
                        ||baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_REPORT_CHANGE){
                    sendMail=StrUtil.emailAddStr(sendMail,OiqOrderLinkDTO.listToStr(linkMap,"2",","));
                }

                String cc=stringJoiner.toString();
                cc=StrUtil.strDeduplicate(cc,sendMail);
                baseSendDTO.setSendMail(sendMail);
                baseSendDTO.setSendCc(cc);
                if("1900".equals(baseOrderDTO.getBu()) && StringUtils.isBlank(sendMail)){
                    baseSendDTO.setSendMail(cc);
                    baseSendDTO.setSendCc("");
                }

            }

            //客服是 发送给销售和cs
            if(baseMailSend.getMailType()==2L){
                String sendMail=baseOrderDTO.getCsEmail();
                if(OiqMailEnum.OFF_PAY==baseMailSend.getOiqMailEnum()||OiqMailEnum.REFUND==baseMailSend.getOiqMailEnum()
                ||OiqMailEnum.ONLINE_PAY==baseMailSend.getOiqMailEnum()){
                    SysPersonDTO sysPersonDTO=ssoTemplateSV.qryPersonByUser(baseOrderDTO.getOperatorCode());
                    if(!ValidationUtil.isEmpty(sysPersonDTO) && StringUtils.isNotBlank(sysPersonDTO.getPersonMail())){
                        sendMail=StrUtil.emailAddStr(sendMail,sysPersonDTO.getPersonMail());
                    }
                }
                baseSendDTO.setSendMail(StrUtil.strDeduplicate(sendMail));
                String cc=StrUtil.emailAddStr(baseOrderDTO.getCsEmail(),baseOrderDTO.getSalesEmail());
                baseSendDTO.setSendCc(cc);
                // 修改服务顾问后通知新的服务顾问去报价
                if (OiqMailEnum.OIQ_PORTAL_UPDATE_BUSINESS_PERSON == baseMailSend.getOiqMailEnum()) {
                    baseSendDTO.setSendMail(baseOrderDTO.getBusinessPersonEmail());
                    baseSendDTO.setSendCc("");
                }
            }

            baseSendDTO.setSendCc(StrUtil.strDeduplicate(baseSendDTO.getSendCc(),baseSendDTO.getSendMail()));

            // 增加userId
            baseSendDTO.setUserId(baseOrderDTO.getUserId());
        }catch (Exception e){
            logger.info("邮件异常",e);
            return null;
        }
        return baseSendDTO;
    }


    public void addOtherDate(Map<String, Object> mapDate,BaseMailSend baseMailSend) {

        //添加 申请表pdf
        if(baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_UPDATE_FORM){
            //附件没有不报错
            try {
                List<BaseFileReq> fileReqList=memberRestTemplateService.getFormPDFFileList(baseMailSend.getOrderNo());
                MyFileUtils.mapAddFile(mapDate,fileReqList);
            }catch (Exception e){

            }
        }

        if(ValidationUtil.isEmpty(baseMailSend.getOiqOtherDTO())){
            return;
        }
        if(StringUtils.isNotBlank(baseMailSend.getOiqOtherDTO().getInvoiceFileUrl())){
            mapDate.put(EmailUtil.INVOICE_FILE_URL,baseMailSend.getOiqOtherDTO().getInvoiceFileUrl());
        }
        if(StringUtils.isNotBlank(baseMailSend.getOiqOtherDTO().getReportNoStr())){
            mapDate.put(EmailUtil.REPORT_NO_STR,baseMailSend.getOiqOtherDTO().getReportNoStr());
        }



        if (!ValidationUtil.isEmpty(baseMailSend.getOiqOtherDTO().getFileReqList())) {
            MyFileUtils.mapAddFile(mapDate, baseMailSend.getOiqOtherDTO().getFileReqList());
        }

        MailExpressDTO mailExpressDTO=baseMailSend.getOiqOtherDTO().getMailExpressDTO();
        if(!ValidationUtil.isEmpty(mailExpressDTO)){
            mapDate.put(EmailUtil.GOODS_NAME, mailExpressDTO.getGoodsName());
            mapDate.put(EmailUtil.EXPRESS_CODE,mailExpressDTO.getExpressCodeShow());
            mapDate.put(EmailUtil.EXPRESS_NO, mailExpressDTO.getExpressNo());
        }







    }





    public Map<String, Object> getBaseMap(BaseOrderDTO baseOrderDTO,BaseOrderDTO sub) throws Exception {
        Map<String,Object> map = new HashMap<>();
        JSONObject jsonObject=orderService.qryMoreOrderDTO(baseOrderDTO.getOrderId(),"",new BOSysPerson());
        map.put(EmailUtil.ORDER,jsonObject);
        JSONObject formJson=oiqOrderApplicationService.qryInfo(new OrderNoReq(baseOrderDTO.getOrderNo()),new BOSysPerson());
        BaseOrderDTO inquiryDTO=orderBaseInfoDomainService.selectBaseOrder(baseOrderDTO.getRelateOrderNo());
        map.put(EmailUtil.FORM,formJson);
        map.put(EmailUtil.ORDER_BASE,baseOrderDTO);
        map.put(EmailUtil.INQUIRY_BASE,inquiryDTO);
        map.put(EmailUtil.MEMBER_HOST, memberHost);
        map.put(EmailUtil.ORDER_HOST, orderHost);
        map.put(EmailUtil.IND_HOST,indHost);
        if(Objects.nonNull(inquiryDTO) && "1900".equals(inquiryDTO.getBu())){
            map.put(EmailUtil.IND_HOST,minHost);
        }
        map.put(EmailUtil.TFS_HOST,tfsHost);

        map.put(EmailUtil.ORDER_NO,baseOrderDTO.getOrderNo());
        map.put(EmailUtil.ORDER_ID,baseOrderDTO.getOrderId());
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        map.put(EmailUtil.PRICE,decimalFormat.format(NumUtil.toZero(baseOrderDTO.getRealAmount())));
        map.put(EmailUtil.COMPANY_NAME,StrUtil.toStr(baseOrderDTO.getCompanyName()));
        map.put(EmailUtil.CUSTOMER_NAME,StrUtil.toStr(baseOrderDTO.getUserName()));
        map.put(EmailUtil.CS_EMAIL, StrUtil.toStr(baseOrderDTO.getCsEmail()));
        String phone=StringUtils.isNotBlank(baseOrderDTO.getUserPhone())?baseOrderDTO.getUserPhone():baseOrderDTO.getUserEmail();
        map.put(EmailUtil.CUSTOMER_PHONE,StrUtil.toStr(phone));
        map.put(EmailUtil.NOW_TIME, UseDateUtil.getDateString(new Date()));
        map.put(EmailUtil.BUSINESS_PERSON_EMAIL, StrUtil.toStr(baseOrderDTO.getBusinessPersonEmail()));
        map.put(EmailUtil.ORDER_TYPE, baseOrderDTO.getOrderType());
        map.put(EmailUtil.USER_PHONE_SHOW, ShieldUtil.phoneMask(baseOrderDTO.getUserPhone()));
        if(!ValidationUtil.isEmpty(sub)){
            map.put(EmailUtil.SUB_ORDER_NO, sub.getOrderNo());
            map.put(EmailUtil.SUB_REAL_AMOUNT, sub.getRealAmount());
        }
        map.put(EmailUtil.PLATFORM_ORDER, StrUtil.toStr(baseOrderDTO.getPlatformOrder()));
        map.put(EmailUtil.OUT_ORDER_NO, StrUtil.toStr(baseOrderDTO.getOutOrderNo()));


        BigDecimal amount=NumUtil.toZero(baseOrderDTO.getRealAmount()).add(NumUtil.toZero(baseOrderDTO.getDiscountAmount()));
        BigDecimal discountNum=(BigDecimal.ONE.subtract(NumUtil.divide(baseOrderDTO.getRealAmount(),amount,4))).multiply(new BigDecimal("100"));
        map.put(EmailUtil.DISCOUNTNUM,discountNum);
        map.put(EmailUtil.AMOUNT,NumUtil.toFormat(amount));

        map.put(EmailUtil.TO_ORDER_NO, baseOrderDTO.getOrderNo());
        map.put(EmailUtil.USER_SEX, UserInfoSexEnum.getNameCh(baseOrderDTO.getUserSex()));
        map.put(EmailUtil.TEST_CYCLE, baseOrderDTO.getTestCycle());// 加急
        map.put(EmailUtil.CATAGORY, baseOrderDTO.getCategoryPath());
        map.put(EmailUtil.LAST_DATE, baseOrderDTO.getLastResponseDate());
        map.put(EmailUtil.CREATE_DATE, baseOrderDTO.getCreateDate());
        map.put(EmailUtil.DATE_TIME, TimeCalendarUtil.getFormatString(baseOrderDTO.getCreateDate()));
        map.put(EmailUtil.BUSINESS_LINE, baseOrderDTO.getBusinessLine());
        map.put(EmailUtil.NOW_TIME, UseDateUtil.getDateString(new Date()));
        map.put(EmailUtil.INVOICE_FILE_URL, "");
        map.put(EmailUtil.REPORT_NO_STR, "");
        map.put("payWarn",0);
        if(StringUtils.isNotBlank(baseOrderDTO.getCsCode())){
            SysPersonDTO sysPersonDTO=ssoTemplateSV.qryPersonByUser(baseOrderDTO.getCsCode());
            if(sysPersonDTO!=null && StringUtils.isNotBlank(sysPersonDTO.getOtherQrCode())){
                map.put("payWarn",1);
                map.put("payWarnUrl",sysPersonDTO.getOtherQrCode());
            }
        }

        if(Objects.nonNull(inquiryDTO) && "1900".equals(inquiryDTO.getBu())){
            List<OiqSampleDTO> sampleDTOList=orderSampleDomainService.getOiqOrderSample(baseOrderDTO.getOrderNo(),baseOrderDTO.getGroupNo(),baseOrderDTO.getApplicationLineId(),true);
            SampleCategoryDTO sampleCategoryDTO= OrderSampleDO.listToSampleCategory(sampleDTOList);
            Map<String, String> sampleCategoryMap = centerService.qrySampleCategory(
                baseOrderDTO.getBu(), baseOrderDTO.getApplicationLineId());
            map.put("sampleName",StrUtil.toStr(sampleCategoryMap.get(sampleCategoryDTO.getSampleCategoryCode())));
        }else{
            map.put("sampleName", StrUtil.toStr(getSampleNameListStr(getSampleNameList(baseOrderDTO.getOrderNo(), baseOrderDTO.getGroupNo()))));
        }

        return map;
    }

    public String getSendCcByOrder( BaseMailSend baseMailSend,BaseOrderDTO baseOrderDTO){
        StringJoiner stringJoiner=new StringJoiner(",");
        if(!ValidationUtil.isEmpty(baseMailSend.getOiqOtherDTO())
                && baseMailSend.getOiqOtherDTO().getSendOperatorCodeFlg()==1 && StringUtils.isNotBlank(baseOrderDTO.getOperatorCode())){
            SysPersonDTO sysPersonDTO=ssoTemplateSV.qryPersonByUser(baseOrderDTO.getOperatorCode());
            if(!ValidationUtil.isEmpty(sysPersonDTO)){
                stringJoiner.add(sysPersonDTO.getPersonMail());
            }
        }

        if(baseMailSend.getOiqMailEnum()== OiqMailEnum.OIQ_REMIND_PAYMENT ){
            OrderInvoiceDTO orderInvoiceDTO=orderInvoiceDomainService.qryOneOrderInvoiceByOrder(baseOrderDTO.getOrderNo());
            if(!ValidationUtil.isEmpty(orderInvoiceDTO)){
                stringJoiner.add(orderInvoiceDTO.getPayerEmail());
            }
        }
        return stringJoiner.toString();
    }

    public String getSendCcByPortal(BaseMailSend baseMailSend,BaseOrderDTO baseOrderDTO){
        String cc="";
        if(baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_UPDATE ){
            OrderInvoiceDTO orderInvoiceDTO=orderInvoiceDomainService.qryOneOrderInvoiceByOrder(baseOrderDTO.getOrderNo());
            if(!ValidationUtil.isEmpty(orderInvoiceDTO)){
                cc=orderInvoiceDTO.getPayerEmail();
            }
        }
        if(baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_SERVICE_SUCCESS||
                baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_REPORT_CHANGE||
                baseMailSend.getOiqMailEnum()== OiqMailEnum.PORTAL_INVOICE){
            cc=StrUtil.emailAddStr(baseOrderDTO.getBusinessPersonEmail(),baseOrderDTO.getSalesEmail());
            cc=StrUtil.emailAddStr(baseOrderDTO.getCsEmail(),cc);
        }
        return cc;
    }


    
    /**
    * 获取报价单附件
    * @param map
    * @param oiqMailEnum
    * @param baseOrderDTO 
    * @return void
    * <AUTHOR> || created at 2025/1/10 16:41 
    * @throws Exception 抛出错误    
    */
    private void getQuotationFile(Map map,OiqMailEnum oiqMailEnum,BaseOrderDTO baseOrderDTO) throws Exception {
        String orderNo=baseOrderDTO.getOrderNo();
        if(OiqMailEnum.OIQ_PORTAL_QUOTATION_CONFIRM==oiqMailEnum ||OiqMailEnum.PORTAL_UPDATE==oiqMailEnum){
            List<BaseFileReq> baseList=new ArrayList<>();

            Map<String,Object> mapAtt=new HashMap<>();
            mapAtt.put(SelectBaseUtil.ORDER_NO,orderNo);
            mapAtt.put(SelectMapUtil.ATT_TYPE, OrderAttachmentTypeEnum.IND_QUOTATION_FILE.getIndex());
            Map<String, List<OrderAttachmentDTO>> fileListMap = orderAttachmentDomainService.selectAttachmentMapByOrderNoList(Arrays.asList(orderNo));
            List<OrderAttachmentDTO> list= OrderAttachmentDO.getFileListByAttType(fileListMap,orderNo, OrderAttachmentTypeEnum.IND_QUOTATION_FILE);
            //判断有没有文件 没有文件的情况下 去DR 生成最新的附件
            if(!ValidationUtil.isEmpty(list)){
                baseList=MyFileUtils.fileListToFileReq(list);
            }else{
                if(StringUtils.isNotBlank(baseOrderDTO.getRelateOrderNo())){
                    BaseOrderDTO inquiry=orderBaseInfoDomainService.selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());
                    OrderWordPrintReq orderPrintReq=new OrderWordPrintReq(inquiry.getOrderNo(),"quotation");;
                    try {
                        baseList =memberRestTemplateService.getWordPdf(orderPrintReq);
                    }catch (Exception e){
                        System.out.println("==getWordPdf=="+e);
                    }
                }
            }
            MyFileUtils.mapAddFile(map, baseList);
        }
    }


    public String getSampleNameListStr(List<OrderSampleDTO> list) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int n = 0; n < list.size(); n++) {
            String nameCn = list.get(n).getSampleNameCn();
            String nameEn = list.get(n).getSampleNameEn();

            stringBuilder.append(StrUtil.strAddStr(nameCn, nameEn,'/'));
            stringBuilder.append(",");
        }
        String str = stringBuilder.toString();
        if (StringUtils.isNotBlank(str)) {
            str = str.substring(0, str.length() - 1);
        }
        return str;
    }

    public List<OrderSampleDTO> getSampleNameList(String orderNo, String groupNo) {
        List<OrderSampleDTO> orderSampleDTOList = orderSampleService.qrySampleList(orderNo, groupNo, true);
        return orderSampleDTOList;
    }
}
