package com.sgs.ecom.order.dto.user;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserWechatRelateDTO {

    /**
     * 主键ID
     */
    private Long relateId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 微信APPID
     */
    private String appId;

    /**
     * 微信OPENID
     */
    private String openId;

    /**
     * 微信UNIONID
     */
    private String unionId;

    /**
     * 是否微信手机登录
     */
    private Integer isWechatPhone;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 微信类型
     */
    private String wechatType;
}
