package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import java.util.Date; 
import com.platform.util.json.DateFormatSerializer; 
import com.platform.util.json.PriceFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOAcctBalance{
 
 	public static final String SEQUENCE = "BALANCE_ID"; 
  
 	public static final String BO_SQL = "ACCT_BALANCE"; 
 
 	public static final String OWNER ="member";

 	public static final String AREA_ID="areaId";
 	public static final String EXP_DATE="expDate";
 	public static final String FREEZE_AMOUNT="freezeAmount";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String BALANCE="balance";
 	public static final String CREATE_DATE="createDate";
 	public static final String BALANCE_TYPE="balanceType";
 	public static final String BALANCE_ID="balanceId";
 	public static final String SKU_ID="skuId";
 	public static final String USER_TYPE="userType";
 	public static final String STORE_ID="storeId";
 	public static final String EFF_DATE="effDate";

 	@BeanAnno("AREA_ID")
 	private long areaId;
 	@BeanAnno("EXP_DATE")
 	private Date expDate;
 	@BeanAnno("FREEZE_AMOUNT")
 	private long freezeAmount;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("STATE")
 	private long state;
 	@BeanAnno("BALANCE")
 	private long balance;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("BALANCE_TYPE")
 	private long balanceType;
 	@BeanAnno("BALANCE_ID")
 	private long balanceId;
 	@BeanAnno("SKU_ID")
 	private long skuId;
 	@BeanAnno("USER_TYPE")
 	private int userType;
 	@BeanAnno("STORE_ID")
 	private long storeId;
 	@BeanAnno("EFF_DATE")
 	private Date effDate;

 	public void setAreaId(long areaId){
 		 this.areaId=areaId;
 	}
 	public long getAreaId(){
 		 return this.areaId;
 	}
 
 	 
 	public void setExpDate(Date expDate){
 		 this.expDate=expDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getExpDate(){
 		 return this.expDate;
 	}
 
 	 
 	public void setFreezeAmount(long freezeAmount){
 		 this.freezeAmount=freezeAmount;
 	}
 	@JsonSerialize(using = PriceFormatSerializer.class) 
 	public long getFreezeAmount(){
 		 return this.freezeAmount;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	public void setBalance(long balance){
 		 this.balance=balance;
 	}
 	public long getBalance(){
 		 return this.balance;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBalanceType(long balanceType){
 		 this.balanceType=balanceType;
 	}
 	public long getBalanceType(){
 		 return this.balanceType;
 	}
 
 	 
 	public void setBalanceId(long balanceId){
 		 this.balanceId=balanceId;
 	}
 	public long getBalanceId(){
 		 return this.balanceId;
 	}
 
 	 
 	public void setSkuId(long skuId){
 		 this.skuId=skuId;
 	}
 	public long getSkuId(){
 		 return this.skuId;
 	}
 
 	 
 	public void setUserType(int userType){
 		 this.userType=userType;
 	}
 	public int getUserType(){
 		 return this.userType;
 	}
 
 	 
 	public void setStoreId(long storeId){
 		 this.storeId=storeId;
 	}
 	public long getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	public void setEffDate(Date effDate){
 		 this.effDate=effDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getEffDate(){
 		 return this.effDate;
 	}
 
 	 
}