package com.sgs.ecom.order.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.platform.annotation.Master;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.config.OiqApplicationProperties;
import com.sgs.ecom.order.domain.order.OrderSampleDO;
import com.sgs.ecom.order.domain.order.OrderSampleFromDO;
import com.sgs.ecom.order.domain.service.order.interfaces.*;
import com.sgs.ecom.order.dto.bbc.TicSampleJOSNDTO;
import com.sgs.ecom.order.dto.center.BusinessLineDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.custom.OrderSampleNameDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.order.dto.order.*;
import com.sgs.ecom.order.dto.rpc.CenterSampleDTO;
import com.sgs.ecom.order.dto.sys.SysEnumConfigDTO;
import com.sgs.ecom.order.dto.sys.SysSampleBasicDTO;
import com.sgs.ecom.order.entity.order.OrderSampleFrom;
import com.sgs.ecom.order.enums.DynamicSamplesEnum;
import com.sgs.ecom.order.enums.SplitOrderType;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.bbc.AppFormEnum;
import com.sgs.ecom.order.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.order.mapper.order.OrderSampleMapper;
import com.sgs.ecom.order.request.OrderSampleReq;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.rpc.CenterBusinessReq;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.IMemberTemplateService;
import com.sgs.ecom.order.service.order.interfaces.*;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.RedisUtils;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.check.CheckUtil;
import com.sgs.ecom.order.util.select.SelectBaseUtil;
import com.sgs.ecom.order.util.select.SelectListUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.vo.VOOrderSample;
import com.sgs.ecom.order.vo.sys.VOSysSampleBasic;
import com.sgs.redis.RedisClient;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderSampleServiceImpl extends BaseService implements IOrderSampleService {

    @Autowired
    private OrderSampleMapper orderSampleMapper;
    @Autowired
    private IOrderDetailService orderDetailService;
    @Autowired
    private IOrderSampleRelateService orderSampleRelateService;
    @Autowired
    private IOrderUtilService orderUtilService;
    @Autowired
    private IOrderBaseInfoService orderBaseInfoService;
    @Autowired
    private IOrderSampleFromService orderSampleFromService;
    @Autowired
    private IOrderAttachmentService orderAttachmentService;
    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Autowired
    private IOrderProductService orderProductService;
    @Autowired
    private ICenterTemplateSV centerTemplateSV;
    @Resource
    private IOrderAttachmentDomainService orderAttachmentDomainService;
    @Resource
    private  IMemberTemplateService memberTemplateService;
    @Resource
    private  RedisClient redisClient;
    @Resource
    private IOrderSampleFromDomainService orderSampleFromDomainService;
    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;

    @Resource
    private IOrderSampleRelateDomainService orderSampleRelateDomainService;
    @Resource
    private IOrderDetailDomainService orderDetailDomainService;
    @Resource
    private IOrderApplicationAttrService orderApplicationAttrService;
    @Resource
    private OiqApplicationProperties oiqSampleProperties;
    @Resource
    private IOrderSampleDomainService orderSampleDomainService;
    @Resource
    private RedisUtils redisUtils;
    public String insertSelective(OrderSampleReq orderSampleReq,String orderNo,String groupNo) throws Exception{
        StringBuilder stringBuilder=new StringBuilder();
        int num=orderSampleReq.getNum();
        List<OrderSampleFrom> list=new ArrayList<>();
        for(int n=0;n<num;n++){
            VOOrderSample voOrderSampleAdd=new VOOrderSample();
            String sampleNo=orderUtilService.getNewSampleNo();
            voOrderSampleAdd.setOrderNo(orderNo);
            voOrderSampleAdd.setSampleName(orderSampleReq.getSampleName());
            //开始无效
            voOrderSampleAdd.setState(1);

            voOrderSampleAdd.setCreateDate(UseDateUtil.getDateString(new Date()));
            voOrderSampleAdd.setStateDate(UseDateUtil.getDateString(new Date()));
            voOrderSampleAdd.setGroupNo(groupNo);
            voOrderSampleAdd.setSampleNo(sampleNo);
            stringBuilder.append(sampleNo);
            stringBuilder.append(",");
            if(!ValidationUtil.isEmpty(orderSampleReq.getSampleFromDTOList())){
                for(OrderSampleFromDTO orderSampleFromDTO:orderSampleReq.getSampleFromDTOList()){
                    OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
                    baseCopyObj.copyWithNull(orderSampleFrom,orderSampleFromDTO);
                    orderSampleFrom.setState(1);
                    orderSampleFrom.setOrderNo(orderNo);
                    orderSampleFrom.setGroupNo(groupNo);
                    orderSampleFrom.setSampleNo(sampleNo);
                    list.add(orderSampleFrom);
                }
            }

            //样品重新赋值
            Map<String, Object> collectMap = orderSampleReq.getSampleFromDTOList().stream().collect(Collectors.toMap(E->E.getSampleKey(),
                    E->E.getSampleValue(), (key1, key2) -> key2));
            //样品重新赋值
            voOrderSampleAdd.setSampleNameCn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_CN,"").toString());
            voOrderSampleAdd.setSampleNameEn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_EN,"").toString());
            //样品的中英文赋值
            orderSampleMapper.insertVOSelective(voOrderSampleAdd);
        }
        String str=stringBuilder.toString();
        if(str.length()>0){
            str=str.substring(0,str.length()-1);
        }
       // orderSampleFromService.insertForeach(list);
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseOrder(orderNo);
        orderSampleFromDomainService.insertForeachAddCenter(list,baseOrderDTO.getApplicationLineId());
        return str;
    }

    @Override
    public void insertForeach(List<VOOrderSample> voOrderSampleList) {
        orderSampleMapper.insertForeach(voOrderSampleList);
    }

    public void insertSelective(VOOrderSample voOrderSample){
        orderSampleMapper.insertVOSelective(voOrderSample);
    }


    public void updateByPrimaryKeySelective(OrderSampleReq orderSamleReq,Long sampleId) {
        VOOrderSample voOrderSampleUpdate=new VOOrderSample();

        voOrderSampleUpdate.setSampleId(sampleId);
        voOrderSampleUpdate.setMaterialGrade(orderSamleReq.getMaterialGrade());
        voOrderSampleUpdate.setSampleName(orderSamleReq.getSampleName());
        voOrderSampleUpdate.setSampleNameEn(orderSamleReq.getSampleNameEn());
        voOrderSampleUpdate.setSampleNameCn(orderSamleReq.getSampleNameCn());
        voOrderSampleUpdate.setProductBatch(orderSamleReq.getProductBatch());
        voOrderSampleUpdate.setProductInfo(orderSamleReq.getProductInfo());
        voOrderSampleUpdate.setRemark(orderSamleReq.getRemark());
        //开始无效
        voOrderSampleUpdate.setStateDate(UseDateUtil.getDateString(new Date()));
         orderSampleMapper.updateVOByPrimaryKeySelective(voOrderSampleUpdate);

    }

    @Master
    @Transactional
    public void deleteByPrimaryKey(OrderSampleReq orderSampleReq,BOSysPerson boSysPerson, PrivilegeLevelDTO privilegeLevelDTO){

        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderSampleReq.getOrderId(),boSysPerson,privilegeLevelDTO);

        if(String.valueOf(orderBaseInfoCheckDTO.getState()).equals(OrderStateEnum.CLOSE.getIndex())){
            throw new BusinessException(ResultEnumCode.ORDER_STATE_ERROR);
        }
        String tmpGroupNo=orderDetailService.getSelectGroup(orderBaseInfoCheckDTO);
        Map<String,Object> map=new HashMap<>();
        map.put(SelectMapUtil.ORDER_NO,orderBaseInfoCheckDTO.getOrderNo());
        map.put(SelectMapUtil.GROUP_NO,tmpGroupNo);
        map.put(SelectMapUtil.SAMPLE_NO,orderSampleReq.getSampleNo());
        List<OrderSampleDTO> orderSampleDTOS=selectListByMap(map);
        if(orderSampleDTOS.size()>0){
            Long sampleId=orderSampleDTOS.get(0).getSampleId();
            orderSampleMapper.deleteByPrimaryKey(sampleId);
            orderSampleRelateService.delRelateBySampleNo(tmpGroupNo,orderSampleReq.getSampleNo());
        }

    }

    @Override
   @Master
    public JSONArray qrySampleList(OrderSampleReq orderSampleReq, BOSysPerson boSysPerson, PrivilegeLevelDTO privilegeLevelDTO) throws Exception{
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderSampleReq.getOrderId(),boSysPerson,privilegeLevelDTO);
        String selectGroup="";
        if(StringUtils.isNotBlank(orderSampleReq.getGroupNo())){
            selectGroup=orderSampleReq.getGroupNo();
        }else{
            selectGroup=StringUtils.isNotBlank(orderBaseInfoCheckDTO.getTmpGroupNo())?orderBaseInfoCheckDTO.getTmpGroupNo():orderBaseInfoCheckDTO.getGroupNo();
        }
        //groupNo为空表示没有样品
        if(StringUtils.isBlank(selectGroup)){
            return jsonTransUtil.toJSONArray(OiqSampleDTO.class,new JSONArray(), BaseOrderFilter.OiqFormInfo.class);
        }

        Boolean flg=String.valueOf(orderBaseInfoCheckDTO.getOrderType()).equals(OrderTypeEnum.OIQ_ORDER.getIndex())?true:false;
        if(OrderSubStateEnum.QUESTION_FLG.getIndex()==orderBaseInfoCheckDTO.getSubState()){
            return jsonTransUtil.toJSONArray(OrderSampleDTO.class,new JSONArray(), BaseQryFilter.Default.class);
        }
        return  jsonTransUtil.toJSONArray(OiqSampleDTO.class,qrySampleMoreList(orderBaseInfoCheckDTO.getOrderNo(),selectGroup,flg), BaseOrderFilter.OiqFormInfo.class);
    }

    //flg=true 订单要剔除没选中的测试项目
    @Override
    public List<OrderSampleDTO> qrySampleList(String orderNo,String selectGroup,Boolean flg) {

        Map<String,Object> map=new HashMap<>();
        map.put(SelectMapUtil.ORDER_NO,orderNo);
        map.put(SelectMapUtil.GROUP_NO,selectGroup);
        List<OrderSampleDTO> orderSampleDTOS=selectListByMap(map);
        //样品添加中标数据
        map.put(SelectBaseUtil.STATE,1);
        List<OrderSampleFromDTO> orderSampleFromDTOList=orderSampleFromService.selectListByMap(map);
        Map<String,List<OrderSampleFromDTO>> sampleNoMap=orderSampleFromDTOList.stream().collect(Collectors.groupingBy(E ->E.getSampleNo()));
        for(int n=0;n<orderSampleDTOS.size();n++){
            OrderSampleDTO orderSampleDTO=orderSampleDTOS.get(n);
            if(sampleNoMap.containsKey(orderSampleDTO.getSampleNo())){
                List<OrderSampleFromDTO> list=sampleNoMap.get(orderSampleDTO.getSampleNo());
                orderSampleDTO.setSampleFromDTOList(list.stream().sorted(Comparator.comparing(E->E.getSortShow())).collect(Collectors.toList()));
            }
        }
        if(!flg){
            return orderSampleDTOS;
        }
        return orderSampleDTOS.stream().filter(a -> a.getState()==1).collect(Collectors.toList());
    }


    public List<OiqSampleDTO> qrySampleMoreList(String orderNo,String selectGroup,Boolean flg)throws Exception {
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(orderNo);
        return orderSampleDomainService.getOiqOrderSample(orderNo,selectGroup,baseOrderDTO.getApplicationLineId(),flg);
    }

    @Override
    public List<OrderSampleDTO> selectListByMap(Map map){
       return orderSampleMapper.selectListByMap(map);
    }
    public List<OrderSampleMoreDTO> selectMoreListByMap(Map map){
        return orderSampleMapper.selectMoreListByMap(map);
    }

    @Override
    public void updateNewGroup(String oldGroup,String newGroup,String orderNo) {
        Map<String,Object> map=new HashMap<>();
        map.put("oldGroupNo",oldGroup);
        map.put("newGroupNo",newGroup);
        map.put(SelectMapUtil.ORDER_NO,orderNo);
        orderSampleMapper.updateNewGroup(map);
        orderSampleFromService.updateNewGroup(map);

    }


    @Override
    public void updateStateByRelate(String orderNo, String groupNo,List<String> sampleNoList) {
        Map<String,Object> map=new HashMap<>();
        map.put(SelectMapUtil.GROUP_NO,groupNo);
        map.put(SelectMapUtil.ORDER_NO,orderNo);
        map.put(SelectListUtil.SAMPLE_NO_LIST,sampleNoList);
        orderSampleMapper.updateStateByRelate(map);
    }

    @Override
    public List<OrderSampleNameDTO> selectSampleName(Map map) {
        return orderSampleMapper.selectSampleName(map);
    }

    @Override
    public List<String> selectSampleNo(String orderNo, String groupNo) {
        Map<String,Object> map=new HashMap<>();
        map.put(SelectMapUtil.ORDER_NO,orderNo);
        map.put(SelectMapUtil.GROUP_NO,groupNo);
        return orderSampleMapper.selectSampleNo(map);
    }

    @Override
    public void delOrderSampleByGroup(String oldGroup) {
        orderSampleMapper.delOrderSampleByGroup(oldGroup);
    }

    /**
    *@Function: orderSampleList
    *@Description 查询出订单样品的数据
    *@param: [orderSampleReq, personInfo, power]
    *@author: Xiwei_Qiu @date: 2022/5/7 @version:
    **/
    public JSONObject orderSampleList(OrderIdReq orderIdReq, BOSysPerson personInfo, PrivilegeLevelDTO power) throws Exception{
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderIdReq.getOrderId(),personInfo,power);
        return jsonTransUtil.toJSONString(OrderSampleMoreDTO.class, orderSampleList(orderBaseInfoCheckDTO.getOrderNo(),orderBaseInfoCheckDTO.getGroupNo()), BaseQryFilter.Default.class);
    }




    public  List<OrderSampleMoreDTO> orderSampleList(String orderNo,String groupNo) throws Exception{
        Map<String,Object> map=new HashMap<>();
        map.put(SelectBaseUtil.ORDER_NO,orderNo);
        map.put(SelectBaseUtil.GROUP_NO,groupNo);
        List<OrderSampleMoreDTO> orderSampleMoreDTOList=selectMoreListByMap(map);
        if(ValidationUtil.isEmpty(orderSampleMoreDTOList)){
          return new ArrayList<>();
        }
        Map<String,List<OrderAttachmentDTO>> fileMap=new HashMap<>();
        Map<String,List<OrderAttachmentDTO>> rstsSDSFileMap=new HashMap<>();
        Map<String,List<OrderAttachmentDTO>> allFileMap=new HashMap<>();
        BaseOrderDTO baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(orderNo);
        Boolean isTic=false;
        Map<String, Object> productMap = new HashMap<>();
        productMap.put(SelectBaseUtil.ORDER_NO, baseOrderDTO.getOrderNo());
        List<OrderProductDTO> orderProductDTOList = orderProductService.selectListByMap(productMap);
        String subBuCode = "";
        if(!ValidationUtil.isEmpty(orderProductDTOList)){
            subBuCode = orderProductDTOList.get(0).getSubBuCode();

        }
        List<SysSampleBasicDTO> sysSampleBasicDTOList = new ArrayList<>();
        Map<String, List<SysSampleBasicDTO>> sampleMap = new HashMap<>();
        if(OrderTypeEnum.TIC.getIndex().equals(baseOrderDTO.getOrderType())){
            //查询所有的附件信息
            List<OrderAttachmentDTO> allFileList =   orderAttachmentDomainService.qryListByMap(baseOrderDTO.getOrderNo());
            List<OrderAttachmentDTO> list= new ArrayList<>();
            List<OrderAttachmentDTO> rstsOrderAttachmentDTOS = new ArrayList<>();
            if(!ValidationUtil.isEmpty(allFileList)){
                allFileMap = allFileList.stream().filter(orderAttachmentDTO -> !ValidationUtil.isEmpty(orderAttachmentDTO.getGroupNo())).collect(Collectors.groupingBy(E -> E.getGroupNo()));
                list = allFileList.stream().filter(orderAttachmentDTO -> orderAttachmentDTO.getAttType().equals(OrderAttachmentTypeEnum.INS_CHECK_IMG.getIndex())).collect(Collectors.toList());
                fileMap=list.stream().filter(orderAttachmentDTO -> !ValidationUtil.isEmpty(orderAttachmentDTO.getGroupNo())).collect(Collectors.groupingBy(E -> E.getGroupNo()));

//                String buCode = OrderBUTransitionUtil.transitionSubBuCode(baseOrderDTO.getBu(), subBuCode);
//
//                if(buCode.equals(StoreEnum.RSTS.getName())){//rsts专属附件
//                    rstsOrderAttachmentDTOS  = allFileList.stream().filter(orderAttachmentDTO -> orderAttachmentDTO.getAttType().equals(OrderAttachmentTypeEnum.RSTS_SDS_FILES.getIndex())).collect(Collectors.toList());
//                    rstsSDSFileMap=rstsOrderAttachmentDTOS.stream().filter(orderAttachmentDTO -> !ValidationUtil.isEmpty(orderAttachmentDTO.getGroupNo())).collect(Collectors.groupingBy(E -> E.getGroupNo()));
//                }
            }
            isTic=true;
//            List<OrderAttachmentDTO> list=orderAttachmentService.selectFileByAttType(orderNo, OrderAttachmentTypeEnum.INS_CHECK_IMG);
//            fileMap=list.stream().filter(orderAttachmentDTO -> !ValidationUtil.isEmpty(orderAttachmentDTO.getGroupNo())).collect(Collectors.groupingBy(E -> E.getGroupNo()));
//
//            String buCode = OrderBUTransitionUtil.transitionSubBuCode(baseOrderDTO.getBu(), subBuCode);
//            if(buCode.equals(StoreEnum.RSTS.getName())){//rsts专属附件
//                List<OrderAttachmentDTO> rstsOrderAttachmentDTOS = orderAttachmentService.selectFileByAttType(orderNo, OrderAttachmentTypeEnum.RSTS_SDS_FILES);
//                rstsSDSFileMap=rstsOrderAttachmentDTOS.stream().filter(orderAttachmentDTO -> !ValidationUtil.isEmpty(orderAttachmentDTO.getGroupNo())).collect(Collectors.groupingBy(E -> E.getGroupNo()));
//            }
            //当是tic订单的时候查询动态样品配置信息
            VOSysSampleBasic voSysSampleBasic = new VOSysSampleBasic(DynamicSamplesEnum.LVTHREE_CPCH_EFFICACY.getBusinessType()
                    ,subBuCode,1);
            sysSampleBasicDTOList =  memberTemplateService.qryDynamicSample(voSysSampleBasic);
            if(!ValidationUtil.isEmpty(sysSampleBasicDTOList)){
                sampleMap = sysSampleBasicDTOList.stream().collect(Collectors.groupingBy(E ->E.getSampleKey()));
            }
        }
        OrderSampleFromDO orderSampleFromDO = new OrderSampleFromDO();
        //查询样品与套餐的关联关系
        List<OrderSampleRelateDTO> orderSampleRelationDTOList =  orderSampleRelateDomainService.selectListByOrderNo(orderNo,null);
        Map<String, List<OrderSampleRelateDTO>> sampleNoRelationMap =  new HashMap<>();
        if(!ValidationUtil.isEmpty(orderSampleRelationDTOList)){
            sampleNoRelationMap = orderSampleRelationDTOList.stream().collect(Collectors.groupingBy(E -> E.getSampleNo()));
        }
        //查询出套餐信息
        List<OrderDetailDTO> orderDetailDTOList = orderDetailDomainService.selectListByOrderNo(orderNo, null);
        List<OrderDetailDTO> packageDetailDTOList = new ArrayList<>();
        if(!ValidationUtil.isEmpty(orderDetailDTOList)){
            packageDetailDTOList = orderDetailDTOList.stream().filter(orderDetailDTO -> orderDetailDTO.getItemType() == 1).collect(Collectors.toList());
        }
        //查询attr表数据
        Map<String,Object> qryMap=new HashMap<>();
        qryMap.put(SelectBaseUtil.ORDER_NO,orderNo);
        qryMap.put(SelectBaseUtil.STATE,1);
        qryMap.put(SelectMapUtil.NOT_AREA_CODE,Arrays.asList("labInfo"));
        List<OrderApplicationAttrDTO> orderApplicationAttrList = orderApplicationAttrService.selectListByMap(map);
        List<OrderApplicationAttrDTO> orderApplicationAttrDTOList = null;
        if(!ValidationUtil.isEmpty(orderApplicationAttrList)){
            Map<String, List<OrderApplicationAttrDTO>> stringListMap = orderApplicationAttrList.stream().collect(Collectors.groupingBy(E -> E.getAreaCode() + E.getAttrCode()));
            orderApplicationAttrDTOList = ValidationUtil.isEmpty(stringListMap)?null:stringListMap.get("ticAppFormDTO"+ AppFormEnum.METHODS_USED.getName());

        }
        orderSampleMoreDTOList=orderSampleMoreDTOList.stream().filter(a -> a.getState()==1).collect(Collectors.toList());
        map.put(SelectBaseUtil.STATE,1);
        List<OrderSampleFromDTO> orderSampleFromDTOList=orderSampleFromService.selectListByMap(map);
        Map<String,List<OrderSampleFromDTO>> sampleNoMap=orderSampleFromDTOList.stream().collect(Collectors.groupingBy(E ->E.getSampleNo()));

        String enumCache ="";
        List<SysEnumConfigDTO> lstEnum = null;
        boolean flag = false;//是否需要转换数据
        List<String> subBuList = Arrays.asList(subBuCode);
        String ruler  = redisUtils.getNewSplitShopRuler(baseOrderDTO.getBu(), subBuList);

        for(int n=0;n<orderSampleMoreDTOList.size();n++){
            OrderSampleMoreDTO orderSampleMoreDTO=orderSampleMoreDTOList.get(n);
            if(sampleNoMap.containsKey(orderSampleMoreDTO.getSampleNo())){
                List<OrderSampleFromDTO> list=sampleNoMap.get(orderSampleMoreDTO.getSampleNo());
                list=list.stream().sorted(Comparator.comparing(E->E.getSortShow())).collect(Collectors.toList());
                //当是bbc的时候 循环解出来
                if(isTic){

                    for(OrderSampleFromDTO orderSampleFromDTO:list){
                        String lua = orderSampleFromDTO.getLua();
                        List<SysSampleBasicDTO> sysSampleBasicDTOList1 = sampleMap.get(orderSampleFromDTO.getSampleKey());
                        if(!ValidationUtil.isEmpty(sysSampleBasicDTOList1)){
                            List<SysSampleBasicDTO> collect =sysSampleBasicDTOList1;
                            if(!ValidationUtil.isEmpty(lua)){
                                collect = sysSampleBasicDTOList1.stream().filter(orderSampleFromDTO1 -> lua.equals(orderSampleFromDTO1.getLua())).collect(Collectors.toList());
                            }
                            if(!ValidationUtil.isEmpty(collect)){
                                String sampleKey = orderSampleFromDTO.getSampleKey();
                                String sampleKeyName = orderSampleFromDTO.getSampleKeyName();
                                baseCopyObj.copy(orderSampleFromDTO,collect.get(0));
                                orderSampleFromDTO.setSampleKey(sampleKey);
                                orderSampleFromDTO.setSampleKeyName(sampleKeyName);
                                orderSampleFromDTO.setLua(lua);
                            }

                        }
                        if(!ValidationUtil.isEmpty(orderSampleFromDTO.getEnumConfig())){//枚举不是空
                            enumCache = redisClient.getValue("enumMap", orderSampleFromDTO.getEnumConfig());
                            addEnums(orderSampleFromDTO);
                            if(SplitOrderType.NEW_SPLIT.getName().equals(ruler)){//新规则
                                lstEnum = JSON.parseArray(enumCache, SysEnumConfigDTO.class);
                                if(!ValidationUtil.isEmpty(lstEnum) && lstEnum.size() >0){
                                    String sampleValue = orderSampleFromDTO.getSampleValue();
                                    if(!ValidationUtil.isEmpty(sampleValue) && !ValidationUtil.isEmpty(lstEnum)){
                                        orderSampleFromDTO.setSampleExplain(orderUtilService.getEnumStringByKeys(orderSampleFromDTO.getEnumConfig(), orderSampleFromDTO.getSampleValue()));
                                    }
                                }
                            }
                        }
                        //处理特殊字段
                        if(!ValidationUtil.isEmpty(orderSampleFromDTO.getShowAttr())){
                            ShowAttrDTO showAttrDTO = JSON.parseObject(orderSampleFromDTO.getShowAttr(), ShowAttrDTO.class);
                            orderSampleFromDTO.setShowAttrDTO(showAttrDTO);

                        }
                        if(CheckUtil.checkStringIsJSON(orderSampleFromDTO.getSampleValue())){
                            String jsonStr=orderSampleFromDTO.getSampleValue();
                            LinkedHashMap jsonMap =  JSON.parseObject(jsonStr,LinkedHashMap.class, Feature.OrderedField);
                            Iterator i = jsonMap.values().iterator();
                            int t=0;
                            TicSampleJOSNDTO ticSampleJOSNDTO=new TicSampleJOSNDTO();
                            while (i.hasNext()){
                                String value = i.next().toString();//取出值
                                t++;
                                if(!ValidationUtil.isEmpty(lstEnum) && lstEnum.size() >0){
                                    String sampleValue = orderSampleFromDTO.getSampleValue();
                                    if(!ValidationUtil.isEmpty(sampleValue) && !ValidationUtil.isEmpty(lstEnum) && !ValidationUtil.isEmpty(value) && t!=3){
                                        value =  orderUtilService.getEnumStringByKeys(orderSampleFromDTO.getEnumConfig(),value);
                                    }
                                }
                                if(t==1){
                                    ticSampleJOSNDTO.setSingleChoice(value);
                                }
                                if(t==2){
                                    ticSampleJOSNDTO.setMultipleChoice(value);
                                }
                                if(t==3){
                                    ticSampleJOSNDTO.setOther(value);
                                }
                            }
                            orderSampleFromDTO.setTicSampleFlg(1);
                            orderSampleFromDTO.setTicSampleJOSNDTO(ticSampleJOSNDTO);


                        }
//                        String buCode = OrderBUTransitionUtil.transitionSubBuCode(baseOrderDTO.getBu(), subBuCode);
//                        if(buCode.equals(StoreEnum.RSTS.getName()) &&
//                                orderSampleFromDTO.getSampleKey().equals("SDSIngredient") &&
//                                rstsSDSFileMap.containsKey(orderSampleMoreDTO.getSampleNo())){//RSTS申请表，并且是SGS附件
//                            List<OrderAttachmentDTO> orderAttachmentDTOS = rstsSDSFileMap.get(orderSampleMoreDTO.getSampleNo());
//                            orderSampleFromDTO.setRstsSDSFileList(!ValidationUtil.isEmpty(orderAttachmentDTOS)?orderAttachmentDTOS.stream().filter(orderAttachmentDTO -> orderAttachmentDTO.getAttType().equals(OrderAttachmentTypeEnum.RSTS_SDS_FILES.getIndex())).collect(Collectors.toList()):orderAttachmentDTOS);
//                        }
                        //公共的  根据sampleKey匹配对应的附件
                        List<OrderAttachmentDTO> orderAttachmentDTOS = allFileMap.get(orderSampleMoreDTO.getSampleNo());
                        orderSampleFromDTO.setOrderAttachmentFileList(!ValidationUtil.isEmpty(orderAttachmentDTOS)?orderAttachmentDTOS.stream()
                                .filter(orderAttachmentDTO -> orderSampleFromDTO.getSampleKey().equals(orderAttachmentDTO.getAttType()))
                                .collect(Collectors.toList())  :new ArrayList<>());

                        //为了SL-HZ-WB-REGULAR 的testMemo 字段没有sampleKeyName的值
                        if(ValidationUtil.isEmpty(orderSampleFromDTO.getSampleKeyName())){
                            orderSampleFromDTO.setSampleKeyName("客户备注");
                        }
                    }
                    //处理样品与套餐的关联关系
                    if(!ValidationUtil.isEmpty(orderSampleRelationDTOList) && !ValidationUtil.isEmpty(list)){//有样品关联关系
                        orderSampleFromDomainService.appendRelationDetails(list,sampleNoRelationMap,packageDetailDTOList,orderDetailDTOList,orderApplicationAttrDTOList);
                    }
                }
                if(!ValidationUtil.isEmpty(list)){
                    list=list.stream().sorted(Comparator.comparing(E->E.getSortShow())).collect(Collectors.toList());
                }
                orderSampleMoreDTO.setSampleFromDTOList(list);
                orderSampleMoreDTO.setAllFileList(allFileMap.get(orderSampleMoreDTO.getSampleNo()));
            }
            if(fileMap.containsKey(orderSampleMoreDTO.getSampleNo())){
                orderSampleMoreDTO.setFileReqList(fileMap.get(orderSampleMoreDTO.getSampleNo()));
            }
        }

        return orderSampleMoreDTOList;
    }

    @Override
    public List<OrderSampleMoreDTO> oiqOrderSampleList(String orderNo, String groupNo,Boolean useState) throws Exception{
        Map<String,Object> map=new HashMap<>();
        map.put(SelectBaseUtil.ORDER_NO,orderNo);
        map.put(SelectBaseUtil.GROUP_NO,groupNo);
        List<OrderSampleMoreDTO> orderSampleMoreDTOList=selectMoreListByMap(map);
        if(ValidationUtil.isEmpty(orderSampleMoreDTOList)){
            return new ArrayList<>();
        }
        if(useState){
            orderSampleMoreDTOList=orderSampleMoreDTOList.stream().filter(a -> a.getState()==1).collect(Collectors.toList());
        }
        map.put(SelectBaseUtil.STATE,1);
        List<OrderSampleFromDTO> orderSampleFromDTOList=orderSampleFromService.selectListByMap(map);
        Map<String,List<OrderSampleFromDTO>> sampleNoMap=orderSampleFromDTOList.stream().collect(Collectors.groupingBy(E ->E.getSampleNo()));

        for(int n=0;n<orderSampleMoreDTOList.size();n++){
            OrderSampleMoreDTO orderSampleMoreDTO=orderSampleMoreDTOList.get(n);
            if(sampleNoMap.containsKey(orderSampleMoreDTO.getSampleNo())){
                List<OrderSampleFromDTO> orderSampleFromDTOS=sampleNoMap.get(orderSampleMoreDTO.getSampleNo());
                orderSampleMoreDTO.setSampleFromDTOList(orderSampleFromDTOS.stream().sorted(Comparator.comparing(E->E.getSortShow())).collect(Collectors.toList()));
            }

        }
        return orderSampleMoreDTOList;
    }

    private void addEnums(OrderSampleFromDTO orderSampleFromDTO) throws Exception {//将枚举code转换成中文
        String enumConfig = orderSampleFromDTO.getEnumConfig();
        orderSampleFromDTO.setEnums(getEnumCache("enumMap", enumConfig,
                orderSampleFromDTO.getType() == 4 ? 1 : 0));
    }

    public List<SysEnumConfigDTO> getEnumCache(String enumMap,String mapKey,int isCascade) throws Exception {
        String enumCache = redisClient.getValue(enumMap, mapKey);
        if(ValidationUtil.isEmpty(enumCache))
            return new ArrayList<SysEnumConfigDTO>();

        List<SysEnumConfigDTO> lstEnum = JSON.parseArray(enumCache, SysEnumConfigDTO.class);
        if (isCascade == 0)
            return lstEnum;

        List<SysEnumConfigDTO> lstResult = convertToEnumData(lstEnum, "");
        return lstResult;
    }

    private List<SysEnumConfigDTO> convertToEnumData(List<SysEnumConfigDTO> lstEnum,String parentCode) {
        List<SysEnumConfigDTO> lst = new ArrayList<>();
        for (SysEnumConfigDTO enumConfig : lstEnum) {
            if (parentCode.equals(enumConfig.getParentEnumCode())) {
                List<SysEnumConfigDTO> children = convertToEnumData(lstEnum, enumConfig.getEnumCode());

                if (!children.isEmpty()) {
                    enumConfig.setChildren(children);
                }

                lst.add(enumConfig);
            }
        }
        return lst;
    }


    public void copySampleBySql(String orderNo,String newOrderNo,String oldGroupNo,String newGroupNo) {
        Map mapCopy = new HashMap();
        mapCopy.put("orderNo", orderNo);
        mapCopy.put("newOrderNo", newOrderNo);
        mapCopy.put("oldGroupNo", oldGroupNo);
        mapCopy.put("newGroupNo", newGroupNo);
        orderSampleMapper.copySampleBySql(mapCopy);
        orderSampleFromService.copySampleFormBySql(orderNo,newOrderNo,oldGroupNo,newGroupNo);
    }

    @Override
    public JSONObject qryBaseSample(CenterBusinessReq centerBusinessReq) throws Exception {
        BusinessLineDTO businessLineDTO=centerTemplateSV.qryByPlatformCode(centerBusinessReq.getBusinessCode());
        List<CenterSampleDTO> centerSampleDTOList=centerTemplateSV.qryBusiByLine(businessLineDTO.getConfigId(),BusinessLineDTO.LINE);
        OrderSampleDO orderSampleDO=new OrderSampleDO();
        int size=oiqSampleProperties.getSampleSizeByLine(businessLineDTO.getConfigCode());
        List<OrderSampleMoreDTO> sampleDTOList= orderSampleDO.centerSampleToSample(centerSampleDTOList, size);
        orderSampleFromDomainService.sampleFromAddEnum(sampleDTOList);
        return jsonTransUtil.transJSONString(OrderSampleMoreDTO.class,sampleDTOList, BaseOrderFilter.OiqFormInfo.class);
    }


}
