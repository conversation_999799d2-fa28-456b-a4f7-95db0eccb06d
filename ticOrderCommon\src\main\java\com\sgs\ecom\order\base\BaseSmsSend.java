package com.sgs.ecom.order.base;

import com.sgs.ecom.order.dto.send.mail.OiqOtherDTO;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;

import java.util.HashMap;
import java.util.Map;

public class BaseSmsSend {

	private String orderNo;
	private String orderType;
	private String sendPhone;
	private OiqSmsEnum oiqSmsEnum;
	private String buType;
	private Boolean useOrderId;
	private Map<String,Object> mapData=new HashMap<>();
	private Boolean isSub;//是否是子单
	private OiqOtherDTO oiqOtherDTO;
	public BaseSmsSend() {
	}

	public BaseSmsSend(String orderNo, String orderType,OiqSmsEnum oiqSmsEnum) {
		this.orderNo = orderNo;
		this.orderType = orderType;
		this.oiqSmsEnum=oiqSmsEnum;
	}

	public BaseSmsSend(String orderNo, String orderType, OiqSmsEnum oiqSmsEnum, OiqOtherDTO oiqOtherDTO) {
		this.orderNo = orderNo;
		this.orderType = orderType;
		this.oiqSmsEnum=oiqSmsEnum;
		this.oiqOtherDTO=oiqOtherDTO;
	}


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getSendPhone() {
		return sendPhone;
	}

	public void setSendPhone(String sendPhone) {
		this.sendPhone = sendPhone;
	}

	public OiqSmsEnum getOiqSmsEnum() {
		return oiqSmsEnum;
	}

	public void setOiqSmsEnum(OiqSmsEnum oiqSmsEnum) {
		this.oiqSmsEnum = oiqSmsEnum;
	}

	public String getBuType() {
		return buType;
	}

	public void setBuType(String buType) {
		this.buType = buType;
	}

	public Boolean getUseOrderId() {
		return useOrderId;
	}

	public void setUseOrderId(Boolean useOrderId) {
		this.useOrderId = useOrderId;
	}

	public Map<String, Object> getMapData() {
		return mapData;
	}

	public void setMapData(Map<String, Object> mapData) {
		this.mapData = mapData;
	}

	public Boolean getSub() {
		return isSub;
	}

	public void setSub(Boolean sub) {
		isSub = sub;
	}

	public OiqOtherDTO getOiqOtherDTO() {
		return oiqOtherDTO;
	}

	public void setOiqOtherDTO(OiqOtherDTO oiqOtherDTO) {
		this.oiqOtherDTO = oiqOtherDTO;
	}
}
