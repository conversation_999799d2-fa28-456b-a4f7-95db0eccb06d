package com.sgs.ecom.order.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.ItemCategoryDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqItemDTO;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.order.OtherExplainDTO;
import com.sgs.ecom.order.dto.rpc.dml.ItemsRpcDTO;
import com.sgs.ecom.order.entity.order.OrderDetail;
import com.sgs.ecom.order.request.oiq.OiqItemReq;
import com.sgs.ecom.order.util.CommonConstant;
import com.sgs.ecom.order.vo.VOOrderDetail;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class OrderDetailDO extends OrderDetail {

    private BaseCopyObj baseCopy = new BaseCopyObj();


    public OrderDetail getDetailByDmlRpc(BaseOrderDTO orderDTO, ItemsRpcDTO itemsRpcDTO,String dateStr){
        OrderDetail orderDetail=new OrderDetail();
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        baseCopyObj.copy(orderDetail,itemsRpcDTO);
        orderDetail.setOrderNo(orderDTO.getOrderNo());
        if("1900".equals(orderDTO.getBu())){
            orderDetail.setTotalPrice(itemsRpcDTO.getPrice().multiply(new BigDecimal(orderDetail.getBuyNums())));
        }else{
            orderDetail.setPrice(orderDetail.getTotalPrice().divide(new BigDecimal(orderDetail.getBuyNums()),2, BigDecimal.ROUND_HALF_UP));
        }
        orderDetail.setOtherExplain(JSON.toJSONString(new OtherExplainDTO("","")));
        orderDetail.setOrderNo(orderDTO.getOrderNo());
        orderDetail.setState(1);
        orderDetail.setIsDefault(1);
        orderDetail.setCreateDate(dateStr);
        orderDetail.setStateDate(dateStr);
        return orderDetail;
    }




    public OrderDetail itemToEntity(OiqItemReq item, OiqOrderReqDTO oiqOrderReqDTO){
        OrderDetail orderDetailAdd=new OrderDetail();
        baseCopy.copyWithNull(orderDetailAdd,item);
        orderDetailAdd.setOrderNo(oiqOrderReqDTO.getOrderNo());
        orderDetailAdd.setGroupNo(oiqOrderReqDTO.getGroupNo());
        orderDetailAdd.setState(1);
        orderDetailAdd.setIsDefault(1);
        orderDetailAdd.setBuyNums(1);
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getOiqOrderReq().getItemCategory()) && StringUtils.isNotBlank(oiqOrderReqDTO.getOiqOrderReq().getItemCategory().getCategoryPath())){
            orderDetailAdd.setCategoryPath(oiqOrderReqDTO.getOiqOrderReq().getItemCategory().getCategoryPath());
        }
        return orderDetailAdd;
    }

    public OrderDetail dmlItemToEntity(OiqItemReq item, String orderNo, String groupNo){
        OrderDetail orderDetailAdd=new OrderDetail();
        baseCopy.copyWithNull(orderDetailAdd,item);
        orderDetailAdd.setOrderNo(orderNo);
        orderDetailAdd.setGroupNo(groupNo);
        orderDetailAdd.setState(1);
        orderDetailAdd.setIsDefault(1);
        return orderDetailAdd;
    }


    public OrderDetail initMainByProductCS(String orderNo, VOOrderDetail orderDetailDTO, String dateStr) {
        OrderDetail orderDetail = new OrderDetail();
        baseCopy.copy(orderDetail, orderDetailDTO);
        orderDetail.setItemName(orderDetailDTO.getIsCs() == 1 ?"客服定制":orderDetailDTO.getItemName());
        orderDetail.setStandardCode("");
        orderDetail.setMemo(orderDetailDTO.getMemo());
        orderDetail.setItemType(1);
        orderDetail.setTestName(orderDetailDTO.getTestName());
        orderDetail.setStandardCode(orderDetailDTO.getStandardCode());
        orderDetail.setIsCs(orderDetailDTO.getIsCs());
        orderDetail.setProductId(orderDetailDTO.getProductId());
        orderDetail.setIsDefault(1);
        initOrderInfo(orderDetail, orderNo, dateStr);
        initCommon(orderDetail);
        return orderDetail;
    }

    private void initOrderInfo(OrderDetail orderDetail,String orderNo, String dateStr){
        orderDetail.setCreateDate(dateStr);
        orderDetail.setOrderNo(orderNo);
    }

    private void initCommon(OrderDetail orderDetail){
        orderDetail.setState(1);
    }

    public List<OrderDetail> initSubProduct(List<VOOrderDetail> subDetails, Long detailId, VOOrderDetail voOrderDetail, String dateStr) {
        List<OrderDetail> lstDetail = new ArrayList<>();
        for(VOOrderDetail detail : subDetails){

            OrderDetail orderDetail = new OrderDetail();
            baseCopy.copy(orderDetail, detail);
            orderDetail.setProductId(voOrderDetail.getProductId());
            orderDetail.setItemType(2);
            orderDetail.setBuyNums(1);
            orderDetail.setProductId(voOrderDetail.getProductId());
            if(!ValidationUtil.isEmpty(detailId)){
                orderDetail.setParentDetailId(detailId);
            }
            orderDetail.setTotalPrice(voOrderDetail.getPrice());
            orderDetail.setIsOptional(0);
            orderDetail.setIsDefault(1);
            initOrderInfo(orderDetail, voOrderDetail.getOrderNo(), dateStr);
            initCommon(orderDetail);
            lstDetail.add(orderDetail);
        }

        return lstDetail;
    }

    public OrderDetail copy(OrderDetailDTO orderDetailDTO, String dateStr) {
        OrderDetail orderDetail = new OrderDetail();
        baseCopy.copyWithNull(orderDetail, orderDetailDTO);
        orderDetail.setStateDate(dateStr);
        return orderDetail;
    }

    public void copySubDetail(List<VOOrderDetail> subDetails, Long detailId,  String dateStr, VOOrderDetail parentOrderDetailDTO) {
        VOOrderDetail orderDetailDTO = new VOOrderDetail();
        orderDetailDTO.setProductId(parentOrderDetailDTO.getProductId());
        orderDetailDTO.setItemType("2");
        orderDetailDTO.setBuyNums(1);
        if(!ValidationUtil.isEmpty(detailId)){
            orderDetailDTO.setParentDetailId(detailId);
        }
        orderDetailDTO.setIsCs(parentOrderDetailDTO.getIsCs());
        orderDetailDTO.setTotalPrice(parentOrderDetailDTO.getPrice());
        orderDetailDTO.setCreateDate(dateStr);
        orderDetailDTO.setOrderNo(parentOrderDetailDTO.getOrderNo());
        orderDetailDTO.setState(1);
        orderDetailDTO.setItemName(parentOrderDetailDTO.getItemName());
        orderDetailDTO.setStandardCode(parentOrderDetailDTO.getStandardCode());
        orderDetailDTO.setPrice(parentOrderDetailDTO.getPrice());
        orderDetailDTO.setTotalPrice(parentOrderDetailDTO.getPrice());
        orderDetailDTO.setIsOptional(0);
        orderDetailDTO.setIsDefault(1);
        subDetails.add(orderDetailDTO);
    }

    public List<OrderDetail> initParentDetail(List<VOOrderDetail> lstDetail) {
        List<OrderDetail> lst = new ArrayList<>();
        for(VOOrderDetail detail : lstDetail){
            OrderDetail orderDetail = new OrderDetail();
            baseCopy.copyWithNull(orderDetail, detail);
            orderDetail.setState(1);
            lst.add(orderDetail);

            if (detail.getSubDetails().isEmpty()) {
                continue;
            }

            lst.addAll(initSubDetail(detail.getProductId(), detail.getSubDetails()));
        }
        return lst;
    }

    private List<OrderDetail> initSubDetail(Long productId, List<VOOrderDetail> lstDetail) {
        List<OrderDetail> lst = new ArrayList<>();
        for(VOOrderDetail detail : lstDetail) {
            OrderDetail orderDetail = new OrderDetail();
            baseCopy.copyWithNull(orderDetail, detail);
            orderDetail.setProductId(productId);
            orderDetail.setState(1);
            lst.add(orderDetail);
        }
        return lst;
    }

    public static ItemCategoryDTO listToItemCategory(List<OiqItemDTO> list){
        ItemCategoryDTO itemCategoryDTO=new ItemCategoryDTO();
        for(OiqItemDTO oiqItemDTO:list){
            itemCategoryDTO.setCategoryPath(oiqItemDTO.getCategoryPath());
        }
        return itemCategoryDTO;
    }
}
