package com.sgs.ecom.order.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.oiq.OiqOrderLinkDTO;
import com.sgs.ecom.order.dto.user.UserInvoiceDTO;


import java.util.ArrayList;
import java.util.List;

public class OiqOrderInvoiceDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String payerName;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String payerPhone;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String payerEmail;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqOrderLinkDTO> orderLinkList=new ArrayList<>();
    private Long invoiceId;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Integer invoiceType;
    @BeanAnno(dtocls = {UserInvoiceDTO.class})
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private UserInvoiceDTO invoice=new UserInvoiceDTO();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqAddressDTO> addressList=new ArrayList<>();

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getPayerPhone() {
        return payerPhone;
    }

    public void setPayerPhone(String payerPhone) {
        this.payerPhone = payerPhone;
    }

    public String getPayerEmail() {
        return payerEmail;
    }

    public void setPayerEmail(String payerEmail) {
        this.payerEmail = payerEmail;
    }



    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }




    public UserInvoiceDTO getInvoice() {
        return invoice;
    }

    public void setInvoice(UserInvoiceDTO invoice) {
        this.invoice = invoice;
    }




    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public List<OiqOrderLinkDTO> getOrderLinkList() {
        return orderLinkList;
    }

    public void setOrderLinkList(List<OiqOrderLinkDTO> orderLinkList) {
        this.orderLinkList = orderLinkList;
    }

    public List<OiqAddressDTO> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<OiqAddressDTO> addressList) {
        this.addressList = addressList;
    }
}
