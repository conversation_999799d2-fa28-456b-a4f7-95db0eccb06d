package com.sgs.ecom.order.cust.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.order.cust.service.interfaces.ICustInvoiceSV;
import com.sgs.ecom.order.util.ResultBody;

//@RestController
//@RequestMapping("/business/api.v1.cust/invoice")
public class CustInvoiceController extends BaseAction {

	private static ICustInvoiceSV custInvoiceSV = CollectionService.getService(ICustInvoiceSV.class);
	
//    /**   
//	* @Function: addInvoice
//	* @Description: 新增月结发票
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "add", method = { RequestMethod.POST })
//    public ResultBody addInvoice(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//		custInvoiceSV.addInvoice(data, getUserInfo(token));
//		return ResultBody.success();
//	}
    
//    /**   
//	* @Function: modInvoice
//	* @Description: 修改月结发票
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "mod", method = { RequestMethod.POST })
//    public ResultBody modInvoice(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//		custInvoiceSV.modInvoice(data, getUserInfo(token));
//		return ResultBody.success();
//	}
    
//    /**   
//	* @Function: delInvoice
//	* @Description: 删除月结发票
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "del", method = { RequestMethod.POST })
//    public ResultBody delInvoice(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//		custInvoiceSV.delInvoice(data, getUserInfo(token));
//		return ResultBody.success();
//	}
//    
//    /**   
//	* @Function: qryInvoice
//	* @Description: 查询月结发票
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "qry", method = { RequestMethod.POST })
//    public ResultBody qryInvoice(@RequestBody String data) throws Exception {
//		return ResultBody.newInstance(custInvoiceSV.qryInvoice(data));
//	}
//    
//    /**   
//	* @Function: qryInvoiceDtl
//	* @Description: 查询月结发票详情
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "qryDtl", method = { RequestMethod.POST })
//    public ResultBody qryInvoiceDtl(@RequestBody String data) throws Exception {
//		return ResultBody.newInstance(custInvoiceSV.qryInvoiceDtl(data));
//	}
//    
//    /**   
//	* @Function: qryUnbindInvoice
//	* @Description: 查询未关联发票
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "qryUnbind", method = { RequestMethod.POST })
//    public ResultBody qryUnbindInvoice(@RequestBody String data) throws Exception {
//    	return ResultBody.newInstance(custInvoiceSV.qryUnbindInvoice(data));
//	}
}
