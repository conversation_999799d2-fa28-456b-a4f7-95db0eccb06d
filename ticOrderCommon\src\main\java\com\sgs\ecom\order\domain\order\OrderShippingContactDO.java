package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.oiq.OiqOrderDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OrderShippingContactDTO;
import com.sgs.ecom.order.entity.order.OrderShippingContact;
import com.sgs.ecom.order.request.oiq.OiqOrderReq;
import com.sgs.ecom.order.request.oiq.OrderShippingContactReq;
import com.sgs.ecom.order.util.UseDateUtil;


import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrderShippingContactDO extends OrderShippingContact {

    public static final String SHIPPER="shipper";

    public static final String CONSIGNEE="consignee";

    public static final String NOTIFY="notify";

    public static final String MANUFACTURE="manufacture";

    public static OrderShippingContact getEntityByReq(OrderShippingContactReq orderShippingContactReq, String orderNo){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderShippingContact orderShippingContact=new OrderShippingContact();
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        baseCopyObj.copyWithNull(orderShippingContact,orderShippingContactReq);
        orderShippingContact.setOrderNo(orderNo);
        orderShippingContact.setStateDate(dateStr);
        orderShippingContact.setCreateDate(dateStr);
        orderShippingContact.setState(1);
        return orderShippingContact;

    }

    public static List<OrderShippingContactReq> reqToList(OiqOrderReq oiqOrderReq){
        List<OrderShippingContactReq> list=new ArrayList<>();
        OrderShippingContactReq shipper=oiqOrderReq.getShipper();
        if(!ValidationUtil.isEmpty(shipper)){
            shipper.setContactType(SHIPPER);
            list.add(shipper);
        }
        OrderShippingContactReq consignee=oiqOrderReq.getConsignee();
        if(!ValidationUtil.isEmpty(consignee)){
            consignee.setContactType(CONSIGNEE);
            list.add(consignee);
        }
        OrderShippingContactReq notify=oiqOrderReq.getNotify();
        if(!ValidationUtil.isEmpty(notify)){
            notify.setContactType(NOTIFY);
            list.add(notify);
        }
        OrderShippingContactReq manufacture=oiqOrderReq.getManufacture();
        if(!ValidationUtil.isEmpty(manufacture)){
            manufacture.setContactType(MANUFACTURE);
            list.add(manufacture);
        }
        return list;
    }


    public static void listToOiqDTO(OiqOrderDTO oiqOrderDTO, List<OrderShippingContactDTO> list){
        if(ValidationUtil.isEmpty(list)){
            return;
        }
        Map<String,OrderShippingContactDTO> map=list.stream().collect(Collectors.toMap(OrderShippingContactDTO::getContactType, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));

        if(map.containsKey(SHIPPER)){
            oiqOrderDTO.setShipper(map.get(SHIPPER));
        }
        if(map.containsKey(CONSIGNEE)){
            oiqOrderDTO.setConsignee(map.get(CONSIGNEE));
        }
        if(map.containsKey(NOTIFY)){
            oiqOrderDTO.setNotify(map.get(NOTIFY));
        }
        if(map.containsKey(MANUFACTURE)){
            oiqOrderDTO.setManufacture(map.get(MANUFACTURE));
        }
    }
}
