package com.sgs.ecom.order.controller.custom;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.service.custom.interfaces.ICustApplyRelateSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOCustApplyRelate;

@RestController
@RequestMapping("/business/api.v1.cust/relate")
public class CusyApplyRelateController extends ControllerUtil {

	@Autowired
	private ICustApplyRelateSV custApplyRelateSV;

    /**   
	* @Function: relateUser
	* @Description: 月结客户关联
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "relateUser", method = { RequestMethod.POST })
    public ResultBody relateUser(@RequestBody VOCustApplyRelate custApplyRelate,
    	@RequestHeader(value="accessToken") String token) throws Exception {
    	custApplyRelateSV.relateUser(custApplyRelate, getPersonInfo(token));
		return ResultBody.success();
	}
    
}
