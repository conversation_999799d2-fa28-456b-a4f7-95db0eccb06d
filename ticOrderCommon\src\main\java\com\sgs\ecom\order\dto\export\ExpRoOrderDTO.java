package com.sgs.ecom.order.dto.export;

import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.order.FromSourceEnum;
import com.sgs.ecom.order.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.order.enumtool.pay.MonthPayEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.util.collection.ListUtil;
import com.sgs.ecom.order.util.collection.StrUtil;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

public class ExpRoOrderDTO {
    
    private String orderNo;//订单号

    private String createDate;//下单时间

    private String confirmOrderDate;//5合1下单时间

    private String state;
    private String stateShow;

    private String payState;
    private String payStateShow;

    private String currency;
    
    private String companyNameCn;//公司名称
    
    private String realAmount;//总金额
    
    private String platformAmount;//实付总金额5合1
    
    private String relateOrderNo;//原订单号
    
    private String labName;//送样地区
    
    private String linkPerson;//客户联系人
    
    private String linkPhone;//客户手机号
    
    private String linkEmail;//客户邮箱
    
    private String salesCode;//销售账号
    
    private String csCode;//CS账号
    
    private String csEmail;//CS邮箱
    
    private String userName;//下单人

    private String fromSource;

    private String fromSourceShow;

    private int monthPay;

    private String monthPayShow;

    private String payMethod;
    private String payMethodShow;
    private String sumOnlineAmount;
    private String sumPay;
    private String toSumPay;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCreateDate() {
        return StrUtil.isTime(createDate);
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getConfirmOrderDate() {
        return StrUtil.isTime(confirmOrderDate);
    }

    public void setConfirmOrderDate(String confirmOrderDate) {
        this.confirmOrderDate = confirmOrderDate;
    }

    public String getCompanyNameCn() {
        return companyNameCn;
    }

    public void setCompanyNameCn(String companyNameCn) {
        this.companyNameCn = companyNameCn;
    }

    public String getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(String realAmount) {
        this.realAmount = realAmount;
    }

    public String getPlatformAmount() {
        return platformAmount;
    }

    public void setPlatformAmount(String platformAmount) {
        this.platformAmount = platformAmount;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public String getLinkEmail() {
        return linkEmail;
    }

    public void setLinkEmail(String linkEmail) {
        this.linkEmail = linkEmail;
    }

    public String getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(String salesCode) {
        this.salesCode = salesCode;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getStateShow() {
        return OrderStateEnum.getNameCh(state);
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public String getPayState() {
        return payState;
    }

    public void setPayState(String payState) {
        this.payState = payState;
    }

    public String getPayStateShow() {
        if(StringUtils.isBlank(payMethod)){
            return "未支付";
        }
        return "已支付";
    }

    public void setPayStateShow(String payStateShow) {
        this.payStateShow = payStateShow;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getFromSourceShow() {
        if(StringUtils.isNotBlank(fromSource)){
            return FromSourceEnum.getNameCh(fromSource);
        }
        return "";
    }

    public int getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(int monthPay) {
        this.monthPay = monthPay;
    }

    public String getMonthPayShow() {
        if(monthPay==0){
            return "现付";
        }
        if(monthPay==1){
            return "月结";
        }
        return "";
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getSumOnlineAmount() {
        return sumOnlineAmount;
    }

    public void setSumOnlineAmount(String sumOnlineAmount) {
        this.sumOnlineAmount = sumOnlineAmount;
    }

    public String getSumPay() {
        return sumPay;
    }

    public void setSumPay(String sumPay) {
        this.sumPay = sumPay;
    }

    public String getToSumPay() {
        return toSumPay;
    }

    public void setToSumPay(String toSumPay) {
        this.toSumPay = toSumPay;
    }

    public String getPayMethodShow() {
        if(StringUtils.isBlank(payMethod)){
            return "";
        }
        return PayMethodEnum.getNameCh(payMethod);

    }
}
