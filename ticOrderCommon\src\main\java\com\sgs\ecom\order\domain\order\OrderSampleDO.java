package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.order.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.order.dto.rpc.CenterSampleDTO;
import com.sgs.ecom.order.dto.rpc.dml.DmlOrderSampleDTO;
import com.sgs.ecom.order.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.order.entity.order.OrderSample;
import com.sgs.ecom.order.entity.order.OrderSampleFrom;
import com.sgs.ecom.order.request.oiq.OiqOrderSampleFromReq;
import com.sgs.ecom.order.request.oiq.OiqSampleReq;
import com.sgs.ecom.order.request.oiq.SampleCategoryReq;
import com.sgs.ecom.order.util.UseDateUtil;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class OrderSampleDO extends OrderSample {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public List<OrderSample> sampleFromListToSampleList(String orderNo,String groupNo,List<OrderSampleFrom> orderSampleFromList){
        List<OrderSample> list=new ArrayList<>();
        Map<String,List<OrderSampleFrom>> map=orderSampleFromList.stream().collect(Collectors.groupingBy(E ->E.getSampleNo()));
        String dateStr= UseDateUtil.getDateString(new Date());
        for(String key:map.keySet()){

            Map<String, Object> collectMap = map.get(key).stream().collect(Collectors.toMap(E->E.getSampleKey(),
                    E->E.getSampleValue(), (key1, key2) -> key2));
            OrderSample orderSample=new OrderSample();
            initState(orderSample,dateStr);
            //样品重新赋值
            orderSample.setSampleNameCn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_CN,"").toString());
            orderSample.setSampleNameEn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_EN,"").toString());

            orderSample.setOrderNo(orderNo);
            orderSample.setGroupNo(groupNo);
            orderSample.setSampleNo(key);


            list.add(orderSample);
        }

        return list;
    }

    public  List<OrderSampleMoreDTO> centerSampleToSample(List<CenterSampleDTO> centerSampleDTOList,int size){
        List<OrderSampleMoreDTO> list=new ArrayList<>();
        OrderSampleMoreDTO orderSampleMoreDTO=new OrderSampleMoreDTO();
        List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();
        for(CenterSampleDTO centerSampleDTO:centerSampleDTOList){
            OrderSampleFromDTO orderSampleFromDTO=new OrderSampleFromDTO();
            baseCopy.copyWithNull(orderSampleFromDTO,centerSampleDTO);
            if(orderSampleFromDTO.getFillLen()==0){
                orderSampleFromDTO.setFillLen(size);
            }
            sampleFromDTOList.add(orderSampleFromDTO);
        }
        orderSampleMoreDTO.setSampleFromDTOList(sampleFromDTOList);
        list.add(orderSampleMoreDTO);
        return list;
    }

    public OrderSample orderSampleAddSampleNo(OiqSampleReq oiqSampleReq,OiqOrderReqDTO oiqOrderReqDTO, String sampleNo, Map<String, String> collectMap){
        String dateStr=UseDateUtil.getDateString(new Date());
        OrderSample orderSample=new OrderSample();
        baseCopy.copyWithNull(orderSample,oiqSampleReq);
        initState(orderSample,dateStr);
        orderSample.setSampleNo(sampleNo);
        orderSample.setOrderNo(oiqOrderReqDTO.getOrderNo());
        orderSample.setGroupNo(oiqOrderReqDTO.getGroupNo());
        orderSample.setSampleNameCn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_CN,""));
        orderSample.setSampleNameEn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_EN,""));
        return orderSample;
    }

    public OrderSample orderSampleAddSampleNo(OiqSampleReq oiqSampleReq, OiqOrderReqDTO oiqOrderReqDTO, String sampleNo){
        String dateStr=UseDateUtil.getDateString(new Date());
        OrderSample orderSample=new OrderSample();
        baseCopy.copyWithNull(orderSample,oiqSampleReq);
        initState(orderSample,dateStr);
        orderSample.setSampleNo(sampleNo);
        orderSample.setOrderNo(oiqOrderReqDTO.getOrderNo());
        orderSample.setGroupNo(oiqOrderReqDTO.getGroupNo());
        return orderSample;
    }

    public OrderSample orderSampleAddSampleNo(String sampleNo,String orderNo,String groupNo){
        String dateStr=UseDateUtil.getDateString(new Date());
        OrderSample orderSample=new OrderSample();
        initState(orderSample,dateStr);
        orderSample.setSampleNo(sampleNo);
        orderSample.setOrderNo(orderNo);
        orderSample.setGroupNo(groupNo);
        return orderSample;
    }

    public List<OrderSampleFrom> orderSampleFormAddSampleNo(OiqSampleReq oiqSampleReq,String orderNo,String groupNo, String sampleNo){
        List<OrderSampleFrom> list=new ArrayList<>();
        for(OiqOrderSampleFromReq orderSampleFromDTO:oiqSampleReq.getSampleFromDTOList()){
            OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
            baseCopy.copy(orderSampleFrom,orderSampleFromDTO);
            orderSampleFrom.setSampleNo(sampleNo);
            orderSampleFrom.setOrderNo(orderNo);
            orderSampleFrom.setGroupNo(groupNo);
            orderSampleFrom.setState(1);
            list.add(orderSampleFrom);
        }
        return list;
    }

    private void initState(OrderSample orderSample,String dateStr) {
        orderSample.setState(1);
        orderSample.setCreateDate(dateStr);
        orderSample.setStateDate(dateStr);
    }




    public  OrderSampleMoreDTO sampleToMore(OrderSample orderSample,List<OrderSampleFrom> list){
        OrderSampleMoreDTO orderSampleMoreDTO=new OrderSampleMoreDTO();
        baseCopy.copy(orderSampleMoreDTO,orderSample);

        List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();
        for(OrderSampleFrom orderSampleFrom:list){
            OrderSampleFromDTO orderSampleFromDTO=new OrderSampleFromDTO();
            baseCopy.copy(orderSampleFromDTO,orderSampleFrom);
            sampleFromDTOList.add(orderSampleFromDTO);
        }
        orderSampleMoreDTO.setSampleFromDTOList(sampleFromDTOList);
        return orderSampleMoreDTO;
    }

    public List<OrderSampleFrom> orderSampleFormAddSampleNo(OiqSampleReq oiqSampleReq, OiqOrderReqDTO oiqOrderReqDTO, String sampleNo){
        List<OrderSampleFrom> list=new ArrayList<>();
        for(OiqOrderSampleFromReq orderSampleFromDTO:oiqSampleReq.getSampleFromDTOList()){
            OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
            baseCopy.copyWithNull(orderSampleFrom,orderSampleFromDTO);
            orderSampleFrom.setSampleNo(sampleNo);
            orderSampleFrom.setOrderNo(oiqOrderReqDTO.getOrderNo());
            orderSampleFrom.setGroupNo(oiqOrderReqDTO.getGroupNo());
            orderSampleFrom.setState(1);
            list.add(orderSampleFrom);
        }
        return list;
    }

    public List<OrderSampleFrom> orderSampleFormAddSampleNo(DmlOrderSampleDTO dmlOrderSampleDTO,  String sampleNo,String orderNo,String groupNo){
        List<OrderSampleFrom> list=new ArrayList<>();
        for(OiqOrderSampleFromReq orderSampleFromDTO:dmlOrderSampleDTO.getSampleAttr()){
            OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
            baseCopy.copyWithNull(orderSampleFrom,orderSampleFromDTO);
            orderSampleFrom.setSampleNo(sampleNo);
            orderSampleFrom.setOrderNo(orderNo);
            orderSampleFrom.setGroupNo(groupNo);
            orderSampleFrom.setSampleKeyName(orderSampleFromDTO.getSampleName());
            orderSampleFrom.setState(1);
            list.add(orderSampleFrom);
        }
        return list;
    }

    public static Map<String,Object>  getSampleFromDTOMap(List<OrderSampleFromDTO> list) {
        if(ValidationUtil.isEmpty(list)){
            return new HashMap();
        }
        return list.stream().collect(Collectors.toMap(OrderSampleFromDTO::getSampleKey, OrderSampleFromDTO::getSampleValue, (key1, key2) -> key2));
    }

    public List<OrderSampleMoreDTO>  sampleMoreAddCenterSample(List<OrderSampleMoreDTO> sampleDTOList,List<CenterSampleDTO> centerSampleDTOList,int size){
        if(ValidationUtil.isEmpty(centerSampleDTOList)){
            return sampleDTOList;
        }
        Map<String,CenterSampleDTO> centerSampleDTOMap=centerSampleDTOList.stream().collect(Collectors.toMap(CenterSampleDTO::getSampleKey, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
        for(int n=0;n<sampleDTOList.size();n++){
            OrderSampleMoreDTO orderSampleMoreDTO=sampleDTOList.get(n);
            List<OrderSampleFromDTO> list=orderSampleMoreDTO.getSampleFromDTOList();
            if(!ValidationUtil.isEmpty(list)){
                for(int t=0;t<list.size();t++){
                    OrderSampleFromDTO orderSampleFromDTO=list.get(t);
                    CenterSampleDTO centerSampleDTO=centerSampleDTOMap.getOrDefault(orderSampleFromDTO.getSampleKey(),new CenterSampleDTO());
                    orderSampleFromDTO.setIsMust(centerSampleDTO.getIsMust());
                    orderSampleFromDTO.setFillLen(centerSampleDTO.getFillLen());
                    orderSampleFromDTO.setRowNum(centerSampleDTO.getRowNum());
                    orderSampleFromDTO.setRegexRule(centerSampleDTO.getRegexRule());
                    orderSampleFromDTO.setAttrRules(centerSampleDTO.getAttrRules());
                    orderSampleFromDTO.setShowAttr(centerSampleDTO.getShowAttr());
                    orderSampleFromDTO.setType(centerSampleDTO.getType());
                    orderSampleFromDTO.setFillNotice(centerSampleDTO.getFillNotice());
                    if(StringUtils.isBlank(orderSampleFromDTO.getLua())){
                        orderSampleFromDTO.setLua(centerSampleDTO.getLua());
                    }
                    if(StringUtils.isBlank(orderSampleFromDTO.getSampleGroup())){
                        orderSampleFromDTO.setSampleGroup(centerSampleDTO.getSampleGroup());
                    }
                    if(orderSampleFromDTO.getFillLen()==0){
                        orderSampleFromDTO.setFillLen(size);
                    }
                    if(orderSampleFromDTO.getRowNum()==0){
                        orderSampleFromDTO.setRowNum(2);
                    }
                    if(StringUtils.isBlank(orderSampleFromDTO.getEnumConfig())){
                        orderSampleFromDTO.setEnumConfig(centerSampleDTO.getEnumConfig());
                    }
                    if(StringUtils.isBlank(orderSampleFromDTO.getRemarkEnumConfig())){
                        orderSampleFromDTO.setRemarkEnumConfig(centerSampleDTO.getRemarkEnumConfig());
                    }

                }

            }
        }
        return sampleDTOList;
    }


    public List<OrderSampleFromDTO>  sampleFormAddCenterSample(List<OrderSampleFromDTO> sampleDTOList,List<CenterSampleDTO> centerSampleDTOList,int size){
        if(ValidationUtil.isEmpty(centerSampleDTOList)){
            return sampleDTOList;
        }
        if(ValidationUtil.isEmpty(sampleDTOList)){
            return sampleDTOList;
        }

        Map<String,CenterSampleDTO> centerSampleDTOMap=centerSampleDTOList.stream().collect(Collectors.toMap(CenterSampleDTO::getSampleKey, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
        for(int t=0;t<sampleDTOList.size();t++){
            OrderSampleFromDTO orderSampleFromDTO=sampleDTOList.get(t);
            CenterSampleDTO centerSampleDTO=centerSampleDTOMap.getOrDefault(orderSampleFromDTO.getSampleKey(),new CenterSampleDTO());
            baseCopy.copyWithNull(orderSampleFromDTO,centerSampleDTO);
            if(orderSampleFromDTO.getFillLen()==0){
                orderSampleFromDTO.setFillLen(size);
            }

        }
        return sampleDTOList;
    }

    public static SampleCategoryDTO listToSampleCategory(List<OiqSampleDTO> sampleDTOList){
        SampleCategoryDTO sampleCategoryDTO=new SampleCategoryDTO();
        for(OiqSampleDTO oiqSampleDTO:sampleDTOList){
            sampleCategoryDTO.setSampleShapeCode(oiqSampleDTO.getSampleShapeCode());
            sampleCategoryDTO.setSampleCategoryCode(oiqSampleDTO.getSampleCategoryCode());
            sampleCategoryDTO.setSampleName(oiqSampleDTO.getSampleName());
        }
        return sampleCategoryDTO;
    }


    public List<OiqSampleReq>  addOther(List<OiqSampleReq> sampleList, SampleCategoryReq sampleCategory) {
        if(ValidationUtil.isEmpty(sampleList) || ValidationUtil.isEmpty(sampleCategory)){
            return sampleList;
        }
        if(StringUtils.isBlank(sampleCategory.getSampleCategoryCode()) && StringUtils.isBlank(sampleCategory.getSampleShapeCode())){
            return sampleList;
        }
        for(OiqSampleReq oiqSampleReq:sampleList){
            oiqSampleReq.setSampleCategoryCode(sampleCategory.getSampleCategoryCode());
            oiqSampleReq.setSampleShapeCode(sampleCategory.getSampleShapeCode());
            oiqSampleReq.setSampleName(sampleCategory.getSampleName());
        }
        return sampleList;

    }



}
