package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

@ExplainAnno(value="enum",name="余额操作类型")
public enum TransType implements EnumMessage {

	ORDER("订单支付", 100000), CREDIT("信用支付", 300000), CREDIT_REFUND("信用返回", 300001);
	
    // 成员变量  
    private String name;  
    private int index;  
    // 构造方法  
    private TransType(String name, int index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(int index) {  
        for (TransType c : TransType.values()) {  
            if (c.getIndex() == index) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public int getIndex() {  
        return index;  
    }  

	@Override
	public String getKey() {
		return String.valueOf(index);
	}

	@Override
	public String getValue() {
		return name;
	}  
}
