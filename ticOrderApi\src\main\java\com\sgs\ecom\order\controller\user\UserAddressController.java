package com.sgs.ecom.order.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.user.UserAddressListReq;
import com.sgs.ecom.order.service.user.interfaces.IUserAddressSV;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户地址Controller
 * @date 2024/12/11 15:01
 */
@RestController
@RequestMapping("/business/api.v1.user/address")
public class UserAddressController {
    @Resource
    private IUserAddressSV userAddressSV;

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "list", method = {RequestMethod.POST})
    public ResultBody list(@RequestBody UserAddressListReq userAddressListReq) throws Exception {
        return ResultBody.newInstance(userAddressSV.getPageList(userAddressListReq));
    }
}
