package com.sgs.ecom.order.domain.user;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.user.UserAddressDTO;
import com.sgs.ecom.order.entity.user.UserAddress;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;

public class UserAddressDO extends UserAddress {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public UserAddress getUserAddressByDTO(UserAddressDTO userAddressDTO, Long userId){
        UserAddress userAddress=new UserAddress();
        baseCopy.copy(userAddress,userAddressDTO);
        userAddress.setState(1);
        userAddress.setUserId(userId);
        String dateStr= UseDateUtil.getDateString(new Date());
        userAddress.setCreateDate(dateStr);
        userAddress.setStateDate(dateStr);
        return userAddress;
    }

    public UserAddressDTO entityToDTO(UserAddress userAddress){
        UserAddressDTO userAddressDTO=new UserAddressDTO();
        baseCopy.copyWithNull(userAddressDTO,userAddress);
        return userAddressDTO;
    }



}
