package com.sgs.ecom.order.base;

import com.platform.bo.BOSysPerson;
import com.sgs.ecom.order.dto.send.ApiOtherDTO;
import com.sgs.ecom.order.dto.send.TicOtherApiDTO;
import com.sgs.ecom.order.dto.send.WechatOtherApiDTO;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;

/**
 * <AUTHOR>
 */
public class BaseApi {

	private String orderNo;
	private TicOtherApiDTO ticOtherApiDTO;
	private EventEnum eventEnum;
	private WechatEnum wechatEnum;
	private WechatOtherApiDTO wechatOtherApiDTO;
	private BOSysPerson sysPerson;
	private ApiOtherDTO apiOtherDTO;

	/**
	 * 其他请求信息
	 */
	private String payload;


	public BaseApi() {
	}

	public BaseApi(EventEnum eventEnum) {
		this.eventEnum = eventEnum;
	}

	public BaseApi(String orderNo, EventEnum eventEnum) {
		this.orderNo = orderNo;
		this.eventEnum = eventEnum;
	}

	public BaseApi(String orderNo, TicOtherApiDTO ticOtherApiDTO, EventEnum eventEnum) {
		this.orderNo = orderNo;
		this.ticOtherApiDTO = ticOtherApiDTO;
		this.eventEnum = eventEnum;
	}

	public BaseApi(String orderNo, EventEnum eventEnum, ApiOtherDTO apiOtherDTO) {
		this.orderNo = orderNo;
		this.eventEnum = eventEnum;
		this.apiOtherDTO=apiOtherDTO;
	}

	public EventEnum getEventEnum() {
		return eventEnum;
	}

	public void setEventEnum(EventEnum eventEnum) {
		this.eventEnum = eventEnum;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public TicOtherApiDTO getTicOtherApiDTO() {
		return ticOtherApiDTO;
	}

	public void setTicOtherApiDTO(TicOtherApiDTO ticOtherApiDTO) {
		this.ticOtherApiDTO = ticOtherApiDTO;
	}

	public WechatEnum getWechatEnum() {
		return wechatEnum;
	}

	public void setWechatEnum(WechatEnum wechatEnum) {
		this.wechatEnum = wechatEnum;
	}

	public WechatOtherApiDTO getWechatOtherApiDTO() {
		return wechatOtherApiDTO;
	}

	public void setWechatOtherApiDTO(WechatOtherApiDTO wechatOtherApiDTO) {
		this.wechatOtherApiDTO = wechatOtherApiDTO;
	}

	public BOSysPerson getSysPerson() {
		return sysPerson;
	}

	public void setSysPerson(BOSysPerson sysPerson) {
		this.sysPerson = sysPerson;
	}

	public ApiOtherDTO getApiOtherDTO() {
		return apiOtherDTO;
	}

	public void setApiOtherDTO(ApiOtherDTO apiOtherDTO) {
		this.apiOtherDTO = apiOtherDTO;
	}

	public String getPayload() {
		return payload;
	}

	public void setPayload(String payload) {
		this.payload = payload;
	}
}
