<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!--定义日志文件的存储地址 勿在 LogBack 的配置中使用相对路径-->
    <!--设置日志目录-->
    <property name="log.path" value="/mnt/datadisk1/applogs" />
    <!--这里需要将value改成项目名称-->
    <property name="LOG_NAME" value="ticOrder"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceabilityId}] [%X{traceId}] [ppTraceId:  %X{PtxId} , ppSpanId: %X{PspanId}] [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>


    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/tic-order-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 日志文件的路径，按月滚动 -->
            <fileNamePattern>${log.path}/tic-order-info.%d{yyyy-MM-dd}.log.gz</fileNamePattern>
            <!-- 保留每月的历史日志一个月 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceabilityId}] [%X{traceId}] [ppTraceId:  %X{PtxId} , ppSpanId: %X{PspanId}] [%thread] [%X{requestId}] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
    </appender>
    <!--
    <logger name="com.sgs.ecom.center.dao" level="DEBUG"/>
    <logger name="com.sgs.ecom.center.dao" level="DEBUG">
        <appender-ref ref="CONSOLE"/>
    </logger>
    -->

    <!-- 日志输出级别 -->
    <root level="DEBUG">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>

</configuration>