package com.sgs.ecom.order.dto.pdf;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

public class QuotationFormDataDTO {

	private String orderNo;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String createDate;

	private int submitFlg;//0未提交 1已提交
	private String submitShow;

	private String to;//
	private String from;
	private String attn;
	private String contact;
	private String add;
	private String csEmail;
	private String tel;
	private String email;

	public QuotationFormDataDTO() {
	}

	public QuotationFormDataDTO(BaseOrderDTO baseOrderDTO) {
		this.orderNo = baseOrderDTO.getOrderNo();
		this.createDate = baseOrderDTO.getCreateDate();
	}

	public String getTo() {
		return to;
	}

	public void setTo(String to) {
		this.to = to;
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public String getAttn() {
		return attn;
	}

	public void setAttn(String attn) {
		this.attn = attn;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getAdd() {
		return add;
	}

	public void setAdd(String add) {
		this.add = add;
	}

	public String getCsEmail() {
		return csEmail;
	}

	public void setCsEmail(String csEmail) {
		this.csEmail = csEmail;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public int getSubmitFlg() {
		return submitFlg;
	}

	public void setSubmitFlg(int submitFlg) {
		this.submitFlg = submitFlg;
	}

	public String getSubmitShow() {
		return submitShow;
	}

	public void setSubmitShow(String submitShow) {
		this.submitShow = submitShow;
	}
}
