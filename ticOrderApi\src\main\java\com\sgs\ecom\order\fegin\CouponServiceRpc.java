package com.sgs.ecom.order.fegin;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.request.OrderSampleNoReq;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.service.user.interfaces.IUserCouponService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/rpc.v1.order/coupon")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class CouponServiceRpc {

	@Autowired
	private IUserCouponService userCouponService;


	@RequestMapping(value = "qryCoupon", method = { RequestMethod.POST })
	public ResultBody qryCoupon (
		@RequestBody OrderNoReq orderNoReq
		)throws Exception{
		return ResultBody.newInstance(userCouponService.qryCoupon(orderNoReq));
	}

	@RequestMapping(value = "qryUseCoupon", method = { RequestMethod.POST })
	public ResultBody qryUseCoupon (
		@RequestBody OrderNoReq orderNoReq
	)throws Exception{
		return ResultBody.newInstance(userCouponService.qryUseCoupon(orderNoReq));
	}
}
