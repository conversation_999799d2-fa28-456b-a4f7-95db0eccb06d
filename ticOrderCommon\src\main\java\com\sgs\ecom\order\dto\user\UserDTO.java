package com.sgs.ecom.order.dto.user;

import com.sgs.ecom.order.enumtool.user.UserSexEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;

public class UserDTO {
    //{"userId":12430,"userPhone":"17312190752","userName":"17312190752","userEmail":"<EMAIL>","userSex":0,"expDate":1596157092576,"appId":"99999999"}

    //


    private String userName;
    private String userPhone;
    private Integer userSex;
    private Long userId;
    private String userEmail;
    private String userNick;
    private int isTest;

    private int userIdFlg;//当flg为1的时候 相当于没有用户id

    private int csCodeFlg;//当是客服的动作 日志记录客服,userNick是写客服的code

    private String logUserShow;

    private List<Map> auths;

    //ro5合1登录会有下面几个值
    private String csCode;
    private String csEmail;
    private String csPhone;
    private String csName;


    public UserDTO() {
    }

    public UserDTO(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userNick;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public Integer getUserSex() {
        return userSex;
    }

    public void setUserSex(Integer userSex) {
        this.userSex = userSex;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserNick() {
        return userNick;
    }

    public void setUserNick(String userNick) {
        this.userNick = userNick;
    }

    public int getUserIdFlg() {
        return userIdFlg;
    }

    public void setUserIdFlg(int userIdFlg) {
        this.userIdFlg = userIdFlg;
    }

    public Integer getIsTest() {
        return isTest;
    }

    public void setIsTest(Integer isTest) {
        this.isTest = isTest;
    }

    public String getLogUserShow() {
        StringJoiner stringJoiner=new StringJoiner("");
        stringJoiner.add(StringUtils.isBlank(userNick)?"":userNick);
        int userSex1=userSex==null?0:userSex;
        if(userSex1==1 || userSex1==2){
            stringJoiner.add(" "+ UserSexEnum.getNameCh(userSex1));
        }
        if(StringUtils.isNotBlank(userPhone)){
            stringJoiner.add("("+userPhone+")");
        }else{
            stringJoiner.add("("+userEmail+")");
        }
        return stringJoiner.toString();
    }

    public void setLogUserShow(String logUserShow) {
        this.logUserShow = logUserShow;
    }

    public int getCsCodeFlg() {
        return csCodeFlg;
    }

    public void setCsCodeFlg(int csCodeFlg) {
        this.csCodeFlg = csCodeFlg;
    }

    public List<Map> getAuths() {
        return auths;
    }

    public void setAuths(List<Map> auths) {
        this.auths = auths;
    }

    public List<String> getProdList(UserDTO userDTO){
        List<Map> list=userDTO.getAuths();
        String key="HOTEL";
        List<String> prodList=new ArrayList<>();
        for(Map map:list){
            if(map.containsKey(key)){
                prodList.addAll((List)map.get(key));
            }
        }
        return prodList;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getCsPhone() {
        return csPhone;
    }

    public void setCsPhone(String csPhone) {
        this.csPhone = csPhone;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }
}
