package com.sgs.ecom.order.controller.order;


import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.bo.<PERSON><PERSON>ys<PERSON>erson;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.OrderChangePriceDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.request.exp.ExpDetailReq;
import com.sgs.ecom.order.request.log.LogAddReq;
import com.sgs.ecom.order.request.operator.AddOrderInfoReq;
import com.sgs.ecom.order.request.operator.OrderOperatorReq;
import com.sgs.ecom.order.request.operator.OrderSourceReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.order.interfaces.IOrderDetailService;
import com.sgs.ecom.order.service.order.interfaces.IOrderOperatorService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.util.ResultCode;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import com.sgs.ecom.order.vo.order.VOChangeOrderPrice;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;


@RestController
@RequestMapping("/business/api.v2.order/detail")
public class OrderDetailController extends ControllerUtil {

    @Resource
    private IOrderBaseInfoService orderBaseInfoService;
    @Resource
    private IOrderOperatorService orderOperatorService;
    @Resource
    private IOrderDetailService orderDetailService;


    /**
     * @Function: updatePrice
     * @Description 订单改价
     * @param: [orderPriceReq, token]
     * @author: Xiwei_Qiu @date: 2021/5/14 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4302", "4501", "4507"})
    @RequestMapping(value = "updatePrice", method = {RequestMethod.POST})
    public ResultBody updatePrice(
            @RequestBody OrderPriceReq orderPriceReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        BOSysPerson boSysPerson = getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO = gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(orderPriceReq.getOrderId(), boSysPerson, privilegeLevelDTO);
        orderOperatorService.updatePrice(orderPriceReq, orderBaseInfoCheckDTO, boSysPerson);
        return ResultBody.success();
    }

    /**
     * @param orderPriceReq:
     * @param token:
     * @Description: TIC订单改价
     * @Author: bowen zhang
     * @Date: 2022/5/12
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4302", "4501", "4507"})
    @RequestMapping(value = "updatePublicPrice", method = {RequestMethod.POST})
    public ResultBody updatePublicPrice(
            @RequestBody OrderPriceReq orderPriceReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.updatePublicPrice(orderPriceReq, getPersonInfo(token));
        return ResultBody.success();
    }

    /**
     * @param orderPriceReq:
     * @param token:
     * @Description : 批量改价接口
     * <AUTHOR> Zhang
     * @Date 2023/3/29
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4302", "4501", "4507"})
    @RequestMapping(value = "batchUpdatePublicPrice", method = {RequestMethod.POST})
    public ResultBody batchUpdatePublicPrice(
            @RequestBody OrderPriceReq orderPriceReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.batchUpdatePublicPrice(orderPriceReq, getPersonInfo(token));
        return ResultBody.success();
    }


    /**
     * @Function: addMemo
     * @Description 备注 加添加测试说明解释
     * @param: [logAddReq, token]
     * @author: Xiwei_Qiu @date: 2021/5/14 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4307", "4502", "4514"})
    @RequestMapping(value = "addMemo", method = {RequestMethod.POST})
    public ResultBody addMemo(
            @RequestBody LogAddReq logAddReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        BOSysPerson boSysPerson = getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO = null;
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(logAddReq.getOrderId(), boSysPerson, privilegeLevelDTO);
        if (OrderOperatorTypeEnum.getEnum(logAddReq.getOperatorType()) == null) {
            throw new BusinessException(ResultCode.ENUM_IS_NULL);
        }
        orderOperatorService.addMemo(orderBaseInfoCheckDTO, logAddReq, boSysPerson);
        return ResultBody.success();
    }

    /**
     * @Function: addOrderInfo
     * @Description
     * @param: [addOrderInfoReq, token]
     * @author: Xiwei_Qiu
     * @date: 2021/3/26
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = false)
    @RequestMapping(value = "addLeadsCode", method = {RequestMethod.POST})
    public ResultBody addOrderInfo(
            @RequestBody AddOrderInfoReq addOrderInfoReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.AddOrderInfo(addOrderInfoReq, getPersonInfo(token), getPower(token), false);
        return ResultBody.success();
    }

    /**
     * @Function: addTestLabel
     * @Description
     * @param: [addOrderInfoReq, token]
     * @author: Xiwei_Qiu
     * @date: 2021/3/26
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "addTestLabel", method = {RequestMethod.POST})
    public ResultBody addTestLabel(
            @RequestBody AddOrderInfoReq addOrderInfoReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.AddOrderInfo(addOrderInfoReq, getPersonInfo(token), getPower(token), true);
        return ResultBody.success();
    }

    /**
     * @Function: editOrderSource
     * @Description 修改订单来源
     * @param: [orderSourceReq, token]
     * @author: Xiwei_Qiu @date: 2022/6/8 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "editOrderSource", method = {RequestMethod.POST})
    public ResultBody editOrderSource(
            @RequestBody OrderSourceReq orderSourceReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.editOrderSource(orderSourceReq, getPersonInfo(token), getPower(token));
        return ResultBody.success();
    }

    /**
     * @param orderSourceReq:
     * @param token:
     * @Description : 批量修改订单来源
     * <AUTHOR> Zhang
     * @Date 2023/2/21
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "batchEditOrderSource", method = {RequestMethod.POST})
    public ResultBody batchEditOrderSource(
            @RequestBody OrderSourceReq orderSourceReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.batchEditOrderSource(orderSourceReq, getPersonInfo(token), getPower(token));
        return ResultBody.success();
    }


    /**
     * @Function: updatePerson
     * @Description 操作人
     * @param: [orderOperatorReq, token]
     * @author: Xiwei_Qiu @date: 2021/5/14 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4512"})
    @RequestMapping(value = "updatePerson", method = {RequestMethod.POST})
    public ResultBody updatePerson(
            @RequestBody OrderOperatorReq orderOperatorReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        BOSysPerson boSysPerson = getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO = gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(orderOperatorReq.getOrderId(), boSysPerson, privilegeLevelDTO);
        orderOperatorService.updatePerson(orderBaseInfoCheckDTO, orderOperatorReq, boSysPerson);
        return ResultBody.success();
    }

    /**
     * @Function: updatePerson
     * @Description 操作人(新)
     * @param: [orderOperatorReq, token]
     * @author: Xiwei_Qiu @date: 2021/5/14 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4512"})
    @RequestMapping(value = "updateOperator", method = {RequestMethod.POST})
    public ResultBody updateOperator(
            @RequestBody OrderOperatorReq orderOperatorReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.updateOperator(orderOperatorReq, getPersonInfo(token), getPower(token));
        return ResultBody.success();
    }

    /**
     * @param orderOperatorReq:
     * @param token:
     * @Description : 批量修改订单操作人
     * <AUTHOR> Zhang
     * @Date 2023/4/11
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4512"})
    @RequestMapping(value = "batchUpdateOperator", method = {RequestMethod.POST})
    public ResultBody batchUpdateOperator(
            @RequestBody OrderOperatorReq orderOperatorReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.batchUpdateOperator(orderOperatorReq, getPersonInfo(token), getPower(token));
        return ResultBody.success();
    }


    /**
     * @Function: updatePersonList
     * @Description 批量修改操作人
     * @param: [orderOperatorReq, token]
     * @author: Xiwei_Qiu @date: 2021/9/13 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "updatePersonList", method = {RequestMethod.POST})
    public ResultBody updatePersonList(
            @RequestBody OrderOperatorReq orderOperatorReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderOperatorService.updatePersonList(orderOperatorReq, getPersonInfo(token));
        return ResultBody.success();
    }

    /**
     * @Function: expDetail
     * @Description 导出项目
     * @param: [expDetailReq, token, res]
     * @author: Xiwei_Qiu @date: 2022/1/18 @version:
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "exp", method = {RequestMethod.POST})
    public void expDetail(
            @RequestBody ExpDetailReq expDetailReq,
            @RequestHeader(value = "accessToken") String token,
            HttpServletResponse res) throws Exception {
        orderOperatorService.expDetail(expDetailReq, getPersonInfo(token), getPower(token), res);
    }

    /**
     * @Function: queryTestItem
     * @Description 添加检测项目
     * @param: [expDetailReq, token, res]
     * @author: sundeqing @date: 2022/5/23 @version:
     **/
    @CheckType
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "queryTestItem", method = {RequestMethod.POST})
    public ResultBody queryTestItem(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody OrderReq orderReq) throws Exception {
        return ResultBody.newInstance(orderDetailService.queryTestItem(orderReq, getPersonInfo(token)));
    }

    /**
     * @Function: addTestItem
     * @Description 添加检测项目
     * @param: [expDetailReq, token, res]
     * @author: sundeqing @date: 2022/5/23 @version:
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "addTestItem", method = {RequestMethod.POST})
    public ResultBody addTest(
            @RequestBody ExpDetailReq expDetailReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderDetailService.addTestItem(expDetailReq, getPersonInfo(token), getPower(token));
        return ResultBody.success();
    }

    /**
     * @Function: updateTestItem
     * @Description 修改检测项目
     * @param: [expDetailReq, token, res]
     * @author: sundeqing @date: 2022/5/23 @version:
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "updateTest", method = {RequestMethod.POST})
    public ResultBody updateTest(
            @RequestBody ExpDetailReq expDetailReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        orderDetailService.updateTestItem(expDetailReq, getPersonInfo(token), getPower(token));
        return ResultBody.success();
    }

    /**
     * @Function: delectTestItem
     * @Description 修改检测项目
     * @param: [expDetailReq, token, res]
     * @author: sundeqing @date: 2022/5/23 @version:
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "deleteTestItem", method = {RequestMethod.POST})
    public ResultBody deleteTest(
            @RequestBody ExpDetailReq expDetailReq,
            @RequestHeader(value = "accessToken") String token,
            HttpServletResponse res) throws Exception {
        orderDetailService.deleteTestItem(expDetailReq, getPersonInfo(token), getPower(token));
        return ResultBody.success();
    }

    /**
     * @param token:
     * @param voOrderBaseInfo:
     * @Description: OIQ提醒客户付款的功能
     * @Author: bowen zhang
     * @Date: 2022/7/12
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "remindPayment", method = {RequestMethod.POST})
    public ResultBody remindPayment(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody VOOrderBaseInfo voOrderBaseInfo
    ) throws Exception {
        orderDetailService.remindPayment(voOrderBaseInfo, getPersonInfo(token));
        return ResultBody.success();
    }

    /**
     * @param token:
     * @param changeOrderPrice:
     * @Description :LV3新版订单改价
     * <AUTHOR> Zhang
     * @Date 2024/6/25
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "changeOrderPrice", method = {RequestMethod.POST})
    public ResultBody changeOrderPrice(
            @RequestHeader(value = "accessToken") String token,
            @Validated(BaseBean.Update.class)
            @RequestBody VOChangeOrderPrice changeOrderPrice
    ) throws Exception {
        OrderChangePriceDTO orderChangePriceDTO = orderDetailService.changeOrderPrice(changeOrderPrice, getPersonInfo(token));
        orderDetailService.sendChangeMailAndSms(orderChangePriceDTO);
        return ResultBody.success();
    }

    /**
     * @Function: updateOrderByAction
     * @Description 根据不同行为修改申请表订单信息
     * @param: [orderOperatorReq, token]
     * @author: Hengxu Song
     * @date: 2025/3/4
     * @version: 1.0.0
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true, permission = {"4512"})
    @RequestMapping(value = "updateOrderByAction", method = {RequestMethod.POST})
    public ResultBody updateOrderByAction(
            @RequestBody OrderOperatorReq orderOperatorReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        BOSysPerson boSysPerson = getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO = gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(orderOperatorReq.getOrderId(), boSysPerson, privilegeLevelDTO);
        orderOperatorService.updateOrderByAction(orderBaseInfoCheckDTO, orderOperatorReq, boSysPerson);
        orderOperatorService.sendNotification(orderBaseInfoCheckDTO, orderOperatorReq.getAction());
        return ResultBody.success();
    }
}
