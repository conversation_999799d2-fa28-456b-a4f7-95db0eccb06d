package com.sgs.ecom.order.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno;
import com.platform.annotation.IsNullAnno; 

public class CustInvoiceDTO extends BaseQryFilter{
 
 	public static final String CREATE_SQL = "select CUST_ID,TAX_NO,PROVICE,BANK_NUMBER,customer_number,STATE_DATE,STATE,memo,INVOICE_ID,CREATE_DATE,REG_ADDRESS,INVOICE_TYPE,COUNTRY,CITY,TOWN,INVOICE_TITLE,IS_DEFAULT,PAYMENT_CODE,REG_PHONE,BANK_NAME from TB_CUST_INVOICE"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	@ApiAnno(groups={QuerySummary.class,QueryList.class,QueryDtl.class},serviceName={"qryCust","qryCustByPaycode"})
 	@BeanAnno(value="TAX_NO", getName="getTaxNo", setName="setTaxNo")
 	private String taxNo;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="BANK_NUMBER", getName="getBankNumber", setName="setBankNumber")
 	private String bankNumber;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class})
 	@BeanAnno(value="customer_number", getName="getCustomerNumber", setName="setCustomerNumber")
 	private String customerNumber;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@IsNullAnno(serviceName={"modInvoice","qryInvoiceDtl"})
 	@ApiAnno(groups={QuerySummary.class,QueryList.class,QueryDtl.class},serviceName={"qryCust"})
 	@BeanAnno(value="INVOICE_ID", getName="getInvoiceId", setName="setInvoiceId")
 	private long invoiceId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="REG_ADDRESS", getName="getRegAddress", setName="setRegAddress")
 	private String regAddress;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="INVOICE_TYPE", getName="getInvoiceType", setName="setInvoiceType")
 	private int invoiceType;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="COUNTRY", getName="getCountry", setName="setCountry")
 	private String country;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@ApiAnno(groups= {QuerySummary.class,QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode","qryCust"})
 	@BeanAnno(value="INVOICE_TITLE", getName="getInvoiceTitle", setName="setInvoiceTitle")
 	private String invoiceTitle;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="IS_DEFAULT", getName="getIsDefault", setName="setIsDefault")
 	private int isDefault;
 	@IsNullAnno(serviceName={"qryCustByPaycode","addInvoice","modInvoice"})
 	@ApiAnno(groups={QuerySummary.class,QueryList.class,QueryDtl.class},serviceName={"qryCust"})
 	@BeanAnno(value="PAYMENT_CODE", getName="getPaymentCode", setName="setPaymentCode")
 	private String paymentCode;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="REG_PHONE", getName="getRegPhone", setName="setRegPhone")
 	private String regPhone;
 	@ApiAnno(groups={QueryList.class,QueryDtl.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="BANK_NAME", getName="getBankName", setName="setBankName")
 	private String bankName;
 	@ApiAnno(serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="USER_NAME", getName="getUserName", setName="setUserName")
 	private String userName;
 	@ApiAnno(groups={QueryList.class},serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="USER_PHONE", getName="getUserPhone", setName="setUserPhone")
 	private String userPhone;
 	@ApiAnno(serviceName={"qryCustByPaycode"})
 	@BeanAnno(value="USER_EMAIL", getName="getUserEmail", setName="setUserEmail")
 	private String userEmail;
 	@ApiAnno(groups={QueryList.class})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(groups={QueryList.class})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;

 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}
 
 	 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 
 	public void setCustomerNumber(String customerNumber){
 		 this.customerNumber=customerNumber;
 	}
 	public String getCustomerNumber(){
 		 return this.customerNumber;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setRegAddress(String regAddress){
 		 this.regAddress=regAddress;
 	}
 	public String getRegAddress(){
 		 return this.regAddress;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 
 	public void setPaymentCode(String paymentCode){
 		 this.paymentCode=paymentCode;
 	}
 	public String getPaymentCode(){
 		 return this.paymentCode;
 	}
 
 	 
 	public void setRegPhone(String regPhone){
 		 this.regPhone=regPhone;
 	}
 	public String getRegPhone(){
 		 return this.regPhone;
 	}
 
 	 
 	public void setBankName(String bankName){
 		 this.bankName=bankName;
 	}
 	public String getBankName(){
 		 return this.bankName;
 	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserPhone() {
		return userPhone;
	}
	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}
	public String getUserEmail() {
		return userEmail;
	}
	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}
	public String getCustCode() {
		return custCode;
	}
	public void setCustCode(String custCode) {
		this.custCode = custCode;
	}
	public String getCompanyName() {
		return companyName;
	}
	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}
 
 	 
}