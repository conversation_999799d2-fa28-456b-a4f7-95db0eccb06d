package com.sgs.ecom.order.scheduler;

import java.util.Date;

import com.sgs.util.date.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.platform.util.SysCommonConstant;
import com.platform.util.date.DateFormat;
import com.sgs.ecom.order.service.bill.interfaces.IBillInfoService;
import com.sgs.redis.RedisClient;

@Component
public class BillScheduler {

	@Autowired
	private RedisClient redisClient;
	@Autowired
	private IBillInfoService billInfoService;
	@Autowired
	ApplicationArguments applicationArguments;

	private static final Logger logger = LoggerFactory.getLogger(BillScheduler.class);
	private static DateUtil dateUtil=new DateUtil();

	@Scheduled(cron = "0 1 0 1 * ?")
	//@Scheduled(cron = "*/5 * * * * ?") //测试用
	public void createBill() throws Exception {
		boolean isGray = applicationArguments.containsOption("gray");
		if (isGray)
			return;
		String key="TIC_ORDER_BILL_THREADS";
		String value=String.valueOf(System.currentTimeMillis() + 600 * 1000);
		try {
			logger.debug("账单处理开始 :", DateFormat.dateToString(new Date(), 
					SysCommonConstant.DateFormat.DatetimeFormat));
			//使用redis分布式锁处理负载均衡
			boolean flag = redisClient.setLock(key, value, 600000);
			
			if (flag) {
				//获取时间
				Date date = DateFormat.addMonths(new Date(), -1);

				Date startDate = dateUtil.getMonthFirst(date);
				Date endDate = dateUtil.getMonthLast(date);
				String billCycle = DateFormat.dateToString(startDate, SysCommonConstant.DateFormat.YmFormat);
				
				//测试月结暂时屏蔽
				/*billInfoSV.createBill(DateFormat.dateToString(startDate, SysCommonConstant.DateFormat.DatetimeFormat),
						DateFormat.dateToString(endDate, SysCommonConstant.DateFormat.DatetimeFormat), billCycle);*/
				// 迁移到member月结处理
				billInfoService.createBill(DateFormat.dateToString(startDate, SysCommonConstant.DateFormat.DatetimeFormat),
						DateFormat.dateToString(endDate, SysCommonConstant.DateFormat.DatetimeFormat), billCycle);
			}
		} catch (InterruptedException e) {
			logger.debug("createBill error", e);
			Thread.currentThread().interrupt();

	    } finally {
			try {
				logger.debug("匹配处理完成 {} ", DateFormat.dateToString(new Date(), 
					SysCommonConstant.DateFormat.DatetimeFormat));
			} catch (Exception e) {
				logger.error("异常 {}" ,e.getMessage());
			}
	    	Thread.sleep(60000);
			redisClient.releaseLock(key,value);
		}
	}
}
