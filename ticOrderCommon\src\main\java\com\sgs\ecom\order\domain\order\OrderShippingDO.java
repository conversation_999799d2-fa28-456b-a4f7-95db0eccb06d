package com.sgs.ecom.order.domain.order;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.entity.order.OrderShipping;
import com.sgs.ecom.order.request.oiq.OrderShippingReq;
import com.sgs.ecom.order.util.UseDateUtil;
import org.apache.commons.lang.StringUtils;


import java.util.Date;

/**
 * <AUTHOR>
 */
public class OrderShippingDO extends OrderShipping {

    public static OrderShipping getEntityByReq(OrderShippingReq orderShippingReq,String orderNo){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderShipping orderShipping=new OrderShipping();
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        baseCopyObj.copyWithNull(orderShipping,orderShippingReq);
        if(StringUtils.isBlank(orderShipping.getIssueDate())){
            orderShipping.setIssueDate(null);
        }
        if(StringUtils.isBlank(orderShipping.getArrivalDate())){
            orderShipping.setArrivalDate(null);
        }
        if(StringUtils.isBlank(orderShipping.getShipmentDate())){
            orderShipping.setShipmentDate(null);
        }
        orderShipping.setOrderNo(orderNo);
        orderShipping.setStateDate(dateStr);
        orderShipping.setState(1);
        return orderShipping;
    }
}
