package com.sgs.ecom.order.dto.export;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.order.FromSourceEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.order.TestLabelEnum;
import com.sgs.ecom.order.util.collection.StrUtil;
import com.sgs.ecom.order.util.serializer.ExpTimeFormatSerializer;
import org.apache.commons.lang.StringUtils;

public class ExpOiqInquiryDTO {
    //询价单号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    //BU信息
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bu;
    //业务线
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String businessLine;
    //产品类目
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPath;
    //样品信息
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sampleName;
    //测试类型标签
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testLabel;
    //测试标签备注
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testLabelMemo;
    //测试项目名称
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String itemName;
    //问卷名称
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String questionName;
    //问卷原始测试项目
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String questionPartInfo;

    //修改后产品类目
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryPathAfter;
    //需求描述
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String questionInfo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String questionPartInfoMore;
    //当前报价金额
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String realAmount;
    //当前跟进人
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String csCode;
    //企业名称
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String companyName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userId;
    //客户姓名
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userName;
    //客户电话
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userPhone;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reInquiry;


    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userEmail;
    //所在地区
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String provinceCity;
    //创建时间
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String payDate;
    //状态
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String state;
    //首次认领/分配时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String firstDistributionTime;
    //首次报价时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String firstWaitConfirmTime;
    //预期报价时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String lastResponseDate;
    //最近报价时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String lastWaitConfirmTime;
    //报价确认时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String confirmTime;
    //询价单关闭时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    @JsonSerialize(using = ExpTimeFormatSerializer.class)
    private String closeTime;
    //询价单关闭理由
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String closeReason;
    //询价单关闭备注
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String closeMemo;
    //询价单关闭人
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String closePerson;
    //询价单更新次数
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String updateNum;
    //询价单来源
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderSource;
    //leads单号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String leadsCode;
    //投放来源
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderSourceFrom;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String fromSource;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String fromSourceShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String fromUrl;

    //确认报价来源
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String operatorSource;
    //销售员
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String salesCode;
    //引导员
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String promoInfo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String qryDetailTime;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currency;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }



    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getLeadsCode() {
        return leadsCode;
    }

    public void setLeadsCode(String leadsCode) {
        this.leadsCode = leadsCode;
    }

    public String getCloseMemo() {
        return closeMemo;
    }

    public void setCloseMemo(String closeMemo) {
        this.closeMemo = closeMemo;
    }

    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getProvinceCity() {
        return provinceCity;
    }

    public void setProvinceCity(String provinceCity) {
        this.provinceCity = provinceCity;
    }



    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getState() {
        return OrderStateEnum.getNameCh(state);
    }

    public void setState(String state) {
        this.state = state;
    }



    public void setFirstDistributionTime(String firstDistributionTime) {
        this.firstDistributionTime = firstDistributionTime;
    }



    public String getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(String realAmount) {
        this.realAmount = realAmount;
    }


    public void setTestLabel(String testLabel) {
        this.testLabel = testLabel;
    }

    public void setLastWaitConfirmTime(String lastWaitConfirmTime) {
        this.lastWaitConfirmTime = lastWaitConfirmTime;
    }

    public void setConfirmTime(String confirmTime) {
        this.confirmTime = confirmTime;
    }



    public void setCloseTime(String closeTime) {
        this.closeTime = closeTime;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public String getClosePerson() {
        return closePerson;
    }

    public void setClosePerson(String closePerson) {
        this.closePerson = closePerson;
    }

    public String getUpdateNum() {
        return updateNum;
    }

    public void setUpdateNum(String updateNum) {
        this.updateNum = updateNum;
    }


    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getFirstWaitConfirmTime() {
        return firstWaitConfirmTime;
    }

    public void setFirstWaitConfirmTime(String firstWaitConfirmTime) {
        this.firstWaitConfirmTime = firstWaitConfirmTime;
    }

    public String getLastResponseDate() {
        return lastResponseDate;
    }

    public void setLastResponseDate(String lastResponseDate) {
        this.lastResponseDate = lastResponseDate;
    }

    public String getCreateDate() {
        return createDate;
    }

    public String getFirstDistributionTime() {
        return firstDistributionTime;
    }

    public String getLastWaitConfirmTime() {
        return lastWaitConfirmTime;
    }

    public String getConfirmTime() {
        return confirmTime;
    }


    public String getCloseTime() {
        return closeTime;
    }

    public String getOrderSourceFrom() {
        return orderSourceFrom;
    }

    public void setOrderSourceFrom(String orderSourceFrom) {
        this.orderSourceFrom = orderSourceFrom;
    }

    public String getOrderSource() {
        if(StringUtils.isNotBlank(orderSource)){
            return OrderSourceEnum.getNameCh(orderSource);
        }
        return "";
    }

    public String getTestLabel() {
        if(StringUtils.isNotBlank(testLabel)){
            return TestLabelEnum.getNameCh(Integer.parseInt(testLabel));
        }
        return "";
    }

    public String getOperatorSource() {
        return operatorSource;
    }

    public void setOperatorSource(String operatorSource) {
        this.operatorSource = operatorSource;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(String salesCode) {
        this.salesCode = salesCode;
    }

    public String getPromoInfo() {
        return promoInfo;
    }

    public void setPromoInfo(String promoInfo) {
        this.promoInfo = promoInfo;
    }

    public String getTestLabelMemo() {
        return testLabelMemo;
    }

    public void setTestLabelMemo(String testLabelMemo) {
        this.testLabelMemo = testLabelMemo;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public String getReInquiry() {
        return reInquiry;
    }

    public void setReInquiry(String reInquiry) {
        this.reInquiry = reInquiry;
    }

    public String getQuestionPartInfo() {
        return questionPartInfo;
    }

    public void setQuestionPartInfo(String questionPartInfo) {
        this.questionPartInfo = questionPartInfo;
    }

    public String getQuestionInfo() {
        return questionInfo;
    }

    public void setQuestionInfo(String questionInfo) {
        this.questionInfo = questionInfo;
    }

    public String getQuestionPartInfoMore() {
        return questionPartInfoMore;
    }

    public void setQuestionPartInfoMore(String questionPartInfoMore) {
        this.questionPartInfoMore = questionPartInfoMore;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public String getCategoryPathAfter() {
        return categoryPathAfter;
    }

    public void setCategoryPathAfter(String categoryPathAfter) {
        this.categoryPathAfter = categoryPathAfter;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFromSource() {
        return fromSource;
    }

    public void setFromSource(String fromSource) {
        this.fromSource = fromSource;
    }

    public String getFromSourceShow() {
        return FromSourceEnum.getNameCh(fromSource);
    }

    public void setFromSourceShow(String fromSourceShow) {
        this.fromSourceShow = fromSourceShow;
    }

    public String getFromUrl() {
        return fromUrl;
    }

    public void setFromUrl(String fromUrl) {
        this.fromUrl = fromUrl;
    }

    public String getCreateCode() {
        return createCode;
    }

    public void setCreateCode(String createCode) {
        this.createCode = createCode;
    }

    public String getQryDetailTime() {
        return StrUtil.isTime(qryDetailTime);
    }

    public void setQryDetailTime(String qryDetailTime) {
        this.qryDetailTime = qryDetailTime;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }
}
