package com.sgs.ecom.order.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class OrderShippingDTO {

    private Long shippingId;

    private String orderNo;
    /***预计到港日期 yyyy-mm-dd*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String arrivalDate;
    /***装船日期 yyyy-mm-dd*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String shipmentDate;
    /***提单签发日期 yyyy-mm-dd*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String issueDate;
    /***提单签发地点*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String issuePlace;
    /***目的港船代*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String shippingAgent;
    /***提单号码*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String ladingNo;
    /***船名*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String vesselName;
    /***航次*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String vesselNumber;
    /***船公司*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String shippingLine;
    /***装运国*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String originalCountry;
    /***装运港*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String loadingPort;
    /***卸货港*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String dischargePort;
    /***最终目的国家*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String deliveryCountry;
    /***最终目的城市*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String deliveryCity;
    /***支付方式*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String shippingPaymentMethod;
    /***原产国*/
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String countryOfOrigin;
}
