<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.order.mapper.order.OrderBaseInfoCustomMapper" >


    <!--自定义-->

    <resultMap id="indexMailDTO" type="com.sgs.ecom.order.dto.custom.MailDTO" >
        <result column="USER_ID" property="userId" jdbcType="INTEGER" />
        <result column="USER_PHONE" property="userPhone" jdbcType="INTEGER" />
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
        <result column="USER_SEX" property="userSex" jdbcType="VARCHAR" />
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR" />
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR" />
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR" />
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL" />
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
        <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT" />
        <result column="IS_TEST" property="isTest" jdbcType="TINYINT" />
        <result column="LINE_ID" property="lineId" jdbcType="VARCHAR" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
        <result column="BU" property="bu" jdbcType="VARCHAR" />
        <result column="LAST_RESPONSE_DATE" property="lastResponseDate" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
        <result column="CUST_ID" property="custId" jdbcType="BIGINT" />
    </resultMap>

    <resultMap id="ticIndexMailDTO" type="com.sgs.ecom.order.dto.bbc.TicMailDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR" />
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER" />
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
        <result column="LINK_PERSON" property="linkPerson" jdbcType="INTEGER" />
        <result column="LINK_PHONE" property="linkPhone" jdbcType="VARCHAR" />
        <result column="LINK_EMAIL" property="linkEmail" jdbcType="VARCHAR" />
        <result column="REPORT_SEND_CC" property="reportSendCc" jdbcType="VARCHAR" />
        <result column="CS_SEND_CC" property="csSendCc" jdbcType="VARCHAR" />
        <result column="PROD_ID" property="prodId" jdbcType="INTEGER" />
        <result column="STORE_ID" property="storeId" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="VARCHAR" />
         <result column="REAL_AMOUNT" property="realAmount" jdbcType="VARCHAR" />
        <result column="BU" property="bu" jdbcType="VARCHAR" />
        <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR" />
        <result column="IS_TEST" property="isTest" jdbcType="INTEGER" />
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="COMPANY_NAME_CN" property="companyNameCn" jdbcType="VARCHAR" />
        <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR" />
        <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR" />
        <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR" />
        <result column="PRO_STATE" property="proState" jdbcType="INTEGER" />
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="VARCHAR" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
        <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="BIGINT" />
    </resultMap>


    <resultMap id="baseOrder" type="com.sgs.ecom.order.dto.custom.BaseOrderDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
        <result column="USER_ID" property="userId" jdbcType="BIGINT" />
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR" />
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
        <result column="PLATFORM_ORDER" property="platformOrder" jdbcType="VARCHAR" />
        <result column="PLATFORM_AMOUNT" property="platformAmount" jdbcType="DECIMAL" />
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
        <result column="TMP_GROUP_NO" property="tmpGroupNo" jdbcType="VARCHAR" />
        <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT" />
        <result column="CATAGORY_ID" property="categoryId" jdbcType="BIGINT" />
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="SHOP_DIS_AMOUNT" property="shopDisAmount" jdbcType="DECIMAL"/>
        <result column="CS_DISCOUNT_AMOUNT" property="csDiscountAmount" jdbcType="DECIMAL"/>
        <result column="LAB_ID" property="labId" jdbcType="BIGINT"/>
        <result column="BU" property="bu" jdbcType="VARCHAR" />
        <result column="MONTH_PAY" property="monthPay" jdbcType="VARCHAR" />
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="VARCHAR" />
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER" />
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER" />
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER" />
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR" />
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR" />
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR" />
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR" />
        <result column="FROM_SOURCE" property="fromSource" jdbcType="VARCHAR" />
        <result column="ORDER_SOURCE" property="orderSource" jdbcType="VARCHAR" />
        <result column="LEADS_CODE" property="leadsCode" jdbcType="VARCHAR" />
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR" />
        <result column="IS_URGENT" property="isUrgent" jdbcType="BIGINT" />
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL" />
        <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
        <result column="BUSINESS_PERSON_EMAIL" property="businessPersonEmail" jdbcType="VARCHAR" />
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER"/>
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
        <result column="EXCHANGE_RATE" property="exchangeRate" jdbcType="VARCHAR" />
        <result column="LINE_ID" property="lineId" jdbcType="BIGINT" />
        <result column="APPLICATION_LINE_ID" property="applicationLineId" jdbcType="BIGINT" />
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="VARCHAR" />
        <result column="OFFER_DATE" property="offerDate" jdbcType="VARCHAR" />
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="VARCHAR" />
        <result column="USER_SEX" property="userSex" jdbcType="INTEGER" />
        <result column="LAST_RESPONSE_DATE" property="lastResponseDate" jdbcType="VARCHAR" />
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR" />
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR" />
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR" />
        <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR" />
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR" />
        <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR" />
        <result column="SALES_EMAIL" property="salesEmail" jdbcType="VARCHAR"/>
        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR"/>
        <result column="APPLY_SUBMIT_DATE" property="applySubmitDate" jdbcType="VARCHAR"/>
        <result column="OUT_ORDER_NO" property="outOrderNo" jdbcType="VARCHAR"/>
    </resultMap>
    <sql id="baseSql" >
        ORDER_ID,ORDER_NO,STATE,ORDER_TYPE,USER_ID,CS_CODE,CREATE_DATE,IS_PAY_RECEIVED,PLATFORM_ORDER,
        RELATE_ORDER_NO,PLATFORM_AMOUNT,GROUP_NO,TMP_GROUP_NO,QUESTION_ID,
        CATAGORY_ID,CATEGORY_PATH,ORDER_AMOUNT,REAL_AMOUNT,LAB_ID,BU,MONTH_PAY,
        USER_NAME,USER_PHONE,USER_EMAIL,COMPANY_NAME,FROM_SOURCE,ORDER_SOURCE,LEADS_CODE,IS_URGENT,CS_EMAIL,
        TEST_CYCLE,DISCOUNT_AMOUNT,BUSINESS_PERSON_EMAIL,IS_ELECTRON,PAY_STATE,CURRENCY,EXCHANGE_RATE,LINE_ID,APPLICATION_LINE_ID,OFFER_DATE,ORDER_EXP_DATE,
        USER_SEX,LAST_RESPONSE_DATE,BUSINESS_LINE,OPERATOR_CODE,HIS_STATE,SUB_STATE,REFUND_STATE,PAY_METHOD,DEADLINE_TIME,
        REPORT_FORM,REPORT_FORM_CODE,REPORT_LUA,REPORT_LUA_CODE,SHOP_DIS_AMOUNT,SALES_EMAIL,LAB_NAME,APPLY_SUBMIT_DATE,OUT_ORDER_NO
    </sql>

    <select id="selectBaseByOrderNo"  resultMap="baseOrder"  >
    select
    <include refid="baseSql" />
    from ORDER_BASE_INFO
    where 1=1
     <if test="orderNo != null">
         AND ORDER_NO=#{orderNo,jdbcType=VARCHAR}
     </if>
     <if test="platformOrder != null">
         AND PLATFORM_ORDER=#{platformOrder,jdbcType=VARCHAR}
     </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="userId != null">
            AND USER_ID=#{userId}
        </if>
        <if test="notState != null">
            AND STATE!=#{notState}
        </if>
        <if test="relateOrderNo != null">
            AND RELATE_ORDER_NO=#{relateOrderNo}
        </if>
    limit 1
  </select>


    <select id="selectCountByMap"  resultType="java.lang.Integer"  >
        select count(1)  from ORDER_BASE_INFO
        where 1=1
        <if test="orderNo != null">
            AND ORDER_NO=#{orderNo}
        </if>
        <if test="platformOrder != null">
            AND PLATFORM_ORDER=#{platformOrder}
        </if>

    </select>

    <select id="selectFirstInquiryOrderByUserId"  resultType="java.lang.String"  >
        select ORDER_NO  from ORDER_BASE_INFO
        where USER_ID=#{userId} and ORDER_TYPE in(200000,200001) order by CREATE_DATE asc limit 1
    </select>




    <sql id="mailSql" >
        USER_PHONE, USER_ID,USER_NAME,USER_SEX, USER_EMAIL, CS_CODE,CS_EMAIL,CS_NAME,
        ORDER_NO, ORDER_ID, REAL_AMOUNT, AUDIT_CODE,OPERATOR_CODE, COMPANY_NAME,GROUP_NO,
        TEST_CYCLE, CATEGORY_PATH, IS_DELETE,BUSINESS_LINE,IS_TEST, LAST_RESPONSE_DATE,
        CREATE_DATE,LINE_ID, BU,ORDER_TYPE,RELATE_ORDER_NO,CONFIRM_ORDER_DATE,CUST_ID
    </sql>


    <select id="selectMailDTOByOrderId" resultMap="indexMailDTO">
        select
        <include refid="mailSql" />
        from ORDER_BASE_INFO
        where ORDER_ID = #{orderId}
    </select>

    <select id="selectMailDTOByOrderNo"  resultMap="indexMailDTO"  >
        select
        <include refid="mailSql" />
        from ORDER_BASE_INFO
        where ORDER_NO = #{orderNo}
    </select>

    <select id="selectTicMailDTOByOrderId" resultMap="ticIndexMailDTO">

         SELECT
             distinct
             obi.ORDER_ID,obi.OPERATOR_CODE,obi.AUDIT_CODE,obi.ORDER_NO,obi.ORDER_TYPE,obi.HIS_STATE,
                obi.LAB_ID, obi.USER_ID,
        IF(oaf.FORM_ID is null ,obi.USER_NAME,oaf.LINK_PERSON) LINK_PERSON,
        IF(oaf.FORM_ID is null ,obi.USER_PHONE,oaf.LINK_PHONE) LINK_PHONE,
        IF(oaf.FORM_ID is null ,obi.USER_EMAIL,oaf.LINK_EMAIL) LINK_EMAIL,
        IF(oaf.FORM_ID is null ,obi.COMPANY_NAME,oaf.COMPANY_NAME_CN) COMPANY_NAME_CN,
        IF(oaf.FORM_ID is null ,CONCAT_WS("",obi.PROVINCE,obi.CITY,obi.TOWN,obi.COMPANY_ADDRESS_CN),CONCAT_WS("",oaf.PROVINCE,oaf.CITY,oaf.TOWN,oaf.COMPANY_ADDRESS_CN)) COMPANY_ADDRESS_CN,
        IF(oaf.FORM_ID is null ,obi.COMPANY_ADDRESS_EN,oaf.COMPANY_ADDRESS_EN) COMPANY_ADDRESS_EN,
        IF(oaf.FORM_ID is null ,obi.company_NAME_EN,oaf.company_NAME_EN) COMPANY_NAME_EN,


oaf.REPORT_SEND_CC, op.PROD_ID, op.STORE_ID,obi.CREATE_DATE,
obi.bu,obi.ORDER_AMOUNT,obi.IS_TEST,obi.GROUP_NO,obi.REAL_AMOUNT,
op.STORE_NAME,obi.STATE ,obi.PRO_STATE,obi.DEADLINE_TIME,obi.ORDER_TYPE,
ot.REPORT_SEND_CC CS_SEND_CC,obi.RECOMMEND_REASON
FROM ORDER_BASE_INFO obi
left JOIN ORDER_APPLICATION_FORM oaf on obi.ORDER_NO=oaf.ORDER_NO
left JOIN ORDER_PRODUCT op on obi.ORDER_NO=op.ORDER_NO and op.state = 1
left join ORDER_REPORT ot on obi.ORDER_NO=ot.ORDER_NO and ot.STATE=1
where obi.ORDER_ID= #{orderId}
    </select>



    <resultMap id="checkDTO" type="com.sgs.ecom.order.dto.custom.OrderCheckStateDTO" >
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
        <result column="STATE" property="state" jdbcType="INTEGER" />
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER" />
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER" />
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER" />
        <result column="HIT_STATE" property="hisState" jdbcType="INTEGER" />
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="INTEGER" />
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER" />
        <result column="USER_ID" property="userId" jdbcType="BIGINT" />
        <result column="IS_INVOICE" property="isInvoice" jdbcType="INTEGER" />
        <result column="LAB_ID" property="labId" jdbcType="BIGINT" />
        <result column="BU" property="bu" jdbcType="VARCHAR" />
    </resultMap>
    <sql id="checkSql" >
        ORDER_NO,ORDER_ID,STATE,PAY_STATE,SUB_STATE,ORDER_TYPE,HIS_STATE,
        IS_PAY_RECEIVED,MONTH_PAY,RELATE_ORDER_NO,REAL_AMOUNT,USER_ID,
        IS_INVOICE,LAB_ID,BU
    </sql>
    <select id="selectOrderCheckStateDTO"  resultMap="checkDTO"  >
    select
    <include refid="checkSql" />
    from ORDER_BASE_INFO
    where ORDER_ID = #{orderId}
    </select>

    <select id="selectLastOrderByRelate"  resultMap="checkDTO"  >
    select
    <include refid="checkSql" />
    from ORDER_BASE_INFO
    where RELATE_ORDER_NO = #{relateOrderNo} order by ORDER_ID desc limit 1
  </select>

    <select id="selectCheckStateByMap"  resultMap="checkDTO"  >
    select
    <include refid="checkSql" />
    from ORDER_BASE_INFO
    where 1=1
    <if test="orderIdList != null ">
        AND ORDER_ID in
        <foreach collection="orderIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
        </foreach>
    </if>
    </select>







    <update id="updatePrice" parameterType="com.sgs.ecom.order.vo.VOOrderBaseInfo">
        UPDATE ORDER_BASE_INFO
        <set >
            <if test="realAmount != null" >
                REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="stateDate != null" >
                STATE_DATE = #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="csCode != null" >
                CS_CODE = #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csName != null" >
                CS_NAME = #{csName,jdbcType=VARCHAR},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=BIGINT} and PAY_STATE!=1 and STATE=#{state}
    </update>






    <select id="selectExpCount"  resultType="java.lang.Integer" >
        select
        count(1)
        from ORDER_BASE_INFO
        where 1=1
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="state != null">
            AND STATE=#{state}
        </if>
        <if test="dateFormatTime != null">
            AND DATE_FORMAT(ORDER_EXP_DATE,'%Y%m%d')=#{dateFormatTime}
        </if>
        <if test="orderExpDateEndSub != null">
            AND DATE_ADD(OFFER_DATE, INTERVAL 4 DAY) &lt; now()
        </if>
        <if test="rstsDateFormatTime != null">
            AND DATE_FORMAT( DATE_ADD(CREATE_DATE, INTERVAL #{rstsDate} DAY),'%Y%m%d')=#{rstsDateFormatTime}
        </if>
        <if test="noticeNum != null">
            AND NOTICE_NUM=#{noticeNum}
        </if>

    </select>

    <resultMap id="indexExpDTO" type="com.sgs.ecom.order.dto.custom.OrderSchedulerDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="STATE" property="state" jdbcType="VARCHAR" />
        <result column="CREATE_DATE" property="createDate" jdbcType="VARCHAR" />
        <result column="OFFER_DATE" property="offerDate" jdbcType="VARCHAR" />
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="VARCHAR" />
        <result column="NOTICE_NUM" property="noticeNum" jdbcType="INTEGER" />
    </resultMap>

    <select id="selectExpList"  resultMap="indexExpDTO" >
        select
        ORDER_ID,ORDER_NO,STATE,CREATE_DATE,OFFER_DATE,ORDER_EXP_DATE
        from ORDER_BASE_INFO
        where 1=1
        <if test="state != null">
            AND STATE=#{state}
        </if>
        <if test="isRemind != null" >
            AND IS_REMIND =#{isRemind}
        </if>
        <if test="noticeNum != null">
            AND NOTICE_NUM=#{noticeNum}
        </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="orderExpDateEnd != null">
            AND ORDER_EXP_DATE &lt; now()
        </if>
        <if test="orderExpDateEndSub != null">
            AND DATE_ADD(OFFER_DATE, INTERVAL 4 DAY) &lt; now()
        </if>
        <if test="rstsDate != null">
            AND DATE_ADD(CREATE_DATE, INTERVAL #{rstsDate} DAY) &lt; now()
        </if>
        <if test="orderSendDate != null">
            AND DATE_SUB(LAST_RESPONSE_DATE, INTERVAL 1 HOUR) &lt; now()
        </if>
        <if test="orderSendDate != null">
            AND DATE_SUB(LAST_RESPONSE_DATE, INTERVAL 1 HOUR) &lt; now()
        </if>
        <if test="stateList != null ">
            AND STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    
    <select id="selectExpListTic"  resultMap="indexExpDTO" >
        select
        ORDER_ID,ORDER_NO,STATE,CREATE_DATE,OFFER_DATE,ORDER_EXP_DATE,NOTICE_NUM
        from ORDER_BASE_INFO
        where SUB_STATE = 0
        <if test="notRefundState != null">
            AND REFUND_STATE !=#{notRefundState}
        </if>
        <if test="state != null">
            AND STATE=#{state}
        </if>
        <if test="noticeNum != null">
            AND NOTICE_NUM=#{noticeNum}
        </if>
        <if test="noticeNumAll != null">
            AND (NOTICE_NUM = 0 or NOTICE_NUM = 1)
        </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="orderSendDateTwo != null">
           AND CREATE_DATE &lt; date_add(now(), interval -2 day) 
        </if>
        <if test="orderSendDateFive != null">
           AND CREATE_DATE &lt; date_add(now(), interval -5 day) 
        </if>

    </select>


	<update id="updateSendClose" >
        UPDATE ORDER_BASE_INFO set NOTICE_NUM = #{noticeNum}
        where ORDER_ID = #{orderId,jdbcType=BIGINT} and NOTICE_NUM = #{noticeNumOld}
    </update>


    <update id="updateExpClose" >
        UPDATE ORDER_BASE_INFO
        <set >
            <if test="state != null" >
                STATE = #{state,jdbcType=INTEGER},
            </if>
            <if test="orderType != null" >
                ORDER_TYPE = #{orderType},
            </if>
            <if test="hisState != null" >
                HIS_STATE = #{hisState,jdbcType=INTEGER},
            </if>
            <if test="noticeNum != null" >
                NOTICE_NUM = #{noticeNum},
            </if>
            <if test="isRemind != null" >
                IS_REMIND = #{isRemind},
            </if>
            <if test="closeCode != null" >
                CLOSE_CODE = #{closeCode},
            </if>
            <if test="closeReason != null" >
                CLOSE_REASON = #{closeReason},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=BIGINT}
        <if test="state != null">
            AND STATE!=90
        </if>
        <if test="noticeNum != null">
            AND NOTICE_NUM=0
        </if>
        <if test="isRemind != null" >
            AND IS_REMIND =0
        </if>
    </update>

    <update id="updateStateByLock" >
        UPDATE ORDER_BASE_INFO set  STATE = #{newState}
        <if test="closeCode != null">
            ,CLOSE_CODE= #{closeCode}
        </if>
        <if test="closeReason != null">
            ,CLOSE_REASON= #{closeReason}
        </if>
        <if test="payDate != null">
            ,PAY_DATE= #{payDate}
        </if>
        <if test="payMethod != null">
            ,PAY_METHOD= #{payMethod}
        </if>
        <if test="payMethodIsNull ==1">
            ,PAY_METHOD =null
        </if>
        <if test="payDateIsNull ==1">
            ,PAY_DATE =null
        </if>
        <if test="deleteFlg != null">
            ,IS_DELETE= #{newDelete}
        </if>
        <if test="payFlg != null">
            ,PAY_STATE= #{newPayState}
        </if>
        <if test="isPayReceivedFlg != null">
            ,IS_PAY_RECEIVED= #{newReceived}
        </if>
        <if test="monthPayFlg != null">
            ,MONTH_PAY= #{newMonthPay}
        </if>
        <if test="isTestFlg != null">
            ,IS_TEST= #{newIsTest}
        </if>



        where ORDER_ID = #{orderId} and  STATE= #{oldState}

        <if test="deleteFlg != null">
            AND IS_DELETE=#{oldDelete}
        </if>
        <if test="payFlg != null">
            AND PAY_STATE= #{oldPayState}
        </if>
        <if test="isPayReceivedFlg != null">
            AND IS_PAY_RECEIVED= #{oldReceived}
        </if>
        <if test="monthPayFlg != null">
            AND MONTH_PAY= #{oldMonthPay}
        </if>
        <if test="isTestFlg != null">
            AND IS_TEST= #{oldIsTest}
        </if>
    </update>

    <update id="batchUpdatePayState" >
        UPDATE ORDER_BASE_INFO set PAY_STATE=1,MONTH_PAY=0,PAY_DATE=now() ,PAY_METHOD=300000,STATE=11
        where ORDER_ID in
        <foreach collection="orderIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <update id="batchUpdateInvoice" >
        UPDATE ORDER_BASE_INFO set IS_INVOICE=1
        where ORDER_ID in
        <foreach collection="orderIdList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateOrderUserId" >
        UPDATE ORDER_BASE_INFO set USER_ID=#{userId}
        where USER_PHONE=#{userPhone}
        and ORDER_TYPE in (200000,210000,201000,210001,200001)
        and USER_ID is null
    </update>


    <resultMap id="person" type="com.sgs.ecom.order.dto.SysPersonDTO" >
        <result column="CS_EMAIL" property="personMail" jdbcType="VARCHAR" />
        <result column="CS_CODE" property="personCode" jdbcType="VARCHAR" />
    </resultMap>
    <select id="qryPersonByLineId"   resultMap="person">
        select CS_CODE,CS_EMAIL from ORDER_BASE_INFO   where 1=1
        <if test="userId != null">
            AND USER_ID=#{userId}
        </if>
        <if test="orderType != null">
            AND ORDER_TYPE=#{orderType}
        </if>
        <if test="lineId != null">
            AND LINE_ID=#{lineId}
        </if>
        order by ORDER_ID desc limit 1
    </select>



    <select id="selectReportTimeOverdue"   resultMap="baseOrder">
        select
        <include refid="baseSql" />
        from order_base_info obi
        where obi.ORDER_TYPE=100000 and obi.STATE=14 and obi.DEADLINE_TIME &lt; CURDATE()
        and (select count(1) from order_attachment oa where oa.ORDER_NO=obi.ORDER_NO and ATT_TYPE=10  )=0
    </select>
    <select id="qryRefundTimeOverdue" resultType="com.sgs.ecom.order.dto.order.OrderBaseInfoDTO">
        select * from order_base_info where REFUND_STATE = 4  and STATE in (11,12,13)
    </select>


    <select id="qryOrderBySubOrderNo" resultMap="ticIndexMailDTO">
        SELECT distinct  obi.ORDER_ID,obi.OPERATOR_CODE,obi.AUDIT_CODE,obi.ORDER_NO,obi.ORDER_TYPE,obi.HIS_STATE,
               obi.LAB_ID,
               IF(oaf.FORM_ID is null ,obi.USER_NAME,oaf.LINK_PERSON) LINK_PERSON,
               IF(oaf.FORM_ID is null ,obi.USER_PHONE,oaf.LINK_PHONE) LINK_PHONE,
               IF(oaf.FORM_ID is null ,obi.USER_EMAIL,oaf.LINK_EMAIL) LINK_EMAIL,
               IF(oaf.FORM_ID is null ,obi.COMPANY_NAME,oaf.COMPANY_NAME_CN) COMPANY_NAME_CN,
               IF(oaf.FORM_ID is null ,CONCAT_WS("",obi.PROVINCE,obi.CITY,obi.TOWN,obi.COMPANY_ADDRESS_CN),CONCAT_WS("",oaf.PROVINCE,oaf.CITY,oaf.TOWN,oaf.COMPANY_ADDRESS_CN)) COMPANY_ADDRESS_CN,
               IF(oaf.FORM_ID is null ,obi.COMPANY_ADDRESS_EN,oaf.COMPANY_ADDRESS_EN) COMPANY_ADDRESS_EN,
               IF(oaf.FORM_ID is null ,obi.company_NAME_EN,oaf.company_NAME_EN) COMPANY_NAME_EN,
               oaf.REPORT_SEND_CC, op.PROD_ID, op.STORE_ID,obi.CREATE_DATE,obi.bu,obi.ORDER_AMOUNT,obi.IS_TEST,obi.GROUP_NO,obi.REAL_AMOUNT, op.STORE_NAME,obi.STATE ,
               ot.REPORT_SEND_CC CS_SEND_CC
        FROM ORDER_BASE_INFO obi
                 left JOIN ORDER_APPLICATION_FORM oaf on obi.ORDER_NO=oaf.ORDER_NO
                 left JOIN ORDER_PRODUCT op on obi.ORDER_NO=op.ORDER_NO and op.state = 1
                 left join ORDER_REPORT ot on obi.ORDER_NO=ot.ORDER_NO and ot.STATE=1
        where obi.ORDER_NO = (SELECT RELATE_ORDER_NO from order_base_info where  ORDER_NO = #{orderNo})
    </select>

</mapper>