package com.sgs.ecom.order.dto.order;

import com.sgs.ecom.order.vo.VOOrderAttribute;

import java.math.BigDecimal;
import java.util.List;

public class OrderPriceDTO {

	public VOOrderAttribute voOrderAttributeLua;
	public VOOrderAttribute voOrderAttributeFrom;
	public VOOrderAttribute voOrderAttributeLab;
	private BigDecimal orderAmount;
	private BigDecimal oldRealAmount;
	private BigDecimal discount;

	private BigDecimal newDiscount;//一口价的优惠金额
	private String orderNo;
	private String oldGroup;
	private String newGroup;
	private BigDecimal totalUrgentAmount;
	private int priceType;





	public OrderPriceDTO() {
	}

	public OrderPriceDTO(String orderNo, String oldGroup, String newGroup) {
		this.orderNo = orderNo;
		this.oldGroup = oldGroup;
		this.newGroup = newGroup;
	}



	public VOOrderAttribute getVoOrderAttributeLua() {
		return voOrderAttributeLua;
	}

	public void setVoOrderAttributeLua(VOOrderAttribute voOrderAttributeLua) {
		this.voOrderAttributeLua = voOrderAttributeLua;
	}

	public VOOrderAttribute getVoOrderAttributeFrom() {
		return voOrderAttributeFrom;
	}

	public void setVoOrderAttributeFrom(VOOrderAttribute voOrderAttributeFrom) {
		this.voOrderAttributeFrom = voOrderAttributeFrom;
	}

	public VOOrderAttribute getVoOrderAttributeLab() {
		return voOrderAttributeLab;
	}

	public void setVoOrderAttributeLab(VOOrderAttribute voOrderAttributeLab) {
		this.voOrderAttributeLab = voOrderAttributeLab;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOldGroup() {
		return oldGroup;
	}

	public void setOldGroup(String oldGroup) {
		this.oldGroup = oldGroup;
	}

	public String getNewGroup() {
		return newGroup;
	}

	public void setNewGroup(String newGroup) {
		this.newGroup = newGroup;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	public BigDecimal getTotalUrgentAmount() {
		return totalUrgentAmount;
	}

	public void setTotalUrgentAmount(BigDecimal totalUrgentAmount) {
		this.totalUrgentAmount = totalUrgentAmount;
	}

	public int getPriceType() {
		return priceType;
	}

	public void setPriceType(int priceType) {
		this.priceType = priceType;
	}

	public BigDecimal getOldRealAmount() {
		return oldRealAmount;
	}

	public void setOldRealAmount(BigDecimal oldRealAmount) {
		this.oldRealAmount = oldRealAmount;
	}

	public BigDecimal getNewDiscount() {
		return newDiscount;
	}

	public void setNewDiscount(BigDecimal newDiscount) {
		this.newDiscount = newDiscount;
	}


}
