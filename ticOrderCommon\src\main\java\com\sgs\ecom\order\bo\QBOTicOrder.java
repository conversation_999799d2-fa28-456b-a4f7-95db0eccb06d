package com.sgs.ecom.order.bo; 
 
import java.sql.Timestamp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.CharacterVaild;
import com.platform.util.json.TimeFormatSerializer; 

public class QBOTicOrder{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "VW_TIC_ORDER"; 
 
 	public static final String OWNER ="bbc";

 	public static final String ORDER_NO="orderNo";
 	public static final String STATUS_WAIT_PAYMENT="statusWaitPayment";
 	public static final String CONFIRM_APP_BY="confirmAppBy";
 	public static final String FILE_NAME="fileName";
 	public static final String STATUS_CHECK_APP_CREAT_TIME="statusCheckAppCreatTime";
 	public static final String SKU_ATTR="skuAttr";
 	public static final String USER_NAME="userName";
 	public static final String STATUS_CANCEL="statusCancel";
 	public static final String CONTACT_TEL="contactTel";
 	public static final String STATUS_REFUND="statusRefund";
 	public static final String CANCEL_FLG="cancelFlg";
 	public static final String CHANGE_STATUS="changeStatus";
 	public static final String STATUS_RETURN_CREAT_TIME="statusReturnCreatTime";
 	public static final String COMPANY_ADDRESS="companyAddress";
 	public static final String OPERATOR="operator";
 	public static final String CONTACT_EMAIL="contactEmail";
 	public static final String REMARK="remark";
 	public static final String IS_TICKET="isTicket";
 	public static final String STATUS_CHECK_APP="statusCheckApp";
 	public static final String STATUS_PAYMENT_DOCUMENT_CREATTIME="statusPaymentDocumentCreattime";
 	public static final String STORE_NAME="storeName";
 	public static final String COMPANY_NAME_CN="companyNameCn";
 	public static final String ID="id";
 	public static final String COMPANY_NAME_EN="companyNameEn";
 	public static final String STATUS_CANCEL_APP_CREAT_TIME="statusCancelAppCreatTime";
 	public static final String STATUS_FINISH_APP_CREAT_TIME="statusFinishAppCreatTime";
 	public static final String STORE_ID="storeId";
 	public static final String STATUS_SUBMIT_APP_CREAT_TIME="statusSubmitAppCreatTime";
 	public static final String CONTACT_NAME="contactName";
 	public static final String TOTAL_PRICE="totalPrice";
 	public static final String PAYMENT_STATUS="paymentStatus";
 	public static final String IS_BILL="isBill";
 	public static final String STATUS_RETURN="statusReturn";
 	public static final String QUANTITY="quantity";
 	public static final String PRODUCT_ID="productId";
 	public static final String PRODUCT_NAME="productName";
 	public static final String STATUS_SERVICE_APP_CREATTIME="statusServiceAppCreattime";
 	public static final String STATUS_PAYMENT_DOCUMENT="statusPaymentDocument";
 	public static final String IS_MONTH_PAY="isMonthPay";
 	public static final String STATUS_REFUND_CREAT_TIME="statusRefundCreatTime";
 	public static final String STATUS_FINISH="statusFinish";
 	public static final String USER_ID="userId";
 	public static final String STATUS_SUBMIT_APP="statusSubmitApp";
 	public static final String PRICE="price";
 	public static final String CREATED_DATE="createdDate";
 	public static final String STATUS_WAIT_PAYMENT_CREAT_TIME="statusWaitPaymentCreatTime";
 	public static final String STATUS_SERVICE="statusService";
 	public static final String STATUS="status";

 	@BeanAnno("order_no")
 	private String orderNo;
 	@BeanAnno("status_wait_payment")
 	private String statusWaitPayment;
 	@BeanAnno("confirm_app_by")
 	private String confirmAppBy;
 	@BeanAnno("file_name")
 	private String fileName;
 	@BeanAnno("status_check_app_creat_time")
 	private Timestamp statusCheckAppCreatTime;
 	@BeanAnno("sku_attr")
 	private String skuAttr;
 	@BeanAnno("user_name")
 	private String userName;
 	@BeanAnno("status_cancel")
 	private String statusCancel;
 	@BeanAnno("contact_tel")
 	private String contactTel;
 	@BeanAnno("status_refund")
 	private String statusRefund;
 	@BeanAnno("cancel_flg")
 	private String cancelFlg;
 	@BeanAnno("change_status")
 	private String changeStatus;
 	@BeanAnno("status_return_creat_time")
 	private Timestamp statusReturnCreatTime;
 	@BeanAnno("company_address")
 	private String companyAddress;
 	@BeanAnno("operator")
 	private String operator;
 	@BeanAnno("contact_email")
 	private String contactEmail;
 	@BeanAnno("Remark")
 	private String remark;
 	@BeanAnno("is_ticket")
 	private int isTicket;
 	@BeanAnno("status_check_app")
 	private String statusCheckApp;
 	@BeanAnno("status_payment_document_creatTime")
 	private Timestamp statusPaymentDocumentCreattime;
 	@BeanAnno("store_name")
 	private String storeName;
 	@BeanAnno("company_name_cn")
 	private String companyNameCn;
 	@BeanAnno("id")
 	private String id;
 	@BeanAnno("company_name_en")
 	private String companyNameEn;
 	@BeanAnno("status_cancel_app_creat_time")
 	private Timestamp statusCancelAppCreatTime;
 	@BeanAnno("status_finish_app_creat_time")
 	private Timestamp statusFinishAppCreatTime;
 	@BeanAnno("store_id")
 	private String storeId;
 	@BeanAnno("status_submit_app_creat_time")
 	private Timestamp statusSubmitAppCreatTime;
 	@BeanAnno("contact_name")
 	private String contactName;
 	@BeanAnno("total_price")
 	private double totalPrice;
 	@BeanAnno("payment_status")
 	private String paymentStatus;
 	@BeanAnno("is_bill")
 	private int isBill;
 	@BeanAnno("status_return")
 	private String statusReturn;
 	@BeanAnno("Quantity")
 	private long quantity;
 	@BeanAnno("product_id")
 	private String productId;
 	@BeanAnno("product_name")
 	private String productName;
 	@BeanAnno("status_service_app_creatTime")
 	private Timestamp statusServiceAppCreattime;
 	@BeanAnno("status_payment_document")
 	private String statusPaymentDocument;
 	@BeanAnno("is_month_pay")
 	private int isMonthPay;
 	@BeanAnno("status_refund_creat_time")
 	private Timestamp statusRefundCreatTime;
 	@BeanAnno("status_finish")
 	private String statusFinish;
 	@BeanAnno("user_id")
 	private String userId;
 	@BeanAnno("status_submit_app")
 	private String statusSubmitApp;
 	@BeanAnno("Price")
 	private double price;
 	@BeanAnno("created_date")
 	private Timestamp createdDate;
 	@BeanAnno("status_wait_payment_creat_time")
 	private Timestamp statusWaitPaymentCreatTime;
 	@BeanAnno("status_service")
 	private String statusService;
 	@BeanAnno("status")
 	private String status;

 	@CharacterVaild(len = 100) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusWaitPayment(String statusWaitPayment){
 		 this.statusWaitPayment=statusWaitPayment;
 	}
 	public String getStatusWaitPayment(){
 		 return this.statusWaitPayment;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setConfirmAppBy(String confirmAppBy){
 		 this.confirmAppBy=confirmAppBy;
 	}
 	public String getConfirmAppBy(){
 		 return this.confirmAppBy;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setFileName(String fileName){
 		 this.fileName=fileName;
 	}
 	public String getFileName(){
 		 return this.fileName;
 	}
 
 	 
 	public void setStatusCheckAppCreatTime(Timestamp statusCheckAppCreatTime){
 		 this.statusCheckAppCreatTime=statusCheckAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusCheckAppCreatTime(){
 		 return this.statusCheckAppCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 715827882) 
 	public void setSkuAttr(String skuAttr){
 		 this.skuAttr=skuAttr;
 	}
 	public String getSkuAttr(){
 		 return this.skuAttr;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusCancel(String statusCancel){
 		 this.statusCancel=statusCancel;
 	}
 	public String getStatusCancel(){
 		 return this.statusCancel;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContactTel(String contactTel){
 		 this.contactTel=contactTel;
 	}
 	public String getContactTel(){
 		 return this.contactTel;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusRefund(String statusRefund){
 		 this.statusRefund=statusRefund;
 	}
 	public String getStatusRefund(){
 		 return this.statusRefund;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setCancelFlg(String cancelFlg){
 		 this.cancelFlg=cancelFlg;
 	}
 	public String getCancelFlg(){
 		 return this.cancelFlg;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setChangeStatus(String changeStatus){
 		 this.changeStatus=changeStatus;
 	}
 	public String getChangeStatus(){
 		 return this.changeStatus;
 	}
 
 	 
 	public void setStatusReturnCreatTime(Timestamp statusReturnCreatTime){
 		 this.statusReturnCreatTime=statusReturnCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusReturnCreatTime(){
 		 return this.statusReturnCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyAddress(String companyAddress){
 		 this.companyAddress=companyAddress;
 	}
 	public String getCompanyAddress(){
 		 return this.companyAddress;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setOperator(String operator){
 		 this.operator=operator;
 	}
 	public String getOperator(){
 		 return this.operator;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContactEmail(String contactEmail){
 		 this.contactEmail=contactEmail;
 	}
 	public String getContactEmail(){
 		 return this.contactEmail;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
 	public void setIsTicket(int isTicket){
 		 this.isTicket=isTicket;
 	}
 	public int getIsTicket(){
 		 return this.isTicket;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusCheckApp(String statusCheckApp){
 		 this.statusCheckApp=statusCheckApp;
 	}
 	public String getStatusCheckApp(){
 		 return this.statusCheckApp;
 	}
 
 	 
 	public void setStatusPaymentDocumentCreattime(Timestamp statusPaymentDocumentCreattime){
 		 this.statusPaymentDocumentCreattime=statusPaymentDocumentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusPaymentDocumentCreattime(){
 		 return this.statusPaymentDocumentCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyNameCn(String companyNameCn){
 		 this.companyNameCn=companyNameCn;
 	}
 	public String getCompanyNameCn(){
 		 return this.companyNameCn;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	public String getId(){
 		 return this.id;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
 	public void setStatusCancelAppCreatTime(Timestamp statusCancelAppCreatTime){
 		 this.statusCancelAppCreatTime=statusCancelAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusCancelAppCreatTime(){
 		 return this.statusCancelAppCreatTime;
 	}
 
 	 
 	public void setStatusFinishAppCreatTime(Timestamp statusFinishAppCreatTime){
 		 this.statusFinishAppCreatTime=statusFinishAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusFinishAppCreatTime(){
 		 return this.statusFinishAppCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setStoreId(String storeId){
 		 this.storeId=storeId;
 	}
 	public String getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	public void setStatusSubmitAppCreatTime(Timestamp statusSubmitAppCreatTime){
 		 this.statusSubmitAppCreatTime=statusSubmitAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusSubmitAppCreatTime(){
 		 return this.statusSubmitAppCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setContactName(String contactName){
 		 this.contactName=contactName;
 	}
 	public String getContactName(){
 		 return this.contactName;
 	}
 
 	 
 	public void setTotalPrice(double totalPrice){
 		 this.totalPrice=totalPrice;
 	}
 	public double getTotalPrice(){
 		 return this.totalPrice;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setPaymentStatus(String paymentStatus){
 		 this.paymentStatus=paymentStatus;
 	}
 	public String getPaymentStatus(){
 		 return this.paymentStatus;
 	}
 
 	 
 	public void setIsBill(int isBill){
 		 this.isBill=isBill;
 	}
 	public int getIsBill(){
 		 return this.isBill;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusReturn(String statusReturn){
 		 this.statusReturn=statusReturn;
 	}
 	public String getStatusReturn(){
 		 return this.statusReturn;
 	}
 
 	 
 	public void setQuantity(long quantity){
 		 this.quantity=quantity;
 	}
 	public long getQuantity(){
 		 return this.quantity;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProductId(String productId){
 		 this.productId=productId;
 	}
 	public String getProductId(){
 		 return this.productId;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 
 	 
 	public void setStatusServiceAppCreattime(Timestamp statusServiceAppCreattime){
 		 this.statusServiceAppCreattime=statusServiceAppCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusServiceAppCreattime(){
 		 return this.statusServiceAppCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusPaymentDocument(String statusPaymentDocument){
 		 this.statusPaymentDocument=statusPaymentDocument;
 	}
 	public String getStatusPaymentDocument(){
 		 return this.statusPaymentDocument;
 	}
 
 	 
 	public void setIsMonthPay(int isMonthPay){
 		 this.isMonthPay=isMonthPay;
 	}
 	public int getIsMonthPay(){
 		 return this.isMonthPay;
 	}
 
 	 
 	public void setStatusRefundCreatTime(Timestamp statusRefundCreatTime){
 		 this.statusRefundCreatTime=statusRefundCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusRefundCreatTime(){
 		 return this.statusRefundCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusFinish(String statusFinish){
 		 this.statusFinish=statusFinish;
 	}
 	public String getStatusFinish(){
 		 return this.statusFinish;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserId(String userId){
 		 this.userId=userId;
 	}
 	public String getUserId(){
 		 return this.userId;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusSubmitApp(String statusSubmitApp){
 		 this.statusSubmitApp=statusSubmitApp;
 	}
 	public String getStatusSubmitApp(){
 		 return this.statusSubmitApp;
 	}
 
 	 
 	public void setPrice(double price){
 		 this.price=price;
 	}
 	public double getPrice(){
 		 return this.price;
 	}
 
 	 
 	public void setCreatedDate(Timestamp createdDate){
 		 this.createdDate=createdDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreatedDate(){
 		 return this.createdDate;
 	}
 
 	 
 	public void setStatusWaitPaymentCreatTime(Timestamp statusWaitPaymentCreatTime){
 		 this.statusWaitPaymentCreatTime=statusWaitPaymentCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusWaitPaymentCreatTime(){
 		 return this.statusWaitPaymentCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusService(String statusService){
 		 this.statusService=statusService;
 	}
 	public String getStatusService(){
 		 return this.statusService;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatus(String status){
 		 this.status=status;
 	}
 	public String getStatus(){
 		 return this.status;
 	}
 
 	 
}