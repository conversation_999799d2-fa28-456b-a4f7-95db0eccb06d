package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;

/**
 * <AUTHOR>
 * @Description :lv3报告语言转换
 * @date 2024/1/9
 */
public enum  ReportLuaTransEnum {
    CHI_EN_("中文+英文", "CHI-EN"),
    CHI_EN("中英", "CHI-EN"),
    CHI("中文", "CHI"),
    EN("英文", "EN"),


    ;

    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private ReportLuaTransEnum(String name, String index) {
        this.name = name;
        this.index = index;
    }

    // 普通方法
    public static String getReportLua(String name) {
        if((name.contains("中文") && name.contains("英文")) || name.contains("中英文")){
            return "CHI-EN";
        }else if(name.contains("中文") && !name.contains("英文")){
            return "CHI";
        }else if(!name.contains("中文") && name.contains("英文")){
            return "EN";
        }else {
            return "CHI";
        }
    }
    // get set 方法
    public String getName() {
        return name;
    }

    public String getIndex() {
        return index;
    }


}
