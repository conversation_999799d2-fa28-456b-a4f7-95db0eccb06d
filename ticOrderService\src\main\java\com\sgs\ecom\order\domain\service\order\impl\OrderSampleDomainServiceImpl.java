package com.sgs.ecom.order.domain.service.order.impl;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.config.OiqApplicationProperties;
import com.sgs.ecom.order.domain.order.OrderSampleDO;
import com.sgs.ecom.order.domain.order.OrderSampleFromDO;
import com.sgs.ecom.order.domain.order.OrderSampleRelateDO;
import com.sgs.ecom.order.domain.repository.order.interfaces.IOrderSampleRepository;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleFromDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleRelateDomainService;
import com.sgs.ecom.order.dto.center.BusinessLineDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.order.dto.order.OrderSampleMoreDTO;
import com.sgs.ecom.order.dto.order.OrderSampleRelateDTO;
import com.sgs.ecom.order.dto.rpc.CenterSampleDTO;
import com.sgs.ecom.order.dto.rpc.dml.CreateInquiryRpcDTO;
import com.sgs.ecom.order.dto.rpc.dml.DmlOrderSampleDTO;
import com.sgs.ecom.order.entity.order.OrderBaseInfo;
import com.sgs.ecom.order.entity.order.OrderSample;
import com.sgs.ecom.order.entity.order.OrderSampleFrom;
import com.sgs.ecom.order.entity.order.OrderSampleRelate;
import com.sgs.ecom.order.mapper.order.OrderSampleMapper;
import com.sgs.ecom.order.request.oiq.OiqOrderSampleFromReq;
import com.sgs.ecom.order.request.oiq.OiqSampleReq;
import com.sgs.ecom.order.service.custom.interfaces.ICustomCodeService;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.order.interfaces.IOrderSampleService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.select.SelectBaseUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.vo.VOOrderSample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
public class OrderSampleDomainServiceImpl extends BaseService implements IOrderSampleDomainService {

    @Autowired
    private IOrderSampleFromDomainService orderSampleFromDomainService;
    @Autowired
    private OrderSampleMapper orderSampleMapper;
    @Autowired
    private ICustomCodeService customCodeService;
    @Autowired
    private IOrderSampleService orderSampleService;
    @Autowired
    private ICenterTemplateSV  centerTemplateSV;
    @Autowired
    private IOrderSampleRelateDomainService orderSampleRelateDomainService;
    @Autowired
    private IOrderSampleRepository orderSampleRepository;
    @Autowired
    private OiqApplicationProperties oiqSampleProperties;


    @Override
    public void addSampleRpc(BaseOrderDTO baseInquiryOrderDTO, CreateInquiryRpcDTO createInquiryRpcDTO,Long lindId) {
        //样品是动态列 数据处理
        OrderSampleFromDO orderSampleFromDO=new OrderSampleFromDO();
        List<OrderSampleFrom> formList=orderSampleFromDO.rpcFormToForm(baseInquiryOrderDTO,createInquiryRpcDTO);
        //
        orderSampleFromDomainService.insertForeachAddCenter(formList,lindId);
        OrderSampleDO orderSampleDO=new OrderSampleDO();
        List<OrderSample> list=orderSampleDO.sampleFromListToSampleList(baseInquiryOrderDTO.getOrderNo(),createInquiryRpcDTO.getDmlRpcReqDTO().getNewGroupNo(),formList);
        insertForeach(list);
    }




    @Override
    public List<OrderSampleDTO> qryListByMap(String orderNo) {
        Map<String, Object> map = getBaseMap(orderNo);
        return orderSampleMapper.qryListByMap(map);
    }

    @Override
    public List<OrderSampleDTO> qryListByGroupNo(String orderNo, String groupNo) {
        Map<String,Object> map = new HashMap<>();
        map.put(SelectBaseUtil.ORDER_NO, orderNo);
        map.put(SelectMapUtil.STATE,1);
        map.put(SelectMapUtil.GROUP_NO,groupNo);
        return orderSampleMapper.qryListByMap(map);
    }

    private Map<String, Object> getBaseMap(String orderNo) {
        Map<String,Object> map = new HashMap<>();
        map.put(SelectBaseUtil.ORDER_NO, orderNo);
        map.put(SelectMapUtil.STATE,1);
        return map;
    }

    private void insertForeach(List<OrderSample> orderSampleList){
        if(!ValidationUtil.isEmpty(orderSampleList)){
            orderSampleRepository.insertForeachEntity(orderSampleList);
        }
    }

    @Override
    public List<OiqSampleDTO> getOiqOrderSample( String orderNo, String groupNo,Long lineId,Boolean useState) throws Exception{

        List<CenterSampleDTO> centerSampleDTOList=centerTemplateSV.qryBusiByLine(lineId, BusinessLineDTO.LINE);
        List<OrderSampleMoreDTO> sampleDTOList=orderSampleService.oiqOrderSampleList(orderNo,groupNo,useState);
        OrderSampleDO orderSampleDO=new OrderSampleDO();
        int size=oiqSampleProperties.getSampleSizeByLine("DEFAULT");
        sampleDTOList=orderSampleDO.sampleMoreAddCenterSample(sampleDTOList,centerSampleDTOList,size);
        orderSampleFromDomainService.sampleFromAddEnum(sampleDTOList);
        List<OiqSampleDTO> sampleBaseList=new ArrayList<>();
        for(OrderSampleMoreDTO orderSampleDTO:sampleDTOList){
            OiqSampleDTO oiqSampleDTO=new OiqSampleDTO();
            baseCopyObj.copyWithNull(oiqSampleDTO,orderSampleDTO);
            oiqSampleDTO.setSampleId(Long.parseLong(orderSampleDTO.getSampleId()));
            oiqSampleDTO.setSampleFromDTOList(orderSampleDTO.getSampleFromDTOList());
            sampleBaseList.add(oiqSampleDTO);
        }
        return sampleBaseList;
    }





    public void updateSampleList(List<OiqSampleReq> oiqSampleReqList, OiqOrderReqDTO oiqOrderReqDTO) {
        if(ValidationUtil.isEmpty(oiqSampleReqList)){
            return;
        }
        OrderSampleDO orderSampleDO=new OrderSampleDO();
        List<OrderSample> sampleList=new ArrayList<>();
        List<OrderSample> sampleInquiryList=new ArrayList<>();
        List<OrderSampleFrom> orderSampleFromList=new ArrayList<>();
        List<OrderSampleFrom> orderSampleFromInquiryList=new ArrayList<>();
        //样品对象数据
        List<OrderSampleMoreDTO> orderSampleDTOList=new ArrayList<>();
        //询报价模式做更新 申请表先删后增加
        Map<String,String> codeToKey=new HashMap<>();
        Map<String,Integer> codeToRow=new HashMap<>();
        for(int n=0;n<oiqSampleReqList.size();n++){
            OiqSampleReq oiqSampleReq=oiqSampleReqList.get(n);
            String sampleNo=oiqOrderReqDTO.getPortal()?customCodeService.getNewSampleNo():oiqSampleReq.getSampleNo();
            codeToKey.put(oiqSampleReq.getSampleNo(),sampleNo);
            codeToRow.put(sampleNo, n+1);

            Map<String, String> sampleKeyValMap = oiqSampleReq.getSampleFromDTOList().stream().collect(Collectors.toMap(OiqOrderSampleFromReq::getSampleKey, OiqOrderSampleFromReq::getSampleValue, (v1, v2) -> v2));

            OrderSample orderSample=orderSampleDO.orderSampleAddSampleNo(oiqSampleReq,oiqOrderReqDTO,sampleNo, sampleKeyValMap);
            sampleList.add(orderSample);
            List<OrderSampleFrom> list=orderSampleDO.orderSampleFormAddSampleNo(oiqSampleReq,oiqOrderReqDTO,sampleNo);
            orderSampleFromList.addAll(list);

            if (oiqOrderReqDTO.getAddInquiry() == 1) {
                String inquirySampleNo = customCodeService.getNewSampleNo();
                OrderSample orderSampleInquiry = orderSampleDO.orderSampleAddSampleNo(oiqSampleReq, oiqOrderReqDTO, inquirySampleNo, sampleKeyValMap);
                orderSampleInquiry.setOrderNo(oiqOrderReqDTO.getInquiryOrderNo());
                orderSampleInquiry.setGroupNo(oiqOrderReqDTO.getGroupNo());
                orderSampleInquiry.setSampleName(orderSampleInquiry.getSampleNameCn());
                if (ValidationUtil.isEmpty(orderSampleInquiry.getSampleName())) {
                    orderSampleInquiry.setSampleName(orderSampleInquiry.getSampleNameEn());
                }
                sampleInquiryList.add(orderSampleInquiry);
                List<OrderSampleFrom> inquiryList = orderSampleDO.orderSampleFormAddSampleNo(oiqSampleReq, oiqOrderReqDTO.getInquiryOrderNo(), oiqOrderReqDTO.getInquiryGroupNo(), inquirySampleNo);
                orderSampleFromInquiryList.addAll(inquiryList);
            }

            OrderSampleMoreDTO orderSampleMoreDTO=orderSampleDO.sampleToMore(orderSample,list);
            orderSampleMoreDTO.setRow(String.valueOf(n+1));
            orderSampleDTOList.add(orderSampleMoreDTO);
            if (!oiqOrderReqDTO.getPortal()) {
                orderSampleRepository.updateByPrimaryKeySelective(orderSample);
            }
        }
        if(oiqOrderReqDTO.getPortal()){
            insertForeach(sampleList);
        }

        orderSampleFromDomainService.updateOrderSampleFromByState(oiqOrderReqDTO.getOrderNo());
        //加applicationLineId
        orderSampleFromDomainService.insertForeachAddCenter(orderSampleFromList,oiqOrderReqDTO.getApplicationLineId());

        if (oiqOrderReqDTO.getAddInquiry() == 1) {
            insertForeach(sampleInquiryList);
            orderSampleFromDomainService.insertForeachAddCenter(orderSampleFromInquiryList, oiqOrderReqDTO.getApplicationLineId());
            List<OrderSampleFrom> baseFromListByInquiry = orderSampleFromDomainService.getBaseFromListByInquiry(oiqOrderReqDTO.getInquiryOrderNo(), oiqOrderReqDTO.getApplicationLineId());
            orderSampleFromDomainService.insertForeachAddCenter(baseFromListByInquiry, oiqOrderReqDTO.getApplicationLineId());
        }

        orderSampleFromDomainService.sampleFromAddEnum(orderSampleDTOList);
        oiqOrderReqDTO.getDmlMainReqDTO().setOrderSampleDTOList(orderSampleDTOList);

        oiqOrderReqDTO.setCodeToKey(codeToKey);
        oiqOrderReqDTO.setCodeToRow(codeToRow);
    }

    @Override
    public void dmlAddSampleList(List<DmlOrderSampleDTO> sampleDTOList, String orderNo, String groupNo,Long lindId) {
        OrderSampleDO orderSampleDO=new OrderSampleDO();
        if(ValidationUtil.isEmpty(sampleDTOList)){
            throw new BusinessException(ResultEnumCode.USER_SAMPLE_IS_NULL);
        }

        List<OrderSample> sampleList=new ArrayList<>();


        //获取我们自己的配置数据
        List<OrderSampleFromDTO> fromDTOList=orderSampleFromDomainService.selectListByOrderNo(orderNo);
        if(ValidationUtil.isEmpty(fromDTOList)){
            return;
        }
        Map<String,OrderSampleFromDTO> fromMap=fromDTOList.stream().collect(Collectors.toMap(OrderSampleFromDTO::getSampleKey, Function.identity(),
                (key1, key2) -> key1, LinkedHashMap::new));


        //ba


        List<OrderSampleFrom> newList=new ArrayList<>();
        //申请表先删后增加
        for(int n=0;n<sampleDTOList.size();n++){
            DmlOrderSampleDTO dmlSampleDTO=sampleDTOList.get(n);
            String sampleNo=dmlSampleDTO.getSampleNo();
            OrderSample orderSample=orderSampleDO.orderSampleAddSampleNo(sampleNo,orderNo,groupNo);

            Map<String, Object> collectMap = dmlSampleDTO.getSampleAttr().stream().collect(Collectors.toMap(E->E.getSampleKey(),
                    E->E.getSampleValue(), (key1, key2) -> key2));
            //样品重新赋值
            orderSample.setSampleNameCn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_CN,"").toString());
            orderSample.setSampleNameEn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_EN,"").toString());
            sampleList.add(orderSample);



            List<OrderSampleFrom> list=orderSampleDO.orderSampleFormAddSampleNo(dmlSampleDTO,sampleNo,orderNo,groupNo);
            Map<String,String> sizeMap=new HashMap<>();
            for(int t=0;t<list.size();t++) {
                OrderSampleFrom orderSampleFrom=list.get(t);
                //赋值排序和状态
                if (fromMap.containsKey(orderSampleFrom.getSampleKey())) {
                    OrderSampleFromDTO base=fromMap.get(orderSampleFrom.getSampleKey());
                    orderSampleFrom.setSortShow(base.getSortShow());
                    orderSampleFrom.setGroupNo(base.getGroupNo());
                    sizeMap.put(orderSampleFrom.getSampleKey(),"1");
                } else {
                    orderSampleFrom.setState(0);
                }
                newList.add(orderSampleFrom);
            }
            //当newList的状态为1的少于fromMap 的数量 提交新的数据
            if(sizeMap.size()<fromMap.size()){
                for(String key:fromMap.keySet()){
                    if(!sizeMap.containsKey(key)){
                        OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
                        baseCopyObj.copyWithNull(orderSampleFrom,fromMap.get(key));
                        orderSampleFrom.setSampleValue("");
                        orderSampleFrom.setSampleNo(sampleNo);
                        orderSampleFrom.setOrderNo(orderNo);
                        orderSampleFrom.setGroupNo(groupNo);
                        orderSampleFrom.setState(1);
                        newList.add(orderSampleFrom);
                    }
                }
            }
        }
        orderSampleRepository.delOrderSampleByGroup(groupNo);
        insertForeach(sampleList);
        orderSampleFromDomainService.updateOrderSampleFromByState(orderNo);
        orderSampleFromDomainService.insertForeachAddCenter(newList,lindId);
        //dml没样品关联关系 也删除
        orderSampleRelateDomainService.delSampleRelateByOrderNo(orderNo);
    }

    public void copyInquirySampleByOld(OrderBaseInfo old, OrderBaseInfo newOrder,Map<Long,Long> detailMap)throws Exception{
        //获取基础的样品动态列字段
        List<OrderSampleFrom> fromList=new ArrayList<>();
        OrderSampleDO orderSampleDO=new OrderSampleDO();
        List<CenterSampleDTO> centerSampleDTOList=centerTemplateSV.qryBusiByLine(newOrder.getApplicationLineId(), BusinessLineDTO.LINE);
        OrderSampleFromDO orderSampleFromDO=new OrderSampleFromDO();
        List<OrderSampleFrom> baseSampleFromList=orderSampleFromDO.getBaseFromListByInquiry(newOrder.getOrderNo(),newOrder.getApplicationLineId(),centerSampleDTOList);
        fromList.addAll(baseSampleFromList);
        // 样品逻辑是只要样品名称
        String selectGroup=old.getGroupNo();
        List<OrderSampleMoreDTO> list=orderSampleService.oiqOrderSampleList(old.getOrderNo(),selectGroup,true);
        List<OrderSample> orderSampleList=new ArrayList<>();
        Map<String,String> sampleMap=new HashMap<>();
        for(int n=0;n<list.size();n++){
            OrderSampleMoreDTO orderSampleMoreDTO=list.get(n);
            String sampleNo=customCodeService.getNewSampleNo();
            sampleMap.put(orderSampleMoreDTO.getSampleNo(),sampleNo);
            OrderSample orderSample=orderSampleDO.orderSampleAddSampleNo(sampleNo,newOrder.getOrderNo(),newOrder.getTmpGroupNo());
            orderSample.setSampleName(orderSampleMoreDTO.getSampleName());
            orderSampleList.add(orderSample);
            //用基础的样品配置做新增
            for(int t=0;t<baseSampleFromList.size();t++){
                OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
                baseCopyObj.copyWithNull(orderSampleFrom,baseSampleFromList.get(t));
                orderSampleFrom.setOrderNo(newOrder.getOrderNo());
                orderSampleFrom.setGroupNo(newOrder.getTmpGroupNo());
                orderSampleFrom.setState(1);
                orderSampleFrom.setSampleNo(sampleNo);
                fromList.add(orderSampleFrom);
            }
        }

        //获取旧的关联关系
        List<OrderSampleRelateDTO> orderSampleRelateDTOList = orderSampleRelateDomainService.selectListByOrderNo(old.getOrderNo(),old.getGroupNo());
        List<OrderSampleRelate> orderSampleRelateList=new ArrayList<>();
        for(int n=0;n<orderSampleRelateDTOList.size();n++){
            OrderSampleRelateDTO orderSampleRelateDTO=orderSampleRelateDTOList.get(n);
            OrderSampleRelateDO orderSampleRelateDO=new OrderSampleRelateDO();
            String sampleNo=sampleMap.getOrDefault(orderSampleRelateDTO.getSampleNo(),"");
            Long detailId=detailMap.getOrDefault(Long.parseLong(orderSampleRelateDTO.getDetailId()),null);
            OrderSampleRelate orderSampleRelate=orderSampleRelateDO.getSampleRelate(newOrder.getOrderNo(),newOrder.getTmpGroupNo(),sampleNo,detailId);
            if(!ValidationUtil.isEmpty(orderSampleRelate)){
                orderSampleRelateList.add(orderSampleRelate);
            }
        }

        //保存样品
        insertForeach(orderSampleList);
        orderSampleFromDomainService.insertForeachAddCenter(fromList,newOrder.getApplicationLineId());
        orderSampleRelateDomainService.insertForeach(orderSampleRelateList);

    }

    @Override
    public List<OrderSampleDTO> qryPackageAndSampleRelation(VOOrderSample voOrderSample) {
        return orderSampleRepository.qryPackageAndSampleRelation( voOrderSample);
    }

    private void updateEntity(OrderSample orderSample){
        if(!ValidationUtil.isEmpty(orderSample)){
            //orderSampleMapper.updateEntity(orderSample);
        }
    }


}
