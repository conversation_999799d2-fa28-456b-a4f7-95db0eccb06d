package com.sgs.ecom.order.fegin;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.util.SysException;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.DistributedLock;
import com.sgs.ecom.order.dto.send.mail.OiqOtherDTO;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.request.base.BaseFileReq;
import com.sgs.ecom.order.request.dml.DmlReportReq;
import com.sgs.ecom.order.service.util.interfaces.IDmlSV;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/business/rpc.v1.order/report")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class ReportServiceRpc extends ControllerUtil {

	@Autowired
	private IDmlSV dmlSV;
	@Autowired
	private MailEventUtil mailEventUtil;
	@Autowired
	private SmsEventUtil smsEventUtil;

	/**
	*@Function: saveReport
	*@Description
	*@param:
	*@author: Xiwei_Qiu @date: 2022/8/8 @version:
	**/
	@HystrixCommand
	@RequestMapping(value = "saveReport", method = {RequestMethod.POST})
	public ResultBody saveReport(
		@Validated(BaseBean.Insert.class)
		@RequestBody DmlReportReq dmlReportReq) throws Exception {
		dmlSV.saveReport(dmlReportReq);
		//替换逻辑处理异步
		if(dmlReportReq.getEventFlg()==1 && !ValidationUtil.isEmpty(dmlReportReq.getOiqOtherDTO())){
			//短信发送逻辑
			smsEventUtil.sendSms(dmlReportReq.getEventOrderNo(),dmlReportReq.getOrderType(), OiqSmsEnum.EDIT_REPORT);
			List<BaseFileReq> fileReqList=dmlReportReq.getOiqOtherDTO().getFileReqList();
			if(OrderTypeEnum.OIQ_ORDER.getIndex().equals(dmlReportReq.getOrderType())){
				OiqOtherDTO oiqOtherDTO=new OiqOtherDTO();
				oiqOtherDTO.setSendOperatorCodeFlg(1);
				oiqOtherDTO.setFileReqList(fileReqList);
				mailEventUtil.sendMail(dmlReportReq.getBaseOrderDTO(),OiqMailEnum.UPDATE_REPORT,1L,oiqOtherDTO);

			}
			if(OrderTypeEnum.OIQ_SMALL_ORDER.getIndex().equals(dmlReportReq.getOrderType())){
				mailEventUtil.sendPortalMail(dmlReportReq.getEventOrderNo(),dmlReportReq.getOrderType(), OiqMailEnum.PORTAL_REPORT_CHANGE,1L,dmlReportReq.getOiqOtherDTO());
			}
		}
		return ResultBody.success();
	}
}
