package com.sgs.ecom.order.dto.log;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;

public class BaseLog {

	private String orderNo;
	private String orderType;
	private String detailNo;
	private OrderOperatorTypeEnum orderOperatorTypeEnum;
	private String operatorText;
	private String memo;
	private String csCode;
	private int isShow;

	private boolean updateFlag;

	public boolean isUpdateFlag() {
		return updateFlag;
	}

	public void setUpdateFlag(boolean updateFlag) {
		this.updateFlag = updateFlag;
	}

	public BaseLog(String orderNo, String orderType, OrderOperatorTypeEnum orderOperatorTypeEnum,
				   String operatorText, String memo, String csCode, int isShow) {
		this.orderNo = orderNo;
		this.orderType = orderType;
		this.detailNo = "";
		this.orderOperatorTypeEnum = orderOperatorTypeEnum;
		this.operatorText = operatorText;
		this.memo = memo;
		this.csCode = csCode;
		this.isShow=isShow;
	}

	public BaseLog(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO,OrderOperatorTypeEnum orderOperatorTypeEnum, String operatorText, String memo, String csCode, int isShow) {
		this.orderNo = orderBaseInfoCheckDTO.getOrderNo();
		this.orderType = String.valueOf(orderBaseInfoCheckDTO.getOrderType());
		this.orderOperatorTypeEnum = orderOperatorTypeEnum;
		this.operatorText = operatorText;
		this.memo = memo;
		this.csCode = csCode;
		this.isShow = isShow;
	}

	public BaseLog(BaseOrderDTO baseOrderDTO, OrderOperatorTypeEnum orderOperatorTypeEnum, String operatorText, String memo, String csCode, int isShow) {
		this.orderNo = baseOrderDTO.getOrderNo();
		this.orderType = baseOrderDTO.getOrderType();
		this.orderOperatorTypeEnum = orderOperatorTypeEnum;
		this.operatorText = operatorText;
		this.memo = memo;
		this.csCode = csCode;
		this.isShow = isShow;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getDetailNo() {
		return detailNo;
	}

	public void setDetailNo(String detailNo) {
		this.detailNo = detailNo;
	}

	public OrderOperatorTypeEnum getOrderOperatorTypeEnum() {
		return orderOperatorTypeEnum;
	}

	public void setOrderOperatorTypeEnum(OrderOperatorTypeEnum orderOperatorTypeEnum) {
		this.orderOperatorTypeEnum = orderOperatorTypeEnum;
	}

	public String getOperatorText() {
		return operatorText;
	}

	public void setOperatorText(String operatorText) {
		this.operatorText = operatorText;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public String getCsCode() {
		return csCode;
	}

	public void setCsCode(String csCode) {
		this.csCode = csCode;
	}

	public int getIsShow() {
		return isShow;
	}

	public void setIsShow(int isShow) {
		this.isShow = isShow;
	}
}
