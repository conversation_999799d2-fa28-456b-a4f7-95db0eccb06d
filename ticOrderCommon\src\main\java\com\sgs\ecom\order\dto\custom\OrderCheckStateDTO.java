package com.sgs.ecom.order.dto.custom;

import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;

import java.math.BigDecimal;

public class OrderCheckStateDTO {
    private Long orderId;
    private String orderNo;
    private Long labId;
    private BigDecimal realAmount;
    private Long userId;
    private String relateOrderNo;
    private int orderType;
    private int state;
    private int subState;
    private int payState;
    private int hisState;
    private int isPayReceived;
    private int monthPay;
    private int isInvoice;
    private String bu;

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public int getHisState() {
        return hisState;
    }

    public void setHisState(int hisState) {
        this.hisState = hisState;
    }

    public int getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(int isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public int getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(int monthPay) {
        this.monthPay = monthPay;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public int getIsInvoice() {
        return isInvoice;
    }

    public void setIsInvoice(int isInvoice) {
        this.isInvoice = isInvoice;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public OrderCheckStateDTO(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) {
        this.orderId = orderBaseInfoCheckDTO.getOrderId();
        this.orderNo = orderBaseInfoCheckDTO.getOrderNo();
        this.realAmount = orderBaseInfoCheckDTO.getRealAmount();
        this.orderType = orderBaseInfoCheckDTO.getOrderType();
    }

    public OrderCheckStateDTO() {
    }
}
