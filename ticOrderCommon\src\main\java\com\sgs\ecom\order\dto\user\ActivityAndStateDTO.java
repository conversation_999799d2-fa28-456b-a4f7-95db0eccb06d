package com.sgs.ecom.order.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.util.List;

public class ActivityAndStateDTO {
    /**
     * 活动编号
     */
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String  activityCode;

    /**
     * 状态+数量集合
     */
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<StateAndNumDTO> stateList;


    public String getActivityCode() {
        return activityCode;
    }

    public void setActivityCode(String activityCode) {
        this.activityCode = activityCode;
    }

    public List<StateAndNumDTO> getStateList() {
        return stateList;
    }

    public void setStateList(List<StateAndNumDTO> stateList) {
        this.stateList = stateList;
    }
}
