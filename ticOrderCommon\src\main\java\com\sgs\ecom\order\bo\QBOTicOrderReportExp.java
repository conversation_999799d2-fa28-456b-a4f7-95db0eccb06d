package com.sgs.ecom.order.bo; 
 
import java.sql.Timestamp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.CharacterVaild;
import com.platform.util.json.TimeFormatSerializer; 
public class QBOTicOrderReportExp{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select DISTINCT a.status,IF(changeStatus='P','真实订单','测试订单') change_status,a.orderNo order_no,a.CreatedDate created_date,a.productName product_name,skuAttr sku_attr,IFNULL('N/A',orderFinishTime) order_finish_time,price,quantity,remark,a.totalPrice total_price,companyNameCn company_name_cn,BuyerLoginID buyer_login_id,a.OriginalPrice original_price,PaymentAmount payment_amount,PaymentDate payment_date,PaymentNo payment_no,case paymentType when 'alipay' then '支付宝' when 'unionpay_b2b' then '企业银联' when 'unionpay' then '银联' when 'offline' then '线下支付' else '' end payment_type,b.orderId order_id,if(paymenttype='offline','N/A',if(c.status=1,'Yes','No')) pay_state,a.storeName store_name,a.userName user_name,contactName contact_name,contactTel contact_tel,contactEmail contact_email,companyNameCn company_name_cn,CONCAT(a.province,a.city,a.district,a.companyAddress) company_address,companyNameEn company_name_en,fapiaoType fapiao_type,fapiaoTitle fapiao_title,fapiaoNumber fapiao_number,bankAddress bank_address,bankNumber bank_number,registerAddress register_address,registerTel register_tel,deliveryCompanyName delivery_company_name,deliveryCompanyAddress delivery_company_address,deliveryUserName delivery_user_name,deliveryPhone delivery_phone,deliveryEmail delivery_email,if(a.status='F',if(closeStatus='Y','补差价付款完成','服务完成'),'N/A') close_status,closePayOrder close_pay_order,a.parentId parent_id,operator,case a.storeId when 'SL' then (case a.status when 'M' then '未提交' when 'G' then '未提交' when 'A' then '未提交' when 'R' then '未提交' else sampleName end) when 'CPCH' then (case a.status when 'M' then '未提交' when 'G' then '未提交' when 'A' then '未提交' when 'R' then '未提交' else sampleName end) else 'N/A' end sample_name,if(paymentStatus='abstractPay','月结','现付') payment_status,is_month_pay,is_ticket,leads_code from TIC_Order a JOIN TIC_OrderLine b ON a.orderno=b.orderno LEFT JOIN TIC_Payment c ON a.id=c.orderid LEFT JOIN TIC_OrderExtension d ON a.id=d.orderid LEFT JOIN (select OrderID,sampleName from TIC_ApplicationFormSL union all select OrderID,sampleName from TIC_ApplicationFormCPCH ) e ON a.id=e.orderid JOIN (select orderId,fileName from TIC_Attachment where atttype='report' ) f ON a.id=f.orderid where 1=1 "; 
 
 	public static final String OWNER ="bbc";

 	public static final String DELIVERYPHONE="deliveryPhone";
 	public static final String CLOSESTATUS="closeStatus";
 	public static final String ORIGINALPRICE="originalPrice";
 	public static final String TOTALPRICE="totalPrice";
 	public static final String ORDERID="orderId";
 	public static final String DELIVERYEMAIL="deliveryEmail";
 	public static final String REMARK="remark";
 	public static final String PAYSTATE="payState";
 	public static final String PAYMENTAMOUNT="paymentAmount";
 	public static final String CONTACTTEL="contactTel";
 	public static final String FAPIAONUMBER="fapiaoNumber";
 	public static final String PRODUCTNAME="productName";
 	public static final String PAYMENTNO="paymentNo";
 	public static final String OPERATOR="operator";
 	public static final String PAYMENTTYPE="paymentType";
 	public static final String SAMPLENAME="sampleName";
 	public static final String IS_TICKET="isTicket";
 	public static final String FAPIAOTYPE="fapiaoType";
 	public static final String PRICE="price";
 	public static final String ORDERFINISHTIME="orderFinishTime";
 	public static final String STORENAME="storeName";
 	public static final String PAYMENTSTATUS="paymentStatus";
 	public static final String BUYERLOGINID="buyerLoginID";
 	public static final String FAPIAOTITLE="fapiaoTitle";
 	public static final String BANKNUMBER="bankNumber";
 	public static final String REGISTERTEL="registerTel";
 	public static final String ORDERNO="orderNo";
 	public static final String QUANTITY="quantity";
 	public static final String PAYMENTDATE="paymentDate";
 	public static final String CONTACTEMAIL="contactEmail";
 	public static final String CONTACTNAME="contactName";
 	public static final String COMPANYNAMEEN="companyNameEN";
 	public static final String COMPANYNAMECN="companyNameCN";
 	public static final String USERNAME="userName";
 	public static final String BANKADDRESS="bankAddress";
 	public static final String PARENTID="parentId";
 	public static final String IS_MONTH_PAY="isMonthPay";
 	public static final String REGISTERADDRESS="registerAddress";
 	public static final String DELIVERYCOMPANYNAME="deliveryCompanyName";
 	public static final String DELIVERYUSERNAME="deliveryUserName";
 	public static final String LEADS_CODE="leadsCode";
 	public static final String COMPANYADDRESS="companyAddress";
 	public static final String CREATEDDATE="createdDate";
 	public static final String DELIVERYCOMPANYADDRESS="deliveryCompanyAddress";
 	public static final String CLOSEPAYORDER="closePayOrder";
 	public static final String CHANGESTATUS="changeStatus";
 	public static final String SKUATTR="skuAttr";
 	public static final String STATUS="status";
 	public static final String ROWNUM="rownum";
 	public static final String EXP_DATE="expDate";
 	public static final String CHOOSE_STATUS="chooseStatus";
 	public static final String CHOOSE_DATE="chooseDate";
 	public static final String FILE_NAME="fileName";

 	@BeanAnno(value="deliveryPhone",table="d")
 	private String deliveryPhone;
 	@BeanAnno("closeStatus")
 	private String closeStatus;
 	@BeanAnno(value="OriginalPrice",table="a")
 	private double originalPrice;
 	@BeanAnno(value="totalPrice",table="a")
 	private double totalPrice;
 	@BeanAnno(value="orderId",table="b")
 	private String orderId;
 	@BeanAnno(value="deliveryEmail",table="d")
 	private String deliveryEmail;
 	@BeanAnno(value="remark",table="b")
 	private String remark;
 	@BeanAnno("payState")
 	private String payState;
 	@BeanAnno(value="PaymentAmount",table="c")
 	private double paymentAmount;
 	@BeanAnno(value="contactTel",table="a")
 	private String contactTel;
 	@BeanAnno(value="fapiaoNumber",table="d")
 	private String fapiaoNumber;
 	@BeanAnno(value="productName",table="a")
 	private String productName;
 	@BeanAnno(value="PaymentNo",table="c")
 	private String paymentNo;
 	@BeanAnno(value="operator",table="a")
 	private String operator;
 	@BeanAnno("paymentType")
 	private String paymentType;
 	@BeanAnno("sampleName")
 	private String sampleName;
 	@BeanAnno(value="is_ticket",table="a")
 	private int isTicket;
 	@BeanAnno(value="fapiaoType",table="d")
 	private String fapiaoType;
 	@BeanAnno(value="price",table="b")
 	private double price;
 	@BeanAnno("orderFinishTime")
 	private String orderFinishTime;
 	@BeanAnno(value="storeName",table="a")
 	private String storeName;
 	@BeanAnno("paymentStatus")
 	private String paymentStatus;
 	@BeanAnno(value="BuyerLoginID",table="c")
 	private String buyerLoginID;
 	@BeanAnno(value="fapiaoTitle",table="d")
 	private String fapiaoTitle;
 	@BeanAnno(value="bankNumber",table="d")
 	private String bankNumber;
 	@BeanAnno(value="registerTel",table="d")
 	private String registerTel;
 	@BeanAnno(value="orderNo",table="a")
 	private String orderNo;
 	@BeanAnno(value="quantity",table="b")
 	private long quantity;
 	@BeanAnno(value="PaymentDate",table="c")
 	private String paymentDate;
 	@BeanAnno(value="contactEmail",table="a")
 	private String contactEmail;
 	@BeanAnno(value="contactName",table="a")
 	private String contactName;
 	@BeanAnno(value="companyNameEn",table="a")
 	private String companyNameEN;
 	@BeanAnno(value="companyNameCn",table="a")
 	private String companyNameCN;
 	@BeanAnno(value="userName",table="a")
 	private String userName;
 	@BeanAnno(value="bankAddress",table="d")
 	private String bankAddress;
 	@BeanAnno(value="parentId",table="a")
 	private String parentId;
 	@BeanAnno(value="is_month_pay",table="a")
 	private int isMonthPay;
 	@BeanAnno(value="registerAddress",table="d")
 	private String registerAddress;
 	@BeanAnno(value="deliveryCompanyName",table="d")
 	private String deliveryCompanyName;
 	@BeanAnno(value="deliveryUserName",table="d")
 	private String deliveryUserName;
 	@BeanAnno(value="leads_code",table="a")
 	private String leadsCode;
 	@BeanAnno("companyAddress")
 	private String companyAddress;
 	@BeanAnno(value="CreatedDate",table="a")
 	private Timestamp createdDate;
 	@BeanAnno(value="deliveryCompanyAddress",table="d")
 	private String deliveryCompanyAddress;
 	@BeanAnno(value="closePayOrder",table="a")
 	private String closePayOrder;
 	@BeanAnno("changeStatus")
 	private String changeStatus;
 	@BeanAnno(value="skuAttr",table="b")
 	private String skuAttr;
 	@BeanAnno(value="status",table="a")
 	private String status;
 	@BeanAnno(value="rownum")
 	private int rownum;
 	@BeanAnno(value="exp_date")
 	private String expDate;
 	@BeanAnno(value="choose_status")
 	private String chooseStatus;
 	@BeanAnno(value="choose_date")
 	private String chooseDate;
 	@BeanAnno(value="fileName")
 	private String fileName;

 	@CharacterVaild(len = 100) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setOriginalPrice(double originalPrice){
 		 this.originalPrice=originalPrice;
 	}
 	public double getOriginalPrice(){
 		 return this.originalPrice;
 	}
 
 	 
 	@CharacterVaild(len = 3) 
 	public void setPayState(String payState){
 		 this.payState=payState;
 	}
 	public String getPayState(){
 		 return this.payState;
 	}
 
 	 
 	@CharacterVaild(len = 7) 
 	public void setCloseStatus(String closeStatus){
 		 this.closeStatus=closeStatus;
 	}
 	public String getCloseStatus(){
 		 return this.closeStatus;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setFapiaoTitle(String fapiaoTitle){
 		 this.fapiaoTitle=fapiaoTitle;
 	}
 	public String getFapiaoTitle(){
 		 return this.fapiaoTitle;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setRegisterTel(String registerTel){
 		 this.registerTel=registerTel;
 	}
 	public String getRegisterTel(){
 		 return this.registerTel;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setBankAddress(String bankAddress){
 		 this.bankAddress=bankAddress;
 	}
 	public String getBankAddress(){
 		 return this.bankAddress;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContactTel(String contactTel){
 		 this.contactTel=contactTel;
 	}
 	public String getContactTel(){
 		 return this.contactTel;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
 	@CharacterVaild(len = 4) 
 	public void setChangeStatus(String changeStatus){
 		 this.changeStatus=changeStatus;
 	}
 	public String getChangeStatus(){
 		 return this.changeStatus;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setFapiaoNumber(String fapiaoNumber){
 		 this.fapiaoNumber=fapiaoNumber;
 	}
 	public String getFapiaoNumber(){
 		 return this.fapiaoNumber;
 	}
 
 	 
 	@CharacterVaild(len = 800) 
 	public void setCompanyAddress(String companyAddress){
 		 this.companyAddress=companyAddress;
 	}
 	public String getCompanyAddress(){
 		 return this.companyAddress;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setOperator(String operator){
 		 this.operator=operator;
 	}
 	public String getOperator(){
 		 return this.operator;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContactEmail(String contactEmail){
 		 this.contactEmail=contactEmail;
 	}
 	public String getContactEmail(){
 		 return this.contactEmail;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setRegisterAddress(String registerAddress){
 		 this.registerAddress=registerAddress;
 	}
 	public String getRegisterAddress(){
 		 return this.registerAddress;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDeliveryUserName(String deliveryUserName){
 		 this.deliveryUserName=deliveryUserName;
 	}
 	public String getDeliveryUserName(){
 		 return this.deliveryUserName;
 	}
 
 	 
 	public void setIsTicket(int isTicket){
 		 this.isTicket=isTicket;
 	}
 	public int getIsTicket(){
 		 return this.isTicket;
 	}
 
 	 
 	public void setPrice(double price){
 		 this.price=price;
 	} 
 	public double getPrice(){
 		 return this.price;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBuyerLoginID(String buyerLoginID){
 		 this.buyerLoginID=buyerLoginID;
 	}
 	public String getBuyerLoginID(){
 		 return this.buyerLoginID;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyNameCN(String companyNameCN){
 		 this.companyNameCN=companyNameCN;
 	}
 	public String getCompanyNameCN(){
 		 return this.companyNameCN;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyNameEN(String companyNameEN){
 		 this.companyNameEN=companyNameEN;
 	}
 	public String getCompanyNameEN(){
 		 return this.companyNameEN;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setPaymentDate(String paymentDate){
 		 this.paymentDate=paymentDate;
 	}
 	public String getPaymentDate(){
 		 return this.paymentDate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDeliveryPhone(String deliveryPhone){
 		 this.deliveryPhone=deliveryPhone;
 	}
 	public String getDeliveryPhone(){
 		 return this.deliveryPhone;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSampleName(String sampleName){
 		 this.sampleName=sampleName;
 	}
 	public String getSampleName(){
 		 return this.sampleName;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setFapiaoType(String fapiaoType){
 		 this.fapiaoType=fapiaoType;
 	}
 	public String getFapiaoType(){
 		 return this.fapiaoType;
 	}
 
 	 
 	@CharacterVaild(len = *********) 
 	public void setSkuAttr(String skuAttr){
 		 this.skuAttr=skuAttr;
 	}
 	public String getSkuAttr(){
 		 return this.skuAttr;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setContactName(String contactName){
 		 this.contactName=contactName;
 	}
 	public String getContactName(){
 		 return this.contactName;
 	}
 
 	 
 	public void setQuantity(long quantity){
 		 this.quantity=quantity;
 	}
 	public long getQuantity(){
 		 return this.quantity;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDeliveryCompanyAddress(String deliveryCompanyAddress){
 		 this.deliveryCompanyAddress=deliveryCompanyAddress;
 	}
 	public String getDeliveryCompanyAddress(){
 		 return this.deliveryCompanyAddress;
 	}
 
 	 
 	public void setTotalPrice(double totalPrice){
 		 this.totalPrice=totalPrice;
 	} 
 	public double getTotalPrice(){
 		 return this.totalPrice;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setOrderFinishTime(String orderFinishTime){
 		 this.orderFinishTime=orderFinishTime;
 	}
 	public String getOrderFinishTime(){
 		 return this.orderFinishTime;
 	}
 
 	 
 	@CharacterVaild(len = 2) 
 	public void setPaymentStatus(String paymentStatus){
 		 this.paymentStatus=paymentStatus;
 	}
 	public String getPaymentStatus(){
 		 return this.paymentStatus;
 	}
 
 	 
 	public void setPaymentAmount(double paymentAmount){
 		 this.paymentAmount=paymentAmount;
 	}
 	public double getPaymentAmount(){
 		 return this.paymentAmount;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 
 	 
 	public void setIsMonthPay(int isMonthPay){
 		 this.isMonthPay=isMonthPay;
 	}
 	public int getIsMonthPay(){
 		 return this.isMonthPay;
 	}
 
 	 
 	@CharacterVaild(len = 4) 
 	public void setPaymentType(String paymentType){
 		 this.paymentType=paymentType;
 	}
 	public String getPaymentType(){
 		 return this.paymentType;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDeliveryCompanyName(String deliveryCompanyName){
 		 this.deliveryCompanyName=deliveryCompanyName;
 	}
 	public String getDeliveryCompanyName(){
 		 return this.deliveryCompanyName;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDeliveryEmail(String deliveryEmail){
 		 this.deliveryEmail=deliveryEmail;
 	}
 	public String getDeliveryEmail(){
 		 return this.deliveryEmail;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setClosePayOrder(String closePayOrder){
 		 this.closePayOrder=closePayOrder;
 	}
 	public String getClosePayOrder(){
 		 return this.closePayOrder;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setParentId(String parentId){
 		 this.parentId=parentId;
 	}
 	public String getParentId(){
 		 return this.parentId;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setLeadsCode(String leadsCode){
 		 this.leadsCode=leadsCode;
 	}
 	public String getLeadsCode(){
 		 return this.leadsCode;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setPaymentNo(String paymentNo){
 		 this.paymentNo=paymentNo;
 	}
 	public String getPaymentNo(){
 		 return this.paymentNo;
 	}
 
 	 
 	public void setCreatedDate(Timestamp createdDate){
 		 this.createdDate=createdDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreatedDate(){
 		 return this.createdDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderId(String orderId){
 		 this.orderId=orderId;
 	}
 	public String getOrderId(){
 		 return this.orderId;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatus(String status){
 		 this.status=status;
 	}
 	public String getStatus(){
 		 return this.status;
 	}
	public int getRownum() {
		return rownum;
	}
	public void setRownum(int rownum) {
		this.rownum = rownum;
	}
	public String getExpDate() {
		return expDate;
	}
	public void setExpDate(String expDate) {
		this.expDate = expDate;
	}
	public String getChooseStatus() {
		return chooseStatus;
	}
	public void setChooseStatus(String chooseStatus) {
		this.chooseStatus = chooseStatus;
	}
	public String getChooseDate() {
		return chooseDate;
	}
	public void setChooseDate(String chooseDate) {
		this.chooseDate = chooseDate;
	}
	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
 	 
}