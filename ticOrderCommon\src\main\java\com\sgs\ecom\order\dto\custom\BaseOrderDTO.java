package com.sgs.ecom.order.dto.custom;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.TfsStateEnum;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;

public class BaseOrderDTO {
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String createDate;
	private Long orderId;
	private String orderNo;
	private String relateOrderNo;
	private String orderType;
	private Long userId;
	private int state;
	private String platformOrder;
	private BigDecimal platformAmount;
	private int isPayReceived;
	private String groupNo;
	private String tmpGroupNo;
	private Long questionId;
	private Long categoryId;
	private String categoryPath;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String csCode;
	private BigDecimal orderAmount;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private BigDecimal realAmount;
	private BigDecimal discountAmount;
	private BigDecimal shopDisAmount;
	private Long labId;
	private String bu;
	private int monthPay;
	private int payState;

	private String orderSource;
	private String leadsCode;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String userName;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String userPhone;
	private String userEmail;
	private String companyName;
	private String fromSource;
	private String payDate;
	private String operatorCode;
	private int hisState;
	private int subState;
	private int refundState;

	private String dateStr;

	private String csEmail;
	private String payMethod;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String businessPersonEmail;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private Integer isUrgent;
	private BigDecimal testCycle;
	private Integer isElectron;//是否是上海开票显示电子 0-否 1-是
	private String currency;
	private BigDecimal exchangeRate;
	private String deadlineTime;
	private Long lineId;
	private Long applicationLineId;
	private String offerDate;
	private String orderExpDate;
	private Integer userSex;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String businessLine;
	private String lastResponseDate;

	private String reportLuaCode;
	private String reportLua;
	private String reportFormCode;
	private String reportForm;
	private BigDecimal serviceAmount;
	private BigDecimal csDiscountAmount;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String salesEmail;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String labName;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String applySubmitDate;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String lineCode;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String stateShow;
	@ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
	private String outOrderNo;

	public BigDecimal getCsDiscountAmount() {
		return csDiscountAmount;
	}

	public void setCsDiscountAmount(BigDecimal csDiscountAmount) {
		this.csDiscountAmount = csDiscountAmount;
	}

	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public String getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public int getHisState() {
		return hisState;
	}

	public void setHisState(int hisState) {
		this.hisState = hisState;
	}

	public int getSubState() {
		return subState;
	}

	public void setSubState(int subState) {
		this.subState = subState;
	}

	public int getRefundState() {
		return refundState;
	}

	public void setRefundState(int refundState) {
		this.refundState = refundState;
	}
	public BaseOrderDTO() {
	}

	public BaseOrderDTO(String orderNo, String orderType) {
		this.orderNo = orderNo;
		this.orderType = orderType;
	}

	public String getCsEmail() {
		return csEmail;
	}

	public void setCsEmail(String csEmail) {
		this.csEmail = csEmail;
	}

	public Integer getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(Integer isUrgent) {
		this.isUrgent = isUrgent;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getRelateOrderNo() {
		return relateOrderNo;
	}

	public void setRelateOrderNo(String relateOrderNo) {
		this.relateOrderNo = relateOrderNo;
	}

	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public String getTmpGroupNo() {
		return tmpGroupNo;
	}

	public void setTmpGroupNo(String tmpGroupNo) {
		this.tmpGroupNo = tmpGroupNo;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public String getCsCode() {
		return csCode;
	}

	public void setCsCode(String csCode) {
		this.csCode = csCode;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getCategoryPath() {
		if(categoryPath==null){
			return "";
		}

		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public int getIsPayReceived() {
		return isPayReceived;
	}

	public void setIsPayReceived(int isPayReceived) {
		this.isPayReceived = isPayReceived;
	}

	public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getFromSource() {
		return fromSource;
	}

	public void setFromSource(String fromSource) {
		this.fromSource = fromSource;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public String getLeadsCode() {
		return leadsCode;
	}

	public void setLeadsCode(String leadsCode) {
		this.leadsCode = leadsCode;
	}

	public String getPayDate() {
		return payDate;
	}

	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public String getDateStr() {
		return dateStr;
	}

	public void setDateStr(String dateStr) {
		this.dateStr = dateStr;
	}

	public String getPlatformOrder() {
		return platformOrder;
	}

	public void setPlatformOrder(String platformOrder) {
		this.platformOrder = platformOrder;
	}

	public BigDecimal getTestCycle() {
		return testCycle;
	}

	public void setTestCycle(BigDecimal testCycle) {
		this.testCycle = testCycle;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getBusinessPersonEmail() {
		return businessPersonEmail;
	}

	public void setBusinessPersonEmail(String businessPersonEmail) {
		this.businessPersonEmail = businessPersonEmail;
	}

	public int getPayState() {
		return payState;
	}

	public void setPayState(int payState) {
		this.payState = payState;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public Long getLineId() {
		return lineId;
	}

	public void setLineId(Long lineId) {
		this.lineId = lineId;
	}

	public Long getApplicationLineId() {
		return applicationLineId;
	}

	public void setApplicationLineId(Long applicationLineId) {
		this.applicationLineId = applicationLineId;
	}

	public String getOfferDate() {
		return offerDate;
	}

	public void setOfferDate(String offerDate) {
		this.offerDate = offerDate;
	}

	public String getOrderExpDate() {
		return orderExpDate;
	}

	public void setOrderExpDate(String orderExpDate) {
		this.orderExpDate = orderExpDate;
	}

	public Integer getUserSex() {
		return userSex;
	}

	public void setUserSex(Integer userSex) {
		this.userSex = userSex;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	public String getLastResponseDate() {
		return lastResponseDate;
	}

	public void setLastResponseDate(String lastResponseDate) {
		this.lastResponseDate = lastResponseDate;
	}

	public BigDecimal getExchangeRate() {
		return exchangeRate;
	}

	public void setExchangeRate(BigDecimal exchangeRate) {
		this.exchangeRate = exchangeRate;
	}

	public String getReportLuaCode() {
		return reportLuaCode;
	}

	public void setReportLuaCode(String reportLuaCode) {
		this.reportLuaCode = reportLuaCode;
	}

	public String getReportLua() {
		return reportLua;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}

	public String getReportFormCode() {
		return reportFormCode;
	}

	public void setReportFormCode(String reportFormCode) {
		this.reportFormCode = reportFormCode;
	}

	public String getReportForm() {
		return reportForm;
	}

	public void setReportForm(String reportForm) {
		this.reportForm = reportForm;
	}

	public String getSalesEmail() {
		return salesEmail;
	}

	public void setSalesEmail(String salesEmail) {
		this.salesEmail = salesEmail;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public String getStateShow() {
		if("2700".equals(bu)){
			return TfsStateEnum.getNameCh(state);
		}

		return OrderStateEnum.getNameCh(String.valueOf(state));
	}

	public void setStateShow(String stateShow) {
		this.stateShow = stateShow;
	}

	public String getOutOrderNo() {
		return outOrderNo;
	}

	public void setOutOrderNo(String outOrderNo) {
		this.outOrderNo = outOrderNo;
	}
}
