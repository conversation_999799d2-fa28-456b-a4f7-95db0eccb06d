package com.sgs.ecom.order.bo; 
 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.BeanAnno; 

public class BOSysShop{
 
 	public static final String SEQUENCE = "SHOP_ID"; 
  
 	public static final String BO_SQL = "sysshop_shop"; 
 
 	public static final String OWNER ="bbc";

 	public static final String QQ="qq";
 	public static final String SHOPUSER_NAME="shopuserName";
 	public static final String SHOP_AREA="shopArea";
 	public static final String WANGWANG="wangwang";
 	public static final String MOBILE="mobile";
 	public static final String SHOP_ADDR="shopAddr";
 	public static final String OPEN_TIME="openTime";
 	public static final String SHOP_CATE="shopCate";
 	public static final String CLOSE_TIME="closeTime";
 	public static final String SHOP_NAME="shopName";
 	public static final String SHOP_LOGO="shopLogo";
 	public static final String SHOP_ID="shopId";
 	public static final String SHOP_TYPE="shopType";
 	public static final String SHOPUSER_IDENTITY_IMG="shopuserIdentityImg";
 	public static final String CLOSE_REASON="closeReason";
 	public static final String SHOPUSER_IDENTITY="shopuserIdentity";
 	public static final String SHOP_DESCRIPT="shopDescript";
 	public static final String SELLER_ID="sellerId";
 	public static final String EMAIL="email";
 	public static final String BULLETIN="bulletin";
 	public static final String STATUS="status";

 	@BeanAnno("qq")
 	private String qq;
 	@BeanAnno("shopuser_name")
 	private String shopuserName;
 	@BeanAnno("shop_area")
 	private String shopArea;
 	@BeanAnno("wangwang")
 	private String wangwang;
 	@BeanAnno("mobile")
 	private String mobile;
 	@BeanAnno("shop_addr")
 	private String shopAddr;
 	@BeanAnno("open_time")
 	private long openTime;
 	@BeanAnno("shop_cate")
 	private String shopCate;
 	@BeanAnno("close_time")
 	private long closeTime;
 	@BeanAnno("shop_name")
 	private String shopName;
 	@BeanAnno("shop_logo")
 	private String shopLogo;
 	@BeanAnno("shop_id")
 	private long shopId;
 	@BeanAnno("shop_type")
 	private String shopType;
 	@BeanAnno("shopuser_identity_img")
 	private String shopuserIdentityImg;
 	@BeanAnno("close_reason")
 	private String closeReason;
 	@BeanAnno("shopuser_identity")
 	private String shopuserIdentity;
 	@BeanAnno("shop_descript")
 	private String shopDescript;
 	@BeanAnno("seller_id")
 	private long sellerId;
 	@BeanAnno("email")
 	private String email;
 	@BeanAnno("bulletin")
 	private String bulletin;
 	@BeanAnno("status")
 	private String status;

 	@CharacterVaild(len = 255) 
 	public void setQq(String qq){
 		 this.qq=qq;
 	}
 	public String getQq(){
 		 return this.qq;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setShopuserName(String shopuserName){
 		 this.shopuserName=shopuserName;
 	}
 	public String getShopuserName(){
 		 return this.shopuserName;
 	}
 
 	 
 	@CharacterVaild(len = 255) 
 	public void setShopArea(String shopArea){
 		 this.shopArea=shopArea;
 	}
 	public String getShopArea(){
 		 return this.shopArea;
 	}
 
 	 
 	@CharacterVaild(len = 255) 
 	public void setWangwang(String wangwang){
 		 this.wangwang=wangwang;
 	}
 	public String getWangwang(){
 		 return this.wangwang;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setMobile(String mobile){
 		 this.mobile=mobile;
 	}
 	public String getMobile(){
 		 return this.mobile;
 	}
 
 	 
 	@CharacterVaild(len = 715827882) 
 	public void setShopAddr(String shopAddr){
 		 this.shopAddr=shopAddr;
 	}
 	public String getShopAddr(){
 		 return this.shopAddr;
 	}
 
 	 
 	public void setOpenTime(long openTime){
 		 this.openTime=openTime;
 	}
 	public long getOpenTime(){
 		 return this.openTime;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setShopCate(String shopCate){
 		 this.shopCate=shopCate;
 	}
 	public String getShopCate(){
 		 return this.shopCate;
 	}
 
 	 
 	public void setCloseTime(long closeTime){
 		 this.closeTime=closeTime;
 	}
 	public long getCloseTime(){
 		 return this.closeTime;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setShopName(String shopName){
 		 this.shopName=shopName;
 	}
 	public String getShopName(){
 		 return this.shopName;
 	}
 
 	 
 	@CharacterVaild(len = 255) 
 	public void setShopLogo(String shopLogo){
 		 this.shopLogo=shopLogo;
 	}
 	public String getShopLogo(){
 		 return this.shopLogo;
 	}
 
 	 
 	public void setShopId(long shopId){
 		 this.shopId=shopId;
 	}
 	public long getShopId(){
 		 return this.shopId;
 	}
 
 	 
 	@CharacterVaild(len = 5) 
 	public void setShopType(String shopType){
 		 this.shopType=shopType;
 	}
 	public String getShopType(){
 		 return this.shopType;
 	}
 
 	 
 	@CharacterVaild(len = 255) 
 	public void setShopuserIdentityImg(String shopuserIdentityImg){
 		 this.shopuserIdentityImg=shopuserIdentityImg;
 	}
 	public String getShopuserIdentityImg(){
 		 return this.shopuserIdentityImg;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCloseReason(String closeReason){
 		 this.closeReason=closeReason;
 	}
 	public String getCloseReason(){
 		 return this.closeReason;
 	}
 
 	 
 	@CharacterVaild(len = 18) 
 	public void setShopuserIdentity(String shopuserIdentity){
 		 this.shopuserIdentity=shopuserIdentity;
 	}
 	public String getShopuserIdentity(){
 		 return this.shopuserIdentity;
 	}
 
 	 
 	@CharacterVaild(len = 715827882) 
 	public void setShopDescript(String shopDescript){
 		 this.shopDescript=shopDescript;
 	}
 	public String getShopDescript(){
 		 return this.shopDescript;
 	}
 
 	 
 	public void setSellerId(long sellerId){
 		 this.sellerId=sellerId;
 	}
 	public long getSellerId(){
 		 return this.sellerId;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setEmail(String email){
 		 this.email=email;
 	}
 	public String getEmail(){
 		 return this.email;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBulletin(String bulletin){
 		 this.bulletin=bulletin;
 	}
 	public String getBulletin(){
 		 return this.bulletin;
 	}
 
 	 
 	@CharacterVaild(len = 6) 
 	public void setStatus(String status){
 		 this.status=status;
 	}
 	public String getStatus(){
 		 return this.status;
 	}
 
 	 
}