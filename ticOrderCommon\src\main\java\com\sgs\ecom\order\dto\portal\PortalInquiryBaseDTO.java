package com.sgs.ecom.order.dto.portal;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.order.OrderSampleStrDTO;

import java.util.ArrayList;
import java.util.List;

public class PortalInquiryBaseDTO {
    private String orderNo;
    private int state;
    private String csCode;
    private String csEmail;
    private List<OrderDetailDTO> orderDetailDTOList=new ArrayList<>();






    public PortalInquiryBaseDTO() {
    }

    public PortalInquiryBaseDTO(String orderNo, int state, List<OrderDetailDTO> list) {
        this.orderNo = orderNo;
        this.state = state;
        this.orderDetailDTOList=list;
    }

    public PortalInquiryBaseDTO(BaseOrderDTO baseOrderDTO) {
        this.orderNo = baseOrderDTO.getOrderNo();
        this.state = baseOrderDTO.getState();
        this.csCode=baseOrderDTO.getCsCode();
        this.csEmail=baseOrderDTO.getCsEmail();
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public List<OrderDetailDTO> getOrderDetailDTOList() {
        return orderDetailDTOList;
    }

    public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
        this.orderDetailDTOList = orderDetailDTOList;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }


}
