package com.sgs.ecom.order.dto.order; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class OrderRelateExternalDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select RELATE_ID,CREATE_DATE,EXTERNAL_SYSTEM,STATE_DATE,STATE,ORDER_NO,EXTERNAL_NO from ORDER_RELATE_EXTERNAL"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="RELATE_ID", getName="getRelateId", setName="setRelateId")
 	private long relateId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="EXTERNAL_SYSTEM", getName="getExternalSystem", setName="setExternalSystem")
 	private String externalSystem;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="EXTERNAL_NO", getName="getExternalNo", setName="setExternalNo")
 	private String externalNo;

 	public void setRelateId(long relateId){
 		 this.relateId=relateId;
 	}
 	public long getRelateId(){
 		 return this.relateId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setExternalSystem(String externalSystem){
 		 this.externalSystem=externalSystem;
 	}
 	public String getExternalSystem(){
 		 return this.externalSystem;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setExternalNo(String externalNo){
 		 this.externalNo=externalNo;
 	}
 	public String getExternalNo(){
 		 return this.externalNo;
 	}
 
 	 
}