package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class QBOUserRelatePayment{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.CUST_ID,b.TAX_NO,b.memo,b.PROVICE,b.BANK_NUMBER,b.customer_number,b.INVOICE_ID,b.REG_ADDRESS,b.INVOICE_TYPE,b.COUNTRY,b.CITY,b.TOWN,b.INVOICE_TITLE,b.IS_DEFAULT,b.PAYMENT_CODE,b.REG_PHONE,b.BANK_NAME,b.STATE_DATE,b.STATE,b.CREATE_DATE,c.USER_ID,c.USER_PHONE,c.USER_EMAIL from TB_CUST_APPLY_RELATE a,TB_CUST_INVOICE b,USER_INFO c where a.CUST_ID=b.CUST_ID and a.USER_ID=c.USER_ID"; 
 
 	public static final String OWNER ="member";

 	public static final String CUST_ID="custId";
 	public static final String TAX_NO="taxNo";
 	public static final String PROVICE="provice";
 	public static final String BANK_NUMBER="bankNumber";
 	public static final String CUSTOMER_NUMBER="customerNumber";
 	public static final String USER_EMAIL="userEmail";
 	public static final String STATE_DATE="stateDate";
 	public static final String MEMO="memo";
 	public static final String STATE="state";
 	public static final String USER_ID="userId";
 	public static final String INVOICE_ID="invoiceId";
 	public static final String CREATE_DATE="createDate";
 	public static final String USER_PHONE="userPhone";
 	public static final String REG_ADDRESS="regAddress";
 	public static final String INVOICE_TYPE="invoiceType";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String INVOICE_TITLE="invoiceTitle";
 	public static final String IS_DEFAULT="isDefault";
 	public static final String PAYMENT_CODE="paymentCode";
 	public static final String REG_PHONE="regPhone";
 	public static final String BANK_NAME="bankName";

 	@BeanAnno(value="CUST_ID",table="a")
 	private long custId;
 	@BeanAnno(value="TAX_NO",table="b")
 	private String taxNo;
 	@BeanAnno(value="PROVICE",table="b")
 	private String provice;
 	@BeanAnno(value="BANK_NUMBER",table="b")
 	private String bankNumber;
 	@BeanAnno(value="customer_number",table="b")
 	private String customerNumber;
 	@BeanAnno(value="USER_EMAIL",table="c")
 	private String userEmail;
 	@BeanAnno(value="STATE_DATE",table="b")
 	private Timestamp stateDate;
 	@BeanAnno(value="memo",table="b")
 	private String memo;
 	@BeanAnno(value="STATE",table="b")
 	private int state;
 	@BeanAnno(value="USER_ID",table="c")
 	private long userId;
 	@BeanAnno(value="INVOICE_ID",table="b")
 	private long invoiceId;
 	@BeanAnno(value="CREATE_DATE",table="b")
 	private Timestamp createDate;
 	@BeanAnno(value="USER_PHONE",table="c")
 	private String userPhone;
 	@BeanAnno(value="REG_ADDRESS",table="b")
 	private String regAddress;
 	@BeanAnno(value="INVOICE_TYPE",table="b")
 	private int invoiceType;
 	@BeanAnno(value="COUNTRY",table="b")
 	private String country;
 	@BeanAnno(value="CITY",table="b")
 	private String city;
 	@BeanAnno(value="TOWN",table="b")
 	private String town;
 	@BeanAnno(value="INVOICE_TITLE",table="b")
 	private String invoiceTitle;
 	@BeanAnno(value="IS_DEFAULT",table="b")
 	private int isDefault;
 	@BeanAnno(value="PAYMENT_CODE",table="b")
 	private String paymentCode;
 	@BeanAnno(value="REG_PHONE",table="b")
 	private String regPhone;
 	@BeanAnno(value="BANK_NAME",table="b")
 	private String bankName;

 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCustomerNumber(String customerNumber){
 		 this.customerNumber=customerNumber;
 	}
 	public String getCustomerNumber(){
 		 return this.customerNumber;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 11) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setRegAddress(String regAddress){
 		 this.regAddress=regAddress;
 	}
 	public String getRegAddress(){
 		 return this.regAddress;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setPaymentCode(String paymentCode){
 		 this.paymentCode=paymentCode;
 	}
 	public String getPaymentCode(){
 		 return this.paymentCode;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setRegPhone(String regPhone){
 		 this.regPhone=regPhone;
 	}
 	public String getRegPhone(){
 		 return this.regPhone;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBankName(String bankName){
 		 this.bankName=bankName;
 	}
 	public String getBankName(){
 		 return this.bankName;
 	}
 
 	 
}