package com.sgs.ecom.order.dto.pay;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;


public class PayBaseDTO {
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private Integer payMethod;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private Integer payPrice;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private String payDate;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private String accountName;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private String transNo;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private String payAccount;

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getPayPrice() {
        return payPrice;
    }

    public void setPayPrice(Integer payPrice) {
        this.payPrice = payPrice;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }

    public String getPayAccount() {
        return payAccount;
    }

    public void setPayAccount(String payAccount) {
        this.payAccount = payAccount;
    }
}
