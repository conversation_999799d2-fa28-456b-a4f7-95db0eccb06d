package com.sgs.ecom.order.bill.controller;

import com.sgs.base.BaseController;
import org.springframework.web.bind.annotation.*;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.order.bill.service.interfaces.IBillLogSV;
import com.sgs.ecom.order.util.ResultBody;

@RestController
@RequestMapping("/business/api.v1.bill/billlog")
public class BillLogController extends BaseController {

	private static IBillLogSV billLogSV = CollectionService.getService(IBillLogSV.class);
	
    /**   
	* @Function: qryBillLog
	* @Description: 查询账单备注列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qry", method = { RequestMethod.POST })
    public ResultBody qryBillLog(@RequestBody String data) throws Exception {
    	return ResultBody.newInstance(billLogSV.qryBillLog(data));
	}
    
    /**   
	* @Function: addBillLog
	* @Description: 新增账单备注
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "add", method = { RequestMethod.POST })
    public ResultBody addBillLog(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
    	billLogSV.addBillLog(data,getPersonInfo(token));
    	return ResultBody.success();
	}
    
    /**   
	* @Function: delBillLog
	* @Description: 删除账单备注
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "del", method = { RequestMethod.POST })
    public ResultBody delBillLog(@RequestBody String data) throws Exception {
    	billLogSV.delBillLog(data);
		return ResultBody.success();
	}
}
