package com.sgs.ecom.order.controller.user;

import com.platform.annotation.AuthRequired;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.infrastructure.wechat.OaWechatAppPropertiesUtils;
import com.sgs.ecom.order.service.user.interfaces.IOfficialUserSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.util.ResultCode;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.wechat.VOWechatApp;
import com.sgs.redis.RedisClient;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/api.v1.user/userWechat")
public class UserWechatController extends ControllerUtil {

    private static final Logger log = LoggerFactory.getLogger(UserWechatController.class);

    @Autowired
    private IOfficialUserSV iOfficialUserSV;
    @Resource
    private OaWechatAppPropertiesUtils oaWechatAppPropertiesUtils;
    @Resource
    private RedisClient redisClient;
    private final static int MAX_RETRY = 100;

    /**
     * 获取公众服务号粉丝信息
     *
     * @return
     * @throws Exception
     */
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryOfficialFans", method = {RequestMethod.POST})
    public ResultBody qryOfficialFans(
        @RequestHeader(value="resetOpenIds", required = false) String resetOpenIds,
        @RequestHeader(value="resetFullOpenIds", required = false) String resetFullOpenIds
    ) throws Exception {
        Map<String, VOWechatApp> wechatAppMap = oaWechatAppPropertiesUtils.getWxMap();
        wechatAppMap.forEach((appId, wechatApp) -> {
            String nextOpenIdRedisKey = RedisKeyUtil.getNextOpenIdRedisKey(appId);
            String fullOpenIdRedisKey = RedisKeyUtil.getFullOpenIdRedisKey(appId);

            if (!ValidationUtil.isEmpty(wechatApp.getMonitorFollow()) &&
                StringUtils.equalsAny(wechatApp.getMonitorFollow(), "1", "2")) {
                if (StringUtils.equals("1", resetOpenIds)) {
                    // 先清空redisKey，相当于全部重新跑
                    redisClient.del(nextOpenIdRedisKey);
                }
                if (StringUtils.equals("1", resetFullOpenIds)) {
                    redisClient.del(fullOpenIdRedisKey);
                }

                // 从Redis里获取存量OPENID
                Set<String> fullOpenIdSet = new HashSet<>(
                    redisClient.lrange(fullOpenIdRedisKey, 0, -1));

                // 获取公众号粉丝信息
                try {
                    int count;
                    int currentCount = 0;
                    do {
                        count = iOfficialUserSV.getAndPushOfficialUserList(appId, wechatApp, fullOpenIdSet);
                        currentCount++;
                    } while (count > 0 && currentCount < MAX_RETRY);

                } catch (Exception e) {
                    log.error("获取公众号粉丝信息 失败 {}, {}", appId, e.getMessage());
                    ResultBody.error(ResultCode.SYSTEM_IS_BUSINESS);
                }
            }
        });
        return ResultBody.success();
    }

}
