package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.bo.BOSysPerson;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.enumtool.application.DeliverTypeEnum;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.request.express.*;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.order.interfaces.IOrderExpressService;
import com.sgs.ecom.order.service.order.interfaces.IOrderOperatorService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.util.json.JsonTransUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/express")
public class OrderExpressController extends ControllerUtil {

    @Autowired
    private IOrderBaseInfoService orderBaseInfoService;

    @Autowired
    private IOrderExpressService orderExpressService;

    @Autowired
    private JsonTransUtil jsonTransUtil;

    @Autowired
    private IOrderOperatorService orderOperatorService;
    @Autowired
    private ApiEventUtil apiEventUtil;

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4520" })
    @RequestMapping(value = "save", method = { RequestMethod.POST })
    public ResultBody save(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderExpressReq orderExpressReq) throws Exception {
        BOSysPerson boSysPerson=getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO=gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderExpressReq.getOrderId(),boSysPerson,privilegeLevelDTO);

        orderExpressService.saveExpress(orderBaseInfoCheckDTO,boSysPerson,orderExpressReq);
        if(orderExpressReq.getEventFlg()==1){
            apiEventUtil.sendWechatMsg(orderExpressReq.getEventOrderNo(), EventEnum.SEND_WECHAT_MSG, WechatEnum.EXPRESS,orderExpressReq.getWechatOtherApiDTO());
        }
        return ResultBody.success();
    }

    /**
     * @Description: 批量保存关联订单快递信息(只针对保存)
     * @Author: bowen zhang
     * @Date: 2022/5/12
     * @param token:
     * @param orderExpressReq:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4520" })
    @RequestMapping(value = "saveBatchExpress", method = { RequestMethod.POST })
    public ResultBody saveBatchExpress(
            @RequestHeader(value="accessToken") String token,
            @Validated(value = BaseBean.Insert.class)
            @RequestBody OrderDirectTicExpressReq orderExpressReq) throws Exception {
        orderExpressService.saveBatchExpress(orderExpressReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }


    /**
     * @Description : 通过excel批量导入物流快递信息
     * <AUTHOR> Zhang
     * @Date  2023/4/17
     * @param token:
     * @param orderImpExpressReq:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
//    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4520" })
    @RequestMapping(value = "batchImpExpressList", method = { RequestMethod.POST })
    public ResultBody batchImpExpressList(
            @RequestHeader(value="accessToken") String token,
            @Validated(value = BaseBean.Insert.class)
            @RequestBody OrderImpExpressReq orderImpExpressReq) throws Exception {
        orderExpressService.batchImpExpressList(orderImpExpressReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }


    /**
     * 通过快递单号更新物流
     * @param token
     * @return
     * @throws Exception
     */
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "updateExpressState", method = { RequestMethod.POST })
    public ResultBody updateExpressState(
            @RequestHeader(value="accessToken") String token,
            @RequestBody TicExpressModReq ticExpressModReq) throws Exception {
        orderExpressService.updateExpressState(ticExpressModReq,getPersonInfo(token));
        return ResultBody.success();
    }





    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "del", method = { RequestMethod.POST })
    public ResultBody del(
            @RequestHeader(value="accessToken") String token,
            @RequestBody ExpressIdReq expressIdReq) throws Exception {
        BOSysPerson boSysPerson=getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO=gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(expressIdReq.getOrderId(),boSysPerson,privilegeLevelDTO);
        orderExpressService.delExpress(orderBaseInfoCheckDTO,boSysPerson,expressIdReq);
        return ResultBody.success();
    }

    /**
     * @Description: tic订单物流信息删除
     * @Author: bowen zhang
     * @Date: 2022/5/23
     * @param token:
     * @param expressIdReq:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "delPublicExpress", method = { RequestMethod.POST })
    public ResultBody delPublicExpress(
            @RequestHeader(value="accessToken") String token,
            @RequestBody ExpressIdReq expressIdReq) throws Exception {
        orderExpressService.delPublicExpress(expressIdReq,getPersonInfo(token),gePrivilegeLevelValue(token));
        return ResultBody.success();
    }

    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "list", method = { RequestMethod.POST })
    public ResultBody list(
            @RequestHeader(value="accessToken") String token,
            @RequestBody ExpressIdReq expressIdReq) throws Exception {
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.qryBase(Long.parseLong(expressIdReq.getOrderId()));
        List<OrderExpressDTO>orderExpressDTOList=orderExpressService.selectExpress(orderBaseInfoCheckDTO, DeliverTypeEnum.invoice);
        return ResultBody.success( jsonTransUtil.toJSONString(OrderExpressDTO.class, orderExpressDTOList, BaseQryFilter.Default.class));
    }

    /**
     * @Description: TIC查询物流信息
     * @Author: bowen zhang
     * @Date: 2022/5/23
     * @param token:
     * @param expressIdReq:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryExpressList", method = { RequestMethod.POST })
    public ResultBody publicListqryExpressList(
            @RequestHeader(value="accessToken") String token,
            @RequestBody ExpressIdReq expressIdReq) throws Exception {
        return ResultBody.success(orderOperatorService.qryExpressList(getPersonInfo(token),expressIdReq,gePrivilegeLevelValue(token)));
    }

    /**
     * @Description: 客服手动添加[样品寄送]-添加物流单号功能
     * @Author: bowen zhang
     * @Date: 2022/12/13
     * @param token:
     * @param orderExpressOptReq:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "saveExpressByCS", method = { RequestMethod.POST })
    public ResultBody saveExpressByCS(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Insert.class)
            @RequestBody OrderExpressOptReq orderExpressOptReq) throws Exception {
        orderExpressService.saveExpressByCS(getPersonInfo(token),orderExpressOptReq,gePrivilegeLevelValue(token));
        return ResultBody.success();
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "delExpressByCS", method = { RequestMethod.POST })
    public ResultBody delExpressByCS(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderExpressOptReq orderExpressOptReq) throws Exception {
        orderExpressService.delExpressByCS(getPersonInfo(token),orderExpressOptReq,gePrivilegeLevelValue(token));
        return ResultBody.success();
    }


    /**
     * @Description : 寄样物流关联订单分页查询
     * <AUTHOR> Zhang
     * @Date  2023/2/14
     * @param token:
     * @param orderExpressSampleReq:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryPageRelevanceOrder", method = { RequestMethod.POST })
    public ResultBody qryPageRelevanceOrder(
            @RequestHeader(value="accessToken") String token,
            @Validated(BaseBean.Query.class)
            @RequestBody OrderExpressSampleReq orderExpressSampleReq) throws Exception {
        return ResultBody.newInstance(orderExpressService.qryPageRelevanceOrder(orderExpressSampleReq, getPersonInfo(token), getPower(token)));
    }
}
