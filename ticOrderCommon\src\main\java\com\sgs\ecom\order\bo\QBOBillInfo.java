package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import java.util.Date; 
import com.platform.util.json.DateFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="")
public class QBOBillInfo{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.CUST_ID,a.ORDER_NUM,a.bu,a.bill_start_date,a.CUSTOMER_NUMBER,a.STATE_DATE,a.STATE,a.COMPANY_NAME,a.INVOICE_ID,a.CREATE_DATE,a.BILL_ID,a.STORE_NAME,a.PAY_STATE,a.BILL_CYCLE,a.BILL_DATE,a.CUST_CODE,a.INVOICE_TITLE,a.PAYMENT_CODE,a.BILL_AMOUNT,a.bill_end_date,b.USER_NAME,b.USER_PHONE,b.ORDER_NO from TB_BILL_INFO a,TB_BILL_DTL b where a.bill_id=b.bill_id"; 
 
 	public static final String OWNER ="member";

 	public static final String CUST_ID="custId";
 	public static final String ORDER_NUM="orderNum";
 	public static final String BILL_START_DATE="billStartDate";
 	public static final String CUSTOMER_NUMBER="customerNumber";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String ORDER_NO="orderNo";
 	public static final String USER_NAME="userName";
 	public static final String COMPANY_NAME="companyName";
 	public static final String INVOICE_ID="invoiceId";
 	public static final String CREATE_DATE="createDate";
 	public static final String USER_PHONE="userPhone";
 	public static final String BU="bu";
 	public static final String BILL_ID="billId";
 	public static final String STORE_NAME="storeName";
 	public static final String PAY_STATE="payState";
 	public static final String BILL_CYCLE="billCycle";
 	public static final String BILL_DATE="billDate";
 	public static final String CUST_CODE="custCode";
 	public static final String INVOICE_TITLE="invoiceTitle";
 	public static final String PAYMENT_CODE="paymentCode";
 	public static final String BILL_AMOUNT="billAmount";
 	public static final String BILL_END_DATE="billEndDate";

 	@BeanAnno(value="CUST_ID",table="a")
 	private long custId;
 	@BeanAnno(value="ORDER_NUM",table="a")
 	private long orderNum;
 	@BeanAnno(value="bill_start_date",table="a")
 	private Date billStartDate;
 	@BeanAnno(value="CUSTOMER_NUMBER",table="a")
 	private String customerNumber;
 	@BeanAnno(value="STATE_DATE",table="a")
 	private Timestamp stateDate;
 	@BeanAnno(value="STATE",table="a")
 	private int state;
 	@BeanAnno(value="ORDER_NO",table="b")
 	private String orderNo;
 	@BeanAnno(value="USER_NAME",table="b")
 	private String userName;
 	@BeanAnno(value="COMPANY_NAME",table="a")
 	private String companyName;
 	@BeanAnno(value="INVOICE_ID",table="a")
 	private long invoiceId;
 	@BeanAnno(value="CREATE_DATE",table="a")
 	private Timestamp createDate;
 	@BeanAnno(value="USER_PHONE",table="b")
 	private String userPhone;
 	@BeanAnno(value="bu",table="a")
 	private String bu;
 	@BeanAnno(value="BILL_ID",table="a")
 	private long billId;
 	@BeanAnno(value="STORE_NAME",table="a")
 	private String storeName;
 	@BeanAnno(value="PAY_STATE",table="a")
 	private int payState;
 	@BeanAnno(value="BILL_CYCLE",table="a")
 	private String billCycle;
 	@BeanAnno(value="BILL_DATE",table="a")
 	private Timestamp billDate;
 	@BeanAnno(value="CUST_CODE",table="a")
 	private String custCode;
 	@BeanAnno(value="INVOICE_TITLE",table="a")
 	private String invoiceTitle;
 	@BeanAnno(value="PAYMENT_CODE",table="a")
 	private String paymentCode;
 	@BeanAnno(value="BILL_AMOUNT",table="a")
 	private double billAmount;
 	@BeanAnno(value="bill_end_date",table="a")
 	private Date billEndDate;

 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setOrderNum(long orderNum){
 		 this.orderNum=orderNum;
 	}
 	public long getOrderNum(){
 		 return this.orderNum;
 	}
 
 	 
 	public void setBillStartDate(Date billStartDate){
 		 this.billStartDate=billStartDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getBillStartDate(){
 		 return this.billStartDate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCustomerNumber(String customerNumber){
 		 this.customerNumber=customerNumber;
 	}
 	@CheckAnno(len = 100) 
 	public String getCustomerNumber(){
 		 return this.customerNumber;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	@CheckAnno(len = 50) 
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	@CheckAnno(len = 100) 
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	@CheckAnno(len = 20) 
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	@CheckAnno(len = 50) 
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setBillId(long billId){
 		 this.billId=billId;
 	}
 	public long getBillId(){
 		 return this.billId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	@CheckAnno(len = 50) 
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	public void setPayState(int payState){
 		 this.payState=payState;
 	}
 	public int getPayState(){
 		 return this.payState;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setBillCycle(String billCycle){
 		 this.billCycle=billCycle;
 	}
 	@CheckAnno(len = 20) 
 	public String getBillCycle(){
 		 return this.billCycle;
 	}
 
 	 
 	public void setBillDate(Timestamp billDate){
 		 this.billDate=billDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getBillDate(){
 		 return this.billDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	@CheckAnno(len = 50) 
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	@CheckAnno(len = 500) 
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setPaymentCode(String paymentCode){
 		 this.paymentCode=paymentCode;
 	}
 	@CheckAnno(len = 20) 
 	public String getPaymentCode(){
 		 return this.paymentCode;
 	}
 
 	 
 	public void setBillAmount(double billAmount){
 		 this.billAmount=billAmount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getBillAmount(){
 		 return this.billAmount;
 	}
 
 	 
 	public void setBillEndDate(Date billEndDate){
 		 this.billEndDate=billEndDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getBillEndDate(){
 		 return this.billEndDate;
 	}
 
 	 
}