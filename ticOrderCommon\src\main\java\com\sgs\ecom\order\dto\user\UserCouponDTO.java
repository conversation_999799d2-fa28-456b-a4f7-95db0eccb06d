package com.sgs.ecom.order.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseListFilter;
import com.sgs.ecom.order.enumtool.coupon.CouponStateEnum;

import java.math.BigDecimal;
import java.sql.Timestamp;

public class UserCouponDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select AREA_ID,EXP_DATE,COUPON_NAME,STATE_DATE,USER_ID,STATE,DISCOUNT_RATE,ORDER_NO,IS_COMPOSIT,DISCOUNT_LIMIT,COUPON_ID,BUY_PRICE,DISCOUNT_RULE,USE_AMOUNT,CREATE_DATE,USE_LIMIT,COUPON_DESC,SKU_ID,COUPON_TYPE,STORE_ID,COUPON_CODE,EFF_DATE,COUPON_SCOPE from USER_COUPON"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="AREA_ID", getName="getAreaId", setName="setAreaId")
 	private long areaId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="EXP_DATE", getName="getExpDate", setName="setExpDate")
 	private String expDate;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="COUPON_NAME", getName="getCouponName", setName="setCouponName")
 	private String couponName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class, BaseListFilter.CouponCodeList.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private Long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DISCOUNT_RATE", getName="getDiscountRate", setName="setDiscountRate")
 	private BigDecimal discountRate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_COMPOSIT", getName="getIsComposit", setName="setIsComposit")
 	private int isComposit;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DISCOUNT_LIMIT", getName="getDiscountLimit", setName="setDiscountLimit")
 	private int discountNums;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COUPON_ID", getName="getCouponId", setName="setCouponId")
 	private long couponId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="BUY_PRICE", getName="getBuyPrice", setName="setBuyPrice")
 	private int buyPrice;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="DISCOUNT_RULE", getName="getDiscountRule", setName="setDiscountRule")
 	private int discountRule;
 	@ApiAnno(groups={Default.class,QueryDtl.class})
 	@BeanAnno(value="USE_AMOUNT", getName="getUseAmount", setName="setUseAmount")
 	private int useAmount;
 	@ApiAnno(groups={Default.class, BaseListFilter.CouponCodeList.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USE_LIMIT", getName="getUseLimit", setName="setUseLimit")
 	private int useLimit;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COUPON_DESC", getName="getCouponDesc", setName="setCouponDesc")
 	private String couponDesc;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SKU_ID", getName="getSkuId", setName="setSkuId")
 	private long skuId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COUPON_TYPE", getName="getCouponType", setName="setCouponType")
 	private int couponType;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STORE_ID", getName="getStoreId", setName="setStoreId")
 	private long storeId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COUPON_CODE", getName="getCouponCode", setName="setCouponCode")
 	private String couponCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="EFF_DATE", getName="getEffDate", setName="setEffDate")
 	private String effDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COUPON_SCOPE", getName="getCouponScope", setName="setCouponScope")
 	private String couponScope;
	@ApiAnno(groups={Default.class})
	private String couponNo;
	@ApiAnno(groups={Default.class,QueryDtl.class})
	private String couponMemo;
	@ApiAnno(groups={Default.class,QueryDtl.class})
	private String discountAmount;
	@ApiAnno(groups={Default.class,QueryDtl.class})
	private String subDiscountAmount;
	@ApiAnno(groups={Default.class,QueryDtl.class, BaseListFilter.CouponCodeList.class})
	private String csCode;

	@ApiAnno(groups={Default.class})
	private String stateShow;

	@ApiAnno(groups={BaseListFilter.CouponCodeList.class})
	private String userName;
	@ApiAnno(groups={BaseListFilter.CouponCodeList.class})
	private String userPhone;
	@ApiAnno(groups={BaseListFilter.CouponCodeList.class})
	private String userEmail;

 	public void setAreaId(long areaId){
 		 this.areaId=areaId;
 	}
 	public long getAreaId(){
 		 return this.areaId;
 	}
 
 	 

 
 	 
 	public void setCouponName(String couponName){
 		 this.couponName=couponName;
 	}
 	public String getCouponName(){
 		 return this.couponName;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}


	public BigDecimal getDiscountRate() {
		return discountRate;
	}

	public void setDiscountRate(BigDecimal discountRate) {
		this.discountRate = discountRate;
	}

	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setIsComposit(int isComposit){
 		 this.isComposit=isComposit;
 	}
 	public int getIsComposit(){
 		 return this.isComposit;
 	}


	public int getDiscountNums() {
		return discountNums;
	}

	public void setDiscountNums(int discountNums) {
		this.discountNums = discountNums;
	}

	public void setCouponId(long couponId){
 		 this.couponId=couponId;
 	}
 	public long getCouponId(){
 		 return this.couponId;
 	}
 
 	 
 	public void setBuyPrice(int buyPrice){
 		 this.buyPrice=buyPrice;
 	}
 	public int getBuyPrice(){
 		 return this.buyPrice;
 	}
 
 	 
 	public void setDiscountRule(int discountRule){
 		 this.discountRule=discountRule;
 	}
 	public int getDiscountRule(){
 		 return this.discountRule;
 	}
 
 	 
 	public void setUseAmount(int useAmount){
 		 this.useAmount=useAmount;
 	}
 	public int getUseAmount(){
 		 return this.useAmount;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setUseLimit(int useLimit){
 		 this.useLimit=useLimit;
 	}
 	public int getUseLimit(){
 		 return this.useLimit;
 	}
 
 	 
 	public void setCouponDesc(String couponDesc){
 		 this.couponDesc=couponDesc;
 	}
 	public String getCouponDesc(){
 		 return this.couponDesc;
 	}
 
 	 
 	public void setSkuId(long skuId){
 		 this.skuId=skuId;
 	}
 	public long getSkuId(){
 		 return this.skuId;
 	}
 
 	 
 	public void setCouponType(int couponType){
 		 this.couponType=couponType;
 	}
 	public int getCouponType(){
 		 return this.couponType;
 	}
 
 	 
 	public void setStoreId(long storeId){
 		 this.storeId=storeId;
 	}
 	public long getStoreId(){
 		 return this.storeId;
 	}
 
 	 
 	public void setCouponCode(String couponCode){
 		 this.couponCode=couponCode;
 	}
 	public String getCouponCode(){
 		 return this.couponCode;
 	}


	public String getExpDate() {
		return expDate;
	}

	public void setExpDate(String expDate) {
		this.expDate = expDate;
	}

	public String getEffDate() {
		return effDate;
	}

	public void setEffDate(String effDate) {
		this.effDate = effDate;
	}

	public void setCouponScope(String couponScope){
 		 this.couponScope=couponScope;
 	}
 	public String getCouponScope(){
 		 return this.couponScope;
 	}

	public String getStateShow() {
		return CouponStateEnum.getNameCh(state);
	}

	public void setStateShow(String stateShow) {
		this.stateShow = stateShow;
	}

	public String getCouponNo() {
		return couponNo;
	}

	public void setCouponNo(String couponNo) {
		this.couponNo = couponNo;
	}

	public String getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(String discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getCouponMemo() {
		return couponMemo;
	}

	public void setCouponMemo(String couponMemo) {
		this.couponMemo = couponMemo;
	}

	public String getCsCode() {
		return csCode;
	}

	public void setCsCode(String csCode) {
		this.csCode = csCode;
	}

	public String getSubDiscountAmount() {
		return subDiscountAmount;
	}

	public void setSubDiscountAmount(String subDiscountAmount) {
		this.subDiscountAmount = subDiscountAmount;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}
}