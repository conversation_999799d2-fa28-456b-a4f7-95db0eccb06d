package com.sgs.ecom.order.dto.center;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class QuestionQryDtlDTO {
    private String questionId;
    private String questionName;
    private String state;

    private String itemId;
    private String itemName;
    private String bu;
    private List<CenterCategory> catagoryItem=new ArrayList<>();
    private int isDelete;
    private String lineCode;

    private String useCategoryPath;

    public String getQuestionId() {
        return questionId;
    }

    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }



    public String getQuestionName() {
        return questionName;
    }

    public void setQuestionName(String questionName) {
        this.questionName = questionName;
    }

    public List<CenterCategory> getCatagoryItem() {
        return catagoryItem;
    }

    public void setCatagoryItem(List<CenterCategory> catagoryItem) {
        this.catagoryItem = catagoryItem;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getUseCategoryPath() {
        return useCategoryPath;
    }

    public void setUseCategoryPath(String useCategoryPath) {
        this.useCategoryPath = useCategoryPath;
    }

    public String getLineCode() {
        return lineCode;
    }

    public void setLineCode(String lineCode) {
        this.lineCode = lineCode;
    }
}
