package com.sgs.ecom.order.dto.bbc;

import com.sgs.ecom.order.vo.VOOrderApplicationForm;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import org.apache.commons.lang.StringUtils;

public class TicMailDTO {

	private Long orderId;
	private String orderNo;
	private String linkPerson;
	private String linkPhone;
	private String linkEmail;
	private String operatorCode;
	private String auditCode;
	private String reportSendCc;
	private String csSendCc;
	private Long prodId;
	private String storeId;
	private String createDate;
	private String bu;
	private String orderAmount;
	private int isTest;
	private String storeName;
	private int state;
	private String companyNameCn;
	private String companyNameEn;
	private String companyAddressCn;
	private String companyAddressEn;
	private String groupNo;
	private String buType;
	private String realAmount;
	private int proState;
	private String deadlineTime;
	private String orderType;
	private int  hisState;
	private Long  labId;
	private String recommendReason;

	private Long userId;

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public TicMailDTO() {
	}

	public TicMailDTO(VOOrderBaseInfo voOrderBaseInfo, VOOrderApplicationForm voOrderApplicationForm) {
		this.orderNo = voOrderBaseInfo.getOrderNo();
		this.linkPerson = voOrderApplicationForm.getLinkPerson();
		this.linkPhone = voOrderApplicationForm.getLinkPhone();
		this.linkEmail = voOrderApplicationForm.getLinkEmail();
		this.createDate=voOrderBaseInfo.getCreateDate();
		this.orderAmount=voOrderBaseInfo.getOrderAmount()==null?"":voOrderBaseInfo.getOrderAmount().toString();
		this.isTest=0;
	}

	public int getHisState() {
		return hisState;
	}

	public void setHisState(int hisState) {
		this.hisState = hisState;
	}

	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	public String getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(String realAmount) {
		this.realAmount = realAmount;
	}

	public String getBuType() {
		return buType;
	}

	public void setBuType(String buType) {
		this.buType = buType;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getLinkPerson() {
		return linkPerson;
	}

	public void setLinkPerson(String linkPerson) {
		this.linkPerson = linkPerson;
	}

	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}


	public String getLinkEmail() {
		return linkEmail;
	}

	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail;
	}

	public String getReportSendCc() {
		return reportSendCc;
	}

	public void setReportSendCc(String reportSendCc) {
		this.reportSendCc = reportSendCc;
	}

	public Long getProdId() {
		return prodId;
	}

	public void setProdId(Long prodId) {
		this.prodId = prodId;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getCreateDate() {
		if(StringUtils.isNotBlank(createDate)){
			return createDate.split("\\.")[0];
		}
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(String orderAmount) {
		this.orderAmount = orderAmount;
	}

	public int getIsTest() {
		return isTest;
	}

	public void setIsTest(int isTest) {
		this.isTest = isTest;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}

	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public String getCompanyAddressCn() {
		return companyAddressCn;
	}

	public void setCompanyAddressCn(String companyAddressCn) {
		this.companyAddressCn = companyAddressCn;
	}

	public String getCompanyAddressEn() {
		return companyAddressEn;
	}

	public void setCompanyAddressEn(String companyAddressEn) {
		this.companyAddressEn = companyAddressEn;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public String getCsSendCc() {
		return csSendCc;
	}

	public void setCsSendCc(String csSendCc) {
		this.csSendCc = csSendCc;
	}

	public int getProState() {
		return proState;
	}

	public void setProState(int proState) {
		this.proState = proState;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public String getAuditCode() {
		return auditCode;
	}

	public void setAuditCode(String auditCode) {
		this.auditCode = auditCode;
	}

	public String getRecommendReason() {
		return recommendReason;
	}

	public void setRecommendReason(String recommendReason) {
		this.recommendReason = recommendReason;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
}
