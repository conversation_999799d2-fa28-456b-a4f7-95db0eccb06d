package com.sgs.ecom.order.dto.sample;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;


/**
 * <AUTHOR>
 */
public class SampleCategoryDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleCategoryCode;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String sampleCategoryName;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String sampleShapeCode;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String sampleShapeName;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String sampleCategoryRemark;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String sampleName;

    public String getSampleCategoryCode() {
        return sampleCategoryCode;
    }

    public void setSampleCategoryCode(String sampleCategoryCode) {
        this.sampleCategoryCode = sampleCategoryCode;
    }

    public String getSampleCategoryName() {
        return sampleCategoryName;
    }

    public void setSampleCategoryName(String sampleCategoryName) {
        this.sampleCategoryName = sampleCategoryName;
    }

    public String getSampleShapeCode() {
        return sampleShapeCode;
    }

    public void setSampleShapeCode(String sampleShapeCode) {
        this.sampleShapeCode = sampleShapeCode;
    }

    public String getSampleShapeName() {
        return sampleShapeName;
    }

    public void setSampleShapeName(String sampleShapeName) {
        this.sampleShapeName = sampleShapeName;
    }

    public String getSampleCategoryRemark() {
        return sampleCategoryRemark;
    }

    public void setSampleCategoryRemark(String sampleCategoryRemark) {
        this.sampleCategoryRemark = sampleCategoryRemark;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }
}
