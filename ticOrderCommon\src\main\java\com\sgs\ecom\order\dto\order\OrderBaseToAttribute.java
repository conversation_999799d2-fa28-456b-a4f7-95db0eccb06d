package com.sgs.ecom.order.dto.order;

import java.math.BigDecimal;

public class OrderBaseToAttribute {





    //测试周期
    private BigDecimal testCycle;
    private String sampleRequirements;//样品准备要求
    private String recommendReason;//推荐理由
    private String recommendReasonImage;

    private int priceType;
    private BigDecimal realAmount;
    private BigDecimal discountAmount;
    private BigDecimal orderAmount;
    private BigDecimal serviceAmount;
    private BigDecimal urgentAmount;

    private BigDecimal urgentAmountNum;
    private BigDecimal discountAmountNum;
    private BigDecimal newDiscountAmountNum;


    //客服的数据入库
    private String csName;
    private String csNameEn;
    private String csCode;
    private String testCycleMemo;
    private String reportMemo;
    private String orderOperatorCode;

    private BigDecimal testCycleNum;//testCycle 旧的使用了，

    private String currency;
    private BigDecimal exchangeRate;
    private BigDecimal taxRates;

    private String orderDetailToAttributeJson;



    public BigDecimal getTestCycle() {
        return testCycle;
    }

    public void setTestCycle(BigDecimal testCycle) {
        this.testCycle = testCycle;
    }

    public String getSampleRequirements() {
        return sampleRequirements;
    }

    public void setSampleRequirements(String sampleRequirements) {
        this.sampleRequirements = sampleRequirements;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public BigDecimal getUrgentAmount() {
        return urgentAmount;
    }

    public void setUrgentAmount(BigDecimal urgentAmount) {
        this.urgentAmount = urgentAmount;
    }

    public BigDecimal getUrgentAmountNum() {
        return urgentAmountNum;
    }

    public void setUrgentAmountNum(BigDecimal urgentAmountNum) {
        this.urgentAmountNum = urgentAmountNum;
    }

    public BigDecimal getDiscountAmountNum() {
        return discountAmountNum;
    }

    public void setDiscountAmountNum(BigDecimal discountAmountNum) {
        this.discountAmountNum = discountAmountNum;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public String getCsNameEn() {
        return csNameEn;
    }

    public void setCsNameEn(String csNameEn) {
        this.csNameEn = csNameEn;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getTestCycleMemo() {
        return testCycleMemo;
    }

    public void setTestCycleMemo(String testCycleMemo) {
        this.testCycleMemo = testCycleMemo;
    }

    public String getRecommendReasonImage() {
        return recommendReasonImage;
    }

    public void setRecommendReasonImage(String recommendReasonImage) {
        this.recommendReasonImage = recommendReasonImage;
    }

    public String getReportMemo() {
        return reportMemo;
    }

    public void setReportMemo(String reportMemo) {
        this.reportMemo = reportMemo;
    }

    public String getOrderOperatorCode() {
        return orderOperatorCode;
    }

    public void setOrderOperatorCode(String orderOperatorCode) {
        this.orderOperatorCode = orderOperatorCode;
    }

    public int getPriceType() {
        return priceType;
    }

    public void setPriceType(int priceType) {
        this.priceType = priceType;
    }

    public BigDecimal getTestCycleNum() {
        return testCycleNum;
    }

    public void setTestCycleNum(BigDecimal testCycleNum) {
        this.testCycleNum = testCycleNum;
    }

    public BigDecimal getNewDiscountAmountNum() {
        return newDiscountAmountNum;
    }

    public void setNewDiscountAmountNum(BigDecimal newDiscountAmountNum) {
        this.newDiscountAmountNum = newDiscountAmountNum;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getExchangeRate() {
        return exchangeRate;
    }

    public void setExchangeRate(BigDecimal exchangeRate) {
        this.exchangeRate = exchangeRate;
    }

    public String getOrderDetailToAttributeJson() {
        return orderDetailToAttributeJson;
    }

    public void setOrderDetailToAttributeJson(String orderDetailToAttributeJson) {
        this.orderDetailToAttributeJson = orderDetailToAttributeJson;
    }

    public BigDecimal getTaxRates() {
        return taxRates;
    }

    public void setTaxRates(BigDecimal taxRates) {
        this.taxRates = taxRates;
    }
}
