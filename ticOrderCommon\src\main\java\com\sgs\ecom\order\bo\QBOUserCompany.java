package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="用户公司")
public class QBOUserCompany{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.USER_NICK,a.LEVEL_NAME,a.USER_EMAIL,a.STATE_DATE,a.USER_ID,a.STATE,a.REG_IP,a.USER_NAME,a.U_ID,a.LEVEL_ID,a.USER_PHONE,a.CREATE_DATE,b.COMPANY_NAME,b.COMPANY_NAME_EN,b.PROVICE,b.CITY,b.TOWN,b.ADDRESS,b.COMPANY_ADDR_EN from USER_INFO a,USER_COMPANY b where a.USER_ID=b.USER_ID"; 
 
 	public static final String OWNER ="member";

 	public static final String USER_NICK="userNick";
 	public static final String PROVICE="provice";
 	public static final String LEVEL_NAME="levelName";
 	public static final String USER_EMAIL="userEmail";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String REG_IP="regIp";
 	public static final String USER_NAME="userName";
 	public static final String U_ID="uId";
 	public static final String LEVEL_ID="levelId";
 	public static final String COMPANY_ADDR_EN="companyAddrEn";
 	public static final String COMPANY_NAME="companyName";
 	public static final String USER_PHONE="userPhone";
 	public static final String CREATE_DATE="createDate";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String ADDRESS="address";
 	public static final String COMPANY_NAME_EN="companyNameEn";

 	@BeanAnno(value="USER_NICK",table="a")
 	private String userNick;
 	@BeanAnno(value="PROVICE",table="b")
 	private String provice;
 	@BeanAnno(value="LEVEL_NAME",table="a")
 	private String levelName;
 	@BeanAnno(value="USER_EMAIL",table="a")
 	private String userEmail;
 	@BeanAnno(value="STATE_DATE",table="a")
 	private Timestamp stateDate;
 	@BeanAnno(value="USER_ID",table="a")
 	private long userId;
 	@BeanAnno(value="STATE",table="a")
 	private long state;
 	@BeanAnno(value="REG_IP",table="a")
 	private String regIp;
 	@BeanAnno(value="USER_NAME",table="a")
 	private String userName;
 	@BeanAnno(value="U_ID",table="a")
 	private long uId;
 	@BeanAnno(value="LEVEL_ID",table="a")
 	private long levelId;
 	@BeanAnno(value="COMPANY_ADDR_EN",table="b")
 	private String companyAddrEn;
 	@BeanAnno(value="COMPANY_NAME",table="b")
 	private String companyName;
 	@BeanAnno(value="USER_PHONE",table="a")
 	private String userPhone;
 	@BeanAnno(value="CREATE_DATE",table="a")
 	private Timestamp createDate;
 	@BeanAnno(value="CITY",table="b")
 	private String city;
 	@BeanAnno(value="TOWN",table="b")
 	private String town;
 	@BeanAnno(value="ADDRESS",table="b")
 	private String address;
 	@BeanAnno(value="COMPANY_NAME_EN",table="b")
 	private String companyNameEn;

 	@CharacterVaild(len = 30) 
 	public void setUserNick(String userNick){
 		 this.userNick=userNick;
 	}
 	@CheckAnno(len = 30) 
 	public String getUserNick(){
 		 return this.userNick;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	@CheckAnno(len = 50) 
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setLevelName(String levelName){
 		 this.levelName=levelName;
 	}
 	@CheckAnno(len = 50) 
 	public String getLevelName(){
 		 return this.levelName;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setUserEmail(String userEmail){
 		 this.userEmail=userEmail;
 	}
 	@CheckAnno(len = 100) 
 	public String getUserEmail(){
 		 return this.userEmail;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(long state){
 		 this.state=state;
 	}
 	public long getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setRegIp(String regIp){
 		 this.regIp=regIp;
 	}
 	@CheckAnno(len = 20) 
 	public String getRegIp(){
 		 return this.regIp;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	@CheckAnno(len = 50) 
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setUId(long uId){
 		 this.uId=uId;
 	}
 	public long getUId(){
 		 return this.uId;
 	}
 
 	 
 	public void setLevelId(long levelId){
 		 this.levelId=levelId;
 	}
 	public long getLevelId(){
 		 return this.levelId;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyAddrEn(String companyAddrEn){
 		 this.companyAddrEn=companyAddrEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyAddrEn(){
 		 return this.companyAddrEn;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	@CharacterVaild(len = 11) 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	@CheckAnno(len = 11) 
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	@CheckAnno(len = 30) 
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	@CheckAnno(len = 30) 
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	@CheckAnno(len = 500) 
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
}