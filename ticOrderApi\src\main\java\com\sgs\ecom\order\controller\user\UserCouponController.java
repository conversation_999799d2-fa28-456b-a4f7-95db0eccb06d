package com.sgs.ecom.order.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.center.CenterCouponReq;
import com.sgs.ecom.order.request.coupon.CouponOperateReq;
import com.sgs.ecom.order.request.coupon.UserCouponListReq;
import com.sgs.ecom.order.service.member.interfaces.IMemberRestTemplateService;
import com.sgs.ecom.order.service.util.interfaces.IUserService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.user/coupon")
public class UserCouponController extends ControllerUtil {


	@Autowired
	private IUserService userService;
	@Autowired
	private IMemberRestTemplateService memberRestTemplateService;

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "list", method = {RequestMethod.POST})
	public ResultBody list(
		@RequestBody UserCouponListReq userCouponListReq) throws Exception {
		return ResultBody.newInstance(userService.selectCouponList(userCouponListReq));
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "codeList", method = {RequestMethod.POST})
	public ResultBody codeList(
			@RequestBody UserCouponListReq userCouponListReq) throws Exception {
		return ResultBody.newInstance(userService.codeList(userCouponListReq));
	}


	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "expCodeList", method = {RequestMethod.POST})
	public void expCodeList(
			@RequestBody UserCouponListReq userCouponListReq, HttpServletResponse res) throws Exception {
		userService.expCodeList(userCouponListReq,res);
	}



	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "operate", method = {RequestMethod.POST})
	public ResultBody operate(
		@RequestBody CouponOperateReq couponOperateReq
	) throws Exception {
		userService.operateCoupon(couponOperateReq);
		return ResultBody.success();
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "saveCoupon", method = {RequestMethod.POST})
	public ResultBody saveInvoice(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody CenterCouponReq centerCouponReq) throws Exception {
		centerCouponReq.setCsCode(getPersonInfo(token).getPersonCode());
		memberRestTemplateService.saveCoupon(centerCouponReq);
		return ResultBody.success();
	}

}
