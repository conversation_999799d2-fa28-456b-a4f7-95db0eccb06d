package com.sgs.ecom.order.bo; 
 
import java.sql.Timestamp;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.CharacterVaild;
import com.platform.util.json.ScienceFormatSerializer;
import com.platform.util.json.TimeFormatSerializer; 
public class BOTicTraceLog{
 
 	public static final String SEQUENCE = "ID"; 
  
 	public static final String BO_SQL = "TIC_TraceLog"; 
 
 	public static final String OWNER ="bbc";

 	public static final String OPERATOR="operator";
 	public static final String TYPE="type";
 	public static final String AFTEROPERATOR="afteroperator";
 	public static final String AFTERCHANGEPRICE="afterchangeprice";
 	public static final String FILENAME="filename";
 	public static final String FILEID="fileid";
 	public static final String BEFOREOPERATOR="beforeoperator";
 	public static final String ID="id";
 	public static final String ORDERID="orderid";
 	public static final String OPERATIONTIME="operationtime";
 	public static final String BEFORECHANGEPRICE="beforechangeprice";
 	public static final String REMARK="remark";

 	@BeanAnno("Operator")
 	private String operator;
 	@BeanAnno("Type")
 	private String type;
 	@BeanAnno("AfterOperator")
 	private String afteroperator;
 	@BeanAnno("AfterChangePrice")
 	private double afterchangeprice;
 	@BeanAnno("FileName")
 	private String filename;
 	@BeanAnno("FileID")
 	private String fileid;
 	@BeanAnno("BeforeOperator")
 	private String beforeoperator;
 	@BeanAnno("ID")
 	private String id;
 	@BeanAnno("OrderID")
 	private String orderid;
 	@BeanAnno("OperationTime")
 	private Timestamp operationtime;
 	@BeanAnno("BeforeChangePrice")
 	private double beforechangeprice;
 	@BeanAnno("Remark")
 	private String remark;

 	@CharacterVaild(len = 50) 
 	public void setOperator(String operator){
 		 this.operator=operator;
 	}
 	public String getOperator(){
 		 return this.operator;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setType(String type){
 		 this.type=type;
 	}
 	public String getType(){
 		 return this.type;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setAfteroperator(String afteroperator){
 		 this.afteroperator=afteroperator;
 	}
 	public String getAfteroperator(){
 		 return this.afteroperator;
 	}
 
 	 
 	public void setAfterchangeprice(double afterchangeprice){
 		 this.afterchangeprice=afterchangeprice;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getAfterchangeprice(){
 		 return this.afterchangeprice;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setFilename(String filename){
 		 this.filename=filename;
 	}
 	public String getFilename(){
 		 return this.filename;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setFileid(String fileid){
 		 this.fileid=fileid;
 	}
 	public String getFileid(){
 		 return this.fileid;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setBeforeoperator(String beforeoperator){
 		 this.beforeoperator=beforeoperator;
 	}
 	public String getBeforeoperator(){
 		 return this.beforeoperator;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	public String getId(){
 		 return this.id;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderid(String orderid){
 		 this.orderid=orderid;
 	}
 	public String getOrderid(){
 		 return this.orderid;
 	}
 
 	 
 	public void setOperationtime(Timestamp operationtime){
 		 this.operationtime=operationtime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getOperationtime(){
 		 return this.operationtime;
 	}
 
 	 
 	public void setBeforechangeprice(double beforechangeprice){
 		 this.beforechangeprice=beforechangeprice;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getBeforechangeprice(){
 		 return this.beforechangeprice;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
}