package com.sgs.ecom.order.dto.permission;

import com.platform.bo.BOSysPerson;
import com.sgs.ecom.order.dto.order.OrderProductDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;

import java.util.List;
import java.util.Map;

/**
*@Function:
*@Description
*@param: 订单权限对象
*@author: <PERSON><PERSON>_Qiu @date: 2022/6/10 @version:
**/
public class OrderPermissionDTO {

	private Long orderId;
	private String orderNo;
	private int orderType;
	private int lineId;
	private int prodId;
	private String csCode;
	private int state;
	private int refundState;
	private Long custId;
	private Long labId;
	private String operatorCode;
	private List<OrderProductDTO> productList;

	private BOSysPerson boSysPerson;
	private PrivilegeLevelDTO privilegeLevelDTO;
	private String businessPersonEmail;

	//需要返回后处理
	Map<String,Object> roPermission;

	private List<OrderPermissionDTO> list;

	public List<OrderPermissionDTO> getList() {
		return list;
	}

	public List<OrderProductDTO> getProductList() {
		return productList;
	}

	public void setProductList(List<OrderProductDTO> productList) {
		this.productList = productList;
	}

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public void setList(List<OrderPermissionDTO> list) {
		this.list = list;
	}

	public int getRefundState() {
		return refundState;
	}

	public void setRefundState(int refundState) {
		this.refundState = refundState;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public int getLineId() {
		return lineId;
	}

	public void setLineId(int lineId) {
		this.lineId = lineId;
	}

	public String getCsCode() {
		return csCode;
	}

	public void setCsCode(String csCode) {
		this.csCode = csCode;
	}

	public int getOrderType() {
		return orderType;
	}

	public void setOrderType(int orderType) {
		this.orderType = orderType;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public BOSysPerson getBoSysPerson() {
		return boSysPerson;
	}

	public void setBoSysPerson(BOSysPerson boSysPerson) {
		this.boSysPerson = boSysPerson;
	}

	public PrivilegeLevelDTO getPrivilegeLevelDTO() {
		return privilegeLevelDTO;
	}

	public void setPrivilegeLevelDTO(PrivilegeLevelDTO privilegeLevelDTO) {
		this.privilegeLevelDTO = privilegeLevelDTO;
	}

	public int getProdId() {
		return prodId;
	}

	public void setProdId(int prodId) {
		this.prodId = prodId;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public Map<String, Object> getRoPermission() {
		return roPermission;
	}

	public void setRoPermission(Map<String, Object> roPermission) {
		this.roPermission = roPermission;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public String getBusinessPersonEmail() {
		return businessPersonEmail;
	}

	public void setBusinessPersonEmail(String businessPersonEmail) {
		this.businessPersonEmail = businessPersonEmail;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}
}
