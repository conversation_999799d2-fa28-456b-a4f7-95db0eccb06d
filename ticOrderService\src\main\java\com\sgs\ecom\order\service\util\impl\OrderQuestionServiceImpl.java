package com.sgs.ecom.order.service.util.impl;

import com.platform.annotation.Master;
import com.platform.bo.B<PERSON>ys<PERSON>erson;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.dto.center.QuestionQryDtlDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.log.BaseLog;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.dto.question.OrderQuestionDTO;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.request.question.OrderQuestionReq;
import com.sgs.ecom.order.request.question.QuestionOriginalReq;
import com.sgs.ecom.order.service.custom.interfaces.ICustomLimitService;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.IMemberRestTemplateService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.order.interfaces.IOrderDetailService;
import com.sgs.ecom.order.service.order.interfaces.IOrderOperatorLogService;
import com.sgs.ecom.order.service.util.interfaces.IOrderQuestionService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import com.sgs.redis.RedisClient;
import java.util.ArrayList;
import java.util.List;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class OrderQuestionServiceImpl extends BaseService implements IOrderQuestionService {

	@Autowired
	private IOrderBaseInfoService orderBaseInfoService;
	@Autowired
	private IOrderBaseInfoCustomService orderBaseInfoCustomService;
	@Autowired
	private IMemberRestTemplateService memberRestTemplateService;
	@Autowired
	private RedisClient redisClient;
	@Autowired
	private ICustomLimitService customLimitService;
	@Autowired
	private IOrderDetailService orderDetailService;
	@Autowired
	private IOrderOperatorLogService orderOperatorLogService;
	@Autowired
	private ICenterTemplateSV centerTemplateSV;



	@Override
	public JSONObject copyQuestion(QuestionOriginalReq questionOriginalReq, BOSysPerson personInfo, PrivilegeLevelDTO power) {
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(questionOriginalReq.getOrderId(), personInfo, power);
		return memberRestTemplateService.copyQuestion(orderBaseInfoCheckDTO.getOrderNo(),questionOriginalReq.getIsOriginal());
	}

	@Transactional
	public void saveQuestion(OrderQuestionReq orderQuestionReq, BOSysPerson personInfo, PrivilegeLevelDTO power) throws Exception{
		BaseOrderDTO baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(orderQuestionReq.getOrderNo());
		orderBaseInfoService.checkOrderBase(String.valueOf(baseOrderDTO.getOrderId()), personInfo, power);
		if (!String.valueOf(baseOrderDTO.getState()).equals(OrderStateEnum.distribution.getIndex())
			&& !String.valueOf(baseOrderDTO.getState()).equals(OrderStateEnum.waitConfirm.getIndex())) {
			throw new BusinessException(ResultEnumCode.ORDER_STATE_CHANGE);
		}

		orderQuestionReq.setPersonCode(personInfo.getPersonCode());


		memberRestTemplateService.saveQuestion(orderQuestionReq);
	}

	@Transactional
	public void delQuestion(OrderReq orderReq, BOSysPerson personInfo) throws Exception{
		//删除问卷把数据置为无效
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.qryBase(orderReq.getOrderId());

		customLimitService.updateQuestionState(orderBaseInfoCheckDTO.getOrderNo());
		//删除问卷清空groupNo 获取原始的答案
		OrderQuestionDTO questionDTO=memberRestTemplateService.getQuestion(orderBaseInfoCheckDTO.getOrderNo(),1);
		QuestionQryDtlDTO questionQryDtlDTO = centerTemplateSV.qryByQuestion(String.valueOf(questionDTO.getQuestionId()));

		VOOrderBaseInfo voOrderBaseInfo=new VOOrderBaseInfo();
		voOrderBaseInfo.setOrderId(orderBaseInfoCheckDTO.getOrderId());
		voOrderBaseInfo.setTmpGroupNo("");
		voOrderBaseInfo.setQuestionId(questionDTO.getQuestionId());
		voOrderBaseInfo.setCatagoryId(questionDTO.getCatagoryId());
		voOrderBaseInfo.setLineId(questionDTO.getLineId());
		voOrderBaseInfo.setApplicationLineId(questionDTO.getLineId());
		voOrderBaseInfo.setBusinessLine(questionDTO.getBusinessLine());
		voOrderBaseInfo.setCategoryPath(questionDTO.getCategoryPath());
		voOrderBaseInfo.setBu(questionQryDtlDTO.getBu());

		orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfo);

		BaseOrderDTO baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(orderBaseInfoCheckDTO.getOrderNo());
		//把类目相关的数据都做修改
		if(!String.valueOf(baseOrderDTO.getCategoryId()).equals(questionDTO.getCatagoryId())){
			orderOperatorLogService.addLogByBase(new BaseLog(baseOrderDTO.getOrderNo(),baseOrderDTO.getOrderType(), OrderOperatorTypeEnum.DEL_QUESTION,
				"删除修改问卷",null,personInfo.getPersonCode(),0));
		}



	}

	/**
	 *@Function: qryQuestion
	 *@Description 查询单个问卷
	 *@param: [questionOptionalReq, boSysPerson, privilegeLevelDTO]
	 *@author: Xiwei_Qiu @date: 2022/6/1 @version:
	 **/
	public JSONObject qryQuestion(QuestionOriginalReq questionOriginalReq, BOSysPerson boSysPerson, PrivilegeLevelDTO privilegeLevelDTO) throws Exception {
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(questionOriginalReq.getOrderId(), boSysPerson, privilegeLevelDTO);
		if(String.valueOf(orderBaseInfoCheckDTO.getOrderType()).equals(OrderTypeEnum.OIQ_SMALL_INQUIRY.getIndex())){
			return new JSONObject();
		}

		return memberRestTemplateService.getOptionByData(orderBaseInfoCheckDTO.getOrderNo(),questionOriginalReq.getIsOriginal());
	}


	@Master
	public JSONObject qryQuestionList(QuestionOriginalReq optionalReq, BOSysPerson personInfo, PrivilegeLevelDTO power) throws Exception{
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderBaseInfoService.checkOrderBase(optionalReq.getOrderId(), personInfo, power);
		if(String.valueOf(orderBaseInfoCheckDTO.getOrderType()).equals(OrderTypeEnum.OIQ_SMALL_INQUIRY.getIndex())){
			return jsonTransUtil.toJSONString(new ArrayList<>());
		}

		Long sum=customLimitService.selectQuestionCount(orderBaseInfoCheckDTO.getOrderNo());
		List<JSONObject> list=new ArrayList<>();
		JSONObject one=memberRestTemplateService.getOptionByData(orderBaseInfoCheckDTO.getOrderNo(),1);
		JSONObject two=new JSONObject();
		list.add(one);
		if(sum>0){
			two=memberRestTemplateService.getOptionByData(orderBaseInfoCheckDTO.getOrderNo(),0);
			list.add(two);
		}

		BaseOrderDTO baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(orderBaseInfoCheckDTO.getOrderNo());
		if (StringUtils.isBlank(orderBaseInfoCheckDTO.getTmpGroupNo()) && OrderStateEnum.distribution.getIndex().equals(String.valueOf(orderBaseInfoCheckDTO.getState()))) {
			//没有tmpGroupNo的时候 要查询偏好或者测试项目并入库
			String lockKey = "lock-tmp-" + baseOrderDTO.getOrderNo();
			String value = String.valueOf(System.currentTimeMillis());
			Boolean flag = redisClient.setLock(lockKey, value, 5);
			if (!flag) {
				throw new BusinessException(ResultEnumCode.LOCK_ERROR);
			}
			orderDetailService.addBaseTmpGroup(baseOrderDTO, one);
			redisClient.releaseLock(lockKey, value);
			return jsonTransUtil.toJSONString(list);
		}
		//
		if(orderBaseInfoCheckDTO.getSubState()== OrderSubStateEnum.QUESTION_FLG.getIndex() && sum>0){
			String lockKey = "lock-question-" + baseOrderDTO.getOrderNo();
			String value = String.valueOf(System.currentTimeMillis());
			Boolean flag = redisClient.setLock(lockKey, value, 5);
			if (!flag) {
				throw new BusinessException(ResultEnumCode.LOCK_ERROR);
			}
			orderDetailService.addQuestionTmpGroup(baseOrderDTO, two);
			redisClient.releaseLock(lockKey, value);
		}

		return jsonTransUtil.toJSONString(list);
	}


}
