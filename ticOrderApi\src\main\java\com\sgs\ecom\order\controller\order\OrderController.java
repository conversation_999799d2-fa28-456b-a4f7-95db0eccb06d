package com.sgs.ecom.order.controller.order;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckOrderNo;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderAttachmentDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.request.OrderBaseInfoReq;
import com.sgs.ecom.order.request.OrderBaseInfoUpdateReq;
import com.sgs.ecom.order.request.OrderPrintReq;
import com.sgs.ecom.order.request.OrderStateUpdateReq;
import com.sgs.ecom.order.request.OrderUseOtherReq;
import com.sgs.ecom.order.request.base.OrderGroupReq;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.request.operator.OrderCloseReq;
import com.sgs.ecom.order.request.operator.OrderOperatorReq;
import com.sgs.ecom.order.request.order.OrderProductReq;
import com.sgs.ecom.order.service.bbc.IBBCSV;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderOperatorService;
import com.sgs.ecom.order.service.order.interfaces.IOrderSchedulerService;
import com.sgs.ecom.order.service.rpc.IMemberRpcService;
import com.sgs.ecom.order.service.util.interfaces.IOrderService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import com.sgs.ecom.order.vo.VoOrderReport;
import com.sgs.redis.RedisClient;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/business/api.v2.order/order")
public class OrderController extends ControllerUtil {
	@Resource
	private IOrderService orderService;
	@Resource
	private IOrderOperatorService orderOperatorService;
	@Resource
	private ApiEventUtil eventApiUtil;
	@Resource
	private IBBCSV ibbcsv;
	@Resource
	private MailEventUtil mailEventUtil;
	@Resource
	private SmsEventUtil smsEventUtil;
	@Resource
	private  IOrderSchedulerService orderSchedulerService;
	@Resource
	private RedisClient redisClient;
	@Resource
	private IOrderAttachmentDomainService orderAttachmentDomainService;
	@Resource
	private IOrderBaseInfoDomainService orderBaseInfoDomainService;
	@Resource
	private IOrderBaseInfoCustomService orderBaseInfoCustomService;
	@Resource
	private IMemberRpcService iMemberRpcService;


	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryList", method = {RequestMethod.POST})
	public ResultBody qryList(
		@RequestHeader(value="system") String system,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(orderService.getPageList(orderBaseInfoReq, getPersonInfo(token), getPower(token,system)));
	}

	/**
	 * @Description : LV3列表label订单状态数量查询
	 * <AUTHOR> Zhang
	 * @Date  2023/12/6
	 * @param system:
	 * @param orderBaseInfoReq:
	 * @param token:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@AuthRequired(login = "SGS", sign = false)
	@RequestMapping(value = "qryStateNum", method = {RequestMethod.POST})
	public ResultBody qryStateNum(
			@RequestHeader(value="system") String system,
			@RequestBody OrderBaseInfoReq orderBaseInfoReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(orderService.qryStateNum(orderBaseInfoReq, getPersonInfo(token), getPower(token,system)));
	}


	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryDetail", method = {RequestMethod.POST})
	public ResultBody qryDetail(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderReq orderReq) throws Exception {
		return ResultBody.newInstance(orderService.qryMoreOrderDTO(orderReq.getOrderId(), "", getPersonInfo(token)));
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryRoDetail", method = {RequestMethod.POST})
	public ResultBody qryRoDetail(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderReq orderReq) throws Exception {
		return ResultBody.newInstance(orderService.qryMoreOrderDTO(orderReq.getOrderId(), "", getPersonInfo(token)));
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryGroup", method = {RequestMethod.POST})
	public ResultBody qryGroup(
		@RequestBody OrderIdReq orderIdReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(orderService.qryGroup(orderIdReq.getOrderId(), getPersonInfo(token), getPower(token)));
	}

	@CheckType
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryLastGroup", method = {RequestMethod.POST})
	public ResultBody qryLastGroup(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderIdReq orderIdReq) throws Exception {
		return ResultBody.newInstance(orderService.qryLastGroup(orderIdReq.getOrderId(), getPersonInfo(token)));
	}

	/**
	*@Function: useGroup
	*@Description 引用历史group
	*@param: [orderIdReq, token]
	*@author: Xiwei_Qiu @date: 2022/4/11 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "useGroup", method = {RequestMethod.POST})
	public ResultBody useGroup(
		@RequestBody OrderGroupReq orderGroupReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		orderService.useGroup(orderGroupReq, getPersonInfo(token), getPower(token));
		return ResultBody.success();
	}


	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "useOtherGroup", method = {RequestMethod.POST})
	public ResultBody useOtherGroup(
			@RequestBody OrderUseOtherReq orderGroupReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		orderService.useOtherGroup(orderGroupReq, getPersonInfo(token), getPower(token));
		return ResultBody.success();
	}


	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryGroupDetail", method = {RequestMethod.POST})
	public ResultBody qryGroupDetail(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderGroupReq orderGroupReq) throws Exception {
		return ResultBody.newInstance(orderService.qryMoreOrderDTO(Long.parseLong(orderGroupReq.getOrderId()), orderGroupReq.getGroupNo(), getPersonInfo(token)));
	}


	/**
	 * 获取状态数据
	 */
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "getStateNum", method = {RequestMethod.POST})
	public ResultBody getStateNum(
		@RequestHeader(value="system") String system,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {

		return ResultBody.newInstance(orderService.getStateNum(orderBaseInfoReq, getPersonInfo(token), getPower(token,system)));
	}


	/**
	*@Function: qryConfig
	*@Description 查询附件信息
	*@param: [token, orderGroupReq]
	*@author: Xiwei_Qiu @date: 2022/2/7 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryConfig", method = {RequestMethod.POST})
	public ResultBody qryConfig(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderGroupReq orderGroupReq
	) throws Exception {
		return ResultBody.newInstance(orderService.qryConfig(orderGroupReq, getPersonInfo(token), getPower(token)));
	}


	/**
	 * oiq分配和认领询价单
	 */
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "updateState", method = {RequestMethod.POST})
	public ResultBody updateState(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderStateUpdateReq orderStateUpdateReq) throws Exception {
		orderService.updateState(orderStateUpdateReq, getPersonInfo(token));
		if(orderStateUpdateReq.getInquiryUpdateState()==1){
			mailEventUtil.sendMail(orderStateUpdateReq.getEventOrderNo(),orderStateUpdateReq.getOrderType(), OiqMailEnum.CRM_CHANGE,2L,orderStateUpdateReq.getInquiryOtherDTO());
		}
		//认领或分配触发匹配
		if(orderStateUpdateReq.getEventFlg()==1){
			eventApiUtil.saveEvent(orderStateUpdateReq.getEventOrderNo(),EventEnum.UPDATE_STATE_TO_LEADS);
		}
		//eventApiUtil.sendWechatMsg(orderStateUpdateReq.getEventOrderNo(),EventEnum.SEND_WECHAT_MSG, WechatEnum.DISTRIBUTION);
		return ResultBody.success();
	}


	/**
	 * 待报价（暂存和更新状态） 或更新报价单updateOrderInfo
	 * @param orderBaseInfoUpdateReq
	 */
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "updateOrderInfo", method = {RequestMethod.POST})
	public ResultBody updateOrderInfo(
		@RequestBody OrderBaseInfoUpdateReq orderBaseInfoUpdateReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {

		String lockKey = "lock-" + orderBaseInfoUpdateReq.getOrderId();
		String value = String.valueOf(System.currentTimeMillis()+10000);
		Boolean flag = redisClient.setLock(lockKey, value, 30);
		if (!flag) {
			throw new BusinessException(ResultEnumCode.LOCK_ERROR);
		}
		try {
			orderService.updateOrderInfo(orderBaseInfoUpdateReq, getPersonInfo(token), getPower(token));
		}catch (BusinessException businessException){
			redisClient.releaseLock(lockKey, value);
			throw new BusinessException(businessException.getResultCode(),businessException.getDesc());
		}finally {
			redisClient.releaseLock(lockKey, value);
		}
		if(orderBaseInfoUpdateReq.getEventFlg()==1){
			//生成文件
			BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.updateInfoInquiryToSendMailOrderNo(orderBaseInfoUpdateReq.getEventOrderNo());
			if(OrderTypeEnum.OIQ_INQUIRY.getIndex().equals(baseOrderDTO.getOrderType())){
				eventApiUtil.sendWechatMsg(orderBaseInfoUpdateReq.getEventOrderNo(),EventEnum.SEND_WECHAT_MSG,WechatEnum.UPDATE);
				mailEventUtil.sendMail(orderBaseInfoUpdateReq.getEventOrderNo(),orderBaseInfoUpdateReq.getOrderType(),OiqMailEnum.UPDATE,1L);
				smsEventUtil.sendSms(orderBaseInfoUpdateReq.getEventOrderNo(),orderBaseInfoUpdateReq.getOrderType(), OiqSmsEnum.UPDATE);
			}else{
				mailEventUtil.sendMail(baseOrderDTO,OiqMailEnum.PORTAL_UPDATE,1L);
				smsEventUtil.sendSms(baseOrderDTO.getOrderNo(),OrderTypeEnum.OIQ_SMALL_ORDER.getIndex(), OiqSmsEnum.DML_UPDATE);
			}
		}
		return ResultBody.success();
	}

	/**
	 *
	 * @param orderBaseInfoUpdateReq
	 * @param token
	 * @return 首次更新报价的时候确认实验室
	 * @throws Exception
	 */
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "checkLab", method = {RequestMethod.POST})
	public ResultBody checkLab(
			@RequestBody OrderBaseInfoUpdateReq orderBaseInfoUpdateReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(orderService.checkLabByFirst(orderBaseInfoUpdateReq, getPersonInfo(token), getPower(token)));
	}





	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "open", method = {RequestMethod.POST})
	public ResultBody open(
		@RequestBody OrderIdReq orderIdReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderOperatorService.openOrder(orderIdReq, getPersonInfo(token), getPower(token));

		// 刷新缓存_OIQ最近确认订单列表
		iMemberRpcService.refreshCacheHeaderList(orderBaseInfoCheckDTO.getOrderNo(), orderBaseInfoCheckDTO.getBu(), "0");
		return ResultBody.success();
	}

	/**
	 * @Function: openConfirm
	 * @Description 回退待确认
	 * @param: [orderCloseReq, token]
	 * @author: Xiwei_Qiu @date: 2021/11/17 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "openConfirm", method = {RequestMethod.POST})
	public ResultBody openConfirm(
		@RequestBody OrderCloseReq orderCloseReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderOperatorService.openConfirm(orderCloseReq,
				getPersonInfo(token), getPower(token));
		if(orderCloseReq.getModLabelFlg()==1){
			eventApiUtil.saveEvent(orderCloseReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
		}
		// 刷新缓存_OIQ最近确认订单列表
		iMemberRpcService.refreshCacheHeaderList(orderBaseInfoCheckDTO.getOrderNo(), orderBaseInfoCheckDTO.getBu(), "1");
		return ResultBody.success();
	}

	/**
	 * @Function: close
	 * @Description 取消订单
	 * @param: [orderCloseReq, token]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4506"})
	@RequestMapping(value = "close", method = {RequestMethod.POST})
	public ResultBody close(
		@RequestBody OrderCloseReq orderCloseReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = orderOperatorService.closeOrder(orderCloseReq, getPersonInfo(token), getPower(token));
		if(StringUtils.isNotBlank(orderCloseReq.getApiOrderNo())){
			eventApiUtil.saveEvent(orderCloseReq.getApiOrderNo(), EventEnum.SAVE_ORDER_TO_END);
		}
		if(orderCloseReq.getModLabelFlg()==1){
			eventApiUtil.saveEvent(orderCloseReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
		}

		String orderType = String.valueOf(orderBaseInfoCheckDTO.getOrderType());
		if (StringUtil.equals(orderType, OrderTypeEnum.OIQ_INQUIRY.getIndex()) ||
				StringUtil.equals(orderType, OrderTypeEnum.OIQ_SMALL_INQUIRY.getIndex())) {
			// 刷新缓存_OIQ最近确认订单列表
			iMemberRpcService.refreshCacheHeaderList(orderBaseInfoCheckDTO.getOrderNo(), orderBaseInfoCheckDTO.getBu(), "1");
		}

		return ResultBody.success();
	}

	@CheckOrderNo
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "updateOrder", method = { RequestMethod.POST })
	public ResultBody updateOrder (
			@RequestHeader(value="accessToken") String token,
			@Validated(BaseBean.Update.class)
			@RequestBody VOOrderBaseInfo orderVo)throws Exception{
		orderService.updateOrder(orderVo,getPersonInfo(token));
		orderService.updateOrderNotification(orderVo);
		return ResultBody.success();
	}

	/**
	 * @Description : 批量取消订单-目前只限于LV3
	 * <AUTHOR> Zhang
	 * @Date  2023/3/15
	 * @param orderCloseReq:
	 * @param token:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4506"})
	@RequestMapping(value = "batchClose", method = {RequestMethod.POST})
	public ResultBody batchClose(
			@RequestBody OrderCloseReq orderCloseReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		orderOperatorService.batchCloseOrder(orderCloseReq, getPersonInfo(token), getPower(token));
		if(orderCloseReq.getModLabelFlg()==1){
			List<String> eventOrderNoList = orderCloseReq.getEventOrderNoList();
			eventOrderNoList.stream().forEach(orderNo -> {
				eventApiUtil.saveEvent(orderCloseReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
			});
		}
		return ResultBody.success();
	}

	/**
	 * @Function: isTest
	 * @Description 设置为测试订单
	 * @param: [orderOperatorReq, token]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4505"})
	@RequestMapping(value = "isTest", method = {RequestMethod.POST})
	public ResultBody isTest(
		@RequestBody OrderOperatorReq orderOperatorReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		orderOperatorService.updateTest(orderOperatorReq, getPersonInfo(token), getPower(token));
		return ResultBody.success();
	}

	/**
	 * @Description: OIQ和TIC订单设置为测试订单公共方法
	 * @Author: bowen zhang
	 * @Date: 2022/5/9
	 * @param orderOperatorReq:
	 * @param token:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4505"})
	@RequestMapping(value = "isPublicTest", method = {RequestMethod.POST})
	public ResultBody isPublicTest(
			@RequestBody OrderOperatorReq orderOperatorReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		orderOperatorService.updatePublicTest(orderOperatorReq, getPersonInfo(token), getPower(token));
		return ResultBody.success();
	}

	/**
	 * @Function: isPayReceived
	 * @Description 已到账/未到账
	 * @param: [orderOperatorReq, token]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4517", "4518"})
	@RequestMapping(value = "isPayReceived", method = {RequestMethod.POST})
	public ResultBody isPayReceived(@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderOperatorReq orderOperatorReq) throws Exception {
		BOSysPerson boSysPerson = getPersonInfo(token);
		orderOperatorService.updateReceived(orderOperatorReq, boSysPerson);
		if (orderOperatorReq.getPayToLeads() == 1) {
			// 回写leads已成单状态和成交金额
			eventApiUtil.saveEvent(orderOperatorReq.getEventOrderNo(), EventEnum.PAY_TO_LEADS);
			// 更新DML和虚拟询价单已确认报价状态
			eventApiUtil.saveEvent(orderOperatorReq.getEventOrderNo(), EventEnum.PAY_TO_QUOTATION_CONFIRM, boSysPerson);
		}
		return ResultBody.success();
	}

	/**
	 * @Function: isPayReceived
	 * @Description 新已到账/未到账
	 * @param: [orderOperatorReq, token]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4517", "4518"})
	@RequestMapping(value = "payReceived", method = {RequestMethod.POST})
	public ResultBody payReceived(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderOperatorReq orderOperatorReq) throws Exception {
		String orderNo=orderOperatorService.payReceived(orderOperatorReq, getPersonInfo(token));
		if(StringUtils.isNotBlank(orderNo)){
			eventApiUtil.saveEvent(orderNo, EventEnum.SAVE_ORDER_TO_END);
		}
		return ResultBody.success();
	}

	/**
	 * @Function: batchPayReceived
	 * @Description 批量到账
	 * @param: [orderOperatorReq, token]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4517", "4518"})
	@RequestMapping(value = "batchPayReceived", method = {RequestMethod.POST})
	public ResultBody batchPayReceived(
		@RequestBody OrderOperatorReq orderOperatorReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		orderOperatorService.batchPayReceived(orderOperatorReq, getPersonInfo(token), getPower(token));
		if(!ValidationUtil.isEmpty(orderOperatorReq.getOrderNoList())){
			for(String orderNo:orderOperatorReq.getOrderNoList()){
				eventApiUtil.saveEvent(orderNo, EventEnum.SAVE_ORDER_TO_END);
			}
		}
		return ResultBody.success();
	}


	/**
	 * 询价单订单导出excel
	 * @param orderBaseInfoReq 导出条件
	 * @throws Exception
	 */
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "expOrderList", method = {RequestMethod.POST})
	public void expOrderList(
		@RequestHeader(value="system") String system,
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderBaseInfoReq orderBaseInfoReq, HttpServletResponse res) throws Exception {
		orderService.expOrderList(orderBaseInfoReq, getPersonInfo(token), getPower(token,system), res);
	}





	/**
	 * @Function: qryOrderReportPDF
	 * @Description 打印报价单
	 * @param: [orderGroupReq, token, httpServletResponse]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@AuthRequired(login = "SGS", sign = false, permission = {"4301", "4503"})
	@RequestMapping(value = "qryOrderReportPDF", method = {RequestMethod.POST})
	public void qryOrderReportPDF(
		@RequestBody OrderGroupReq orderGroupReq,
		@RequestHeader(value = "accessToken") String token,
		HttpServletResponse httpServletResponse) throws Exception {
		orderService.getOrderPDF(orderGroupReq,httpServletResponse);
	}


	/**
	 * @Description: TIC订单根据条件查询指定数据列表
	 * @Author: bowen zhang
	 * @Date: 2022/5/10
	 * @param token:
	 * @param voOrderBaseInfo:
	 * @return: void
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryRelationOrderList", method = {RequestMethod.POST})
	public ResultBody qryRelationOrderList(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody VOOrderBaseInfo voOrderBaseInfo) throws Exception {
		return ResultBody.newInstance(orderService.qryRelationOrderList(voOrderBaseInfo, getPersonInfo(token), getPower(token)));
	}

	/**
	 * @Description: 客服端查询邮件抄送人邮箱
	 * @Author: bowen zhang
	 * @Date: 2022/6/28
	 * @param token:
	 * @param voOrderReport:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryServiceSendCcEmail", method = {RequestMethod.POST})
	public ResultBody qryServiceSendCcEmail(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody VoOrderReport voOrderReport
	) throws Exception {
		return ResultBody.newInstance(orderService.qryServiceSendCcEmail(voOrderReport));
	}

	/**
	 * @Description:客服端保存/更新邮件抄送人邮箱
	 * @Author: bowen zhang
	 * @Date: 2022/6/28
	 * @param token:
	 * @param voOrderReport:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "addOrModServiceSendCcEmail", method = {RequestMethod.POST})
	public ResultBody addOrModServiceSendCcEmail(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody VoOrderReport voOrderReport
	) throws Exception {
		orderService.addOrModServiceSendCcEmail(voOrderReport, getPersonInfo(token));
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryQuotationData", method = {RequestMethod.POST})
	public ResultBody qryQuotationData(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderReq orderReq) throws Exception {
		return ResultBody.newInstance(orderService.qryQuotationData(orderReq.getOrderId(),getPersonInfo(token)));
	}


	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryLabInfoListByProId", method = {RequestMethod.POST})
	public ResultBody qryLabInfoListByProId(
			@RequestHeader(value = "accessToken") String token,
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderProductReq orderProductReq) throws Exception {
		return ResultBody.newInstance(ibbcsv.qryLabInfoListByProductId(orderProductReq));
	}


	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryLastLog", method = {RequestMethod.POST})
	public ResultBody qryLastLog(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody OrderReq orderReq) throws Exception {
		return ResultBody.newInstance(orderService.qryCenterLastLog(orderReq));
	}

	/**
	 * @Description : 获取订单基本信息接口
	 * <AUTHOR> Zhang
	 * @Date  2023/5/19
	 * @param orderReq:
	 * @param token:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryOrderDetail", method = {RequestMethod.POST})
	public ResultBody qryOrderDetail(
			@RequestBody OrderReq orderReq,
			@RequestHeader(value = "accessToken") String token
	) throws Exception {
		return ResultBody.newInstance(orderService.qryOrderDetail(orderReq, getPersonInfo(token), getPower(token)));
	}


//	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "updateTicSendEmail", method = {RequestMethod.POST})
	public ResultBody updateTicSendEmail(
			@RequestBody OrderReq orderReq,
			@RequestHeader(value = "accessToken") String token
	) throws Exception {
		orderSchedulerService.updateTicSendEmail(5);
		orderSchedulerService.updateTicSendEmail(2);
		return ResultBody.success();
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "generateOrderPdf", method = { RequestMethod.POST })
	public ResultBody generateOrderPdf (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq) throws Exception {
		return ResultBody.newInstance(orderService.generateOrderPdf(orderNoReq));
	}
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "downloadFormZip", method = { RequestMethod.POST })
	public void downloadFormZip (
			@Validated(value = BaseBean.Query.class)
			@RequestBody OrderPrintReq orderNoReq,
			HttpServletResponse res) throws Exception {
		orderService.downloadFormZip(orderNoReq,res);
	}

}
