package com.sgs.ecom.order.base;



import com.sgs.ecom.order.dto.send.InquiryOtherMailDTO;
import com.sgs.ecom.order.dto.send.TicOtherMailDTO;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;

import java.util.HashMap;
import java.util.Map;

public class BaseSend {
	// mailType 1客户 2客服
	//String orderNo, String mail, String sendCC, Map map, OiqMailEnum oiqMailEnum, String buType, Long mailType
	private String orderNo;
	private String sendMail;
	private String sendCC;
	private Map<String,Object> mapData=new HashMap<>();
	private String mailEnum;
	private String buType;
	private Long mailType;//1用户 2客服
	private Boolean useOrderId;
	private Long orderId;
	private TicOtherMailDTO ticOtherMailDTO;
	private InquiryOtherMailDTO inquiryOtherMailDTO;
	private Long prodId;
	private Integer orderType;
	private Boolean isSub;//是否是子单
	private Long labId;//实验室id

	private String authType;//默认MESSAGE

	private int isTest;

	private Long userId;

	public String getAuthType() {
		return authType;
	}

	public void setAuthType(String authType) {
		this.authType = authType;
	}

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public int getIsTest() {
		return isTest;
	}

	public void setIsTest(int isTest) {
		this.isTest = isTest;
	}
	public BaseSend() {
	}

	//不使用service数据
	public BaseSend(String orderNo, String sendMail, String sendCC, Map<String, Object> mapData, String mailEnum, String buType, Long mailType,Long prodId) {
		this.orderNo = orderNo;
		this.sendMail = sendMail;
		this.sendCC = sendCC;
		this.mapData = mapData;
		this.mailEnum = mailEnum;
		this.buType = buType;
		this.mailType = mailType;
		this.prodId=prodId;
		this.useOrderId=false;
	}

	public BaseSend(Long orderId, OiqMailEnum oiqMailEnum, Long mailType){
		this.orderId = orderId;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
	}

	public BaseSend(Long orderId, OiqMailEnum oiqMailEnum, Long mailType,TicOtherMailDTO ticOtherMailDTO){
		this.orderId = orderId;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
		this.ticOtherMailDTO=ticOtherMailDTO;
	}
	public BaseSend(Long orderId, OiqMailEnum oiqMailEnum, Long mailType,TicOtherMailDTO ticOtherMailDTO, Boolean isSub){
		this.orderId = orderId;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
		this.ticOtherMailDTO=ticOtherMailDTO;
		this.isSub = isSub;
	}

	public BaseSend(Long orderId, OiqMailEnum oiqMailEnum, Long mailType,InquiryOtherMailDTO inquiryOtherMailDTO){
		this.orderId = orderId;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
		this.inquiryOtherMailDTO=inquiryOtherMailDTO;
	}

	public BaseSend(Long orderId, OiqMailEnum oiqMailEnum, Long mailType, Boolean isSub) {
		this.orderId = orderId;
		this.mailEnum = oiqMailEnum.getIndex();
		this.mailType=mailType;
		this.useOrderId=true;
		this.isSub = isSub;
	}

	public Boolean getSub() {
		return isSub;
	}

	public void setSub(Boolean sub) {
		isSub = sub;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSendMail() {
		return sendMail;
	}

	public void setSendMail(String sendMail) {
		this.sendMail = sendMail;
	}

	public String getSendCC() {
		return sendCC;
	}

	public void setSendCC(String sendCC) {
		this.sendCC = sendCC;
	}

	public Map<String, Object> getMapData() {
		return mapData;
	}

	public void setMapData(Map<String, Object> mapData) {
		this.mapData = mapData;
	}

	public String getMailEnum() {
		return mailEnum;
	}

	public void setMailEnum(String mailEnum) {
		this.mailEnum = mailEnum;
	}

	public String getBuType() {
		return buType;
	}

	public void setBuType(String buType) {
		this.buType = buType;
	}

	public Long getMailType() {
		return mailType;
	}

	public void setMailType(Long mailType) {
		this.mailType = mailType;
	}


	public Boolean getUseOrderId() {
		return useOrderId;
	}

	public void setUseOrderId(Boolean useOrderId) {
		this.useOrderId = useOrderId;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public Long getProdId() {
		return prodId;
	}

	public void setProdId(Long prodId) {
		this.prodId = prodId;
	}

	public TicOtherMailDTO getTicOtherMailDTO() {
		return ticOtherMailDTO;
	}

	public void setTicOtherMailDTO(TicOtherMailDTO ticOtherMailDTO) {
		this.ticOtherMailDTO = ticOtherMailDTO;
	}

	public InquiryOtherMailDTO getInquiryOtherMailDTO() {
		return inquiryOtherMailDTO;
	}

	public void setInquiryOtherMailDTO(InquiryOtherMailDTO inquiryOtherMailDTO) {
		this.inquiryOtherMailDTO = inquiryOtherMailDTO;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}
}
