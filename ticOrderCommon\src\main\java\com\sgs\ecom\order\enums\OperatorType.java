package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

@ExplainAnno(value="enum",name="月结操作类型")
public enum OperatorType implements EnumMessage {

	ADD_CUST("新增月结客户", "ADD_CUST"), MOD_CUST("修改月结客户", "MOD_CUST"), DEL_CUST("删除月结客户", "DEL_CUST"),  
	FREEZE_CUST("冻结月结客户", "FREEZE_CUST"), UNFREEZE_CUST("解冻月结客户", "UNFREEZE_CUST"),  
	ADD_INVOICE("新增月结发票", "ADD_INVOICE"), MOD_INVOICE("修改月结发票", "MOD_INVOICE"), DEL_INVOICE("删除月结发票", "DEL_INVOICE"), 
	RELARE_INVOICE("关联发票", "RELARE_INVOICE"),
	RELARE_USER("关联/解除关联", "RELARE_USER");
	
    // 成员变量  
    private String name;  
    private String index;  
    // 构造方法  
    private OperatorType(String name, String index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (OperatorType c : OperatorType.values()) {  
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }  

	@Override
	public String getKey() {
		return String.valueOf(index);
	}

	@Override
	public String getValue() {
		return name;
	}  
}
