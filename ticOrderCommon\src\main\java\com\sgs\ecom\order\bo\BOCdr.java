package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 

public class BOCdr{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select * from cdr where 1=2"; 
 
 	public static final String OWNER ="bbc";

 	public static final String S_CALLDATE="calldate";
 	public static final String S_USERFIELD="userfield";
 	public static final String S_HANGUP="hangup";
 	public static final String S_DST="dst";
 	public static final String S_SNAME="sname";
 	public static final String S_RINGTIME="ringtime";
 	public static final String S_ID="id";
 	public static final String S_DCONTEXT="dcontext";
 	public static final String S_MIXUNIQUEID="mixuniqueid";
 	public static final String S_LASTDATA="lastdata";
 	public static final String S_ACCOUNTCODE="accountcode";
 	public static final String S_SRC="src";
 	public static final String S_DISPOSITION="disposition";
 	public static final String S_DSTCHANNEL="dstchannel";
 	public static final String S_LASTAPP="lastapp";
 	public static final String S_CALLTYPE="calltype";
 	public static final String S_DNAME="dname";
 	public static final String S_BILLSEC="billsec";
 	public static final String S_DID="did";
 	public static final String S_AGENT="agent";
 	public static final String S_UNIQUEID="uniqueid";
 	public static final String S_DURATION="duration";
 	public static final String S_CLID="clid";
 	public static final String S_AMAFLAGS="amaflags";
 	public static final String S_SERVER="server";
 	public static final String S_MIXCALLTYPE="mixcalltype";
 	public static final String S_CHANNEL="channel";

 	public static final String CALLDATE="calldate";
 	public static final String USERFIELD="userfield";
 	public static final String HANGUP="hangup";
 	public static final String DST="dst";
 	public static final String SNAME="sname";
 	public static final String RINGTIME="ringtime";
 	public static final String ID="id";
 	public static final String DCONTEXT="dcontext";
 	public static final String MIXUNIQUEID="mixuniqueid";
 	public static final String LASTDATA="lastdata";
 	public static final String ACCOUNTCODE="accountcode";
 	public static final String SRC="src";
 	public static final String DISPOSITION="disposition";
 	public static final String DSTCHANNEL="dstchannel";
 	public static final String LASTAPP="lastapp";
 	public static final String CALLTYPE="calltype";
 	public static final String DNAME="dname";
 	public static final String BILLSEC="billsec";
 	public static final String DID="did";
 	public static final String AGENT="agent";
 	public static final String UNIQUEID="uniqueid";
 	public static final String DURATION="duration";
 	public static final String CLID="clid";
 	public static final String AMAFLAGS="amaflags";
 	public static final String SERVER="server";
 	public static final String MIXCALLTYPE="mixcalltype";
 	public static final String CHANNEL="channel";

 	private Timestamp calldate;
 	private String userfield;
 	private String hangup;
 	private String dst;
 	private String sname;
 	private long ringtime;
 	private long id;
 	private String dcontext;
 	private String mixuniqueid;
 	private String lastdata;
 	private String accountcode;
 	private String src;
 	private String disposition;
 	private String dstchannel;
 	private String lastapp;
 	private String calltype;
 	private String dname;
 	private long billsec;
 	private String did;
 	private String agent;
 	private String uniqueid;
 	private long duration;
 	private String clid;
 	private long amaflags;
 	private String server;
 	private String mixcalltype;
 	private String channel;

 	public void setCalldate(Timestamp calldate){
 		 this.calldate=calldate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCalldate(){
 		 return this.calldate;
 	}
 
 	 
 	@CharacterVaild(len = 255) 
 	public void setUserfield(String userfield){
 		 this.userfield=userfield;
 	}
 	public String getUserfield(){
 		 return this.userfield;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setHangup(String hangup){
 		 this.hangup=hangup;
 	}
 	public String getHangup(){
 		 return this.hangup;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setDst(String dst){
 		 this.dst=dst;
 	}
 	public String getDst(){
 		 return this.dst;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setSname(String sname){
 		 this.sname=sname;
 	}
 	public String getSname(){
 		 return this.sname;
 	}
 
 	 
 	public void setRingtime(long ringtime){
 		 this.ringtime=ringtime;
 	}
 	public long getRingtime(){
 		 return this.ringtime;
 	}
 
 	 
 	public void setId(long id){
 		 this.id=id;
 	}
 	public long getId(){
 		 return this.id;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setDcontext(String dcontext){
 		 this.dcontext=dcontext;
 	}
 	public String getDcontext(){
 		 return this.dcontext;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setMixuniqueid(String mixuniqueid){
 		 this.mixuniqueid=mixuniqueid;
 	}
 	public String getMixuniqueid(){
 		 return this.mixuniqueid;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setLastdata(String lastdata){
 		 this.lastdata=lastdata;
 	}
 	public String getLastdata(){
 		 return this.lastdata;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setAccountcode(String accountcode){
 		 this.accountcode=accountcode;
 	}
 	public String getAccountcode(){
 		 return this.accountcode;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setSrc(String src){
 		 this.src=src;
 	}
 	public String getSrc(){
 		 return this.src;
 	}
 
 	 
 	@CharacterVaild(len = 45) 
 	public void setDisposition(String disposition){
 		 this.disposition=disposition;
 	}
 	public String getDisposition(){
 		 return this.disposition;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setDstchannel(String dstchannel){
 		 this.dstchannel=dstchannel;
 	}
 	public String getDstchannel(){
 		 return this.dstchannel;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setLastapp(String lastapp){
 		 this.lastapp=lastapp;
 	}
 	public String getLastapp(){
 		 return this.lastapp;
 	}
 
 	 
 	@CharacterVaild(len = 15) 
 	public void setCalltype(String calltype){
 		 this.calltype=calltype;
 	}
 	public String getCalltype(){
 		 return this.calltype;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setDname(String dname){
 		 this.dname=dname;
 	}
 	public String getDname(){
 		 return this.dname;
 	}
 
 	 
 	public void setBillsec(long billsec){
 		 this.billsec=billsec;
 	}
 	public long getBillsec(){
 		 return this.billsec;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setDid(String did){
 		 this.did=did;
 	}
 	public String getDid(){
 		 return this.did;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setAgent(String agent){
 		 this.agent=agent;
 	}
 	public String getAgent(){
 		 return this.agent;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setUniqueid(String uniqueid){
 		 this.uniqueid=uniqueid;
 	}
 	public String getUniqueid(){
 		 return this.uniqueid;
 	}
 
 	 
 	public void setDuration(long duration){
 		 this.duration=duration;
 	}
 	public long getDuration(){
 		 return this.duration;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setClid(String clid){
 		 this.clid=clid;
 	}
 	public String getClid(){
 		 return this.clid;
 	}
 
 	 
 	public void setAmaflags(long amaflags){
 		 this.amaflags=amaflags;
 	}
 	public long getAmaflags(){
 		 return this.amaflags;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setServer(String server){
 		 this.server=server;
 	}
 	public String getServer(){
 		 return this.server;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setMixcalltype(String mixcalltype){
 		 this.mixcalltype=mixcalltype;
 	}
 	public String getMixcalltype(){
 		 return this.mixcalltype;
 	}
 
 	 
 	@CharacterVaild(len = 80) 
 	public void setChannel(String channel){
 		 this.channel=channel;
 	}
 	public String getChannel(){
 		 return this.channel;
 	}
 
 	 
}