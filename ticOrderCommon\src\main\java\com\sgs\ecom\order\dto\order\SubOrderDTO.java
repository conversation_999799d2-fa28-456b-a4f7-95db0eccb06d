package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.pay.OrderPayDTO;
import com.sgs.ecom.order.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.order.enumtool.pay.OiqCurrencyEnum;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class SubOrderDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long orderId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String groupNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer orderType;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String relateOrderNo;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer isPayReceived;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String recommendReason;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String operatorCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer totalNums;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal orderAmount;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int payState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int refundState;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int state;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payMethod;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private List<OrderPayDTO> orderPayDTOList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private  List<OrderDetailDTO> orderDetailDTOList=new ArrayList<>();
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal taxRates=BigDecimal.ZERO;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Integer isRemind;

    @ApiAnno(groups={BaseOrderFilter.Default.class})
    private int isRefundAll;//发起的退款是全额还是部分退款  0-无退款信息 1-全部  2-部分

    private String currency;
    private String currencyMark;
    private String toPayAmount;
    private int monthPay;

    private OrderOperatorLogDTO lastConfirmLog;

    private String offPayAmount;
    private String onlinePayAmount;

    public int getIsRefundAll() {
        return isRefundAll;
    }

    public void setIsRefundAll(int isRefundAll) {
        this.isRefundAll = isRefundAll;
    }


    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getStateDate() {
        return stateDate;
    }

    public void setStateDate(String stateDate) {
        this.stateDate = stateDate;
    }

    public Integer getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(Integer isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public String getRecommendReason() {
        return recommendReason;
    }

    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public Integer getTotalNums() {
        return totalNums;
    }

    public void setTotalNums(Integer totalNums) {
        this.totalNums = totalNums;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public List<OrderPayDTO> getOrderPayDTOList() {
        return orderPayDTOList;
    }

    public void setOrderPayDTOList(List<OrderPayDTO> orderPayDTOList) {
        this.orderPayDTOList = orderPayDTOList;
    }

    public int getRefundState() {
        return refundState;
    }

    public void setRefundState(int refundState) {
        this.refundState = refundState;
    }

    public String getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payStateShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String refundStateShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String stateShow;


    public String getRefundStateShow() {
        return OrderRefundStateEnum.getNameCh(refundState);
    }

    public String getPayStateShow() {
        return OrderPayStateEnum.getNameCh(payState);
    }

    public List<OrderDetailDTO> getOrderDetailDTOList() {
        return orderDetailDTOList;
    }

    public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
        this.orderDetailDTOList = orderDetailDTOList;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getTaxRates() {
        return taxRates;
    }

    public void setTaxRates(BigDecimal taxRates) {
        this.taxRates = taxRates;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }



    public String getStateShow() {
        if(state==91){
            return "已取消";
        }
        return stateShow;
    }

    public void setStateShow(String stateShow) {
        this.stateShow = stateShow;
    }

    public Integer getIsRemind() {
        if(isRemind!=null && isRemind==2){
            isRemind=1;
        }

        return isRemind;
    }

    public void setIsRemind(Integer isRemind) {
        this.isRemind = isRemind;
    }

    public OrderOperatorLogDTO getLastConfirmLog() {
        return lastConfirmLog;
    }

    public void setLastConfirmLog(OrderOperatorLogDTO lastConfirmLog) {
        this.lastConfirmLog = lastConfirmLog;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }



    public String getToPayAmount() {
        return toPayAmount;
    }

    public void setToPayAmount(String toPayAmount) {
        this.toPayAmount = toPayAmount;
    }

    public int getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(int monthPay) {
        this.monthPay = monthPay;
    }

    public String getOffPayAmount() {
        return offPayAmount;
    }

    public void setOffPayAmount(String offPayAmount) {
        this.offPayAmount = offPayAmount;
    }

    public String getOnlinePayAmount() {
        return onlinePayAmount;
    }

    public void setOnlinePayAmount(String onlinePayAmount) {
        this.onlinePayAmount = onlinePayAmount;
    }

    public String getCurrencyMark() {
        return OiqCurrencyEnum.getKeyShow(currency);
    }

    public void setCurrencyMark(String currencyMark) {
        this.currencyMark = currencyMark;
    }
}
