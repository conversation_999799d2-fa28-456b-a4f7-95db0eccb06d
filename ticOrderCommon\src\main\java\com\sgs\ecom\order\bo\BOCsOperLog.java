package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOCsOperLog{
 
 	public static final String SEQUENCE = "LOG_ID"; 
  
 	public static final String BO_SQL = "TB_CS_OPER_LOG"; 
 
 	public static final String OWNER ="member";

 	public static final String BUSINESS_ID="businessId";
 	public static final String CS_CODE="csCode";
 	public static final String BUSINESS_TYPE="businessType";
 	public static final String REQ_PARA="reqPara";
 	public static final String LOG_ID="logId";
 	public static final String CS_ID="csId";
 	public static final String REMARK="remark";
 	public static final String OPERATOR_DATE="operatorDate";
 	public static final String OPERATOR_TYPE="operatorType";

 	@BeanAnno("BUSINESS_ID")
 	private long businessId;
 	@BeanAnno("CS_CODE")
 	private String csCode;
 	@BeanAnno("BUSINESS_TYPE")
 	private int businessType;
 	@BeanAnno("REQ_PARA")
 	private String reqPara;
 	@BeanAnno("LOG_ID")
 	private long logId;
 	@BeanAnno("CS_ID")
 	private long csId;
 	@BeanAnno("REMARK")
 	private String remark;
 	@BeanAnno("OPERATOR_DATE")
 	private Timestamp operatorDate;
 	@BeanAnno("OPERATOR_TYPE")
 	private String operatorType;

 	public void setBusinessId(long businessId){
 		 this.businessId=businessId;
 	}
 	public long getBusinessId(){
 		 return this.businessId;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCsCode(String csCode){
 		 this.csCode=csCode;
 	}
 	public String getCsCode(){
 		 return this.csCode;
 	}
 
 	 
 	public void setBusinessType(int businessType){
 		 this.businessType=businessType;
 	}
 	public int getBusinessType(){
 		 return this.businessType;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setReqPara(String reqPara){
 		 this.reqPara=reqPara;
 	}
 	public String getReqPara(){
 		 return this.reqPara;
 	}
 
 	 
 	public void setLogId(long logId){
 		 this.logId=logId;
 	}
 	public long getLogId(){
 		 return this.logId;
 	}
 
 	 
 	public void setCsId(long csId){
 		 this.csId=csId;
 	}
 	public long getCsId(){
 		 return this.csId;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
 	public void setOperatorDate(Timestamp operatorDate){
 		 this.operatorDate=operatorDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getOperatorDate(){
 		 return this.operatorDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOperatorType(String operatorType){
 		 this.operatorType=operatorType;
 	}
 	public String getOperatorType(){
 		 return this.operatorType;
 	}
 
 	 
}