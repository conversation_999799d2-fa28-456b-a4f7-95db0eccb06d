package com.sgs.ecom.order.entity;



public class UserQuestion {
 



 	private String cloudId;

 	private String questionNameEn;

 	private Integer questionType;

 	private String stateDate;

 	private Integer state;

 	private Long userId;

 	private String orderNo;

 	private Integer finalScore;

 	private String questionName;


 	private String createDate;

 	private String languageFlag;

 	private String currentPart;

 	private Long questionId;

 	private Long replyId;

 	private Integer realScore;

 	private String replyNo;

 	private String attachmentName;


 	private String fileKey;
 	private int isOriginal;
 	private String categoryPath;
 	private Long categoryId;
 	private Long lineId;
 	private String businessLine;

 	public void setCloudId(String cloudId){
 		 this.cloudId=cloudId;
 	}
 	public String getCloudId(){
 		 return this.cloudId;
 	}
 
 	 
 	public void setQuestionNameEn(String questionNameEn){
 		 this.questionNameEn=questionNameEn;
 	}
 	public String getQuestionNameEn(){
 		 return this.questionNameEn;
 	}
 
 	 
 	public void setQuestionType(Integer questionType){
 		 this.questionType=questionType;
 	}
 	public Integer getQuestionType(){
 		 return this.questionType;
 	}
 
 	 
 	public void setStateDate(String stateDate){
 		 this.stateDate=stateDate;
 	}
 	public String getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(Integer state){
 		 this.state=state;
 	}
 	public Integer getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserId(Long userId){
 		 this.userId=userId;
 	}
 	public Long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setFinalScore(Integer finalScore){
 		 this.finalScore=finalScore;
 	}
 	public Integer getFinalScore(){
 		 return this.finalScore;
 	}
 
 	 
 	public void setQuestionName(String questionName){
 		 this.questionName=questionName;
 	}
 	public String getQuestionName(){
 		 return this.questionName;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setLanguageFlag(String languageFlag){
 		 this.languageFlag=languageFlag;
 	}
 	public String getLanguageFlag(){
 		 return this.languageFlag;
 	}
 
 	 
 	public void setCurrentPart(String currentPart){
 		 this.currentPart=currentPart;
 	}
 	public String getCurrentPart(){
 		 return this.currentPart;
 	}
 
 	 
 	public void setQuestionId(Long questionId){
 		 this.questionId=questionId;
 	}
 	public Long getQuestionId(){
 		 return this.questionId;
 	}
 
 	 
 	public void setReplyId(Long replyId){
 		 this.replyId=replyId;
 	}
 	public Long getReplyId(){
 		 return this.replyId;
 	}
 
 	 
 	public void setRealScore(Integer realScore){
 		 this.realScore=realScore;
 	}
 	public Integer getRealScore(){
 		 return this.realScore;
 	}
 
 	 
 	public void setReplyNo(String replyNo){
 		 this.replyNo=replyNo;
 	}
 	public String getReplyNo(){
 		 return this.replyNo;
 	}
 
 	 
 	public void setAttachmentName(String attachmentName){
 		 this.attachmentName=attachmentName;
 	}
 	public String getAttachmentName(){
 		 return this.attachmentName;
 	}
 
 	 
 	public void setFileKey(String fileKey){
 		 this.fileKey=fileKey;
 	}
 	public String getFileKey(){
 		 return this.fileKey;
 	}

	public int getIsOriginal() {
		return isOriginal;
	}

	public void setIsOriginal(int isOriginal) {
		this.isOriginal = isOriginal;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Long getLineId() {
		return lineId;
	}

	public void setLineId(Long lineId) {
		this.lineId = lineId;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}
}