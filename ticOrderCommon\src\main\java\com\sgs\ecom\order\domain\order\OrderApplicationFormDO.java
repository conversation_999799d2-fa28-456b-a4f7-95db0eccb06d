package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.rpc.dml.ApplicationInfoDTO;
import com.sgs.ecom.order.entity.order.OrderApplicationForm;
import com.sgs.ecom.order.request.oiq.OiqApplicationReq;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;


public class OrderApplicationFormDO extends OrderApplicationForm {


    private BaseCopyObj baseCopy = new BaseCopyObj();



    public OrderApplicationForm getFormByReq(OiqApplicationReq oiqApplicationReq, OiqOrderReqDTO oiqOrderReqDTO){

        OrderApplicationForm orderApplicationForm = new OrderApplicationForm();
        baseCopy.copyWithNull(orderApplicationForm,oiqApplicationReq);
        orderApplicationForm.setOrderNo(oiqOrderReqDTO.getOrderNo());
        orderApplicationForm.setState(1);
        orderApplicationForm.setCreateDate(oiqOrderReqDTO.getDateStr());
        orderApplicationForm.setStateDate(oiqOrderReqDTO.getDateStr());
        return orderApplicationForm;
    }

    public OrderApplicationForm getFormByDmlReq(String orderNo, ApplicationInfoDTO applicationInfoDTO){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderApplicationForm orderApplicationForm = new OrderApplicationForm();
        //orderApplicationForm.setLinkPhone(applicationInfoDTO.getLinkPhone());
        //orderApplicationForm.setLinkEmail(applicationInfoDTO.getLinkEmail());
        //orderApplicationForm.setLinkPerson(applicationInfoDTO.getLinkPerson());
        orderApplicationForm.setCompanyNameCn(applicationInfoDTO.getCompanyName());
        orderApplicationForm.setCompanyNameEn(applicationInfoDTO.getCompanyNameEn());
        orderApplicationForm.setCompanyAddressCn(applicationInfoDTO.getCompanyAddr());
        orderApplicationForm.setCompanyAddressEn(applicationInfoDTO.getCompanyAddrEn());
        orderApplicationForm.setTestMemo(applicationInfoDTO.getTestMemo());
        orderApplicationForm.setIsRefundSample(applicationInfoDTO.getIsRefundSample());
        orderApplicationForm.setIsTransfer(applicationInfoDTO.getIsTransfer());
        orderApplicationForm.setOrderNo(orderNo);
        orderApplicationForm.setState(1);
        orderApplicationForm.setStateDate(dateStr);
        return orderApplicationForm;
    }



    public void entityToDTO(OrderApplicationFormDTO orderApplicationFormDTO,  OrderApplicationForm orderApplicationForm){
        if(ValidationUtil.isEmpty(orderApplicationFormDTO)){
            orderApplicationFormDTO = new OrderApplicationFormDTO();
        }
        baseCopy.copy(orderApplicationFormDTO,orderApplicationForm);
    }
}
