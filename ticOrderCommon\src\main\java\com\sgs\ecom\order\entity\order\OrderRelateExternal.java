package com.sgs.ecom.order.entity.order;

import javax.persistence.Id;

/**
 * <AUTHOR>
 */
public class OrderRelateExternal{
 
	@Id
 	private Long relateId;

 	private String createDate;

 	private String externalSystem;

 	private String stateDate;

 	private Integer state;

 	private String orderNo;

 	private String externalNo;

 	public void setRelateId(Long relateId){
 		 this.relateId=relateId;
 	}
 	public Long getRelateId(){
 		 return this.relateId;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setExternalSystem(String externalSystem){
 		 this.externalSystem=externalSystem;
 	}
 	public String getExternalSystem(){
 		 return this.externalSystem;
 	}
 
 	 
 	public void setStateDate(String stateDate){
 		 this.stateDate=stateDate;
 	}
 	public String getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(Integer state){
 		 this.state=state;
 	}
 	public Integer getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setExternalNo(String externalNo){
 		 this.externalNo=externalNo;
 	}
 	public String getExternalNo(){
 		 return this.externalNo;
 	}
 
 	 
}