package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.OrderBaseInfoReq;
import com.sgs.ecom.order.service.portal.interfaces.IOrderPortalService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.portal/order")
public class OiqOrderPortalController extends ControllerUtil {

    @Autowired
    private IOrderPortalService orderPortalService;




    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "expOrderList", method = {RequestMethod.POST})
    public void expOrderList(
            @RequestHeader(value="system") String system,
            @RequestHeader(value = "accessToken") String token,
            @RequestBody OrderBaseInfoReq orderBaseInfoReq, HttpServletResponse res) throws Exception {
        orderPortalService.expOrderList(orderBaseInfoReq, getPersonInfo(token), getPower(token,system), res);
    }


}
