package com.sgs.ecom.order.fegin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.service.custom.interfaces.ICustServiceSV;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOCustInfo;
import com.sgs.ecom.order.vo.VOCustService;
import com.sgs.ecom.order.vo.VOUserInfo;

@RestController
@RequestMapping("/business/rpc.v1.order/cs")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class,SysException.class})
public class CustServiceRpc extends ControllerUtil {

	@Autowired
	private ICustServiceSV custServiceSV;
	
	/**   
	* @Function: qryServiceByCust
	* @Description: 查询企业销售
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-01-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-01-30  shenyi    v1.0                 新增
	*/

    @HystrixCommand
    @RequestMapping(value = "qryCs", method = { RequestMethod.POST })
    public ResultBody qryServiceByCust(@RequestBody VOCustService custService) throws Exception {
    	return ResultBody.newInstance(custServiceSV.qryServiceByCust(custService.getCustId(), custService.getBu()));
	}
    
    /**   
	* @Function: addCustService
	* @Description: 增加用户销售
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-01-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-01-30  shenyi    v1.0                 新增
	*/

    @HystrixCommand
    @AuthRequired(login = "NULL", sign = false)
    @RequestMapping(value = "addCs", method = { RequestMethod.POST })
    public ResultBody addCustService(@RequestBody VOUserInfo userInfo) throws Exception {
    	custServiceSV.addCustService(userInfo);
    	return ResultBody.success();
	}
}
