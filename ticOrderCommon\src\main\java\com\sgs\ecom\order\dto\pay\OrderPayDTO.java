package com.sgs.ecom.order.dto.pay;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.enumtool.pay.OrderPayRefundEnum;
import com.sgs.ecom.order.enumtool.pay.OrderRefundCnEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.util.UseDateUtil;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import org.apache.commons.lang.StringUtils;

public class OrderPayDTO extends BaseOrderFilter {
 
 	public static final String CREATE_SQL = "select PAY_ACCOUNT,PAY_PRICE,CREATE_DATE,DETAIL_CODE,TRANS_NO,PAY_DATE,PAYMENT_ID,USER_ID,STATE,ORDER_NO,PAY_METHOD,MEMO from ORDER_PAY"; 
 
 
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="PAY_ACCOUNT", getName="getPayAccount", setName="setPayAccount")
 	private String payAccount;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="PAY_PRICE", getName="getPayPrice", setName="setPayPrice")
 	private int payPrice;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="DETAIL_CODE", getName="getDetailCode", setName="setDetailCode")
 	private String detailCode;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="TRANS_NO", getName="getTransNo", setName="setTransNo")
 	private String transNo;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="PAY_DATE", getName="getPayDate", setName="setPayDate")
 	private Timestamp payDate;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="PAYMENT_ID", getName="getPaymentId", setName="setPaymentId")
 	private long paymentId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="PAY_METHOD", getName="getPayMethod", setName="setPayMethod")
 	private int payMethod;
 	@ApiAnno(groups={Default.class,RstsDetail.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
	@ApiAnno(groups={Default.class})
	private List<OrderAttachmentDTO> payFileList;
	@ApiAnno(groups={Default.class,RstsDetail.class})
	private String accountName;
	@ApiAnno(groups={Default.class,RstsDetail.class})
	private String paymentNo;
	@ApiAnno(groups={Default.class,RstsDetail.class})
	private int payType;
	@ApiAnno(groups={Default.class})
	private String refundInfo;
	@ApiAnno(groups={Default.class})
	private String refundInfoShow;
	@ApiAnno(groups={Default.class})
	private String remark;
	@ApiAnno(groups={Default.class})
	private String refundOperatorShow;
	@ApiAnno(groups={Default.class})
	private String refundStateShow;
	@ApiAnno(groups={Default.class})
	private String refundTypeShow;
	@ApiAnno(groups={Default.class})
	private String refundShow;
	@ApiAnno(groups={Default.class})
	private String userName;
	@ApiAnno(groups={Default.class})
	private String printDate;
	@ApiAnno(groups={Default.class})
	private String buyerInfo;
	@ApiAnno(groups={Default.class})
	private String refundPrice;
	@ApiAnno(groups={Default.class})
	private OrderInvoiceDTO orderInvoiceDTO;
	@ApiAnno(groups={Default.class,RstsDetail.class})
	private BigDecimal payPriceShow;
	@ApiAnno(groups={Default.class})
	private String buyerName;
	@ApiAnno(groups={Default.class,RstsDetail.class})
	private String companyName;

	@ApiAnno(groups={Default.class})
	private int isCs;

	@ApiAnno(groups={Default.class})
	private int isSuccessShow =0;
	@ApiAnno(groups={Default.class})
	private String isTime;


	public int getIsSuccessShow() {
		return isSuccessShow;
	}

	public void setIsSuccessShow(int isSuccessShow) {
		this.isSuccessShow = isSuccessShow;
	}

	public int getIsCs() {
		return isCs;
	}

	public void setIsCs(int isCs) {
		this.isCs = isCs;
	}

	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public void setPayAccount(String payAccount){
 		 this.payAccount=payAccount;
 	}
 	public String getPayAccount(){
 		 return this.payAccount;
 	}
 
 	 
 	public void setPayPrice(int payPrice){
 		 this.payPrice=payPrice;
 	}
 	public int getPayPrice(){
 		 return this.payPrice;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class)
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setDetailCode(String detailCode){
 		 this.detailCode=detailCode;
 	}
 	public String getDetailCode(){
 		 return this.detailCode;
 	}
 
 	 
 	public void setTransNo(String transNo){
 		 this.transNo=transNo;
 	}
 	public String getTransNo(){
 		 return this.transNo;
 	}
 
 	 
 	public void setPayDate(Timestamp payDate){
 		 this.payDate=payDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class)
 	public Timestamp getPayDate(){
 		 return this.payDate;
 	}
 
 	 
 	public void setPaymentId(long paymentId){
 		 this.paymentId=paymentId;
 	}
 	public long getPaymentId(){
 		 return this.paymentId;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setPayMethod(int payMethod){
 		 this.payMethod=payMethod;
 	}
 	public int getPayMethod(){
 		 return this.payMethod;
 	}
 
 	 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}

	public List<OrderAttachmentDTO> getPayFileList() {
		return payFileList;
	}

	public void setPayFileList(List<OrderAttachmentDTO> payFileList) {
		this.payFileList = payFileList;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getPaymentNo() {
		return paymentNo;
	}

	public void setPaymentNo(String paymentNo) {
		this.paymentNo = paymentNo;
	}

	@ApiAnno(groups={Default.class})
	private String payMethodShow;
	public String getPayMethodShow(){
		payMethodShow= PayMethodEnum.getNameCh(String.valueOf(payMethod));
		if(payMethodShow==null){
			payMethodShow="";
		}
		return payMethodShow;
	}
	public String getRefundInfo() {
		return refundInfo;
	}
	public void setRefundInfo(String refundInfo) {
		this.refundInfo = refundInfo;
	}
	
	public int getPayType() {
		return payType;
	}
	public void setPayType(int payType) {
		this.payType = payType;
	}
	public String getRefundInfoShow() {
		if(StringUtils.isBlank(refundInfo)){
			return "";
		}
		if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() == Integer.parseInt(refundInfo)){
			return "";
		}
		refundInfoShow= OrderPayRefundEnum.getNameCh(Integer.parseInt(refundInfo));
		if(refundInfoShow==null){
			refundInfoShow="";
		}
		return refundInfoShow;
	}
	public void setRefundInfoShow(String refundInfoShow) {
		this.refundInfoShow = refundInfoShow;
	}
	public String getRefundOperatorShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(parseInt==91){
			return "";
		}
		if(StringUtils.isBlank(remark) && state == 0){
			refundOperatorShow= OrderRefundCnEnum.getNameCh(2);
		}else if(!StringUtils.isBlank(remark) && state == 0){
			refundOperatorShow= OrderRefundCnEnum.getNameChC(2);
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1){
			refundOperatorShow= OrderRefundCnEnum.getNameChB(2);
		}else if(state == 2){
			refundOperatorShow= OrderRefundCnEnum.getNameChC(2);
		}
		if(refundOperatorShow==null){
			refundOperatorShow="";
		}
		return refundOperatorShow;
	}
	public void setRefundOperatorShow(String refundOperatorShow) {
		this.refundOperatorShow = refundOperatorShow;
	}
	public String getRefundStateShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(parseInt==91 && isSuccessShow == 0){
			return "";
		}
		if(StringUtils.isBlank(remark) && state == 0){
			refundStateShow= OrderRefundCnEnum.getNameCh(3);
		}else if(!StringUtils.isBlank(remark) && state == 0){
			refundStateShow= UseDateUtil.getDateString(payDate) +" "+ OrderRefundCnEnum.getNameChC(3)+"，"+remark;
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1  && isSuccessShow == 0){
			refundStateShow= UseDateUtil.getDateString(payDate) +" "+ OrderRefundCnEnum.getNameChB(3);
		}else if( state == 1  && isSuccessShow == 1){
			refundStateShow= UseDateUtil.getDateString(payDate) +"  已退款";
		}else if(state == 2){
			refundStateShow= UseDateUtil.getDateString(payDate) +" "+ OrderRefundCnEnum.getNameChC(3)+"，"+remark;
		}
		if(refundStateShow==null){
			refundStateShow="";
		}
		return refundStateShow;
	
	}
	public void setRefundStateShow(String refundStateShow) {
		this.refundStateShow = refundStateShow;
	}
	public String getRefundTypeShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() == parseInt){
			this.refundTypeShow = OrderRefundCnEnum.getNameChD(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if(StringUtils.isBlank(remark) && state == 0){
			refundTypeShow= OrderRefundCnEnum.getNameCh(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if(!StringUtils.isBlank(remark) && state == 0){
			this.refundTypeShow= OrderRefundCnEnum.getNameChC(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1){
			refundTypeShow= OrderRefundCnEnum.getNameChB(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}else if(state == 2){
			this.refundTypeShow= OrderRefundCnEnum.getNameChC(4);
			if(!StringUtils.isBlank(payAccount)  && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+payAccount;
			}
			if(StringUtils.isBlank(payAccount) && payMethod == 100000){
				refundTypeShow= OrderRefundCnEnum.getNameCh(5)+buyerInfo;
			}
		}
		if(refundTypeShow==null){
			refundTypeShow="";
		}
		return refundTypeShow;
	}
	public void setRefundTypeShow(String refundTypeShow) {
		this.refundTypeShow = refundTypeShow;
	}
	public String getRefundShow() {
		if(payType != 3){
			return "";
		}
		if(ValidationUtil.isEmpty(refundInfo)){
			return "";
		}
		int parseInt = Integer.parseInt(refundInfo);
		if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() == parseInt){
			refundShow= OrderRefundCnEnum.getNameChD(1);
		}else if(StringUtils.isBlank(remark) && state == 0){
			refundShow= OrderRefundCnEnum.getNameCh(1);
		}else if(!StringUtils.isBlank(remark) && state == 0){
			refundShow= OrderRefundCnEnum.getNameChC(1);
		}else if(OrderPayRefundEnum.SERVICE_REFUND.getIndex() != parseInt  && state == 1){
			refundShow= OrderRefundCnEnum.getNameChB(1);
		}else if(state == 2){//客服驳回申请
			refundShow= OrderRefundCnEnum.getNameChC(1);
		}
		if(refundShow==null){
			refundShow="";
		}
		return refundShow;
	}
	public void setRefundShow(String refundShow) {
		this.refundShow = refundShow;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getPrintDate() {
		return printDate;
	}
	public void setPrintDate(String printDate) {
		this.printDate = printDate;
	}
	public String getBuyerInfo() {
		return buyerInfo;
	}
	public void setBuyerInfo(String buyerInfo) {
		this.buyerInfo = buyerInfo;
	}
	public String getRefundPrice() {
		
		return String.valueOf(new BigDecimal(payPrice).movePointLeft(2));
	}
	public void setRefundPrice(String refundPrice) {
		this.refundPrice = refundPrice;
	}

	public OrderInvoiceDTO getOrderInvoiceDTO() {
		return orderInvoiceDTO;
	}

	public void setOrderInvoiceDTO(OrderInvoiceDTO orderInvoiceDTO) {
		this.orderInvoiceDTO = orderInvoiceDTO;
	}

	public BigDecimal getPayPriceShow() {
		return new BigDecimal(payPrice).movePointLeft(2);
	}

	public void setPayPriceShow(BigDecimal payPriceShow) {
		this.payPriceShow = payPriceShow;
	}

	public String getBuyerName() {
		return buyerName;
	}

	public void setBuyerName(String buyerName) {
		this.buyerName = buyerName;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getIsTime() {
		return isTime;
	}

	public void setIsTime(String isTime) {
		this.isTime = isTime;
	}
}