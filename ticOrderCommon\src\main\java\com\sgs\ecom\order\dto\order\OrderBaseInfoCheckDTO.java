package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.TfsStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

public class OrderBaseInfoCheckDTO extends BaseOrderFilter {
    @ApiAnno(groups={Default.class,OrderDetail.class})
    private Long orderId;
    @ApiAnno(groups={Default.class,OrderDetail.class})
    private int state;
    @ApiAnno(groups={Default.class})
    private String groupNo;
    @ApiAnno(groups={Default.class,OrderDetail.class})
    private String orderNo;
    @ApiAnno(groups={Default.class})
    private Long userId;
    @ApiAnno(groups={Default.class})
    private String userKey;
    @ApiAnno(groups={Default.class})
    private String tmpGroupNo;
    @ApiAnno(groups={Default.class})
    private int hisState;
    @ApiAnno(groups={Default.class})
    private String csCode;
    @ApiAnno(groups={Default.class})
    private String csEmail;
    @ApiAnno(groups={Default.class})
    private String csName;
    @ApiAnno(groups={Default.class})
    private String csNameEn;
    @ApiAnno(groups={Default.class})
    private int payState;
    @ApiAnno(groups={Default.class})
    private int subState;
    @ApiAnno(groups={Default.class})
    private int refundState;
    @ApiAnno(groups={Default.class})
    private BigDecimal realAmount;
    @ApiAnno(groups={Default.class})
    private int orderType;
    @ApiAnno(groups={Default.class})
    private String bu;
    @ApiAnno(groups={Default.class})
    private String operatorCode;
    @ApiAnno(groups={Default.class})
    private String businessLine;
    @ApiAnno(groups={Default.class})
    private int lineId;
    @ApiAnno(groups={Default.class})
    private Long labId;
    @ApiAnno(groups={Default.class})
    private Integer isPayReceived;
    @ApiAnno(groups={Default.class})
    private int payMethod;
    @ApiAnno(groups={Default.class})
    private String userPhone;
    @ApiAnno(groups={Default.class})
    private String userName;
    @ApiAnno(groups={Default.class})
    private String userEmail;
    @ApiAnno(groups={Default.class})
    private String companyName;
    @ApiAnno(groups={Default.class})
    private String province;
    @ApiAnno(groups={Default.class})
    private String city;
    @ApiAnno(groups={Default.class})
    private String companyNameEn;
    @ApiAnno(groups={Default.class})
    private String companyAddressCn;
    @ApiAnno(groups={Default.class})
    private String companyAddressEn;
    @ApiAnno(groups={Default.class})
    private String town;
    @ApiAnno(groups={Default.class})
    private String confirmOrderDate;
    private String salesCode;
    private String salesPhone;

    @ApiAnno(groups={OrderDetail.class})
    private boolean checkStateFlag;//客户发起主单下是否补充订单没有设为已到账/已取消
    @ApiAnno(groups={OrderDetail.class})
    private String relateOrderNo;

    @ApiAnno(groups={OrderDetail.class})
    private boolean subCancelStateFlag;//客服发起退款主单下是否补充订单有没有已取消
    @ApiAnno(groups={OrderDetail.class})
    private boolean subConfirmStateFlag;//客户发起子单退款，客服审核，判断主单是否在服务中/已完成状态

    @ApiAnno(groups={Default.class})
    private int monthPay;

    @ApiAnno(groups={Default.class})
    private Long categoryId;//实验室类别id

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public int getMonthPay() {
        return monthPay;
    }

    public void setMonthPay(int monthPay) {
        this.monthPay = monthPay;
    }

    public boolean isSubConfirmStateFlag() {
        return subConfirmStateFlag;
    }

    public void setSubConfirmStateFlag(boolean subConfirmStateFlag) {
        this.subConfirmStateFlag = subConfirmStateFlag;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public boolean isSubCancelStateFlag() {
        return subCancelStateFlag;
    }

    public void setSubCancelStateFlag(boolean subCancelStateFlag) {
        this.subCancelStateFlag = subCancelStateFlag;
    }

    public boolean isCheckStateFlag() {
        return checkStateFlag;
    }

    public void setCheckStateFlag(boolean checkStateFlag) {
        this.checkStateFlag = checkStateFlag;
    }

    public String getConfirmOrderDate() {
        return confirmOrderDate;
    }

    public void setConfirmOrderDate(String confirmOrderDate) {
        this.confirmOrderDate = confirmOrderDate;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getCompanyNameEn() {
        return companyNameEn;
    }

    public void setCompanyNameEn(String companyNameEn) {
        this.companyNameEn = companyNameEn;
    }

    public String getCompanyAddressCn() {
        return companyAddressCn;
    }

    public void setCompanyAddressCn(String companyAddressCn) {
        this.companyAddressCn = companyAddressCn;
    }

    public String getCompanyAddressEn() {
        return companyAddressEn;
    }

    public void setCompanyAddressEn(String companyAddressEn) {
        this.companyAddressEn = companyAddressEn;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public Integer getIsPayReceived() {
        return isPayReceived;
    }

    public void setIsPayReceived(Integer isPayReceived) {
        this.isPayReceived = isPayReceived;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTmpGroupNo() {
        return tmpGroupNo;
    }

    public void setTmpGroupNo(String tmpGroupNo) {
        this.tmpGroupNo = tmpGroupNo;
    }

    public int getHisState() {
        return hisState;
    }

    public void setHisState(int hisState) {
        this.hisState = hisState;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public String getCsNameEn() {
        return csNameEn;
    }

    public void setCsNameEn(String csNameEn) {
        this.csNameEn = csNameEn;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

    public int getSubState() {
        return subState;
    }

    public void setSubState(int subState) {
        this.subState = subState;
    }


    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    @JsonSerialize(using = PriceNumFormatSerializer.class)
    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public int getLineId() {
        return lineId;
    }

    public void setLineId(int lineId) {
        this.lineId = lineId;
    }

    public int getRefundState() {
        return refundState;
    }

    public void setRefundState(int refundState) {
        this.refundState = refundState;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    @ApiAnno(groups={Default.class})
    private String stateShow;
    @ApiAnno(groups={Default.class})
    private String subStateShow;
    @ApiAnno(groups={Default.class})
    private String payStateShow;
    @ApiAnno(groups={Default.class})
    private String refundStateShow;


    public String getSubStateShow() {
        if(state==91){
            return "";
        }
        return OrderSubStateEnum.getNameCh(subState);
    }

    public String getRefundStateShow() {
        if(state==91){
            return "";
        }
        return  OrderRefundStateEnum.getNameCh(refundState);
    }

    public String getPayStateShow() {
        if(state==91){
            return "";
        }
        return OrderPayStateEnum.getNameCh(payState);
    }

    public String getStateShow() {
        //提交申请表没寄送样品的时候 把状态改完待审核
        if(StringUtils.isNotBlank(bu) && "2700".equals(bu)){
            return TfsStateEnum.getNameCh(state);
        }
        if(orderType==100000 && state==12){
            state=13;
        }
         return OrderStateEnum.getNameCh(String.valueOf(state));
    }

    public int getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(int payMethod) {
        this.payMethod = payMethod;
    }

    public String getUserKey() {
        if(userId==null){
            return "";
        }

        return "0"+userId%10+"_"+userId;
    }

    public void setUserKey(String userKey) {
        this.userKey = userKey;
    }

    public String getSalesCode() {
        return salesCode;
    }

    public void setSalesCode(String salesCode) {
        this.salesCode = salesCode;
    }

    public String getSalesPhone() {
        return salesPhone;
    }

    public void setSalesPhone(String salesPhone) {
        this.salesPhone = salesPhone;
    }
}
