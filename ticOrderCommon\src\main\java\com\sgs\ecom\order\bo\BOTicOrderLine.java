package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 

public class BOTicOrderLine{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "TIC_OrderLine"; 
 
 	public static final String OWNER ="bbc";

 	public static final String S_SUBTITLE="Subtitle";
 	public static final String S_PRODUCTATTR="ProductAttr";
 	public static final String S_CATEGORYNAME="CategoryName";
 	public static final String S_QUANTITY="Quantity";
 	public static final String S_ORDERID="OrderID";
 	public static final String S_MODIFIEDBY="ModifiedBy";
 	public static final String S_TOTALPRICE="TotalPrice";
 	public static final String S_PRODUCTID="ProductID";
 	public static final String S_MODIFIEDDATE="ModifiedDate";
 	public static final String S_SKUATTR="SkuAttr";
 	public static final String S_PICPATH="PicPath";
 	public static final String S_ORDERNO="OrderNo";
 	public static final String S_STOREID="StoreID";
 	public static final String S_STORENAME="StoreName";
 	public static final String S_PRICE="Price";
 	public static final String S_TRIMSID="TrimsID";
 	public static final String S_CREATEDDATE="CreatedDate";
 	public static final String S_CREATEDBY="CreatedBy";
 	public static final String S_ID="ID";
 	public static final String S_PRODUCTNAME="ProductName";
 	public static final String S_REMARK="Remark";

 	public static final String SUBTITLE="subtitle";
 	public static final String PRODUCTATTR="productAttr";
 	public static final String CATEGORYNAME="categoryName";
 	public static final String QUANTITY="quantity";
 	public static final String ORDERID="orderID";
 	public static final String MODIFIEDBY="modifiedBy";
 	public static final String TOTALPRICE="totalPrice";
 	public static final String PRODUCTID="productID";
 	public static final String MODIFIEDDATE="modifiedDate";
 	public static final String SKUATTR="skuAttr";
 	public static final String PICPATH="picPath";
 	public static final String ORDERNO="orderNo";
 	public static final String STOREID="storeID";
 	public static final String STORENAME="storeName";
 	public static final String PRICE="price";
 	public static final String TRIMSID="trimsID";
 	public static final String CREATEDDATE="createdDate";
 	public static final String CREATEDBY="createdBy";
 	public static final String ID="iD";
 	public static final String PRODUCTNAME="productName";
 	public static final String REMARK="remark";

 	@BeanAnno("subtitle")
 	private String subtitle;
 	@BeanAnno("productAttr")
 	private String productAttr;
 	@BeanAnno("categoryName")
 	private String categoryName;
 	@BeanAnno("quantity")
 	private long quantity;
 	@BeanAnno("orderID")
 	private String orderID;
 	@BeanAnno("modifiedBy")
 	private String modifiedBy;
 	@BeanAnno("totalPrice")
 	private double totalPrice;
 	@BeanAnno("productID")
 	private String productID;
 	@BeanAnno("modifiedDate")
 	private Timestamp modifiedDate;
 	@BeanAnno("skuAttr")
 	private String skuAttr;
 	@BeanAnno("picPath")
 	private String picPath;
 	@BeanAnno("orderNo")
 	private String orderNo;
 	@BeanAnno("storeID")
 	private String storeID;
 	@BeanAnno("storeName")
 	private String storeName;
 	@BeanAnno("price")
 	private double price;
 	@BeanAnno("trimsID")
 	private String trimsID;
 	@BeanAnno("createdDate")
 	private Timestamp createdDate;
 	@BeanAnno("createdBy")
 	private String createdBy;
 	@BeanAnno("iD")
 	private String iD;
 	@BeanAnno("productName")
 	private String productName;
 	@BeanAnno("remark")
 	private String remark;

 	@CharacterVaild(len = 100) 
 	public void setSubtitle(String subtitle){
 		 this.subtitle=subtitle;
 	}
 	public String getSubtitle(){
 		 return this.subtitle;
 	}
 
 	 
 	@CharacterVaild(len = 715827882) 
 	public void setProductAttr(String productAttr){
 		 this.productAttr=productAttr;
 	}
 	public String getProductAttr(){
 		 return this.productAttr;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCategoryName(String categoryName){
 		 this.categoryName=categoryName;
 	}
 	public String getCategoryName(){
 		 return this.categoryName;
 	}
 
 	 
 	public void setQuantity(long quantity){
 		 this.quantity=quantity;
 	}
 	public long getQuantity(){
 		 return this.quantity;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderID(String orderID){
 		 this.orderID=orderID;
 	}
 	public String getOrderID(){
 		 return this.orderID;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setModifiedBy(String modifiedBy){
 		 this.modifiedBy=modifiedBy;
 	}
 	public String getModifiedBy(){
 		 return this.modifiedBy;
 	}
 
 	 
 	public void setTotalPrice(double totalPrice){
 		 this.totalPrice=totalPrice;
 	}
 	public double getTotalPrice(){
 		 return this.totalPrice;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProductID(String productID){
 		 this.productID=productID;
 	}
 	public String getProductID(){
 		 return this.productID;
 	}
 
 	 
 	public void setModifiedDate(Timestamp modifiedDate){
 		 this.modifiedDate=modifiedDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getModifiedDate(){
 		 return this.modifiedDate;
 	}
 
 	 
 	@CharacterVaild(len = 715827882) 
 	public void setSkuAttr(String skuAttr){
 		 this.skuAttr=skuAttr;
 	}
 	public String getSkuAttr(){
 		 return this.skuAttr;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setPicPath(String picPath){
 		 this.picPath=picPath;
 	}
 	public String getPicPath(){
 		 return this.picPath;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setStoreID(String storeID){
 		 this.storeID=storeID;
 	}
 	public String getStoreID(){
 		 return this.storeID;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	public void setPrice(double price){
 		 this.price=price;
 	}
 	public double getPrice(){
 		 return this.price;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setTrimsID(String trimsID){
 		 this.trimsID=trimsID;
 	}
 	public String getTrimsID(){
 		 return this.trimsID;
 	}
 
 	 
 	public void setCreatedDate(Timestamp createdDate){
 		 this.createdDate=createdDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreatedDate(){
 		 return this.createdDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCreatedBy(String createdBy){
 		 this.createdBy=createdBy;
 	}
 	public String getCreatedBy(){
 		 return this.createdBy;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setID(String iD){
 		 this.iD=iD;
 	}
 	public String getID(){
 		 return this.iD;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
}