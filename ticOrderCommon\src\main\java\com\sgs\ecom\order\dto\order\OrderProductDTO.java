package com.sgs.ecom.order.dto.order;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.dto.bbc.BbcSkuAttrDTO;
import com.sgs.ecom.order.enums.ReportLuaTransEnum;
import com.sgs.ecom.order.enumtool.bbc.BbcSkuAttrEnum;
import com.sgs.ecom.order.util.attr.AttrUtil;
import com.sgs.ecom.order.util.collection.StrUtil;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderProductDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select PRICE,QUANTITY,PRODUCT_NUMS,STATE_DATE,ORDER_NO,PRODUCT_ID,PRODUCT_NAME,CREATE_DATE,SUB_TITLE,STORE_NAME,TOTAL_PRICE,STORE_ID,PROD_ID from ORDER_PRODUCT"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRICE", getName="getPrice", setName="setPrice")
	@JsonSerialize(using = PriceNumFormatSerializer.class)
 	private BigDecimal price;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="QUANTITY", getName="getQuantity", setName="setQuantity")
 	private int quantity;

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_ID", getName="getProductId", setName="setProductId")
 	private Long productId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PRODUCT_NAME", getName="getProductName", setName="setProductName")
 	private String productName;

 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="SUB_TITLE", getName="getSubTitle", setName="setSubTitle")
 	private String subTitle;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STORE_NAME", getName="getStoreName", setName="setStoreName")
 	private String storeName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOTAL_PRICE", getName="getTotalPrice", setName="setTotalPrice")
 	private BigDecimal totalPrice;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STORE_ID", getName="getStoreId", setName="setStoreId")
 	private String storeId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PROD_ID", getName="getProdId", setName="setProdId")
 	private Long prodId;
	@ApiAnno(groups={Default.class})
	private String skuAttr;
	@ApiAnno(groups={Default.class})
	private String sku;
	@ApiAnno(groups={Default.class})
	private String testItem;//测试项目
	@ApiAnno(groups={Default.class})
	private String productMemo;
	@ApiAnno(groups={Default.class})
	private Map<String,String> skuMap=new HashMap<>();
	@ApiAnno(groups={Default.class})
	private List<BbcSkuAttrDTO> skuAttrList=new ArrayList<>();
	@ApiAnno(groups={Default.class})
	private String checkDate;

	@ApiAnno(groups={Default.class})
	private String customType;
	@ApiAnno(groups={Default.class})
	private String testName;

	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SUB_BU_CODE", getName="getSubBuCode", setName="setSubBuCode")
	private String subBuCode;

	@ApiAnno(groups={Default.class})
	private String reportLua;//给出对应的报告语言

	@ApiAnno(groups={Default.class})
	private String standardCode;//检测标准

	@ApiAnno(groups={Default.class})
	private String packageName;//套餐/自选名称
	@ApiAnno(groups={Default.class})
	private String productImg;//图片

	private List<OrderDetailDTO> details;

	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal realTotalPrice;

	private Integer state;
	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal shopDisAmount;
	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal subDiscountAmount;
	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal subCsDiscountAmount;
	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal subServiceAmount;

	@ApiAnno(groups={Default.class})
	private String pointsNum;//点位数

	/**
	 * 最低售价
	 */
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPrice;

	/**
	 * SKU费用
	 */
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal skuPrice;

	/**
	 * 最低售价补收差额
	 */
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPriceMargin;

	/**
	 * 触发最低售价
	 */
	@ApiAnno(groups={Default.class})
	private boolean triggerLowestPrice;

	/**
	 * 触发最低售价描述
	 */
	@ApiAnno(groups={Default.class})
	private String triggerLowestPriceStr;

	public BigDecimal getSubDiscountAmount() {
		return subDiscountAmount;
	}

	public void setSubDiscountAmount(BigDecimal subDiscountAmount) {
		this.subDiscountAmount = subDiscountAmount;
	}

	public BigDecimal getSubCsDiscountAmount() {
		return subCsDiscountAmount;
	}

	public void setSubCsDiscountAmount(BigDecimal subCsDiscountAmount) {
		this.subCsDiscountAmount = subCsDiscountAmount;
	}

	public BigDecimal getSubServiceAmount() {
		return subServiceAmount;
	}

	public void setSubServiceAmount(BigDecimal subServiceAmount) {
		this.subServiceAmount = subServiceAmount;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getProductImg() {
		return productImg;
	}

	public void setProductImg(String productImg) {
		this.productImg = productImg;
	}

	public BigDecimal getRealTotalPrice() {
		return realTotalPrice;
	}

	public void setRealTotalPrice(BigDecimal realTotalPrice) {
		this.realTotalPrice = realTotalPrice;
	}

	public List<OrderDetailDTO> getDetails() {
		return details;
	}

	public void setDetails(List<OrderDetailDTO> details) {
		this.details = details;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getStandardCode() {
		return standardCode;
	}

	public void setStandardCode(String standardCode) {
		this.standardCode = standardCode;
	}

	public String getTestName() {
		return testName;
	}

	public void setTestName(String testName) {
		this.testName = testName;
	}

	public String getCustomType() {
		return customType;
	}

	public void setCustomType(String customType) {
		this.customType = customType;
	}

	public String getSubBuCode() {
		return subBuCode;
	}

	public void setSubBuCode(String subBuCode) {
		this.subBuCode = subBuCode;
	}

	public void setQuantity(int quantity){
 		 this.quantity=quantity;
 	}
 	public int getQuantity(){
 		 return this.quantity;
 	}
 
 	 


 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setProductId(long productId){
 		 this.productId=productId;
 	}
 	public long getProductId(){
 		 return this.productId;
 	}
 
 	 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 

 	 
 	public void setSubTitle(String subTitle){
 		 this.subTitle=subTitle;
 	}
 	public String getSubTitle(){
 		 return this.subTitle;
 	}
 
 	 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}


	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}


	public Long getProdId() {
		return prodId;
	}

	public void setProdId(Long prodId) {
		this.prodId = prodId;
	}

	public String getSkuAttr() {
		return skuAttr;
	}

	public void setSkuAttr(String skuAttr) {
		this.skuAttr = skuAttr;
	}


	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getSku() {
 		if(StringUtils.isBlank(skuAttr)){
 			return "";
		}


		List<BbcSkuAttrDTO> list=JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
		return  AttrUtil.skuListToSkuStr(list);
	}


	public Map<String,String> getSkuMap() {
 		if(StringUtils.isBlank(skuAttr) ){
 			return new HashMap();
		}
		if(!skuAttr.contains("attrKey") ){
			return new HashMap();
		}

		List<BbcSkuAttrDTO> list=JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
		list=list.stream().filter(a->StringUtils.isNotBlank(a.getAttrKey())).collect(Collectors.toList());

		if(ValidationUtil.isEmpty(list)){
			return new HashMap();
		}
		return list.stream().collect(Collectors.toMap(BbcSkuAttrDTO::getAttrKey, BbcSkuAttrDTO::getAttrValue, (key1, key2) -> key2));
	}

	public String getReportLua() {
		if(StringUtils.isBlank(skuAttr)){
			return "";
		}


		List<BbcSkuAttrDTO> list=JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
		Map<String,BbcSkuAttrDTO> map=list.stream().filter(a->a.getShow())
				.sorted(Comparator.comparing(BbcSkuAttrDTO::getSortShow)).
						collect(Collectors.toMap(BbcSkuAttrDTO::getAttrKey, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
		StringBuilder stringBuilder=new StringBuilder();
		for(String key:map.keySet()){
			stringBuilder.append(map.get(key).getAttrName()+"："+map.get(key).getAttrValue()+"、");
		}
		String reportLuaStr = StrUtil.subLastChar(stringBuilder);
		return ReportLuaTransEnum.getReportLua(reportLuaStr);
	}

	public String getCheckDate() {
		Map<String,String> map=getSkuMap();
		if(!ValidationUtil.isEmpty(map) &&map.containsKey(BbcSkuAttrEnum.CHECK_DATE.getName())){
			return map.get(BbcSkuAttrEnum.CHECK_DATE.getName());
		}
		return "";
	}

	public void setCheckDate(String checkDate) {
		this.checkDate = checkDate;
	}

	public void setSkuMap(Map skuMap) {
		this.skuMap = skuMap;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getTestItem() {
		return testItem;
	}

	public void setTestItem(String testItem) {
		this.testItem = testItem;
	}

	public String getProductMemo() {
		return productMemo;
	}

	public void setProductMemo(String productMemo) {
		this.productMemo = productMemo;
	}

	public List<BbcSkuAttrDTO> getSkuAttrList() {
		if(StringUtils.isBlank(skuAttr) ){
			return new ArrayList<>();
		}
		List<BbcSkuAttrDTO> list=JSON.parseArray(skuAttr, BbcSkuAttrDTO.class);
		list=list.stream().filter(a->StringUtils.isNotBlank(a.getAttrKey())).
				sorted(Comparator.comparing(BbcSkuAttrDTO::getSortShow)).collect(Collectors.toList());
		return list;
	}

	public void setSkuAttrList(List<BbcSkuAttrDTO> skuAttrList) {
		this.skuAttrList = skuAttrList;
	}

	public String getPointsNum() {
		return pointsNum;
	}

	public void setPointsNum(String pointsNum) {
		this.pointsNum = pointsNum;
	}
}