package com.sgs.ecom.order.scheduler;


import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.util.SysException;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.service.order.interfaces.IOrderSchedulerService;
import com.sgs.ecom.order.service.util.interfaces.IOrderService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.redis.RedisClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@Component
public class OrderScheduler {


    @Autowired
    IOrderService orderService;
    @Autowired
    IOrderSchedulerService orderSchedulerService;
    @Autowired
    ApplicationArguments applicationArguments;
    @Autowired
    private RedisClient redisClient;
    @Autowired
    private ApiEventUtil apiEventUtil;



 // @Scheduled(cron = "0 0 12 * * ?")//每天中午12点触发一次
    @Scheduled(cron = "*/60 * * * * ?") //
    public void updateOrderTime() throws Exception {

        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        orderSchedulerService.selectOrderTime();
    }

    //@Scheduled(cron = "*/60 * * * * ?")
    @Scheduled(cron = "0 0 9 * * ?")
    public void updateRstsOrderTime() throws Exception {

        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        orderSchedulerService.updateRstsOrderTime();
    }

  /*  @Scheduled(cron = "0 0/5 * * * ?")*/
    @Scheduled(cron = "0 */30 10-18 * * ? ")
    public void updateSendEmail() throws Exception {
        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        orderSchedulerService.updateSendEmail();
    }

   @Scheduled(cron = "0 0 8,11,14,17,20 * * ?")
   //@Scheduled(cron = "0 */1 * * * ?")
    public void updateSendTic() {
		boolean isGray = applicationArguments.containsOption("gray");
		if (isGray)
			return;
		orderSchedulerService.updateTicSendEmail(5);
		orderSchedulerService.updateTicSendEmail(2);
		
	}

    /**
     * @Description:  作为系统，我可以定时取消INSP待付款且预约日期早于当前日期的订单，以便关闭无效订单
     * @Author: bowen zhang
     * @Date: 2022/12/12
     * @return: void
     **/
     @Scheduled(cron = "0 0 0 * * ?")//每天凌晨0点点触发一次
//    @Scheduled(cron = "0 0/3 * * * ?")//每3分钟执行一次
    public void closeOrderByINS() throws Exception {
        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        orderSchedulerService.closeOrderByINS();
    }


    @Scheduled(cron = "0 0 8 * * ?")//每天凌晨0点点触发一次
   // @Scheduled(cron = "*/10 * * * * ?")//每3分钟执行一次
    public void reportTimeOverdue() throws Exception {
        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        orderSchedulerService.reportTimeOverdue();
    }

    /**
     * @Description : 退款超过30天的订单，订单状态变成已取消
     * <AUTHOR> Zhang
     * @Date  2023/5/22

     * @return: void
     **/
    @Scheduled(cron = "0 0 8 * * ?")//每天凌晨0点点触发一次
//    @Scheduled(cron = "0 0/3 * * * ?")//每3分钟执行一次
    public void refundTimeOverdue() throws Exception {
        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        long time = System.currentTimeMillis();
        String value = String.valueOf(time + 600*1000);

        boolean flag = redisClient.setLock("REFUND_TIME_ORDERDUE", value, 120);
        if ( flag) {
            orderSchedulerService.refundTimeOverdue();
            redisClient.releaseLock("SEND_MAIL_SCHEDULE", value);
        }
    }


    @Scheduled(cron = "0 0 9 * * ?")//每天凌晨9点点触发一次
//    @Scheduled(cron = "0 */10 * * * ?")//每10分钟执行一次
    public void sendRoMailByUnConfirm() throws Exception {
        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        orderSchedulerService.sendRoMailByUnConfirm();

    }

    @Scheduled(cron = "0 0/5 * * * ?")//每5分钟执行一次
    public void sendMonthAddBalance() throws Exception {

        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray){
            return;
        }
        orderSchedulerService.sendMonthAddBalance();
    }
}
