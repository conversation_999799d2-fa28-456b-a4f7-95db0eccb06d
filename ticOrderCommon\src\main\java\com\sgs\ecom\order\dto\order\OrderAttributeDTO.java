package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.ScienceFormatSerializer;
import com.sgs.base.BaseQryFilter;

import java.math.BigDecimal;

public class OrderAttributeDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select ATTR_VALUE,CREATE_DATE,ATTR_NAME,GROUP_NO,ORDER_NO,ATTR_AMOUNT,ID,ATTR_ID,IS_DEFAULT from ORDER_ATTRIBUTE"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_VALUE", getName="getAttrValue", setName="setAttrValue")
 	private String attrValue;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_NAME", getName="getAttrName", setName="setAttrName")
 	private String attrName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_AMOUNT", getName="getAttrAmount", setName="setAttrAmount")
 	private double attrAmount;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ATTR_ID", getName="getAttrId", setName="setAttrId")
 	private long attrId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="IS_DEFAULT", getName="getIsDefault", setName="setIsDefault")
 	private int isDefault;
	@ApiAnno(groups={Default.class})
	private int id;
	@ApiAnno(groups={Default.class})
	private String attrCode;
	@ApiAnno(groups={Default.class})
	private String attrExtend;
	@ApiAnno(groups={Default.class})
	private String orderNo;
	@ApiAnno(groups={Default.class})
	private BigDecimal baseAttrAmount;

 	public void setAttrValue(String attrValue){
 		 this.attrValue=attrValue;
 	}
 	public String getAttrValue(){
 		 return this.attrValue;
 	}
 
 	 

 	 
 	public void setAttrName(String attrName){
 		 this.attrName=attrName;
 	}
 	public String getAttrName(){
 		 return this.attrName;
 	}
 
 	 

 
 	 
 	public void setAttrAmount(double attrAmount){
 		 this.attrAmount=attrAmount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class)
 	public double getAttrAmount(){
 		 return this.attrAmount;
 	}
 
 	 

 
 	 
 	public void setAttrId(long attrId){
 		 this.attrId=attrId;
 	}
 	public long getAttrId(){
 		 return this.attrId;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getAttrCode() {
		return attrCode;
	}

	public void setAttrCode(String attrCode) {
		this.attrCode = attrCode;
	}

	public String getAttrExtend() {
		return attrExtend;
	}

	public void setAttrExtend(String attrExtend) {
		this.attrExtend = attrExtend;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public BigDecimal getBaseAttrAmount() {
		return baseAttrAmount;
	}

	public void setBaseAttrAmount(BigDecimal baseAttrAmount) {
		this.baseAttrAmount = baseAttrAmount;
	}
}