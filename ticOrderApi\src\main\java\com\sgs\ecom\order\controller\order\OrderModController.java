package com.sgs.ecom.order.controller.order;

import com.alibaba.fastjson.JSONObject;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.bo.BOSysPerson;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.event.EventMailUtil;
import com.sgs.ecom.order.event.EventSmsUtil;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.order.ConfirmOrderReq;
import com.sgs.ecom.order.request.order.InquiryReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.rpc.IMemberRpcService;
import com.sgs.ecom.order.service.util.interfaces.IOrderModService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/edit")
public class OrderModController extends ControllerUtil {

	@Autowired
	private IOrderModService orderModService;

	@Autowired
	private MailEventUtil mailEventUtil;
	@Autowired
	private SmsEventUtil smsEventUtil;


	@Autowired
	private ApiEventUtil eventApiUtil;
	@Autowired
	private IOrderBaseInfoCustomService orderBaseInfoCustomService;
	@Autowired
	private IOrderBaseInfoService orderBaseInfoService;
	@Resource
	private IMemberRpcService iMemberRpcService;


	/**
	 *@Function: createSubOrder
	 *@Description 创建补差价订单
	 *@param: [token, orderPriceReq]
	 *@author: Xiwei_Qiu @date: 2022/2/7 @version:
	 **/
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "createSubOrder", method = {RequestMethod.POST})
	public ResultBody createSubOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderPriceReq orderPriceReq) throws Exception {
		orderModService.createSubOrder(orderPriceReq, getPersonInfo(token));
		//创建补充订单异步通知 询报价 邮件 短信 及消息推送
		if(orderPriceReq.getEventFlg()==1){
			mailEventUtil.sendMail(orderPriceReq.getEventOrderNo(),orderPriceReq.getEventOrderType(),OiqMailEnum.OIQ_CREATE_SUB,1L);
			eventApiUtil.sendWechatMsg(orderPriceReq.getEventOrderNo(), EventEnum.SEND_WECHAT_MSG, WechatEnum.OIQ_CREATE_SUB);
		    smsEventUtil.sendSms(orderPriceReq.getEventOrderNo(),orderPriceReq.getEventOrderType(),OiqSmsEnum.OIQ_CREATE_SUB);
		}

		return ResultBody.success();
	}


	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "createInquiryOrder", method = {RequestMethod.POST})
	public ResultBody createInquiryOrder(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody InquiryReq inquiryReq) throws Exception {
		JSONObject jsonObject=orderModService.createInquiryOrder(inquiryReq, getPersonInfo(token));
		if(inquiryReq.getSaveOrderFlg()==1){
			eventApiUtil.saveEvent(inquiryReq.getEventOrderNo(), EventEnum.CREATE_ORDER_TO_LEADS);
			eventApiUtil.saveEvent(inquiryReq.getEventOrderNo(), EventEnum.CREATE_ORDER_ADD_LABEL);
		}
		if(inquiryReq.getEventFlg()==1){
			//eventApiUtil.sendWechatMsg(inquiryReq.getEventOrderNo(), EventEnum.SEND_WECHAT_MSG, WechatEnum.DISTRIBUTION);
		}
		return ResultBody.newInstance(jsonObject);
	}



	/**
	*@Function: confirmOrder
	*@Description 客服帮用户确认报价
	*@param: [token, confirmOrderReq]
	*@author: Xiwei_Qiu @date: 2022/10/11 @version:
	**/
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "confirmOrder", method = {RequestMethod.POST})
	public ResultBody confirmOrder(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody ConfirmOrderReq confirmOrderReq) throws Exception {
		orderModService.updateConfirmOrder(confirmOrderReq,  getPersonInfo(token));
		String orderNo=orderModService.copyOrder(confirmOrderReq.getOrderNo(), getPersonInfo(token).getPersonCode());
		BaseOrderDTO baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(orderNo);
		mailEventUtil.sendMail(baseOrderDTO.getOrderNo(),baseOrderDTO.getOrderType(), OiqMailEnum.CRM_CONFIRM_ORDER, 1L);
		smsEventUtil.sendSms(baseOrderDTO.getOrderNo(),baseOrderDTO.getOrderType(),OiqSmsEnum.CRM_CONFIRM_ORDER);
		eventApiUtil.saveEvent(orderNo, EventEnum.MOD_USER_LABEL);
		eventApiUtil.sendWechatMsg(orderNo,EventEnum.SEND_WECHAT_MSG,WechatEnum.CRM_CONFIRM_ORDER);

		// 刷新缓存_OIQ最近确认订单列表
		iMemberRpcService.refreshCacheHeaderList(confirmOrderReq.getOrderNo(), baseOrderDTO.getBu(), "0");
		return ResultBody.newInstance(orderNo);
	}




	/**
	 * @Function: batchInvoice
	 * @Description 批量出票
	 * @param: [orderOperatorReq, token]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4517", "4518"})
	@RequestMapping(value = "batchInvoice", method = {RequestMethod.POST})
	public ResultBody batchInvoice(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderIdReq orderIdReq) throws Exception {
		orderModService.batchInvoice(orderIdReq, getPersonInfo(token));
		return ResultBody.success();
	}


	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "copyInquiryOrder", method = {RequestMethod.POST})
	public ResultBody copyInquiryOrder(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody OrderNoReq orderNoReq) throws Exception {
		BaseOrderDTO base = orderBaseInfoCustomService.selectBaseByOrderNo(orderNoReq.getOrderNo());
		BOSysPerson boSysPerson=getPersonInfo(token);
		orderBaseInfoService.checkOrderBase(String.valueOf(base.getOrderId()),boSysPerson,getPower(token));
		String str=orderModService.copyInquiryOrder(orderNoReq, boSysPerson);
		JSONObject jsonObject=new JSONObject();
		jsonObject.put(SelectMapUtil.ORDER_NO,str);
		try {
			BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(str);
			jsonObject.put(SelectMapUtil.ORDER_ID,baseOrderDTO.getOrderId());
		}catch (Exception e){

		}
		return ResultBody.newInstance(jsonObject);
	}


}
