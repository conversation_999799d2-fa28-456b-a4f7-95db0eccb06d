package com.sgs.ecom.order.dto.send;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description :支付信息
 * @date 2024/11/4
 */
@Data
public class PaymentDTO {
    //支付方式
    private String paymentType;
    private String paymentTypeName;
    //商户名称
    private String accountName;
    //应付金额
    private BigDecimal originalPrice;
    //付款时间
    private String paymentDate;
    //支付凭证上传时间
    private String createDate;
    //支付凭证
    private String fileName;
}
