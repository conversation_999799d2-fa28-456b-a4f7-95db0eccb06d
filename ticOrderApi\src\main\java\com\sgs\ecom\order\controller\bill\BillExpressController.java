package com.sgs.ecom.order.controller.bill;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.express.ExpressIdReq;
import com.sgs.ecom.order.request.express.OrderExpressReq;
import com.sgs.ecom.order.service.bill.interfaces.IBillExpressService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v1.bill/express")
public class BillExpressController extends ControllerUtil {

	@Autowired
	private IBillExpressService billExpressService;
	/**
	*@Function: qryList
	*@Description 账单快递列表 billId
	*@param: [token, expressIdReq]
	*@author: <PERSON><PERSON>_<PERSON><PERSON> @date: 2021/12/23 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "list", method = { RequestMethod.POST })
	public ResultBody qryList(
		@RequestHeader(value="accessToken") String token,
		@RequestBody ExpressIdReq expressIdReq) throws Exception {
		return ResultBody.newInstance(billExpressService.qryList(expressIdReq) );
	}
	/**
	*@Function: add
	*@Description 添加账单快递
	*@param: [token, expressIdReq]
	*@author: Xiwei_Qiu @date: 2021/12/24 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "add", method = { RequestMethod.POST })
	public ResultBody add(
		@RequestHeader(value="accessToken") String token,
		@RequestBody OrderExpressReq orderExpressReq) throws Exception {
		billExpressService.add(orderExpressReq);
		return ResultBody.success();
	}

	/**
	 *@Function: del
	 *@Description 删除账单快递
	 *@param: [token, expressIdReq]
	 *@author: Xiwei_Qiu @date: 2021/12/24 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "del", method = { RequestMethod.POST })
	public ResultBody del(
		@RequestHeader(value="accessToken") String token,
		@RequestBody ExpressIdReq expressIdReq) throws Exception {
		billExpressService.del(expressIdReq);
		return ResultBody.success();
	}
}
