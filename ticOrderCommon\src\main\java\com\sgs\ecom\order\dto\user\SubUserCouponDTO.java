package com.sgs.ecom.order.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;

public class SubUserCouponDTO {

	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private String couponName;

	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private String couponMemo;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private String discountAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private BigDecimal subDiscountAmount;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private int useAmount;
	private String couponRange;

	public String getCouponName() {
		return couponName;
	}

	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}

	public String getCouponMemo() {
		return couponMemo;
	}

	public void setCouponMemo(String couponMemo) {
		this.couponMemo = couponMemo;
	}

	public String getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(String discountAmount) {
		this.discountAmount = discountAmount;
	}

	public BigDecimal getSubDiscountAmount() {
		return subDiscountAmount;
	}

	public void setSubDiscountAmount(BigDecimal subDiscountAmount) {
		this.subDiscountAmount = subDiscountAmount;
	}

	public int getUseAmount() {
		return useAmount;
	}

	public void setUseAmount(int useAmount) {
		this.useAmount = useAmount;
	}

	public String getCouponRange() {
		return couponRange;
	}

	public void setCouponRange(String couponRange) {
		this.couponRange = couponRange;
	}
}
