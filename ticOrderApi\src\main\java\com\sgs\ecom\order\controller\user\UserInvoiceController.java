package com.sgs.ecom.order.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.user.UserInvoiceListReq;
import com.sgs.ecom.order.service.user.interfaces.IUserInvoiceSV;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 用户发票Controller
 * @date 2024/12/11 13:21
 */
@RestController
@RequestMapping("/business/api.v1.user/invoice")
public class UserInvoiceController {
    @Resource
    private IUserInvoiceSV userInvoiceSV;

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "list", method = { RequestMethod.POST })
    public ResultBody list(@RequestBody UserInvoiceListReq userInvoiceListReq) throws Exception {
        return ResultBody.newInstance(userInvoiceSV.getPageList(userInvoiceListReq));
    }
}
