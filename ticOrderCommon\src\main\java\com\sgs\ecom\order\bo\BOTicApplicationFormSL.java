package com.sgs.ecom.order.bo; 
 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.BeanAnno; 

public class BOTicApplicationFormSL{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "TIC_ApplicationFormSL"; 
 
 	public static final String OWNER ="bbc";

 	public static final String APPFORMID="appformid";
 	public static final String SAMPLENAME="samplename";
 	public static final String SAMPLECOLOR="samplecolor";
 	public static final String REPORTADDRESS="reportaddress";
 	public static final String REPORTTEMPLATE="reporttemplate";
 	public static final String TESTSAMPLEGRADE="testsamplegrade";
 	public static final String COMPANYNAMEEN="companynameen";
 	public static final String TITLENUM="titlenum";
 	public static final String REPORTPROVICE="reportprovice";
 	public static final String REPORTDISTRICT="reportdistrict";
 	public static final String COMPANYNAMEADD="companynameadd";
 	public static final String ORDERID="orderid";
 	public static final String SPECIALREMARKS="specialremarks";
 	public static final String BRANDNAME="brandname";
 	public static final String REPORTTITLE="reporttitle";
 	public static final String TESTSAMPLETYPE="testsampletype";
 	public static final String SECURITYLEVEL="securitylevel";
 	public static final String REPORTCITY="reportcity";
 	public static final String SAMPLEMATERIAL="samplematerial";
 	public static final String ID="id";
 	public static final String SAMPLENUMBER="samplenumber";
 	public static final String PRODUCTERNAME="productername";
 	public static final String REPORTSHOW="reportshow";
 	public static final String OTHERREQUIREMENTS="otherrequirements";

 	@BeanAnno("AppFormID")
 	private String appformid;
 	@BeanAnno("SampleName")
 	private String samplename;
 	@BeanAnno("SampleColor")
 	private String samplecolor;
 	@BeanAnno("ReportAddress")
 	private String reportaddress;
 	@BeanAnno("ReportTemplate")
 	private String reporttemplate;
 	@BeanAnno("TestSampleGrade")
 	private String testsamplegrade;
 	@BeanAnno("CompanyNameEN")
 	private String companynameen;
 	@BeanAnno("TitleNum")
 	private String titlenum;
 	@BeanAnno("ReportProvice")
 	private String reportprovice;
 	@BeanAnno("ReportDistrict")
 	private String reportdistrict;
 	@BeanAnno("CompanyNameAdd")
 	private String companynameadd;
 	@BeanAnno("OrderID")
 	private String orderid;
 	@BeanAnno("SpecialRemarks")
 	private String specialremarks;
 	@BeanAnno("BrandName")
 	private String brandname;
 	@BeanAnno("ReportTitle")
 	private String reporttitle;
 	@BeanAnno("TestSampleType")
 	private String testsampletype;
 	@BeanAnno("SecurityLevel")
 	private String securitylevel;
 	@BeanAnno("ReportCity")
 	private String reportcity;
 	@BeanAnno("SampleMaterial")
 	private String samplematerial;
 	@BeanAnno("ID")
 	private String id;
 	@BeanAnno("SampleNumber")
 	private String samplenumber;
 	@BeanAnno("ProducterName")
 	private String productername;
 	@BeanAnno("ReportShow")
 	private String reportshow;
 	@BeanAnno("OtherRequirements")
 	private String otherrequirements;

 	@CharacterVaild(len = 50) 
 	public void setAppformid(String appformid){
 		 this.appformid=appformid;
 	}
 	public String getAppformid(){
 		 return this.appformid;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSamplename(String samplename){
 		 this.samplename=samplename;
 	}
 	public String getSamplename(){
 		 return this.samplename;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSamplecolor(String samplecolor){
 		 this.samplecolor=samplecolor;
 	}
 	public String getSamplecolor(){
 		 return this.samplecolor;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setReportaddress(String reportaddress){
 		 this.reportaddress=reportaddress;
 	}
 	public String getReportaddress(){
 		 return this.reportaddress;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setReporttemplate(String reporttemplate){
 		 this.reporttemplate=reporttemplate;
 	}
 	public String getReporttemplate(){
 		 return this.reporttemplate;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setTestsamplegrade(String testsamplegrade){
 		 this.testsamplegrade=testsamplegrade;
 	}
 	public String getTestsamplegrade(){
 		 return this.testsamplegrade;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanynameen(String companynameen){
 		 this.companynameen=companynameen;
 	}
 	public String getCompanynameen(){
 		 return this.companynameen;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setTitlenum(String titlenum){
 		 this.titlenum=titlenum;
 	}
 	public String getTitlenum(){
 		 return this.titlenum;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReportprovice(String reportprovice){
 		 this.reportprovice=reportprovice;
 	}
 	public String getReportprovice(){
 		 return this.reportprovice;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReportdistrict(String reportdistrict){
 		 this.reportdistrict=reportdistrict;
 	}
 	public String getReportdistrict(){
 		 return this.reportdistrict;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanynameadd(String companynameadd){
 		 this.companynameadd=companynameadd;
 	}
 	public String getCompanynameadd(){
 		 return this.companynameadd;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderid(String orderid){
 		 this.orderid=orderid;
 	}
 	public String getOrderid(){
 		 return this.orderid;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSpecialremarks(String specialremarks){
 		 this.specialremarks=specialremarks;
 	}
 	public String getSpecialremarks(){
 		 return this.specialremarks;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setBrandname(String brandname){
 		 this.brandname=brandname;
 	}
 	public String getBrandname(){
 		 return this.brandname;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setReporttitle(String reporttitle){
 		 this.reporttitle=reporttitle;
 	}
 	public String getReporttitle(){
 		 return this.reporttitle;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setTestsampletype(String testsampletype){
 		 this.testsampletype=testsampletype;
 	}
 	public String getTestsampletype(){
 		 return this.testsampletype;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setSecuritylevel(String securitylevel){
 		 this.securitylevel=securitylevel;
 	}
 	public String getSecuritylevel(){
 		 return this.securitylevel;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReportcity(String reportcity){
 		 this.reportcity=reportcity;
 	}
 	public String getReportcity(){
 		 return this.reportcity;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSamplematerial(String samplematerial){
 		 this.samplematerial=samplematerial;
 	}
 	public String getSamplematerial(){
 		 return this.samplematerial;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	public String getId(){
 		 return this.id;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSamplenumber(String samplenumber){
 		 this.samplenumber=samplenumber;
 	}
 	public String getSamplenumber(){
 		 return this.samplenumber;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setProductername(String productername){
 		 this.productername=productername;
 	}
 	public String getProductername(){
 		 return this.productername;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setReportshow(String reportshow){
 		 this.reportshow=reportshow;
 	}
 	public String getReportshow(){
 		 return this.reportshow;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setOtherrequirements(String otherrequirements){
 		 this.otherrequirements=otherrequirements;
 	}
 	public String getOtherrequirements(){
 		 return this.otherrequirements;
 	}
 
 	 
}