package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

@ExplainAnno(value="enum",name="报告语言")
public enum TransReportLuaEnum implements EnumMessage {


    ZN("中文报告", 2),
    EN("英文报告", 1),
    ZNANDEN("中文+英文报告", 4),
    ZNANDEN_("英文+中文报告", 4),
    ENANDZN("英文报告+中文报告", 4),
    ENANDZN_("中文报告+英文报告", 4),
    ZNEN("中英文报告", 3);

    // 成员变量
    private String name;
    private int index;
    // 构造方法
    private TransReportLuaEnum(String name, int index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(int index) {
        for (TransReportLuaEnum c : TransReportLuaEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;  
            }  
        }  
        return null;  
    }

    public static int getIndex(String name) {
        for (TransReportLuaEnum c : TransReportLuaEnum.values()) {
            if (c.getName().equals(name)) {
                return c.index;
            }
        }
        return 5;
    }
    // get set 方法  
    public String getName() {  
        return name;  
    }

    public int getIndex() {
        return index;
    }



    @Override
	public String getKey() {
		return String.valueOf(index);
	}

	@Override
	public String getValue() {
		return name;
	}  
}
