package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

@ExplainAnno(value="enum",name="支付方式")
public enum PayMethod implements EnumMessage {

	ZFB("支付宝", 100000), WECHAT("微信", 100001), OFFLINE("线下支付", 300000), 
	BALANCE("余额", 200000), MONTH("月结", 300001), ADVANCE_PAYMENT("预到账", 330000),
    FALSE_MONTH("假月结", 300002);
	
    // 成员变量  
    private String name;  
    private int index;  
    // 构造方法  
    private PayMethod(String name, int index) {  
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(int index) {  
        for (PayMethod c : PayMethod.values()) {  
            if (c.getIndex() == index) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public int getIndex() {  
        return index;  
    }  

	@Override
	public String getKey() {
		return String.valueOf(index);
	}

	@Override
	public String getValue() {
		return name;
	}  
}
