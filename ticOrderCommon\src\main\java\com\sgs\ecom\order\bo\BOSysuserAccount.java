package com.sgs.ecom.order.bo; 
 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.BeanAnno; 

public class BOSysuserAccount{
 
 	public static final String SEQUENCE = "USER_ID"; 
  
 	public static final String BO_SQL = "sysuser_account"; 
 
 	public static final String OWNER ="bbc";

 	public static final String CREATETIME="createtime";
 	public static final String MODIFIED_TIME="modifiedTime";
 	public static final String LOGIN_PASSWORD="loginPassword";
 	public static final String USER_ID="userId";
 	public static final String LOGIN_TYPE="loginType";
 	public static final String MOBILE="mobile";
 	public static final String DISABLED="disabled";
 	public static final String LOGIN_ACCOUNT="loginAccount";
 	public static final String EMAIL="email";
 	public static final String USERNAME="username";

 	@BeanAnno("createtime")
 	private long createtime;
 	@BeanAnno("modified_time")
 	private long modifiedTime;
 	@BeanAnno("login_password")
 	private String loginPassword;
 	@BeanAnno("user_id")
 	private long userId;
 	@BeanAnno("login_type")
 	private String loginType;
 	@BeanAnno("mobile")
 	private String mobile;
 	@BeanAnno("disabled")
 	private int disabled;
 	@BeanAnno("login_account")
 	private String loginAccount;
 	@BeanAnno("email")
 	private String email;
 	@BeanAnno("username")
 	private String username;

 	public void setCreatetime(long createtime){
 		 this.createtime=createtime;
 	}
 	public long getCreatetime(){
 		 return this.createtime;
 	}
 
 	 
 	public void setModifiedTime(long modifiedTime){
 		 this.modifiedTime=modifiedTime;
 	}
 	public long getModifiedTime(){
 		 return this.modifiedTime;
 	}
 
 	 
 	@CharacterVaild(len = 60) 
 	public void setLoginPassword(String loginPassword){
 		 this.loginPassword=loginPassword;
 	}
 	public String getLoginPassword(){
 		 return this.loginPassword;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	@CharacterVaild(len = 60) 
 	public void setLoginType(String loginType){
 		 this.loginType=loginType;
 	}
 	public String getLoginType(){
 		 return this.loginType;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setMobile(String mobile){
 		 this.mobile=mobile;
 	}
 	public String getMobile(){
 		 return this.mobile;
 	}
 
 	 
 	public void setDisabled(int disabled){
 		 this.disabled=disabled;
 	}
 	public int getDisabled(){
 		 return this.disabled;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setLoginAccount(String loginAccount){
 		 this.loginAccount=loginAccount;
 	}
 	public String getLoginAccount(){
 		 return this.loginAccount;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setEmail(String email){
 		 this.email=email;
 	}
 	public String getEmail(){
 		 return this.email;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setUsername(String username){
 		 this.username=username;
 	}
 	public String getUsername(){
 		 return this.username;
 	}
 
 	 
}