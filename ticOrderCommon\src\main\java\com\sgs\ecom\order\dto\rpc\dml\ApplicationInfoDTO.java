package com.sgs.ecom.order.dto.rpc.dml;

import java.util.List;

public class ApplicationInfoDTO {

    private String companyName;

    private String companyNameEn;

    private String  companyAddr;

    private String companyAddrEn;

    private String linkPhone;

    private String linkPerson;

    private String linkEmail;


    private int isTransfer=1;//样品是否分包 0否 1是


    private Integer isRefundSample=0;//是否需要退回样品1是0否


    private Integer reportMethod=1;//一份样品还是多份样品对应报告

    private String reportMethodValue;//其他的备注

    private Integer reportTitleType;

    private String reportTitle;

    private String reportTitleEn;

    private String reportAddress;

    private String reportAddressEn;

    private String reportSendCc;

    private Integer isDispute;

    private String isDisputeMemo;

    private List<DmlFileDTO> attachments;

    //内部使用
    private String testMemo;


    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyNameEn() {
        return companyNameEn;
    }

    public void setCompanyNameEn(String companyNameEn) {
        this.companyNameEn = companyNameEn;
    }

    public String getCompanyAddr() {
        return companyAddr;
    }

    public void setCompanyAddr(String companyAddr) {
        this.companyAddr = companyAddr;
    }

    public String getCompanyAddrEn() {
        return companyAddrEn;
    }

    public void setCompanyAddrEn(String companyAddrEn) {
        this.companyAddrEn = companyAddrEn;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getLinkEmail() {
        return linkEmail;
    }

    public void setLinkEmail(String linkEmail) {
        this.linkEmail = linkEmail;
    }

    public int getIsTransfer() {
        return isTransfer;
    }

    public void setIsTransfer(int isTransfer) {
        this.isTransfer = isTransfer;
    }

    public Integer getIsRefundSample() {
        return isRefundSample;
    }

    public void setIsRefundSample(Integer isRefundSample) {
        this.isRefundSample = isRefundSample;
    }

    public Integer getReportMethod() {
        return reportMethod;
    }

    public void setReportMethod(Integer reportMethod) {
        this.reportMethod = reportMethod;
    }

    public String getReportMethodValue() {
        return reportMethodValue;
    }

    public void setReportMethodValue(String reportMethodValue) {
        this.reportMethodValue = reportMethodValue;
    }

    public Integer getReportTitleType() {
        return reportTitleType;
    }

    public void setReportTitleType(Integer reportTitleType) {
        this.reportTitleType = reportTitleType;
    }

    public String getReportTitle() {
        return reportTitle;
    }

    public void setReportTitle(String reportTitle) {
        this.reportTitle = reportTitle;
    }

    public String getReportTitleEn() {
        return reportTitleEn;
    }

    public void setReportTitleEn(String reportTitleEn) {
        this.reportTitleEn = reportTitleEn;
    }

    public String getReportAddress() {
        return reportAddress;
    }

    public void setReportAddress(String reportAddress) {
        this.reportAddress = reportAddress;
    }

    public String getReportAddressEn() {
        return reportAddressEn;
    }

    public void setReportAddressEn(String reportAddressEn) {
        this.reportAddressEn = reportAddressEn;
    }

    public String getReportSendCc() {
        return reportSendCc;
    }

    public void setReportSendCc(String reportSendCc) {
        this.reportSendCc = reportSendCc;
    }

    public Integer getIsDispute() {
        return isDispute;
    }

    public void setIsDispute(Integer isDispute) {
        this.isDispute = isDispute;
    }

    public String getIsDisputeMemo() {
        return isDisputeMemo;
    }

    public void setIsDisputeMemo(String isDisputeMemo) {
        this.isDisputeMemo = isDisputeMemo;
    }



    public String getTestMemo() {
        return testMemo;
    }

    public void setTestMemo(String testMemo) {
        this.testMemo = testMemo;
    }

    public List<DmlFileDTO> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<DmlFileDTO> attachments) {
        this.attachments = attachments;
    }
}
