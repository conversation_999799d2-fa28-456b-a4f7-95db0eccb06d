package com.sgs.ecom.order.entity.order;

import java.math.BigDecimal;

public class OrderAttribute {


    private String attrValue;

    private String createDate;

    private String attrName;

    private String groupNo;

    private String orderNo;

    private BigDecimal attrAmount;

    private Long id;

    private Long attrId;

    private Integer isDefault;

    private String attrCode;

    private String attrExtend;
    private Long userId;
    private BigDecimal baseAttrAmount;



    public void setAttrValue(String attrValue){
        this.attrValue=attrValue;
    }
    public String getAttrValue(){
        return this.attrValue;
    }


    public void setCreateDate(String createDate){
        this.createDate=createDate;
    }
    public String getCreateDate(){
        return this.createDate;
    }


    public void setAttrName(String attrName){
        this.attrName=attrName;
    }
    public String getAttrName(){
        return this.attrName;
    }


    public void setGroupNo(String groupNo){
        this.groupNo=groupNo;
    }
    public String getGroupNo(){
        return this.groupNo;
    }


    public void setOrderNo(String orderNo){
        this.orderNo=orderNo;
    }
    public String getOrderNo(){
        return this.orderNo;
    }

    public BigDecimal getAttrAmount() {
        return attrAmount;
    }

    public void setAttrAmount(BigDecimal attrAmount) {
        this.attrAmount = attrAmount;
    }

    public void setId(Long id){
        this.id=id;
    }
    public Long getId(){
        return this.id;
    }


    public void setAttrId(Long attrId){
        this.attrId=attrId;
    }
    public Long getAttrId(){
        return this.attrId;
    }


    public void setIsDefault(Integer isDefault){
        this.isDefault=isDefault;
    }
    public Integer getIsDefault(){
        return this.isDefault;
    }

    public String getAttrCode() {
        return attrCode;
    }

    public void setAttrCode(String attrCode) {
        this.attrCode = attrCode;
    }

    public String getAttrExtend() {
        return attrExtend;
    }

    public void setAttrExtend(String attrExtend) {
        this.attrExtend = attrExtend;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public BigDecimal getBaseAttrAmount() {
        return baseAttrAmount;
    }

    public void setBaseAttrAmount(BigDecimal baseAttrAmount) {
        this.baseAttrAmount = baseAttrAmount;
    }
}
