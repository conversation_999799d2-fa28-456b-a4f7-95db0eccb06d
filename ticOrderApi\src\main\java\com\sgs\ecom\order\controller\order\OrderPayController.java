package com.sgs.ecom.order.controller.order;

import com.sgs.ecom.order.advice.CheckOrderNo;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.request.pay.OrderPayConfirmReq;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.bo.BOSysPerson;
import com.platform.util.SysException;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.operator.OrderOperatorReq;
import com.sgs.ecom.order.request.operator.OrderServiceRefundReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.order.interfaces.IOrderPayService;
import com.sgs.ecom.order.service.util.interfaces.IPayUtilService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/pay")
public class OrderPayController extends ControllerUtil {

    @Autowired
    private IOrderBaseInfoService orderBaseInfoService;
    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IPayUtilService payUtilService;
    @Autowired
    private ApiEventUtil eventApiUtil;


    /**
    *@Function: info
    *@Description oiq旧的支付列表
    *@param: [token, orderIdReq]
    *@author: Xiwei_Qiu @date: 2022/5/26 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "info", method = { RequestMethod.POST })
    public ResultBody info (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderIdReq orderIdReq)throws Exception{
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderIdReq.getOrderId(),getPersonInfo(token),getPower(token));
        return ResultBody.newInstance(orderPayService.selectOrderPayDTO(orderBaseInfoCheckDTO.getOrderNo()));
    }



    /**
     *@Function: info
     *@Description 主订单的支付记录
     *@param: [token, orderIdReq]
     *@author: Xiwei_Qiu @date: 2022/5/26 @version:
     **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "payInfo", method = { RequestMethod.POST })
    public ResultBody payInfo (
        @RequestHeader(value="accessToken") String token,
        @RequestBody OrderReq orderReq)throws Exception{
        return ResultBody.newInstance(payUtilService.payInfo(orderReq,getPersonInfo(token)));
    }
    
    /**
    *@Function: paySubInfo
    *@Description 补差价的支付记录
    *@param: [token, orderReq]
    *@author: Xiwei_Qiu @date: 2022/7/6 @version:
    **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "paySubInfo", method = { RequestMethod.POST })
    public ResultBody paySubInfo (
        @RequestHeader(value="accessToken") String token,
        @RequestBody OrderReq orderReq)throws Exception{
        return ResultBody.newInstance(payUtilService.paySubInfo(orderReq,getPersonInfo(token)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "refund", method = { RequestMethod.POST })
    public ResultBody refund (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderOperatorReq orderOperatorReq)throws Exception{
        BOSysPerson boSysPerson=getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO=gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderOperatorReq.getOrderId(),boSysPerson,privilegeLevelDTO);
        orderPayService.refundConfirm(orderBaseInfoCheckDTO,boSysPerson,orderOperatorReq);
        if(orderOperatorReq.getModLabelFlg()==1){
            eventApiUtil.saveEvent(orderOperatorReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
        }
        return ResultBody.success();
    }

    /**
    *@Function: confirm
    *@Description
    *@param: [token, orderOperatorReq]
    *@author: Xiwei_Qiu @date: 2021/5/14 @version:
    **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4303","4304","4508","4509"})
    @RequestMapping(value = "confirm", method = { RequestMethod.POST })
    public ResultBody confirm (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderPayConfirmReq orderPayConfirmReq)throws Exception{
        BOSysPerson boSysPerson=getPersonInfo(token);
        orderPayService.confirm(orderPayConfirmReq,boSysPerson);
        if(orderPayConfirmReq.getPayToLeads()==1){
            eventApiUtil.saveEvent(orderPayConfirmReq.getEventOrderNo(),EventEnum.PAY_TO_LEADS);
            eventApiUtil.saveEvent(orderPayConfirmReq.getEventOrderNo(), EventEnum.PAY_TO_QUOTATION_CONFIRM,boSysPerson);
        }
        return ResultBody.success();
    }


    @CheckOrderNo
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "cancelOffline", method = { RequestMethod.POST })
    public ResultBody cancelOffline (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq)throws Exception{
        orderPayService.cancelOffline(orderNoReq,getPersonInfo(token));
        return ResultBody.success();
    }

    /**
    *@Function: confirmOff
    *@Description 线下支付审核通过或拒绝
    *@param: [token, orderOperatorReq]
    *@author: Xiwei_Qiu @date: 2022/5/20 @version:
    **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4303","4304","4508","4509"})
    @RequestMapping(value = "confirmOff", method = { RequestMethod.POST })
    public ResultBody confirmOff (
        @RequestHeader(value="accessToken") String token,
        @RequestBody OrderOperatorReq orderOperatorReq)throws Exception{
        payUtilService.confirmOff(orderOperatorReq,getPersonInfo(token));
        return ResultBody.success();
    }
    
    /**
     *@Function: serviceRefund
     *@Description 客服退款接口
     *@param: [token, orderServiceRefundReq]
     *@author: sundeqing @date: 2022/5/11 @version:
     **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "serviceRefund", method = { RequestMethod.POST })
    public ResultBody serviceRefund (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderServiceRefundReq orderServiceRefundReq)throws Exception{
        orderPayService.serviceRefund(orderServiceRefundReq,getPersonInfo(token));
        return ResultBody.success();
    }
    
    /**
     *@Function: confirmRefund
     *@Description 客服确认退款接口
     *@param: [token, orderOperatorReq]
     *@author: sundeqing @date: 2022/5/19 @version:
     **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "confirmRefund", method = { RequestMethod.POST })
    public ResultBody confirmRefund (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderOperatorReq orderOperatorReq)throws Exception{
        orderPayService.confirmRefund(orderOperatorReq,getPersonInfo(token));
        if(orderOperatorReq.getModLabelFlg()==1){
            eventApiUtil.saveEvent(orderOperatorReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
        }
        return ResultBody.success();
    }
    
}
