package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.oiq.OiqOrderLinkDTO;
import com.sgs.ecom.order.dto.order.OrderLinkDTO;
import com.sgs.ecom.order.entity.OrderLink;
import com.sgs.ecom.order.request.oiq.OiqOrderLinkReq;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.collection.ListUtil;
import org.apache.commons.lang.StringUtils;


import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderLinkDO extends OrderLink {


    public static List<OrderLink> getOrderLinkList(List<OiqOrderLinkReq> oiqOrderLinkDTOList, String orderNo, int busiType) {
        String dateStr= UseDateUtil.getDateString(new Date());
        if(ValidationUtil.isEmpty(oiqOrderLinkDTOList)){
            return new ArrayList<>();
        }
        List<OrderLink> list=new ArrayList<>();
        for(OiqOrderLinkReq oiqOrderLinkDTO:oiqOrderLinkDTOList){
            OrderLink orderLink=new OrderLink(oiqOrderLinkDTO);
            orderLink.setOrderNo(orderNo);
            orderLink.setBusiType(busiType);
            orderLink.setCreateDate(dateStr);
            list.add(orderLink);
        }
        return list;
    }

    public static List<OiqOrderLinkDTO> orderLinkToOiqOrderLinkDTO(List<OrderLinkDTO> list,String linkEmail){
        if(ValidationUtil.isEmpty(list)){
            return new ArrayList<>();
        }
        List<OiqOrderLinkDTO> linkDTOList=new ArrayList<>();
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        for(int n=0;n<list.size();n++){
            OrderLinkDTO orderLinkDTO=list.get(n);
            if(StringUtils.isNotBlank(linkEmail)&& StringUtils.isNotBlank(orderLinkDTO.getLinkEmail()) &&
                orderLinkDTO.getLinkEmail().equals(linkEmail)){
                continue;
            }

            OiqOrderLinkDTO oiqOrderLinkDTO=new OiqOrderLinkDTO();
            baseCopyObj.copyWithNull(oiqOrderLinkDTO,orderLinkDTO);
            linkDTOList.add(oiqOrderLinkDTO);
        }
        return linkDTOList;
    }

    public static List<OiqOrderLinkDTO> orderLinkToOiqOrderLinkDTO(List<OrderLinkDTO> list){
        return orderLinkToOiqOrderLinkDTO(list,"");
    }

    public static  List<OrderLink> strToOrderLinkList(String str,String orderNo,String dateStr,int busiType){
        List<OrderLink> orderLinkList=new ArrayList<>();
        if(StringUtils.isBlank(str)){
            return new ArrayList<>();
        }
        List<String> reportList= ListUtil.stringToList(str,"[,;]");
        for(String email:reportList){
            OrderLink orderLink=new OrderLink();
            orderLink.setCreateDate(dateStr);
            orderLink.setOrderNo(orderNo);
            orderLink.setBusiType(busiType);
            orderLink.setLinkEmail(email);
            orderLinkList.add(orderLink);
        }
        return orderLinkList;

    }

    public static List<OrderLinkDTO> getOrderLinkListByEntity(List<OrderLink> orderLinkList) {
        if(ValidationUtil.isEmpty(orderLinkList)){
            return new ArrayList<>();
        }

        List<OrderLinkDTO> list=new ArrayList<>();
        for(OrderLink orderLink:orderLinkList){
            OrderLinkDTO orderLinkDTO=new OrderLinkDTO(orderLink);
            orderLinkDTO.setBusiType(orderLink.getBusiType());
            list.add(orderLinkDTO);
        }
        return list;
    }


}
