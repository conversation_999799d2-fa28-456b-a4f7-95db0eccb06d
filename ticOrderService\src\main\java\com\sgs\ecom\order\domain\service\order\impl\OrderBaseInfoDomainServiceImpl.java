package com.sgs.ecom.order.domain.service.order.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.bo.UserInfo;
import com.sgs.ecom.order.domain.order.OrderApplicationAttrDO;
import com.sgs.ecom.order.domain.order.OrderBaseInfoDO;
import com.sgs.ecom.order.domain.order.OrderReportDO;
import com.sgs.ecom.order.domain.order.OrderSampleDO;
import com.sgs.ecom.order.domain.order.OrderShippingContactDO;
import com.sgs.ecom.order.domain.order.service.interfaces.IOrderCustomerDomainService;
import com.sgs.ecom.order.domain.repository.order.interfaces.IOrderBaseInfoRepository;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationFormDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderAttachmentDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderDetailDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderExpressDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderInvoiceDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderLabelDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderLinkDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderMemoDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderOperatorLogDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderPayDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderProductDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderReportDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleRelateDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderShippingContactDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderShippingDomainService;
import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.center.BusinessLineDTO;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.custom.OrderCheckStateDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.dto.detail.OrderReportDTO;
import com.sgs.ecom.order.dto.log.BaseLog;
import com.sgs.ecom.order.dto.oiq.OiqOrderDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqAddressDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqOrderInvoiceDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqUserAddressDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OrderShippingContactDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OrderShippingDTO;
import com.sgs.ecom.order.dto.order.CustomerIdDTO;
import com.sgs.ecom.order.dto.order.OrderApplicationAttrDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoDTO;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.order.OrderIndexDTO;
import com.sgs.ecom.order.dto.order.OrderLabelDTO;
import com.sgs.ecom.order.dto.order.OrderLogAttachmentDTO;
import com.sgs.ecom.order.dto.order.OrderMemoDTO;
import com.sgs.ecom.order.dto.order.OrderOperatorLogDTO;
import com.sgs.ecom.order.dto.order.OrderProductDTO;
import com.sgs.ecom.order.dto.order.OrderSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.order.dto.order.OrderSampleRelateDTO;
import com.sgs.ecom.order.dto.order.OrderStateNumDTO;
import com.sgs.ecom.order.dto.order.ToOrderDTO;
import com.sgs.ecom.order.dto.pay.OrderPayDTO;
import com.sgs.ecom.order.dto.rpc.dml.BaseInfoRpcDTO;
import com.sgs.ecom.order.dto.rpc.dml.CreateInquiryRpcDTO;
import com.sgs.ecom.order.dto.rpc.dml.DmlApplicationDTO;
import com.sgs.ecom.order.dto.sample.SampleCategoryDTO;
import com.sgs.ecom.order.dto.send.TicOtherMailDTO;
import com.sgs.ecom.order.dto.sys.SysEnumConfigDTO;
import com.sgs.ecom.order.dto.user.UserCompanyDTO;
import com.sgs.ecom.order.dto.user.UserEmailDTO;
import com.sgs.ecom.order.dto.user.UserInfoDTO;
import com.sgs.ecom.order.dto.user.UserInvoiceDTO;
import com.sgs.ecom.order.entity.order.OrderBaseInfo;
import com.sgs.ecom.order.entity.order.OrderReport;
import com.sgs.ecom.order.enums.OperatorCode;
import com.sgs.ecom.order.enums.PayMethod;
import com.sgs.ecom.order.enums.TransReportFormEnum;
import com.sgs.ecom.order.enums.TransReportLuaEnum;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.order.BaseOrderStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderLabelCodeEnum;
import com.sgs.ecom.order.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.user.UserLabelCodeEnum;
import com.sgs.ecom.order.event.EventMailUtil;
import com.sgs.ecom.order.event.EventSmsUtil;
import com.sgs.ecom.order.infrastructure.handle.OrderUtilHandle;
import com.sgs.ecom.order.infrastructure.rpc.res.CenterInfoDTO;
import com.sgs.ecom.order.infrastructure.rpc.template.CenterRespository;
import com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.request.base.OiqOrderInfoReq;
import com.sgs.ecom.order.request.oiq.OiqOrderReq;
import com.sgs.ecom.order.request.operator.OrderCloseReq;
import com.sgs.ecom.order.request.operator.OrderServiceRefundReq;
import com.sgs.ecom.order.request.order.OrderApplicationAttrReq;
import com.sgs.ecom.order.request.order.OrderBaseInfoQryReq;
import com.sgs.ecom.order.request.rsts.OperatorReq;
import com.sgs.ecom.order.response.order.QryOrderRes;
import com.sgs.ecom.order.response.order.other.OrderApplicationForm;
import com.sgs.ecom.order.response.order.other.OrderDelivers;
import com.sgs.ecom.order.response.order.other.OrderInvoice;
import com.sgs.ecom.order.service.custom.interfaces.ICustomCodeService;
import com.sgs.ecom.order.service.custom.interfaces.ICustomLimitService;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.ISSOTemplateSV;
import com.sgs.ecom.order.service.order.interfaces.IOrderAttributeService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.order.interfaces.IOrderInvoiceService;
import com.sgs.ecom.order.service.order.interfaces.IOrderOperatorLogService;
import com.sgs.ecom.order.service.order.interfaces.IOrderPayService;
import com.sgs.ecom.order.service.order.interfaces.ISubOrderService;
import com.sgs.ecom.order.service.user.interfaces.IUserCompanySV;
import com.sgs.ecom.order.service.user.interfaces.IUserInfoSV;
import com.sgs.ecom.order.service.user.interfaces.IUserLabelSV;
import com.sgs.ecom.order.service.user.interfaces.IUserNoticeLogSV;
import com.sgs.ecom.order.service.util.interfaces.ICenterService;
import com.sgs.ecom.order.service.util.interfaces.IDmlSV;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultCode;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.UtilTools;
import com.sgs.ecom.order.util.attr.MemoUtil;
import com.sgs.ecom.order.util.check.CheckOpenUtil;
import com.sgs.ecom.order.util.check.OrderCheckUtil;
import com.sgs.ecom.order.util.collection.ListUtil;
import com.sgs.ecom.order.util.collection.StrUtil;
import com.sgs.ecom.order.util.select.AttributeUtil;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.util.select.SelectBaseUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.util.time.TimeCalendarUtil;
import com.sgs.ecom.order.vo.VOOrderAttribute;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import com.sgs.ecom.order.vo.VOOrderDetail;
import com.sgs.ecom.order.vo.order.VOChangeOrderPrice;
import com.sgs.redis.RedisClient;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Zhang
 * @Description :
 * @date 2023/5/23
 */
@Service
public class OrderBaseInfoDomainServiceImpl extends BaseService implements IOrderBaseInfoDomainService {

    @Autowired
    private IOrderBaseInfoService orderBaseInfoService;
    @Autowired
    private OrderBaseInfoMapper orderBaseInfoMapper;
    @Autowired
    private ICenterTemplateSV centerTemplateSV;
    @Autowired
    private ISubOrderService subOrderService;
    @Autowired
    private IOrderOperatorLogDomainService orderOperatorLogDomainService;
    @Autowired
    private EventMailUtil eventMailUtil;
    @Autowired
    private EventSmsUtil eventSmsUtil;
    @Autowired
    private IUserNoticeLogSV userNoticeLogSV;
    @Autowired
    private IOrderMemoDomainService orderMemoDomainService;
    @Autowired
    private IOrderApplicationFormDomainService orderApplicationFormDomainService;
    @Autowired
    private IOrderSampleDomainService orderSampleDomainService;
    @Autowired
    private IOrderReportDomainService orderReportDomainService;
    @Autowired
    private IOrderDetailDomainService orderDetailDomainService;
    @Autowired
    private IOrderOperatorLogDomainService operatorLogDomainService;
    @Autowired
    private IOrderExpressDomainService orderExpressDomainService;
    @Autowired
    private IUserInfoSV userInfoSV;
    @Autowired
    private IOrderInvoiceService orderInvoiceService;
    @Autowired
    private IDmlSV dmlSV;
    @Autowired
    private ICustomCodeService customCodeService;

    @Autowired
    private IOrderPayDomainService orderPayDomainService;

    @Autowired
    private IOrderPayService orderPayService;
    @Autowired
    private IOrderProductDomainService orderProductDomainService;
    @Autowired
    private UtilTools utilTools;
    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;

    @Autowired
    private IUserLabelSV userLabelSV;
    @Autowired
    private IOrderInvoiceDomainService orderInvoiceDomainService;
    @Autowired
    private IOrderLinkDomainService orderLinkDomainService;
    @Autowired
    private IOrderBaseInfoRepository orderBaseInfoRepository;
    @Resource
    private CenterRespository centerRespository;
    @Autowired
    private IOrderCustomerDomainService orderCustomerDomainService;
    @Autowired
    private IOrderLabelDomainService orderLabelDomainService;
    @Autowired
    private IOrderSampleRelateDomainService orderSampleRelateDomainService;
    @Resource
    private IOrderAttachmentDomainService orderAttachmentDomainService;
    @Autowired
    private IOrderOperatorLogService orderOperatorLogService;
    @Resource
    private RedisClient redisClient;
    @Autowired
    private IOrderUtilService orderUtilService;
    @Resource
    private IOrderAttributeService orderAttributeService;
    @Resource
    private ICustomLimitService customLimitService;
    @Resource
    private ISSOTemplateSV ssoTemplateSV;
    @Resource
    private IUserCompanySV userCompanySV;
    @Resource
    private IOrderShippingDomainService orderShippingDomainService;
    @Resource
    private IOrderShippingContactDomainService orderShippingContactDomainService;
    @Resource
    private OrderUtilHandle orderUtilHandle;
    @Resource
    private ICenterService centerService;


    @Override
    public OrderBaseInfoDTO qryOrderBaseInfoByMap(long orderId) {
        Map<String,Object> map = new HashMap<>();
        map.put(SelectMapUtil.ORDER_ID,orderId);
        OrderBaseInfoDTO orderBaseInfoDTO = orderBaseInfoService.selectOneByMap(map);
        if(ValidationUtil.isEmpty(orderBaseInfoDTO))
            throw new BusinessException(ResultEnumCode.ORDER_NULL);

        return orderBaseInfoDTO;
    }




    @Override
    public void updateOrderBaseInfoByOperatorReq(BaseOrderDTO orderDTO, OperatorReq operatorReq) throws Exception{
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        OrderBaseInfo orderBaseInfo=orderBaseInfoDO.getOrderInfoByState(orderDTO,operatorReq.getOrderState());
        if(operatorReq.getOrderState()==14){
            if(orderDTO.getTestCycle()!=null){
                orderBaseInfo.setDeadlineTime(dmlSV.getDeadlineTimeByDate(new Date(), orderDTO.getTestCycle().intValue()));
            }
            orderBaseInfo.setApplySubmitDate(UseDateUtil.getDateString(new Date()));
            orderBaseInfo.setAuditCode(operatorReq.getCsCode());
            operatorReq.setConfirmForm(1);
            operatorReq.setOrderNo(orderDTO.getOrderNo());
            operatorReq.setOrderType(orderDTO.getOrderType());
        }
        updateOrderBaseInfo(orderBaseInfo);
    }

    @Override
    public OrderBaseInfo  updateInquiryRpcInfo(BaseOrderDTO baseOrderDTO, CreateInquiryRpcDTO createInquiryRpcDTO) {

        String newGroupNo=createInquiryRpcDTO.getDmlRpcReqDTO().getNewGroupNo();
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        OrderBaseInfo orderBaseInfo=orderBaseInfoDO.getOiqInquiryByDmlRpc(baseOrderDTO,createInquiryRpcDTO);
        //业务线相关数据
        BusinessLineDTO businessLineDTO=centerTemplateSV.qryByPlatformCode(createInquiryRpcDTO.getBaseInfo().getBusinessLine());
        //实验室相关数据
        String useLabCode="1900".equals(baseOrderDTO.getBu())?createInquiryRpcDTO.getBaseInfo().getLabCode():createInquiryRpcDTO.getBaseInfo().getLabName();
        LabDTO labDTO=centerTemplateSV.qryByCode(useLabCode);
        createInquiryRpcDTO.getDmlRpcReqDTO().setLabDTO(labDTO);;
        orderBaseInfoDO.getOrderBaseInfoMoreByRpc(orderBaseInfo,labDTO,businessLineDTO);
        orderBaseInfo.setGroupNo(newGroupNo);
        orderBaseInfo.setTmpGroupNo("");
        orderBaseInfo.setTaxRates(createInquiryRpcDTO.getBaseInfo().getTaxRates());
        orderBaseInfo.setPlatformOrder(createInquiryRpcDTO.getBaseInfo().getQuotationNo());
        orderBaseInfo.setStateDate(UseDateUtil.getDateString(new Date()));
        updateOrderBaseInfo(orderBaseInfo);
        return orderBaseInfo;

    }


    @Override
    public void updateOrderByInquiry(BaseOrderDTO base, OrderBaseInfo infoInquiry, CreateInquiryRpcDTO createInquiryRpcDTO) throws Exception{
        BaseInfoRpcDTO baseInfoRpcDTO= createInquiryRpcDTO.getBaseInfo();
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(base.getOrderId());
        orderBaseInfoDO.copyPrice(orderBaseInfo,infoInquiry);
        orderBaseInfo.setCurrency(infoInquiry.getCurrency());
        orderBaseInfo.setTestCycle(infoInquiry.getTestCycle());
        orderBaseInfo.setCsCode(infoInquiry.getCsCode());
        orderBaseInfo.setCsEmail(infoInquiry.getCsEmail());
        orderBaseInfo.setCsName(infoInquiry.getCsName());
        orderBaseInfo.setCsPhone(infoInquiry.getCsPhone());
        orderBaseInfo.setRecommendReason(infoInquiry.getRecommendReason());
        orderBaseInfo.setSampleRequirements(infoInquiry.getSampleRequirements());
        orderBaseInfo.setMonthPay(baseInfoRpcDTO.getMonthPay());
        orderBaseInfo.setAbstractCustcode(baseInfoRpcDTO.getAbstractCustcode());
        if(baseInfoRpcDTO.getMonthPay()==0){
            orderBaseInfo.setPayMethod(PayMethod.OFFLINE.getIndex());
            orderBaseInfo.setPayState(0);
            orderBaseInfo.setIsPayReceived(0);
        }
        if(baseInfoRpcDTO.getMonthPay()==2){
            orderBaseInfo.setPayState(1);
            orderBaseInfo.setIsPayReceived(1);
            orderBaseInfo.setPayDate(UseDateUtil.getDateString(new Date()));
            orderBaseInfo.setPayMethod(PayMethod.FALSE_MONTH.getIndex());
        }
        orderBaseInfo.setTaxRates(baseInfoRpcDTO.getTaxRates());
        if(!ValidationUtil.isEmpty(createInquiryRpcDTO.getSalesInfo())){
            orderBaseInfo.setSalesEmail(createInquiryRpcDTO.getSalesInfo().getSalesEmail());
        }
        if(createInquiryRpcDTO.getBaseInfo().getTestCycle()!=null && base.getState()==BaseOrderStateEnum.SERVICE.getIndex()){
            orderBaseInfo.setDeadlineTime(dmlSV.getDeadlineTimeByDate(TimeCalendarUtil.getStringToDate(base.getApplySubmitDate()),createInquiryRpcDTO.getBaseInfo().getTestCycle().intValue()));
        }
        updateOrderBaseInfo(orderBaseInfo);
    }



    @Override
    public void getFormInfoMore(OiqOrderDTO oiqOrderDTO, BaseOrderDTO baseOrderDTO) throws Exception {
        String orderNo=baseOrderDTO.getOrderNo();
        //询报价的base是自己 申请表订单是上一单的
        baseCopyObj.copy(oiqOrderDTO.getOrderBase(),oiqOrderDTO.getPortal() ? baseOrderDTO : oiqOrderDTO.getCurrentOrder());
        if(!oiqOrderDTO.getPortal()){
            oiqOrderDTO.getOrderBase().setBusinessPersonEmail(oiqOrderDTO.getOrderBase().getCsEmail());
        }
        if(oiqOrderDTO.getOrderBase().getLineId()!=null){
            BusinessLineDTO businessLineDTO=centerRespository.qryBusinessLineDTOByLineId(oiqOrderDTO.getOrderBase().getLineId());
            if(!ValidationUtil.isEmpty(businessLineDTO)){
                oiqOrderDTO.getOrderBase().setBusinessName(businessLineDTO.getConfigName());
                oiqOrderDTO.getOrderBase().setBusinessCode(businessLineDTO.getPlatformCode());
            }
        }

        orderApplicationFormDomainService.getFormByOrderNo(orderNo,oiqOrderDTO);
        orderReportDomainService.getReportByOrderNo(baseOrderDTO,oiqOrderDTO);

        //老的询报价逻辑 历史的
        if(oiqOrderDTO.getUseUseOrder()==1 && !oiqOrderDTO.getPortal() &&oiqOrderDTO.getApplicationLinkNullFlg() ){
            Map map = new HashMap();
            map.put(SelectBaseUtil.USER_ID,baseOrderDTO.getUserId());
            List<UserCompanyDTO> list = userCompanySV.selectListByMap(map);
            if (list.size() > 0) {
                oiqOrderDTO.getApplication().setCompanyNameCn(list.get(0).getCompanyName());
                oiqOrderDTO.getApplication().setCompanyNameEn(list.get(0).getCompanyNameEn());
                oiqOrderDTO.getApplication().setCompanyAddressEn(list.get(0).getCompanyAddrEn());
                oiqOrderDTO.getReport().setReportCompanyNameCn(list.get(0).getCompanyName());
                oiqOrderDTO.getReport().setReportCompanyNameEn(list.get(0).getCompanyNameEn());
                oiqOrderDTO.getReport().setReportAddressEn(list.get(0).getCompanyAddrEn());
            }
            List<UserInfoDTO> userInfoDTOS = userInfoSV.selectListByMap(map);
            if (userInfoDTOS.size() > 0) {
                oiqOrderDTO.getApplication().setLinkPhone(userInfoDTOS.get(0).getUserPhone());
                oiqOrderDTO.getApplication().setLinkPerson(userInfoDTOS.get(0).getUserNick());
                oiqOrderDTO.getApplication().setLinkEmail(userInfoDTOS.get(0).getUserEmail());
            }
        }

        //申请表添加发票
        OrderInvoiceDTO orderInvoiceDTO = orderInvoiceDomainService.qryOneOrderInvoiceByOrder(orderNo);
        if (orderInvoiceDTO != null) {
            OiqOrderInvoiceDTO oiqOrderInvoiceDTO=new OiqOrderInvoiceDTO();
            baseCopyObj.copy(oiqOrderInvoiceDTO,orderInvoiceDTO);
            UserInvoiceDTO userInvoiceDTO=new UserInvoiceDTO();
            baseCopyObj.copy(userInvoiceDTO,orderInvoiceDTO);
            userInvoiceDTO.setRegAddress(orderInvoiceDTO.getRegisterAddr());
            userInvoiceDTO.setRegPhone(orderInvoiceDTO.getRegisterPhone());
            userInvoiceDTO.setBankName(orderInvoiceDTO.getBankAddr());
            oiqOrderInvoiceDTO.setInvoice(userInvoiceDTO);
            oiqOrderInvoiceDTO.setInvoiceType(orderInvoiceDTO.getInvoiceType());
            oiqOrderInvoiceDTO.setInvoiceId(orderInvoiceDTO.getInvoiceId());
            //发票的地址处理 历史给他展示
            if(orderInvoiceDTO.getAddressId()!=null){
                OiqUserAddressDTO userAddressDTO=new OiqUserAddressDTO();
                baseCopyObj.copy(userAddressDTO,orderInvoiceDTO);
                List<OiqAddressDTO> list=new ArrayList<>();
                list.add(new OiqAddressDTO(orderInvoiceDTO.getAddressId(),userAddressDTO));
                oiqOrderInvoiceDTO.setAddressList(list);
            }
            oiqOrderDTO.setOrderInvoice(oiqOrderInvoiceDTO);
        }
        orderExpressDomainService.oiqAddAddress(orderNo,oiqOrderDTO);
        //orderLink取值逻辑调整
        orderLinkDomainService.addLinkToOrder(orderNo,oiqOrderDTO);

        OrderShippingDTO orderShippingDTO=orderShippingDomainService.qryOrderShippingByOrder(orderNo);
        if(!ValidationUtil.isEmpty(orderShippingDTO)){
            oiqOrderDTO.setOrderShipping(orderShippingDTO);
        }
        List<OrderShippingContactDTO> contactDTOList=orderShippingContactDomainService.qryOrderShippingContactListByOrderNo(orderNo);
        if(!ValidationUtil.isEmpty(contactDTOList)){
            OrderShippingContactDO.listToOiqDTO(oiqOrderDTO,contactDTOList);
        }

        //样品数据显示
        List<OrderLabelDTO> orderLabelList= orderLabelDomainService.selectList(Arrays.asList(orderNo), OrderLabelCodeEnum.TAILS);
        Map<String, String> orderLabelFlg=orderLabelList.stream().collect(Collectors.toMap(OrderLabelDTO::getOrderNo, OrderLabelDTO::getLabelValue, (key1, key2) -> key2));
        oiqOrderDTO.setTails(orderLabelFlg.getOrDefault(orderNo,"0"));
        BaseOrderDTO old=oiqOrderDTO.getCurrentOrder();
        if (!ValidationUtil.isEmpty(old)) {
            List<OiqSampleDTO> sampleDTOList=orderSampleDomainService.getOiqOrderSample(old.getOrderNo(),old.getGroupNo(),oiqOrderDTO.getCurrentOrder().getApplicationLineId(), true);
            oiqOrderDTO.setSampleList(sampleDTOList);
            SampleCategoryDTO sampleCategoryDTO= OrderSampleDO.listToSampleCategory(sampleDTOList);
            // 获取样品分类
            Map<String, String> sampleCategoryMap = centerService.qrySampleCategory(
                baseOrderDTO.getBu(), baseOrderDTO.getApplicationLineId());
            sampleCategoryDTO.setSampleCategoryName(sampleCategoryMap.get(sampleCategoryDTO.getSampleCategoryCode()));
            sampleCategoryDTO.setSampleShapeName(orderUtilService.getEnumStringByKey(RedisKeyUtil.MIN_SAMPLE_SHAPE + oiqOrderDTO.getOrderBase().getBusinessCode(),sampleCategoryDTO.getSampleShapeCode()));
            oiqOrderDTO.setSampleCategory(sampleCategoryDTO);
            //增加项目
            orderDetailDomainService.getItemByOrderNo(old.getOrderNo(),old.getGroupNo(),oiqOrderDTO);
        }
        //获取订单分享的数据
        List<CustomerIdDTO> customerIdDTOList=orderCustomerDomainService.qryCustomerByOrderNo(orderNo);
        oiqOrderDTO.setCustomerList(customerIdDTOList);
    }




    @Override
    public void closeOrder(OrderCloseReq orderCloseReq, OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, BOSysPerson boSysPerson)throws Exception{
        String dateStr=UseDateUtil.getDateString(new Date());
        OrderCheckUtil.checkOrderIsClose(orderBaseInfoCheckDTO);
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseOrder(orderBaseInfoCheckDTO.getOrderNo());
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        String enumStr=orderUtilHandle.getEnumStringByKey(RedisKeyUtil.CRM_CLOSE,orderCloseReq.getCloseCode());
        OrderBaseInfo orderBaseInfo = orderBaseInfoDO.getOrderBaseInfo(orderCloseReq,orderBaseInfoCheckDTO,enumStr);
        updateOrderBaseInfo(orderBaseInfo);
        orderOperatorLogDomainService.addLog(baseOrderDTO,OrderOperatorTypeEnum.CRM_CLOSE_ORDER,boSysPerson.getPersonCode(),enumStr+" "+orderCloseReq.getMemo(),1,dateStr,orderCloseReq.getMemo());

    }



    @Override
    public List<OrderBaseInfoDTO> selectNewListByReq(OrderBaseInfoQryReq orderBaseInfoQryReq){
      return   orderBaseInfoMapper.selectNewListByReq(orderBaseInfoQryReq);
    }

    @Override
    public int selectNewListCountByReq(OrderBaseInfoQryReq orderBaseInfoQryReq) {
        return orderBaseInfoMapper.selectNewListCountByReq(orderBaseInfoQryReq);
    }


    @Override
    public List<OrderIndexDTO> getNewStateNum(OrderBaseInfoQryReq orderBaseInfoQryReq){
        return   orderBaseInfoMapper.getNewStateNum(orderBaseInfoQryReq);
    }

    @Override
    public int updateOrderBaseInfo(OrderBaseInfo orderBaseInfo){
       return orderBaseInfoMapper.updateOrderBaseInfo(orderBaseInfo);
    }

    @Override
    public int updateOrderBaseInfoByLeads(OrderBaseInfo orderBaseInfo){
        return orderBaseInfoMapper.updateOrderBaseInfoByLeads(orderBaseInfo);
    }

    @Override
    public int updateBaseSetLabIsNull(Long orderId){
        return  orderBaseInfoMapper.updateBaseSetLabIsNull(orderId);
    }

    @Override
    public void checkOrderApplicationConfirm(BaseOrderDTO baseOrderDTO, boolean isComfirm, OperatorReq operatorReq) {
        //审核申请表判断主条件

        if (baseOrderDTO.getState() != 12 && baseOrderDTO.getState() != 13 ) {
            throw new BusinessException(ResultCode.STATE_IS_NULL);
        }




        //申请退款中的 无法审核申请表
        if (baseOrderDTO.getRefundState() == OrderRefundStateEnum.REFUND.getIndex()) {
            throw new BusinessException(ResultCode.STATE_IS_NULL);
        }

        if (!isComfirm &&
                baseOrderDTO.getOrderType().equals(OrderTypeEnum.TIC.getIndex()) &&
                (baseOrderDTO.getSubState() == OrderSubStateEnum.ORDER_AWAIT_AUDIT.getIndex()))//当是退回操作时判断子状态如果为64，不允许退回
            throw new BusinessException(ResultEnumCode.SL_ORDER_IS_OPEN);

        if (isComfirm && StringUtils.isBlank(operatorReq.getPreReportDate())) //审核成功时这个字段必传
            throw new BusinessException(ResultEnumCode.REPORT_TIME_IS_NULL);
    }

    @Override
    public void cancelOrdeer(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) throws Exception {
        if (OrderStateEnum.CANCELLED.getIndex().equals(String.valueOf(baseOrderDTO.getState())))
            throw new BusinessException(ResultEnumCode.ORDER_STATE_CANCEL);

        if(!baseOrderDTO.getPayMethod().equals(PayMethodEnum.MONTH.getIndex())){
            BigDecimal refundAmount =getRefundAmount(baseOrderDTO);
            OrderServiceRefundReq orderServiceRefundReq = new OrderServiceRefundReq();
            orderServiceRefundReq.setOrderId(baseOrderDTO.getOrderId());
            orderServiceRefundReq.setRefundAmount(refundAmount);
            BOSysPerson boSysPerson = new BOSysPerson();
            boSysPerson.setPersonCode(OperatorCode.PRE_ORDER.getName());
            orderPayService.csRefund(orderServiceRefundReq,boSysPerson,"SODA");
        }
        //针对订单做处理
        updateOrderBaseInfoByClose(baseOrderDTO,operatorReq);
    }

    private void updateOrderBaseInfoByClose(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) throws Exception {
        if(!OrderTypeEnum.getSub(String.valueOf(baseOrderDTO.getOrderType()))){
            if(baseOrderDTO.getState()>=Integer.parseInt(OrderStateEnum.SERVICE.getIndex()) || baseOrderDTO.getSubState() == OrderSubStateEnum.ORDER_AWAIT_AUDIT.getIndex()){
                Map<String,Object> map = new HashMap<>();
                map.put(SelectMapUtil.ORDER_NO,baseOrderDTO.getOrderNo());
                List<OrderProductDTO> orderProductDTOList = orderProductDomainService.selectListByMap(map);
                List<String> cacheInfo = utilTools.getCacheInfo("SODA_USED_ORDER.usedType");
                CheckOpenUtil.checkIsSodaUsedByCancel(orderProductDTOList,cacheInfo,1);
            }
        }
        //主订单要查询有无子订单未出来的
        int count=subOrderService.selectCountByReceived(baseOrderDTO.getOrderNo());
        if(count>0){
            throw new BusinessException(ResultEnumCode.ORDER_CLOSE_ERROR);
        }
        VOOrderBaseInfo voOrderBaseInfoUpdate=new VOOrderBaseInfo();
        voOrderBaseInfoUpdate.setOrderId(baseOrderDTO.getOrderId());
//        voOrderBaseInfoUpdate.setCloseCode(orderCloseReq.getCloseCode());

        OrderOperatorTypeEnum orderOperatorTypeEnum=OrderOperatorTypeEnum.CRM_CLOSE_ORDER;

//        String enumStr=orderUtilService.getEnumStringByKey(RedisKeyUtil.TIC_CRM_CLOSE,orderCloseReq.getCloseCode());
//        voOrderBaseInfoUpdate.setCloseReason(enumStr);
        voOrderBaseInfoUpdate.setState(Integer.parseInt(OrderStateEnum.CANCELLED.getIndex()));
        voOrderBaseInfoUpdate.setStateDate(UseDateUtil.getDateString(new Date()));
        voOrderBaseInfoUpdate.setHisState(baseOrderDTO.getState());
        orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfoUpdate);


        // 取消订单不取消BU标签
//        Map<String,Object> selectMap=new HashMap<>();
//        selectMap.put(SelectMapUtil.BU,baseOrderDTO.getBu());
//        selectMap.put(SelectBaseUtil.USER_ID,baseOrderDTO.getUserId());
//        selectMap.put(SelectMapUtil.STATE_NOT,91);
//        int t=orderBaseInfoService.selectListCountByMap(selectMap);
//        if(t<=1){
//            userLabelSV.updateUserLabel(baseOrderDTO.getUserId(), ListUtil.add(baseOrderDTO.getBu()));
//        }

        String memo=ValidationUtil.isEmpty(operatorReq.getMemo())?"":operatorReq.getMemo();

        //记录日志

        BOSysPerson personInfo = new BOSysPerson();
        personInfo.setPersonCode(OperatorCode.PRE_ORDER.getName());
        operatorLogDomainService.saveOrderLog(baseOrderDTO.getOrderNo(), Integer.valueOf(baseOrderDTO.getOrderType()),
                orderOperatorTypeEnum.CRM_CLOSE_ORDER.getIndex(),personInfo,memo,"");

        // TODO 发送邮件通知关闭订单操作

        OrderCheckStateDTO orderCheckStateDTO=orderBaseInfoCustomService.selectOrderCheckStateDTO(baseOrderDTO.getOrderId());
        Boolean monthFlg=(orderCheckStateDTO.getMonthPay()==1 || orderCheckStateDTO.getMonthPay()==3)?true:false;
        OiqMailEnum oiqMailEnum=monthFlg?OiqMailEnum.TIC_CLOSE_AB:OiqMailEnum.TIC_CLOSE;
        //2024-11-13和昌盛沟通月结节点走非月结短信模板
        OiqSmsEnum oiqSmsEnum=OiqSmsEnum.TIC_CLOSE;

        Boolean sendCs=(String.valueOf(baseOrderDTO.getState()).equals(OrderStateEnum.SERVICE.getIndex())
                ||String.valueOf(baseOrderDTO.getState()).equals(OrderStateEnum.WAITSEND.getIndex())
                || String.valueOf(baseOrderDTO.getState()).equals(OrderStateEnum.WAITEXAMINE.getIndex()))?true:false;

        TicOtherMailDTO ticOtherMailDTO = new TicOtherMailDTO();
        ticOtherMailDTO.setState(voOrderBaseInfoUpdate.getState());
        eventMailUtil.sendTicMail(baseOrderDTO.getOrderId(),oiqMailEnum,1L,ticOtherMailDTO);
        eventMailUtil.sendTicMail(baseOrderDTO.getOrderId(),oiqMailEnum,2L,ticOtherMailDTO);
        eventSmsUtil.sendSMS(baseOrderDTO.getOrderId(),oiqSmsEnum);
        operatorReq.setType(2);
    }

    private BigDecimal getRefundAmount(BaseOrderDTO baseOrderDTO) {
        List<OrderPayDTO> orderPayDTOList = orderPayDomainService.qryOrderPay(Arrays.asList(baseOrderDTO.getOrderNo()), 1, null);


        if(ValidationUtil.isEmpty(orderPayDTOList))
            throw new BusinessException(ResultEnumCode.ORDER_STATE_NULL);

        OrderBaseInfoDTO orderBaseInfoDTO = new OrderBaseInfoDTO();
        orderBaseInfoDTO.setOrderNo(baseOrderDTO.getOrderNo());

        int sum = orderPayDTOList.stream().filter(orderPayDTO -> orderPayDTO.getPayType() == 3).mapToInt(orderPayDTO -> orderPayDTO.getPayPrice()).sum();
        BigDecimal subtract = baseOrderDTO.getRealAmount().multiply(new BigDecimal("100")).subtract(new BigDecimal(sum)).divide(new BigDecimal("100"),2,BigDecimal.ROUND_HALF_UP);
        return subtract;
    }



    @Override
        public OrderBaseInfo comfirmForm(boolean isComfirm, BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) {
        OrderOperatorTypeEnum orderOperatorTypeEnum = null;
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        orderBaseInfo.setSlimNo(operatorReq.getSlimNo());
        orderBaseInfo.setPlatform("SODA");
        orderBaseInfo.setPlatformOrder(operatorReq.getPlatformOrder());
        orderBaseInfo.setStateDate(UseDateUtil.getDateString(new Date()));
        String text = "";
        BaseLog baseLog = null;
        if (isComfirm) {//申请表确认
            orderBaseInfo.setState(Integer.parseInt(OrderStateEnum.SERVICE.getIndex()));
            orderBaseInfo.setApplySubmitDate(operatorReq.getOperatorDate());
            orderBaseInfo.setDeadlineTime(operatorReq.getPreReportDate());

            orderOperatorTypeEnum = OrderOperatorTypeEnum.FORMSUCCESS;
           text = " 预计报告日期 :" + operatorReq.getPreReportDate() + (StringUtils.isBlank(operatorReq.getMemo()) ? "" : "备注:" + operatorReq.getMemo());
           baseLog = new BaseLog(baseOrderDTO.getOrderNo(), baseOrderDTO.getOrderType(), orderOperatorTypeEnum, text, "", OperatorCode.PRE_ORDER.getName(), 1);

            userNoticeLogSV.insertNoticByFormConfirm(baseOrderDTO);
            //修改报告出具时间的时候
            if(StringUtils.isNotBlank(operatorReq.getMemo())){
                orderMemoDomainService.addOrderMemo(baseOrderDTO.getOrderNo(), MemoUtil.DEADLINE_TIME_MEMO, operatorReq.getMemo());
            }
        } else {

            orderBaseInfo.setSubState(OrderSubStateEnum.ORDER_AWAIT_AUDIT.getIndex());
//            orderBaseInfo.setState(Integer.parseInt(OrderStateEnum.WAITAPPLY.getIndex()));
            orderBaseInfo.setHisState(baseOrderDTO.getState());
            orderOperatorTypeEnum = OrderOperatorTypeEnum.IS_OPEN_ORDER;
            baseLog = new BaseLog(baseOrderDTO.getOrderNo(), baseOrderDTO.getOrderType(), orderOperatorTypeEnum, text, "", OperatorCode.PRE_ORDER.getName(), 1);
        }
        orderBaseInfo.setAuditCode(operatorReq.getCsCode());
        orderOperatorLogDomainService.addLogByBase(baseLog);
        updateOrderBaseInfo(orderBaseInfo);
        operatorReq.setType(0);
        return orderBaseInfo;
    }

    @Override
    public void orderEnd(OperatorReq operatorReq, BaseOrderDTO baseOrderDTO) {
//        List<FileReq> reqReports = operatorReq.getReports();
//        List<OrderAttachment> orderAttachmentList =  orderAttachmentDomainService.getListByReq(reqReports,baseOrderDTO.getOrderNo(),null, OrderAttachmentTypeEnum.OIQ_REPORT,UseDateUtil.getDateString(new Date()));
//        orderAttachmentDomainService.insertForeach(orderAttachmentList);
//        OrderOperatorTypeEnum orderOperatorTypeEnum = OrderStateEnum.SERVICE.getIndex().equals(String.valueOf(baseOrderDTO.getState())) ?
//                OrderOperatorTypeEnum.OIQREPORT :
//                OrderOperatorTypeEnum.OIQ_REPORT_MORE;
//        String fileName = reqReports.stream().map(FileReq::getFileName).collect(Collectors.toList()).toString().replace("[", "").replace("]", "");
//        StringBuilder operatorText = new StringBuilder();
//        operatorText.append(" 报告文件：");
//        operatorText.append(fileName);
//        BaseLog baseLog = new BaseLog(baseOrderDTO.getOrderNo(), baseOrderDTO.getOrderType(), orderOperatorTypeEnum, operatorText.toString(), "", OperatorCode.PRE_ORDER.getName(), 1);
//        orderOperatorLogDomainService.addLogByBase(baseLog);
//        operatorReq.setType(1);
//        operatorReq.setEventEnum(EventEnum.SAVE_ORDER_TO_END);
//        operatorReq.setTicOtherApiDTO(new TicOtherApiDTO(reqReports));
    }


    private int getNotCancelOrder(BaseOrderDTO orderBaseInfoDTO) {
        Map<String, Object> selectMap = new HashMap<>();
        selectMap.put(SelectMapUtil.BU, orderBaseInfoDTO.getBu());
        selectMap.put(SelectBaseUtil.USER_ID, orderBaseInfoDTO.getUserId());
        selectMap.put(SelectMapUtil.STATE_NOT, 91);
        int t = orderBaseInfoService.selectListCountByMap(selectMap);
        return t;
    }

    @Override
    public List<String> qryOrderNoListByMap(Map<String, Object> qryMap) {
        List<String> orderNoList = orderBaseInfoService.qryOrderNoListByMap(qryMap);
        return orderNoList.isEmpty()?new ArrayList<>():orderNoList;
    }



    @Override
    public List<QryOrderRes> addData(List<OrderBaseInfoDTO> baseInfoDTOList, Map<String,
            List<OrderExpressDTO>> orderExpressListMap, Map<String, List<OrderProductDTO>> orderProductListMap,
                                     Map<String, List<OrderDetailDTO>> orderDetailListMap,
                                     Map<String, List<OrderApplicationFormDTO>> applicationFormListMap,
                                     Map<String, List<OrderApplicationAttrDTO>> orderApplicationAttrListMap,
                                     Map<String, List<OrderInvoiceDTO>> orderInvoiceListMap,
                                     Map<String, List<OrderSampleFromDTO>> sampleFromListMap,
                                     List<QryOrderRes> resList, Map<String, List<OrderAttachmentDTO>> attListMap,
                                     Map<String, List<OrderMemoDTO>> memoListMap, Map<String,
            List<OrderAttachmentDTO>> attachmentGroupMap, List<OrderSampleRelateDTO> orderSampleRelateDTOS,
                                     Map<String, List<OrderSampleDTO>> samplePackageRelationMap) {


        for (OrderBaseInfoDTO orderBaseInfoDTO : baseInfoDTOList) {
            QryOrderRes qryOrderRes = new QryOrderRes();
            OrderReport orderReport = orderReportDomainService.qryOrderReportByOrderNo(orderBaseInfoDTO.getOrderNo());
            setOrderBaseInfoDTO(qryOrderRes,orderBaseInfoDTO,orderReport);
            setReport(qryOrderRes,orderReport);
            setOrderOrderProductList(qryOrderRes,orderProductListMap);
            setOrderOrderDetailList(qryOrderRes,orderDetailListMap,attachmentGroupMap,orderSampleRelateDTOS,samplePackageRelationMap);
            setOrderOrderFormList(qryOrderRes,applicationFormListMap,orderReport);
            setOrderOrderAttrList(qryOrderRes,orderApplicationAttrListMap);
            setOrderOrderExpressList(qryOrderRes,orderExpressListMap);
            setOrderOrderInvoiceList(qryOrderRes,orderInvoiceListMap);
            setOrderOrderSampleListList(qryOrderRes,sampleFromListMap);
            setOrderOrderMemoListList(qryOrderRes,memoListMap);
            setOrderOrderAttechmentListList(qryOrderRes,attListMap,memoListMap);
            resList.add(qryOrderRes);
        }
        return resList;
    }

    private void setReport(QryOrderRes qryOrderRes, OrderReport orderReport) {
        OrderReportDTO orderReportDTO = new OrderReportDTO();
        baseCopyObj.copy(orderReportDTO,orderReport);
        qryOrderRes.setReport(ValidationUtil.isEmpty(orderReportDTO)?new OrderReportDTO():orderReportDTO);
    }

    private void setOrderOrderMemoListList(QryOrderRes qryOrderRes, Map<String, List<OrderMemoDTO>> memoListMap) {
        List<OrderMemoDTO> list = new ArrayList<>();
        List<OrderMemoDTO> memoDTOList = memoListMap.get(qryOrderRes.getOrderNo());
        if(!ValidationUtil.isEmpty(memoDTOList)){
            list.add(memoDTOList.get(0));
        }
        qryOrderRes.setMemos(list);
    }

    private void setOrderOrderAttechmentListList(QryOrderRes qryOrderRes, Map<String, List<OrderAttachmentDTO>> attListMap, Map<String, List<OrderMemoDTO>> memoListMap) {
        if(ValidationUtil.isEmpty(attListMap.get(qryOrderRes.getOrderNo()))){
            qryOrderRes.setAttachments(new ArrayList<>());
        }else {
            List<OrderAttachmentDTO> orderAttachmentDTOS = attListMap.get(qryOrderRes.getOrderNo());
            List<OrderMemoDTO> memoDTOList = memoListMap.get(qryOrderRes.getOrderNo());
            if(!ValidationUtil.isEmpty(memoDTOList)){
                OrderMemoDTO orderMemoDTO = memoDTOList.get(0);
                List<String> memoIdList = new ArrayList<>();
                 memoDTOList.stream().filter(orderMemoDTO1 -> orderMemoDTO1.getId() != orderMemoDTO.getId()).map(OrderMemoDTO::getId)
                        .forEach(str ->{
                            memoIdList.add(MemoUtil.FORM_MEMO+"-"+str);
                        });
                List<OrderAttachmentDTO> collect = orderAttachmentDTOS.stream().filter(orderMemoDTO2 -> !memoIdList.contains(orderMemoDTO2.getGroupNo())).collect(Collectors.toList());
                orderAttachmentDTOS = collect;
            }
            qryOrderRes.setAttachments(orderAttachmentDTOS);
        }
    }

    private void setOrderOrderSampleListList(QryOrderRes qryOrderRes, Map<String, List<OrderSampleFromDTO>> sampleFromListMap) {
        List<OrderSampleFromDTO> sampleForm = ValidationUtil.isEmpty(sampleFromListMap.get(qryOrderRes.getOrderNo()))?new ArrayList<>() :sampleFromListMap.get(qryOrderRes.getOrderNo());
        if(!ValidationUtil.isEmpty(sampleForm)){
            sampleForm.stream().forEach(orderSample ->{
                String enumConfig = orderSample.getEnumConfig();
                if(!ValidationUtil.isEmpty(enumConfig)){
                    if (StringUtils.equals("LV3_SAMPLE_OPTIONS.SECURITY_LEVEL", enumConfig)) {
                        enumConfig = "LV3_SAMPLE_OPTIONS.SECURITY_LEVEL_TO_SODA";
                    }

                    String enumCache = redisClient.getValue("enumMap", enumConfig);
                    List<SysEnumConfigDTO> lstEnum = JSON.parseArray(enumCache, SysEnumConfigDTO.class);
                    if(!ValidationUtil.isEmpty(lstEnum)){
                        String sampleValue = orderSample.getSampleValue();
                        if(!ValidationUtil.isEmpty(sampleValue) && !ValidationUtil.isEmpty(lstEnum)){
                            String enumStringByKeys = orderUtilService.getEnumStringByKeys(enumConfig, orderSample.getSampleValue());
                            orderSample.setSampleExplain(enumStringByKeys);
                            orderSample.setSampleValue(enumStringByKeys);
                        }
                    }
                }
            });
        }
        qryOrderRes.setSampleForm(sampleForm);
    }

    private void setOrderOrderInvoiceList(QryOrderRes qryOrderRes, Map<String, List<OrderInvoiceDTO>> orderInvoiceListMap) {
        OrderInvoiceDTO orderInvoiceDTO = orderInvoiceListMap.get(qryOrderRes.getOrderNo()).get(0);
        OrderInvoice invoiceDto = new OrderInvoice();
        baseCopyObj.copy(invoiceDto,orderInvoiceDTO);
        qryOrderRes.setInvoice(invoiceDto);
    }

    private void setOrderOrderExpressList(QryOrderRes qryOrderRes, Map<String, List<OrderExpressDTO>> orderExpressListMap) {
        List<OrderExpressDTO> orderExpressDTOS = orderExpressListMap.get(qryOrderRes.getOrderNo());
        List<OrderDelivers> deliverList = new ArrayList<>();
        if(!ValidationUtil.isEmpty(orderExpressDTOS)){
            orderExpressDTOS.stream().forEach(orderExpressDTO -> {
                OrderDelivers orderDelivers = new OrderDelivers();
                orderDelivers.setDeliverType(orderExpressDTO.getDeliverType());
                orderDelivers.setReceiveName(orderExpressDTO.getReceiptPerson());
                orderDelivers.setReceivePhone(orderExpressDTO.getReceiptPhone());
                orderDelivers.setReceiveProvince(orderExpressDTO.getReceiptProvice());
                orderDelivers.setReceiveCity(orderExpressDTO.getReceiptCity());
                orderDelivers.setReceiveTown(orderExpressDTO.getReceiptTown());
                orderDelivers.setExpressNo(orderExpressDTO.getExpressNo());
                orderDelivers.setReceiveEmail(orderExpressDTO.getReceiptEmail());
                deliverList.add(orderDelivers);
            });
        }
        qryOrderRes.setDelivers(deliverList);
    }

    private void setOrderOrderAttrList(QryOrderRes qryOrderRes, Map<String, List<OrderApplicationAttrDTO>> orderApplicationAttrListMap) {
        qryOrderRes.setApplicationAttr(ValidationUtil.isEmpty(orderApplicationAttrListMap.get(qryOrderRes.getOrderNo()))?new ArrayList<>() :orderApplicationAttrListMap.get(qryOrderRes.getOrderNo()));
    }

    private void setOrderOrderFormList(QryOrderRes qryOrderRes, Map<String, List<OrderApplicationFormDTO>> applicationFormListMap, OrderReport orderReport) {
        OrderApplicationFormDTO orderApplicationFormDTO = applicationFormListMap.get(qryOrderRes.getOrderNo()).get(0);
        OrderApplicationForm applicationFormDto = new OrderApplicationForm();
        baseCopyObj.copy(applicationFormDto,orderApplicationFormDTO);
        applicationFormDto.setCompanyName(orderApplicationFormDTO.getCompanyNameCn());
        applicationFormDto.setCompanyNameEn(orderReport.getReportCompanyNameEn());
        applicationFormDto.setCompanyAddr(orderApplicationFormDTO.getCompanyAddressCn());
        applicationFormDto.setCompanyAddrEn(orderReport.getReportAddressEn());
        qryOrderRes.setApplicationForm(applicationFormDto);
    }

    private void setOrderOrderDetailList(QryOrderRes qryOrderRes, Map<String, List<OrderDetailDTO>> orderDetailListMap,
                                         Map<String, List<OrderAttachmentDTO>> attachmentGroupMap, List<OrderSampleRelateDTO> orderSampleRelateDTOS,
                                         Map<String, List<OrderSampleDTO>> samplePackageRelationMap) {
        List<OrderDetailDTO> items = ValidationUtil.isEmpty(orderDetailListMap.get(qryOrderRes.getOrderNo()))?new ArrayList<>():orderDetailListMap.get(qryOrderRes.getOrderNo());
       if(!ValidationUtil.isEmpty(items)){
           items.stream().forEach(orderDetailDTO -> {
               if(orderDetailDTO.getItemType() ==1){//查询附件
                   List<OrderAttachmentDTO> list = attachmentGroupMap.get(orderDetailDTO.getOrderNo() + OrderAttachmentTypeEnum.PACKAGE_DETAIL_FILES.getIndex() + orderDetailDTO.getDetailId());
                   if(!ValidationUtil.isEmpty(list)){
                       orderDetailDTO.setAttachments(list);
                   }
                   if(!ValidationUtil.isEmpty(orderSampleRelateDTOS)){
                       List<OrderSampleRelateDTO> collect = orderSampleRelateDTOS.stream()
                               .filter(orderSampleRelateDTO -> orderSampleRelateDTO.getDetailId().equals(String.valueOf(orderDetailDTO.getDetailId())))
                               .collect(Collectors.toList());
                       if(!ValidationUtil.isEmpty(collect)){
                          orderDetailDTO.setOrderSampleList(collect);
                      }
                   }

                   List<OrderSampleDTO> samplePackageRelationMapOrDefault = samplePackageRelationMap.getOrDefault((orderDetailDTO.getOrderNo() + orderDetailDTO.getDetailId()), new ArrayList<OrderSampleDTO>());
                   if(!ValidationUtil.isEmpty(samplePackageRelationMapOrDefault)){
                       orderDetailDTO.setSampleNameInfo(samplePackageRelationMapOrDefault.stream().map(OrderSampleDTO::getSampleName).collect(Collectors.joining(";")));
                       orderDetailDTO.setSampleNameInfoList(samplePackageRelationMapOrDefault.stream().collect(Collectors.toList()));
                   }

               }
           });
       }

        qryOrderRes.setItems(items);
    }

    private void setOrderOrderProductList(QryOrderRes qryOrderRes, Map<String, List<OrderProductDTO>> orderProductListMap) {
        if(!ValidationUtil.isEmpty(orderProductListMap)){
            OrderProductDTO orderProductDTO = orderProductListMap.get(qryOrderRes.getOrderNo()).get(0);
            qryOrderRes.getBaseInfo().setSubBuCode(orderProductDTO.getSubBuCode());
        }

    }

    private void setOrderBaseInfoDTO(QryOrderRes qryOrderRes, OrderBaseInfoDTO orderBaseInfoDTO, OrderReport orderReport) {
        if(!ValidationUtil.isEmpty(orderBaseInfoDTO)){
            qryOrderRes.setOrderNo(orderBaseInfoDTO.getOrderNo());
            qryOrderRes.setRelateOrderNo(orderBaseInfoDTO.getRelateOrderNo());
            com.sgs.ecom.order.response.order.other.OrderBaseInfo baseInfoDtos = new com.sgs.ecom.order.response.order.other.OrderBaseInfo();
            baseCopyObj.copy(baseInfoDtos,orderBaseInfoDTO);
            baseInfoDtos.setTestCycle(ValidationUtil.isEmpty(orderBaseInfoDTO.getTestCycle())?0:Integer.parseInt(orderBaseInfoDTO.getTestCycle()));
            baseInfoDtos.setIsUrgent(1);
            baseInfoDtos.setReportLua(TransReportLuaEnum.getIndex(orderReport.getReportLua()));
            baseInfoDtos.setReportForm(TransReportFormEnum.getIndex(orderReport.getReportForm()));
            baseInfoDtos.setCsCode(orderBaseInfoDTO.getOperatorCode());
            List<OrderOperatorLogDTO> logList = orderOperatorLogDomainService.getLogList(orderBaseInfoDTO.getOrderNo(), Arrays.asList("31"), null);//提交申请表
            List<OrderOperatorLogDTO> backLogList = orderOperatorLogDomainService.getLogList(orderBaseInfoDTO.getOrderNo(), Arrays.asList("35"), null);//退回申请表
            if(!ValidationUtil.isEmpty(backLogList) && !ValidationUtil.isEmpty(logList)){
                List<OrderOperatorLogDTO> collect = logList.stream().sorted(Comparator.comparing(OrderOperatorLogDTO::getLogId).reversed()).
                        collect(Collectors.toList());
                baseInfoDtos.setModifyDate(ValidationUtil.isEmpty(logList)?null:collect.get(0).getOperatorDate());
            }
            qryOrderRes.setBaseInfo(baseInfoDtos);
        }
    }

    @Override
    public void checkData(OperatorReq operatorReq, BaseOrderDTO baseOrderDTO) {
        if(ValidationUtil.isEmpty(operatorReq.getOrderState()))
            throw new BusinessException(ResultEnumCode.PARAMS_NULL);

        String orderState = String.valueOf(operatorReq.getOrderState());
        if (!OrderStateEnum.CANCELLED.getIndex().equals(orderState) && !OrderStateEnum.SERVICE.getIndex().equals(orderState) &&
                !OrderStateEnum.END.getIndex().equals(orderState) && !OrderStateEnum.WAITAPPLY.getIndex().equals(orderState) &&
                !String.valueOf(OrderSubStateEnum.ORDER_AWAIT_AUDIT.getIndex()).equals(orderState) &&
                !String.valueOf(OrderSubStateEnum.ORDER_TESTING.getIndex()).equals(orderState) &&
                !String.valueOf(OrderSubStateEnum.ORDER_REPORTING.getIndex()).equals(orderState) &&
                !String.valueOf(OrderSubStateEnum.ORDER_PENDING_OR_UNPENDING.getIndex()).equals(orderState)
        )
            throw new BusinessException(ResultEnumCode.ORDER_STATE_NULL);

        if (OrderStateEnum.CANCELLED.getIndex().equals(orderState) && ValidationUtil.isEmpty(operatorReq.getMemo()))
            throw new BusinessException(ResultEnumCode.SL_ORDER_MEMO_IS_NOT);

        if ((OrderStateEnum.SERVICE.getIndex().equals(orderState) || OrderStateEnum.END.getIndex().equals(orderState)) && ValidationUtil.isEmpty(operatorReq.getOperatorDate()))
            throw new BusinessException(ResultEnumCode.SL_ORDER_OPERATOR_DATE_IS_NOT);

        if ((OrderStateEnum.SERVICE.getIndex().equals(orderState) || String.valueOf(OrderSubStateEnum.ORDER_AWAIT_AUDIT.getIndex()).equals(orderState)) && ValidationUtil.isEmpty(operatorReq.getPlatformOrder()))
            throw new BusinessException(ResultEnumCode.SL_PLATFORM_ORDER_NO_IS_NOT);


        if( String.valueOf(OrderSubStateEnum.ORDER_TESTING.getIndex()).equals(orderState)  || String.valueOf(OrderSubStateEnum.ORDER_REPORTING.getIndex()).equals(orderState) ){//Testing & Reporting 都是 订单审核 到 完成之间的状态
            if(!OrderStateEnum.SERVICE.getIndex().equals(String.valueOf(baseOrderDTO.getState()))){
                throw new BusinessException(ResultEnumCode.SL_ORDER_STATE_IS_NOT_FRO_TESTING_REPORTING);
            }
        }

        if((String.valueOf(OrderSubStateEnum.ORDER_REPORTING.getIndex()).equals(orderState) ||
                String.valueOf(OrderSubStateEnum.ORDER_TESTING.getIndex()).equals(orderState)) && ValidationUtil.isEmpty(operatorReq.getPreReportDate()))
            throw new BusinessException(ResultEnumCode.PRE_REPORT_DATE_IS_NULL);

    }


    @Override
    public List<OrderBaseInfoDTO> selectListByMap(Map<String, Object> qryMap) {
        return orderBaseInfoService.selectListByMap(qryMap);
    }


    @Override
    public List<OrderBaseInfoDTO> qryRefundTimeOverdue() {
        return orderBaseInfoService.qryRefundTimeOverdue();
    }


    public void checkIsSodaUsed(String orderNo) throws Exception {
        Map<String,Object> map = new HashMap<>();
        map.put(SelectMapUtil.ORDER_NO,orderNo);
        List<OrderProductDTO> orderProductDTOList = orderProductDomainService.selectListByMap(map);
        List<String> cacheInfo = utilTools.getCacheInfo("SODA_USED_ORDER.usedType");
        CheckOpenUtil.checkIsSodaUsed(orderProductDTOList,cacheInfo,1);
    }

    @Override
    public BaseOrderDTO selectBaseByOrderNo(String orderNo) throws Exception{
        BaseOrderDTO baseOrderDTO=orderBaseInfoMapper.selectBaseByOrderNo(orderNo);
        if(baseOrderDTO==null){
            throw new BusinessException(ResultEnumCode.ORDER_NULL);
        }
        return baseOrderDTO;
    }
    @Override
    public BaseOrderDTO selectBaseOrder(String orderNo) throws Exception{
        return orderBaseInfoMapper.selectBaseByOrderNo(orderNo);
    }

    @Override
    public BaseOrderDTO selectBaseByOrderId(Long orderId) throws Exception{
        BaseOrderDTO baseOrderDTO=orderBaseInfoMapper.selectBaseByOrderId(orderId);
        if(baseOrderDTO==null){
            throw new BusinessException(ResultEnumCode.ORDER_NULL);
        }
        return baseOrderDTO;
    }

    public void cancelPayMethod(String orderNo,String payMethod){
        orderBaseInfoMapper.cancelPayMethod(orderNo,payMethod);
    }

    @Override
    public void testingOrder(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) {
        updateDeadLineTime(baseOrderDTO, operatorReq);
        addLogByTAT(baseOrderDTO, operatorReq);
    }

    @Override
    public void reportOrder(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) {
        updateDeadLineTime(baseOrderDTO, operatorReq);
        addLogByTAT(baseOrderDTO, operatorReq);
    }

    @Override
    public void updateOrderPendingOrUnPending(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) {
        Integer pendingState = operatorReq.getPendingState();
        if(!ValidationUtil.isEmpty(pendingState)){
            addOrderPendingOrPending(baseOrderDTO,operatorReq);
        }
    }

    @Override
    public List<OrderStateNumDTO> qryOrderStateNum(Map<String, Object> map) {
        return orderBaseInfoMapper.qryOrderStateNum(map);
    }

    @Override
    public List<OrderStateNumDTO> qryUsedUploadPaymentVoucher(Map<String, Object> map) {
        return orderBaseInfoMapper.qryUsedUploadPaymentVoucher(map);
    }

    @Override
    public List<OrderStateNumDTO> qryOrderReportDelay(Map<String, Object> map) {
        return orderBaseInfoMapper.qryOrderReportDelay(map);
    }

    @Override
    public List<OrderStateNumDTO> qryOrderRefundConfirmed(Map<String, Object> map) {
        return orderBaseInfoMapper.qryOrderRefundConfirmed(map);
    }

    @Override
    public OrderBaseInfoDTO qryLatestOrderByOperatorCodeIsNotNull(Long userId) {
        return orderBaseInfoMapper.qryLatestOrderByOperatorCodeIsNotNull(userId);
    }

    @Override
    public List<VOOrderDetail> checkChangePriceData(VOChangeOrderPrice changeOrderPrice, BaseOrderDTO baseOrderDTO) {
        List<VOOrderDetail> parentDetails = changeOrderPrice.getParentDetails();
        if(ValidationUtil.isEmpty(changeOrderPrice.getParentDetails()))
            throw new BusinessException(ResultEnumCode.PARAMS_NULL);


        Boolean isMonth = baseOrderDTO.getMonthPay() != 1 && baseOrderDTO.getMonthPay() != 3 ?false:true;
        if(!isMonth && (OrderPayStateEnum.PAY.getIndex()==baseOrderDTO.getPayState() || OrderPayStateEnum.TOPAY.getIndex()==baseOrderDTO.getPayState())){
            throw new BusinessException(ResultCode.STATE_IS_NULL);
        }
        if(String.valueOf(baseOrderDTO.getState()).equals(OrderStateEnum.CANCELLED.getIndex())){
            throw new BusinessException(ResultEnumCode.ORDER_STATE_ERROR);
        }

        //查询样品信息
        List<OrderSampleDTO> orderSampleDTOList = orderSampleDomainService.qryListByMap(baseOrderDTO.getOrderNo());
        List<OrderSampleRelateDTO> orderSampleRelateDTOS = orderSampleRelateDomainService.selectListByOrderNo(baseOrderDTO.getOrderNo(), null);

        List<OrderSampleDTO>  list = new ArrayList<>();
        //月结改价 && 是提交过申请表
        if(isMonth && baseOrderDTO.getState() >= Integer.parseInt(OrderStateEnum.WAITSEND.getIndex()) && baseOrderDTO.getState() != Integer.parseInt(OrderStateEnum.CANCELLED.getIndex())){
            //月结改价数据校验
            parentDetails.stream().forEach(voOrderDetail -> {

                //2.客服再待提交申请表状态下改价，客户已经提交了申请表，需要直接抛错
                if(!ValidationUtil.isEmpty( voOrderDetail.getOrderState()) &&
                        voOrderDetail.getOrderState() != baseOrderDTO.getState() &&
                        voOrderDetail.getOrderState() == Integer.parseInt(OrderStateEnum.WAITAPPLY.getIndex()))//状态不同直接提示
                    throw new BusinessException(ResultEnumCode.ORDER_CHANGE_PRICE_SAMPLE_BUY_NUMS_SUBMIT_ERROR);

                Integer buyNums = voOrderDetail.getBuyNums();//套餐数量
                List<OrderSampleDTO> sampleNameInfoList = voOrderDetail.getSampleNameInfoList();
                if(!ValidationUtil.isEmpty(sampleNameInfoList)){
                    list.addAll(sampleNameInfoList);
                    int sum = sampleNameInfoList.stream().mapToInt(OrderSampleDTO::getBuyNums).sum();//当前套餐关联的样品数量
                    if(buyNums != sum)//判断样品数量和套餐数量是否一致
                        throw new BusinessException(ResultEnumCode.ORDER_CHANGE_PRICE_BUY_NUMS_ERROR);

                }
            });

            if(!ValidationUtil.isEmpty(orderSampleDTOList)){
                //如果客服提交的数据是老的，但是客户已经提交申请表，这样就需要直接抛错
                //1.客服提交没有样品信息，客户提交了申请表，需要直接抛错
                if(ValidationUtil.isEmpty(list) && !ValidationUtil.isEmpty(orderSampleRelateDTOS))
                    throw new BusinessException(ResultEnumCode.ORDER_CHANGE_PRICE_SAMPLE_BUY_NUMS_SUBMIT_ERROR);


                }


                Map<String, Integer> stringIntegerMap = list.stream()
                        .collect(Collectors.groupingBy(OrderSampleDTO::getSampleNo,
                                Collectors.summingInt(OrderSampleDTO::getBuyNums)));

                for (OrderSampleDTO orderSampleDTO : list) {
                    Integer buyNums = stringIntegerMap.get(orderSampleDTO.getSampleNo());
                    if(buyNums == null ||  buyNums == 0){
                        throw new BusinessException(ResultEnumCode.ORDER_CHANGE_PRICE_SAMPLE_BUY_NUMS_ERROR);

                    }
                }

        }


        return parentDetails;
    }


    @Override
    public void updateAmountToBaseOrder(BaseOrderDTO baseOrderDTO, BOSysPerson personInfo, VOChangeOrderPrice changeOrderPrice, Map<String, String> flagMap) {
        updateFlagMap(baseOrderDTO,flagMap,changeOrderPrice);
        OrderBaseInfoDO orderBaseInfoDO = new OrderBaseInfoDO();
        OrderBaseInfo orderBaseInfo =  orderBaseInfoDO.copyBaseOrder(baseOrderDTO,personInfo,changeOrderPrice);
        baseOrderDTO.setRealAmount(orderBaseInfo.getRealAmount());//为了邮件中使用
        orderBaseInfoRepository.updateEntity(orderBaseInfo);
    }

    private void updateFlagMap(BaseOrderDTO baseOrderDTO, Map<String, String> flagMap, VOChangeOrderPrice changeOrderPrice) {
        //计算客服折扣率
        BigDecimal shopDisAmount = ValidationUtil.isEmpty(baseOrderDTO.getShopDisAmount())?BigDecimal.ZERO:baseOrderDTO.getShopDisAmount();
        BigDecimal discountAmount =ValidationUtil.isEmpty(baseOrderDTO.getDiscountAmount())?BigDecimal.ZERO:baseOrderDTO.getDiscountAmount();
        BigDecimal orderAmount =ValidationUtil.isEmpty(baseOrderDTO.getOrderAmount())?BigDecimal.ZERO:baseOrderDTO.getOrderAmount();
        BigDecimal realAmount = ValidationUtil.isEmpty(baseOrderDTO.getRealAmount()) ? BigDecimal.ZERO : baseOrderDTO.getRealAmount();
        BigDecimal serviceAmount = ValidationUtil.isEmpty(baseOrderDTO.getServiceAmount()) ? BigDecimal.ZERO : baseOrderDTO.getServiceAmount();
        BigDecimal subtract = realAmount.subtract(serviceAmount);
        BigDecimal divide = subtract.divide((orderAmount.subtract(shopDisAmount).subtract(discountAmount)), 6, BigDecimal.ROUND_HALF_UP);
        if(divide.compareTo(changeOrderPrice.getCsDisCountRate()) != 0){
            flagMap.put("serviceDiscount","ok");
        }
        //判断测试周期有没有修改
        if(changeOrderPrice.getIsUrgent() != (ValidationUtil.isEmpty(baseOrderDTO.getIsUrgent())?0:baseOrderDTO.getIsUrgent())){
            flagMap.put("updateUrgent","ok");
        }
    }


    private void sendMsg(Boolean isMonth, BaseOrderDTO baseOrderDTO) {
        TicOtherMailDTO ticOtherMailDTO = new  TicOtherMailDTO();
        OrderPriceReq orderPriceReq = new OrderPriceReq();
        orderPriceReq.setPrice(baseOrderDTO.getRealAmount().toString());
        ticOtherMailDTO.setOrderPriceReq(orderPriceReq);
        //发送邮件
        if(isMonth){
            eventMailUtil.sendTicMail(baseOrderDTO.getOrderId(), OiqMailEnum.TIC_SERVICE_CHANGE_PRICE_AB,1L,ticOtherMailDTO);
            eventSmsUtil.sendSMS(baseOrderDTO.getOrderId(), OiqSmsEnum.TIC_SERVICE_CHANGE_PRICE_AB);
        }else{
            eventMailUtil.sendTicMail(baseOrderDTO.getOrderId(), OiqMailEnum.TIC_SERVICE_CHANGE_PRICE,1L,ticOtherMailDTO);
            eventSmsUtil.sendSMS(baseOrderDTO.getOrderId(), OiqSmsEnum.TIC_SERVICE_CHANGE_PRICE);
        }
    }

    private Boolean saveLog(BaseOrderDTO baseOrderDTO, StringBuilder sb, BOSysPerson personInfo, BigDecimal realAmount, List<OrderLogAttachmentDTO> fileList) {
        Boolean isMonth = baseOrderDTO.getMonthPay() != 1 && baseOrderDTO.getMonthPay() != 3 ?false:true;
        OrderOperatorTypeEnum orderOperatorTypeEnum=isMonth? OrderOperatorTypeEnum.MONTH_CHANGE: OrderOperatorTypeEnum.CHANGEPRICE;
        orderOperatorLogService.addLogByPrice(new BaseLog(baseOrderDTO.getOrderNo(), String.valueOf(baseOrderDTO.getOrderType()), orderOperatorTypeEnum,
                sb.toString(), null, personInfo.getPersonCode(), 1), realAmount, baseOrderDTO.getRealAmount(), JSONObject.toJSONString(fileList));
        return isMonth;
    }


    private void addPriceLog(StringBuilder sb, BigDecimal oldRealAmount, BigDecimal realAmount, StringBuilder updatePriceReason, Integer isUrgent, VOChangeOrderPrice changeOrderPrice) {
        sb.append("修改前价格：");
        sb.append(oldRealAmount);
        sb.append(" 修改后价格：");
        sb.append(realAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
//        updatePriceReason.append(" 改价理由：");
        if(changeOrderPrice.getCsDiscountAmount().compareTo(BigDecimal.ZERO) ==0){
            updatePriceReason.append(" 折扣优惠;");
        }
        if(isUrgent > 0){
            updatePriceReason.append(" 加急;");
        }
    }


//    private BigDecimal calculatOneProductRealAmount(BigDecimal realAmount, BigDecimal totalPrice, BigDecimal otherOrderAmount,
//                                                    BigDecimal discountAmount, BigDecimal serviceAmount, List<OrderProductDTO> orderProductDTOS) {
//        //先计算优惠金额
//        BigDecimal productDiscount = totalPrice
//                .divide(otherOrderAmount, 8, BigDecimal.ROUND_HALF_UP)
//                .multiply(discountAmount);
//        //原始套餐分配的店铺优惠金额
//        BigDecimal subShopDisCountAmount = BigDecimal.ZERO;
//        if(!ValidationUtil.isEmpty(orderProductDTOS)){
//            subShopDisCountAmount = orderProductDTOS.get(0).getShopDisAmount();
//        }
//        //在计算加急费
//        BigDecimal subUrgentAmount = totalPrice
//                .divide(otherOrderAmount, 8, BigDecimal.ROUND_HALF_UP)
//                .multiply(serviceAmount);
//        return totalPrice.subtract(ValidationUtil.isEmpty(subShopDisCountAmount)?BigDecimal.ZERO:subShopDisCountAmount)
//                .subtract(productDiscount)
//                .add(subUrgentAmount)
//                .setScale(2, RoundingMode.HALF_UP);
//    }

    private void addOrderPendingOrPending(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) {
        String operatorText = ValidationUtil.isEmpty(operatorReq.getMemo())?"":" 原因:"+operatorReq.getMemo();
        OrderOperatorTypeEnum orderOperatorTypeEnum = (0 == operatorReq.getPendingState())  ? OrderOperatorTypeEnum.ORDER_UN_PENDING:(1 == operatorReq.getPendingState())?OrderOperatorTypeEnum.ORDER_PENDING:null;
        orderOperatorLogDomainService.addLog(baseOrderDTO,orderOperatorTypeEnum,
                ValidationUtil.isEmpty(operatorReq.getCsCode())?OperatorCode.PRE_ORDER.getName():operatorReq.getCsCode(),
                operatorText,1,
                ValidationUtil.isEmpty(operatorReq.getOperatorDate())?UseDateUtil.getDateString(new Date()):operatorReq.getOperatorDate(),
                "");
    }

    private void updateDeadLineTime(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq) {
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        orderBaseInfo.setDeadlineTime(operatorReq.getPreReportDate());
        updateOrderBaseInfo(orderBaseInfo);
    }

    public void addLogByTAT(BaseOrderDTO baseOrderDTO, OperatorReq operatorReq){

        String operatorText = "把" + TimeCalendarUtil.getStringToDateString(baseOrderDTO.getDeadlineTime()) + "改为" + StrUtil.timeStr(operatorReq.getPreReportDate()) +" 备注:"+
                (!ValidationUtil.isEmpty(operatorReq.getMemo())?operatorReq.getMemo():"");
        OrderOperatorTypeEnum orderOperatorTypeEnum = OrderOperatorTypeEnum.MOD_DEADLINE_TIME;
        orderOperatorLogDomainService.addLog(baseOrderDTO,orderOperatorTypeEnum,
                ValidationUtil.isEmpty(operatorReq.getCsCode())?OperatorCode.PRE_ORDER.getName():operatorReq.getCsCode(),
                operatorText,1,
                ValidationUtil.isEmpty(operatorReq.getOperatorDate())?UseDateUtil.getDateString(new Date()):operatorReq.getOperatorDate(),
                "");
    }

    @Override
    public void saveApplicationOrder(OiqOrderReqDTO oiqOrderReqDTO) throws Exception {
        OiqOrderReq oiqOrderReq = oiqOrderReqDTO.getOiqOrderReq();
        if (ValidationUtil.isEmpty(oiqOrderReqDTO.getUserDTO().getUserId())) {
            UserEmailDTO userEmailDTO = userInfoSV.qryUserByPhone(oiqOrderReq.getApplication().getLinkPhone());
            if (!ValidationUtil.isEmpty(userEmailDTO)) {
                oiqOrderReqDTO.getUserDTO().setUserId(userEmailDTO.getUserId());
            }
        }
        OrderBaseInfoDO orderBaseInfoDO = new OrderBaseInfoDO();
        //1询价单 2订单
        OrderBaseInfo orderBaseInfo1 = null;
        OrderBaseInfo orderBaseInfo2 = null;
        BaseOrderDTO oldBaseOrderDTO = oiqOrderReqDTO.getOrderDTO();
        if (oiqOrderReqDTO.getPortal()) {
            if (ValidationUtil.isEmpty(oiqOrderReqDTO.getBusinessLineDTO())) {
                throw new BusinessException(ResultEnumCode.ENUM_ERROR);
            }
            //业务线相关数据
            oiqOrderReqDTO.setLineId(oiqOrderReqDTO.getBusinessLineDTO().getConfigId());
            OrderReportDO orderReportDO = new OrderReportDO();
            oiqOrderReqDTO.setOrderReport(orderReportDO.getReportByReq(oiqOrderReq.getReport(), oiqOrderReqDTO.getOrderNo(), oiqOrderReqDTO));
            //虚拟询价单和订单(订单使用groupNo)
            orderBaseInfo2 = oiqOrderReqDTO.getAdd() ? orderBaseInfoDO.addOrderByApplication(oiqOrderReqDTO, OrderTypeEnum.OIQ_SMALL_ORDER) :
                    orderBaseInfoDO.updateOrderByApplication(oiqOrderReqDTO, OrderTypeEnum.OIQ_SMALL_ORDER);
            orderBaseInfo2.setSubState(oiqOrderReq.getType() == 0 ? OrderSubStateEnum.PORTAL_FLG.getIndex() : OrderSubStateEnum.NO_OPERATOR.getIndex());
            orderBaseInfoDO.companyInfo(oiqOrderReq, orderBaseInfo2);
            orderBaseInfo2.setCsEmail(orderBaseInfo2.getBusinessPersonEmail());
            setByBu(oiqOrderReqDTO, orderBaseInfo2);
            // 虚拟询价单和订单(订单使用groupNo)
            // 如果是提交的情况下 查历史有没有单子 没有的话新增 不做修改
            if (oiqOrderReqDTO.getOiqOrderReq().getType() != 0) {
                BaseOrderDTO baseOrderDTO = orderBaseInfoRepository.selectBaseByOrderNo(oiqOrderReqDTO.getInquiryOrderNo());
                if (ValidationUtil.isEmpty(baseOrderDTO)) {
                    orderBaseInfo1 = orderBaseInfoDO.addOrderByApplication(oiqOrderReqDTO, OrderTypeEnum.OIQ_SMALL_INQUIRY);
                    orderBaseInfo1.setCompanyName(orderBaseInfo2.getCompanyName());
                    oiqOrderReqDTO.setAddInquiry(1);
                    orderBaseInfo2.setRelateOrderNo(orderBaseInfo1.getOrderNo());
                }
            }
            orderBaseInfoDO.oiqOrderToDml(oiqOrderReqDTO, orderBaseInfo2);
            SysPersonDTO personDTO = oiqOrderReqDTO.getBusinessPerson();
            orderBaseInfo2.setOperatorCode("");
            if (!ValidationUtil.isEmpty(personDTO) && StringUtils.isNotBlank(personDTO.getPersonCode())) {
                if(oiqOrderReqDTO.getAddInquiry()==1){
                    orderBaseInfoDO.entityAddCsPerson(orderBaseInfo1,personDTO);
                }
                String operatorCode = ssoTemplateSV.getOrderCsCodeAttr(personDTO.getPersonCode());
                if (StringUtils.isNotBlank(operatorCode)) {
                    orderBaseInfo2.setOperatorCode(operatorCode);
                }
                if(oiqOrderReqDTO.getOiqOrderReq().getType()!=2){
                    orderBaseInfoDO.entityAddCsPerson(orderBaseInfo2,personDTO);
                }

            }
            oiqOrderReqDTO.getDmlMainReqDTO().setOrderNo(orderBaseInfo2.getOrderNo());
        } else {
            orderBaseInfo2 = new OrderBaseInfo();
            orderBaseInfo2.setOrderId(oldBaseOrderDTO.getOrderId());
            orderBaseInfo2.setStateDate(oiqOrderReqDTO.getDateStr());
            if (oiqOrderReqDTO.getOiqOrderReq().getType() != 0) {
                orderBaseInfo2.setState(BaseOrderStateEnum.WAITSEND.getIndex());
                if (oldBaseOrderDTO.getSubState() == OrderSubStateEnum.BACK.getIndex()) {
                    orderBaseInfo2.setSubState(OrderSubStateEnum.BACK_CONFIRM.getIndex());
                }
            }
        }
        //修改的时候 如果历史已经提交过样品寄送的话 状态跳过样品寄送
        if (!oiqOrderReqDTO.getAdd() && oiqOrderReqDTO.getOiqOrderReq().getType() != 0) {
            Integer hisState = oiqOrderReqDTO.getPortal() ? oldBaseOrderDTO.getState() : oldBaseOrderDTO.getHisState();
            if (hisState != null && BaseOrderStateEnum.WAITEXAMINE.getIndex() == hisState.intValue()) {
                orderBaseInfo2.setState(BaseOrderStateEnum.WAITEXAMINE.getIndex());
            }
        }

        if (oiqOrderReqDTO.getAdd()) {
            orderBaseInfo2.setIsPayReceived(0);
            orderBaseInfo2.setPayState(0);
            insertSelective(orderBaseInfo2);
        } else {
            updateByPrimaryKeySelective(orderBaseInfo2);
        }
        if (!ValidationUtil.isEmpty(orderBaseInfo1)) {
            orderBaseInfo1.setTmpGroupNo(orderBaseInfo1.getGroupNo());
            orderBaseInfo1.setGroupNo("");
            orderBaseInfo1.setConfirmOrderDate(UseDateUtil.getDateString(new Date()));
            SysPersonDTO sysPersonDTO = oiqOrderReqDTO.getBusinessPerson();
            orderBaseInfo1.setCsCode(sysPersonDTO.getPersonCode());
            orderBaseInfo1.setCsName(sysPersonDTO.getPersonName());
            orderBaseInfo1.setCsPhone(sysPersonDTO.getPersonPhone());
            orderBaseInfo1.setTestLabel(1);
            insertSelective(orderBaseInfo1);
            BaseOrderDTO baseOrderDTO = new BaseOrderDTO();
            baseCopyObj.copy(baseOrderDTO, orderBaseInfo1);
            baseOrderDTO.setOrderType(String.valueOf(orderBaseInfo1.getOrderType()));
            List<VOOrderAttribute> list = ssoTemplateSV.personAttr(baseOrderDTO, orderBaseInfo1.getTmpGroupNo());
            if (!ValidationUtil.isEmpty(list)) {
                List<VOOrderAttribute> insertList = list.stream().filter(a -> !(AttributeUtil.LAB_NAME.equals(a.getAttrValue()) || AttributeUtil.REPORT_LUA.equals(a.getAttrValue()) ||
                        AttributeUtil.REPORT_FORM.equals(a.getAttrValue()))).collect(Collectors.toList());
                //加实验室
                VOOrderAttribute labAttr = orderAttributeService.getVOOrderAttribute(orderBaseInfo1.getOrderNo(), orderBaseInfo1.getTmpGroupNo(),
                        AttributeUtil.LAB_NAME, orderBaseInfo2.getLabName(), String.valueOf(orderBaseInfo2.getLabId()), 1, UseDateUtil.getDateString(new Date()));
                VOOrderAttribute luaAttr = orderAttributeService.getVOOrderAttribute(orderBaseInfo1.getOrderNo(), orderBaseInfo1.getTmpGroupNo(),
                        AttributeUtil.REPORT_LUA, orderBaseInfo2.getReportLua(), orderBaseInfo2.getReportLuaCode(), 1, UseDateUtil.getDateString(new Date()));
                VOOrderAttribute formAttr = orderAttributeService.getVOOrderAttribute(orderBaseInfo1.getOrderNo(), orderBaseInfo1.getTmpGroupNo(),
                        AttributeUtil.REPORT_FORM, orderBaseInfo2.getReportForm(), orderBaseInfo2.getReportFormCode(), 1, UseDateUtil.getDateString(new Date()));

                insertList.add(labAttr);
                insertList.add(luaAttr);
                insertList.add(formAttr);
                orderAttributeService.insertForeach(insertList);
            }
            String csCode;
            if (!ValidationUtil.isEmpty(oiqOrderReq.getSysPerson()) && !ValidationUtil.isEmpty(oiqOrderReq.getSysPerson().getPersonCode())) {
                csCode = oiqOrderReq.getSysPerson().getPersonCode();
            } else {
                csCode = oiqOrderReqDTO.getUserDTO().getLogUserShow();
            }
            orderOperatorLogDomainService.addLog(baseOrderDTO, OrderOperatorTypeEnum.DEMAND, csCode);
            Long userId = oiqOrderReqDTO.getUserDTO().getUserId();
            if (userLabelSV.qryUserLabelFlg(userId, UserLabelCodeEnum.INQUIRY_REPURCHASE) == 0 &&
                    customLimitService.selectRepurchaseCount(userId, 0) > 0) {
                userLabelSV.saveUserLabel(userId, UserLabelCodeEnum.INQUIRY_REPURCHASE);
            }
            //申请表订单的虚拟询价单的业务线
            String code = oiqOrderReqDTO.getBusinessLineDTO().getConfigCode();
            if (StringUtils.isNotBlank(code)) {
                userLabelSV.saveUserLabel(userId, ListUtil.add(code));
            }
            oiqOrderReqDTO.getOiqOrderReq().setSaveOrderFlg(1);
            oiqOrderReqDTO.getOiqOrderReq().getApiOtherDTO().setInquiryOrderNo(orderBaseInfo1.getOrderNo());
        }
    }

    private void setByBu(OiqOrderReqDTO oiqOrderReqDTO, OrderBaseInfo orderBaseInfo) {
        if("2700".equals(oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBu())
            && oiqOrderReqDTO.getOiqOrderReq().getType() != 0){
            BaseOrderDTO baseOrderDTO = orderBaseInfoRepository.selectBaseByOrderNo(
                oiqOrderReqDTO.getOrderNo());
            if (Objects.isNull(baseOrderDTO) || ValidationUtil.isEmpty(baseOrderDTO.getPlatformOrder())) {
                orderBaseInfo.setPlatform("TIC");
                List<OrderApplicationAttrReq> destinationList = oiqOrderReqDTO.getOiqOrderReq()
                    .getDestinationList();
                if (!ValidationUtil.isEmpty(destinationList)) {
                    destinationList
                        .stream()
                        .filter(item -> OrderApplicationAttrDO.DESTINATION_COUNTRY.equals(item.getAttrCode())
                            && !ValidationUtil.isEmpty(item.getAttrValue()))
                        .map(OrderApplicationAttrReq::getAttrValue)
                        .findFirst()
                        .ifPresent(destinationCountry -> {
                            String tfsPlatFormOrder = orderUtilService.getTfsPlatFormOrder(
                                destinationCountry);
                            orderBaseInfo.setPlatformOrder(tfsPlatFormOrder);
                        });
                }
            }
        }
    }

    @Override
    public OrderBaseInfo updateOrderBaseInfoByDmlApplication(BaseOrderDTO baseOrderDTO, DmlApplicationDTO dmlApplicationDTO, CenterInfoDTO centerInfoDTO) {
        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        OrderBaseInfo orderBaseInfo=orderBaseInfoDO.updateByDmlApplication(baseOrderDTO,dmlApplicationDTO);
        //业务线
        orderBaseInfo.setLineId(centerInfoDTO.getCenterLineInfoDTO().getConfigId());
        orderBaseInfo.setBusinessLine(centerInfoDTO.getCenterLineInfoDTO().getConfigName());
        //实验室
        orderBaseInfo.setLabId(centerInfoDTO.getCenterLabInfoDTO().getLabId());
        orderBaseInfo.setLabName(centerInfoDTO.getCenterLabInfoDTO().getLabName());

        updateByPrimaryKeySelective(orderBaseInfo);
        return orderBaseInfo;
    }
    public OrderBaseInfo selectEntityByOrderNo(String orderNo) throws Exception{
        BaseOrderDTO baseOrderDTO=selectBaseByOrderNo(orderNo);
        OrderBaseInfo old=orderBaseInfoRepository.selectByPrimaryKey(baseOrderDTO.getOrderId());
        return old;
    }

    @Override
    public OrderBaseInfo copyInquiryOrderByOrderNo(OrderBaseInfo old,BOSysPerson person) throws Exception{
        String dateStr=UseDateUtil.getDateString(new Date());
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        baseCopyObj.copyWithNull(orderBaseInfo,old);
        orderBaseInfo.setLabId(null);
        orderBaseInfo.setLabName(null);
        orderBaseInfo.setCreateDate(dateStr);
        orderBaseInfo.setStateDate(dateStr);
        orderBaseInfo.setOfferDate(null);
        orderBaseInfo.setOrderExpDate(null);
        orderBaseInfo.setOrderId(null);
        orderBaseInfo.setCsCode(person.getPersonCode());
        orderBaseInfo.setCsPhone(person.getPersonPhone());
        orderBaseInfo.setCsName(person.getPersonName());
        orderBaseInfo.setCsEmail(person.getPersonMail());
        String newOrderNo=customCodeService.getInquiryOrderNo(old.getUserPhone(),old.getBu());
        String groupNo=customCodeService.getNewGroupNo();
        orderBaseInfo.setOrderNo(newOrderNo);
        orderBaseInfo.setTmpGroupNo(groupNo);
        orderBaseInfo.setGroupNo(null);
        orderBaseInfo.setState(BaseOrderStateEnum.DISTRIBUTION.getIndex());
        orderBaseInfo.setFromSource("orderCreate");
        orderBaseInfo.setCreateCode(person.getPersonCode());
        orderBaseInfo.setOrderSource(OrderSourceEnum.J.getIndex());
        orderBaseInfo.setLeadsCode("复制询价单");
        orderBaseInfo.setPromoInfo(null);
        if(old.getUserId()!=null){
            UserInfo userInfo=userInfoSV.selectByPrimaryKey(old.getUserId());
            orderBaseInfo.setUserId(userInfo.getUserId());
            orderBaseInfo.setUserName(userInfo.getUserNick());
            orderBaseInfo.setUserPhone(userInfo.getUserPhone());
            orderBaseInfo.setUserEmail(userInfo.getUserEmail());
            orderBaseInfo.setUserSex(userInfo.getUserSex());
        }

        insertSelective(orderBaseInfo);
        return orderBaseInfo;
    }


    private void updateByPrimaryKeySelective(OrderBaseInfo orderBaseInfo){
        orderBaseInfoRepository.updateByPrimaryKeySelective(orderBaseInfo);
    }

    private void insertSelective(OrderBaseInfo orderBaseInfo){
      orderBaseInfoRepository.insertSelective(orderBaseInfo);
    }

    @Override
    public Map<String,ToOrderDTO> getLastOrderNoByInquiry(List<String> relateOrderNoList){
        List<ToOrderDTO> list=orderBaseInfoMapper.getLastOrderNoByInquiry(relateOrderNoList);
        return list.stream().collect(Collectors.toMap(ToOrderDTO::getRelateOrderNo, Function.identity(), (key1, key2) -> key2));
    }

    @Override
    public BaseOrderDTO getBaseOrderDTO(OiqOrderInfoReq oiqOrderInfoReq) throws Exception {
        if (ValidationUtil.isEmpty(oiqOrderInfoReq.getOrderNo())) {
            return null;
        }
        BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(oiqOrderInfoReq.getOrderNo());
        oiqOrderInfoReq.setOldOrder(baseOrderDTO);
        if (ValidationUtil.isEmpty(baseOrderDTO.getUserId())) {
            return baseOrderDTO;
        }
        OrderApplicationFormDTO orderApplicationFormDTO=orderApplicationFormDomainService.selectDTOByOrderNo(oiqOrderInfoReq.getOrderNo());
        if(!ValidationUtil.isEmpty(orderApplicationFormDTO)){
            return baseOrderDTO;
        }
        if(OrderTypeEnum.OIQ_ORDER.getIndex().equals(baseOrderDTO.getOrderType())){
            //询价单的上一单逻辑
            String orderNoStr=orderApplicationFormDomainService.selectLastFormOrderNoByUserLaseVO(baseOrderDTO.getUserId(),baseOrderDTO.getBu(),OrderTypeEnum.OIQ_ORDER);
            oiqOrderInfoReq.setUseOld(true);
            if(StringUtils.isBlank(orderNoStr)){
                return baseOrderDTO;
            }
            return selectBaseByOrderNo(orderNoStr);
        }
        return null;
    }


    /**
    * 更新报价的单号或者最终邮件的单号
    * @param orderNo
    * @return com.sgs.ecom.order.dto.custom.BaseOrderDTO
    * <AUTHOR> || created at 2025/1/10 16:53
    * @throws Exception 抛出错误
    */
    @Override
    public BaseOrderDTO updateInfoInquiryToSendMailOrderNo(String orderNo) throws Exception{

        BaseOrderDTO inquiry=selectBaseByOrderNo(orderNo);
        if(!OrderTypeEnum.OIQ_SMALL_INQUIRY.getIndex().equals(inquiry.getOrderType())){
            return inquiry;
        }
        Map<String, ToOrderDTO> lastMap=getLastOrderNoByInquiry(Arrays.asList(orderNo));
        if(!lastMap.containsKey(orderNo)){
            return inquiry;
        }
        BaseOrderDTO orderDTO=selectBaseOrder(lastMap.get(orderNo).getOrderNo());
        return orderDTO;

    }
}
