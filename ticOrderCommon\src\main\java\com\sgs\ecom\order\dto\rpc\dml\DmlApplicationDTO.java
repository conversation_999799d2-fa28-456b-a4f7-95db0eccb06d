package com.sgs.ecom.order.dto.rpc.dml;

import com.sgs.ecom.order.request.base.OiqEventReq;
import com.sgs.ecom.order.request.oiq.OiqItemReq;

import java.util.List;

public class DmlApplicationDTO extends OiqEventReq {

    private String orderNo;
    private String platform;
    private String platformOrder;

    private BaseInfoRpcDTO baseInfo;

    private ApplicationInfoDTO applicationForm;

    private List<DmlDeliverDTO> delivers;

    private PayerInfoDTO payerInfo;

    private DmlInvoiceDTO invoice;

    private List<DmlOrderSampleDTO> samples;

    private List<OiqItemReq> items;

    private DmlMemoDTO memo;

    //自定义数据 自己使用
   // private CenterLineInfoDTO lineDTO;
    //private LabDTO labDTO;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPlatformOrder() {
        return platformOrder;
    }

    public void setPlatformOrder(String platformOrder) {
        this.platformOrder = platformOrder;
    }

    public BaseInfoRpcDTO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseInfoRpcDTO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public ApplicationInfoDTO getApplicationForm() {
        return applicationForm;
    }

    public void setApplicationForm(ApplicationInfoDTO applicationForm) {
        this.applicationForm = applicationForm;
    }

    public List<DmlDeliverDTO> getDelivers() {
        return delivers;
    }

    public void setDelivers(List<DmlDeliverDTO> delivers) {
        this.delivers = delivers;
    }

    public PayerInfoDTO getPayerInfo() {
        return payerInfo;
    }

    public void setPayerInfo(PayerInfoDTO payerInfo) {
        this.payerInfo = payerInfo;
    }

    public DmlInvoiceDTO getInvoice() {
        return invoice;
    }

    public void setInvoice(DmlInvoiceDTO invoice) {
        this.invoice = invoice;
    }



    public List<OiqItemReq> getItems() {
        return items;
    }

    public void setItems(List<OiqItemReq> items) {
        this.items = items;
    }

    public DmlMemoDTO getMemo() {
        return memo;
    }

    public void setMemo(DmlMemoDTO memo) {
        this.memo = memo;
    }



/*    public LabDTO getLabDTO() {
        return labDTO;
    }

    public void setLabDTO(LabDTO labDTO) {
        this.labDTO = labDTO;
    }*/

    public void setSamples(List<DmlOrderSampleDTO> samples) {
        this.samples = samples;
    }

    public List<DmlOrderSampleDTO> getSamples() {
        return samples;
    }
}
