package com.sgs.ecom.order.dto.dml;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DmlMainDTO {
	@JSONField(name="OIQNo")
	private String OIQNo;//orderNo
	private String labCode;//
	private String lineCode;

	private int acceptSubcon;//isTransfer 样品是否分包 0否 1是

	private String applicant;//companyNameCn
	private String applicantEn;//companyNameEn
	private String applicantAddress;//companyAddressCn
	private String applicantAddressEn;//companyAddressEn
	private String applicantBankName;//申请方开户行?
	private String applicantBankAccount;//申请方开户行账号?
	private String applicantContact;//linkPerson
	private String applicantContactEmail;//linkEmail
	private String applicantContactMobile;//linkPhone
	private String applicantTaxRegistrationNO;//申请方税号（企业客户必填）
	private int sameAsApplicant;//0：否1：是

	private String payer;//付款方名称
	private String payerEn;//付款方英文名称
	private String payerAddress;//付款方地址
	private String payerAddressEn;//付款方英文地址
	private String payerBankName;//付款方开户行
	private String payerBankAccount;//付款方开户行账号
	private String payerContact;//receiveName
	private String payerContactEmail;//receiveEmail
	private String payerContactMobile;//receivePhone
	private String payerTaxRegistrationNO;//付款方税号
	private String payerTel;//发票公司天皇
	private String applicantTel;
	private int dispute;//否 0
	private String disputeRemark;//""
	private int invoiceType;//发票类型0：增值税专用发票1：增值税普通发票
	private int invoiceExpress;//?发票快递至0：申请方1：付款方2：其他
	private String invoiceRecipient;
	private String invoiceRecipientTel;
	private String invoiceRecipientEmail;
	private String invoiceRecipientAddress;//发票地址不要了
	private String reportLanguage;//报告语言，多个,分割0：中文1：英文 2中文加英文
	private String reportType;//报告出具类型，多个,分割0：电子版1：纸质版 2电子加纸
	private int reportQty;
	private int reportProvide;//reportMethod
	private String reportProvideRemark;//reportMethodValue
	private String reportTitleType;//报告抬头类型，多个,分割0：申请方1：付款方2：其他（mor ）
	private String reportTitle;
	private String reportTitleEn;
	private String reportAddress;
	private String reportAddressEn;

	private String reportDeliver;//报告发送，多个,分割0：申请方1：付款方2：：其他  默认0
	private String reportRecipient;//报告收件人
	private String reportRecipientTel;//
	private String reportRecipientAddress;//
	private String reportRecipientEmail;
	private int sampleReturn;//?
	private String sampleRecipient;
	private String sampleRecipientTel;//
	private String sampleRecipientAddress;
	private Integer isUrgent;
	private String otherRequest;
	private String testRequest;
	private String ownerEmail;

	//samples;
	private List<DmlSampleDTO> samples;
	//测试项目
	private List<DmlItemDTO> testItems;
	//文件
	private List<Map> attachments=new ArrayList<>();


	public String getOIQNo() {
		return OIQNo;
	}

	public void setOIQNo(String OIQNo) {
		this.OIQNo = OIQNo;
	}

	public int getAcceptSubcon() {
		return acceptSubcon;
	}

	public void setAcceptSubcon(int acceptSubcon) {
		this.acceptSubcon = acceptSubcon;
	}

	public String getApplicant() {
		return applicant;
	}

	public void setApplicant(String applicant) {
		this.applicant = applicant;
	}

	public String getApplicantEn() {
		return applicantEn;
	}

	public void setApplicantEn(String applicantEn) {
		this.applicantEn = applicantEn;
	}

	public String getApplicantAddress() {
		return applicantAddress;
	}

	public void setApplicantAddress(String applicantAddress) {
		this.applicantAddress = applicantAddress;
	}

	public String getApplicantAddressEn() {
		return applicantAddressEn;
	}

	public void setApplicantAddressEn(String applicantAddressEn) {
		this.applicantAddressEn = applicantAddressEn;
	}

	public String getApplicantBankName() {
		return applicantBankName;
	}

	public void setApplicantBankName(String applicantBankName) {
		this.applicantBankName = applicantBankName;
	}

	public String getApplicantBankAccount() {
		return applicantBankAccount;
	}

	public void setApplicantBankAccount(String applicantBankAccount) {
		this.applicantBankAccount = applicantBankAccount;
	}

	public String getApplicantContact() {
		return applicantContact;
	}

	public void setApplicantContact(String applicantContact) {
		this.applicantContact = applicantContact;
	}

	public String getApplicantContactEmail() {
		return applicantContactEmail;
	}

	public void setApplicantContactEmail(String applicantContactEmail) {
		this.applicantContactEmail = applicantContactEmail;
	}

	public String getApplicantContactMobile() {
		return applicantContactMobile;
	}

	public void setApplicantContactMobile(String applicantContactMobile) {
		this.applicantContactMobile = applicantContactMobile;
	}

	public String getApplicantTaxRegistrationNO() {
		return applicantTaxRegistrationNO;
	}

	public void setApplicantTaxRegistrationNO(String applicantTaxRegistrationNO) {
		this.applicantTaxRegistrationNO = applicantTaxRegistrationNO;
	}

	public int getSameAsApplicant() {
		return sameAsApplicant;
	}

	public void setSameAsApplicant(int sameAsApplicant) {
		this.sameAsApplicant = sameAsApplicant;
	}

	public String getPayer() {
		return payer;
	}

	public void setPayer(String payer) {
		this.payer = payer;
	}

	public String getPayerEn() {
		return payerEn;
	}

	public void setPayerEn(String payerEn) {
		this.payerEn = payerEn;
	}

	public String getPayerAddress() {
		return payerAddress;
	}

	public void setPayerAddress(String payerAddress) {
		this.payerAddress = payerAddress;
	}

	public String getPayerAddressEn() {
		return payerAddressEn;
	}

	public void setPayerAddressEn(String payerAddressEn) {
		this.payerAddressEn = payerAddressEn;
	}

	public String getPayerBankName() {
		return payerBankName;
	}

	public void setPayerBankName(String payerBankName) {
		this.payerBankName = payerBankName;
	}

	public String getPayerBankAccount() {
		return payerBankAccount;
	}

	public void setPayerBankAccount(String payerBankAccount) {
		this.payerBankAccount = payerBankAccount;
	}

	public String getPayerContact() {
		return payerContact;
	}

	public void setPayerContact(String payerContact) {
		this.payerContact = payerContact;
	}

	public String getPayerContactEmail() {
		return payerContactEmail;
	}

	public void setPayerContactEmail(String payerContactEmail) {
		this.payerContactEmail = payerContactEmail;
	}

	public String getPayerContactMobile() {
		return payerContactMobile;
	}

	public void setPayerContactMobile(String payerContactMobile) {
		this.payerContactMobile = payerContactMobile;
	}

	public String getPayerTaxRegistrationNO() {
		return payerTaxRegistrationNO;
	}

	public void setPayerTaxRegistrationNO(String payerTaxRegistrationNO) {
		this.payerTaxRegistrationNO = payerTaxRegistrationNO;
	}

	public int getDispute() {
		return dispute;
	}

	public void setDispute(int dispute) {
		this.dispute = dispute;
	}

	public String getDisputeRemark() {
		return disputeRemark;
	}

	public void setDisputeRemark(String disputeRemark) {
		this.disputeRemark = disputeRemark;
	}

	public int getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(int invoiceType) {
		this.invoiceType = invoiceType;
	}

	public int getInvoiceExpress() {
		return invoiceExpress;
	}

	public void setInvoiceExpress(int invoiceExpress) {
		this.invoiceExpress = invoiceExpress;
	}

	public String getInvoiceRecipient() {
		return invoiceRecipient;
	}

	public void setInvoiceRecipient(String invoiceRecipient) {
		this.invoiceRecipient = invoiceRecipient;
	}

	public String getInvoiceRecipientTel() {
		return invoiceRecipientTel;
	}

	public void setInvoiceRecipientTel(String invoiceRecipientTel) {
		this.invoiceRecipientTel = invoiceRecipientTel;
	}

	public String getInvoiceRecipientAddress() {
		return invoiceRecipientAddress;
	}

	public void setInvoiceRecipientAddress(String invoiceRecipientAddress) {
		this.invoiceRecipientAddress = invoiceRecipientAddress;
	}

	public String getReportLanguage() {
		return reportLanguage;
	}

	public void setReportLanguage(String reportLanguage) {
		this.reportLanguage = reportLanguage;
	}

	public String getReportType() {
		return reportType;
	}

	public void setReportType(String reportType) {
		this.reportType = reportType;
	}

	public int getReportQty() {
		return reportQty;
	}

	public void setReportQty(int reportQty) {
		this.reportQty = reportQty;
	}

	public int getReportProvide() {
		return reportProvide;
	}

	public void setReportProvide(int reportProvide) {
		this.reportProvide = reportProvide;
	}

	public String getReportProvideRemark() {
		return reportProvideRemark;
	}

	public void setReportProvideRemark(String reportProvideRemark) {
		this.reportProvideRemark = reportProvideRemark;
	}

	public String getReportTitleType() {
		return reportTitleType;
	}

	public void setReportTitleType(String reportTitleType) {
		this.reportTitleType = reportTitleType;
	}

	public String getReportTitle() {
		return reportTitle;
	}

	public void setReportTitle(String reportTitle) {
		this.reportTitle = reportTitle;
	}

	public String getReportTitleEn() {
		return reportTitleEn;
	}

	public void setReportTitleEn(String reportTitleEn) {
		this.reportTitleEn = reportTitleEn;
	}

	public String getReportAddress() {
		return reportAddress;
	}

	public void setReportAddress(String reportAddress) {
		this.reportAddress = reportAddress;
	}

	public String getReportAddressEn() {
		return reportAddressEn;
	}

	public void setReportAddressEn(String reportAddressEn) {
		this.reportAddressEn = reportAddressEn;
	}

	public String getReportDeliver() {
		return reportDeliver;
	}

	public void setReportDeliver(String reportDeliver) {
		this.reportDeliver = reportDeliver;
	}

	public String getReportRecipient() {
		return reportRecipient;
	}

	public void setReportRecipient(String reportRecipient) {
		this.reportRecipient = reportRecipient;
	}

	public String getReportRecipientTel() {
		return reportRecipientTel;
	}

	public void setReportRecipientTel(String reportRecipientTel) {
		this.reportRecipientTel = reportRecipientTel;
	}

	public String getReportRecipientAddress() {
		return reportRecipientAddress;
	}

	public void setReportRecipientAddress(String reportRecipientAddress) {
		this.reportRecipientAddress = reportRecipientAddress;
	}

	public int getSampleReturn() {
		return sampleReturn;
	}

	public void setSampleReturn(int sampleReturn) {
		this.sampleReturn = sampleReturn;
	}

	public String getSampleRecipient() {
		return sampleRecipient;
	}

	public void setSampleRecipient(String sampleRecipient) {
		this.sampleRecipient = sampleRecipient;
	}

	public String getSampleRecipientTel() {
		return sampleRecipientTel;
	}

	public void setSampleRecipientTel(String sampleRecipientTel) {
		this.sampleRecipientTel = sampleRecipientTel;
	}

	public String getSampleRecipientAddress() {
		return sampleRecipientAddress;
	}

	public void setSampleRecipientAddress(String sampleRecipientAddress) {
		this.sampleRecipientAddress = sampleRecipientAddress;
	}


	public String getOtherRequest() {
		return otherRequest;
	}

	public void setOtherRequest(String otherRequest) {
		this.otherRequest = otherRequest;
	}

	public String getTestRequest() {
		return testRequest;
	}

	public void setTestRequest(String testRequest) {
		this.testRequest = testRequest;
	}

	public String getOwnerEmail() {
		return ownerEmail;
	}

	public void setOwnerEmail(String ownerEmail) {
		this.ownerEmail = ownerEmail;
	}


	public List<DmlItemDTO> getTestItems() {
		return testItems;
	}

	public void setTestItems(List<DmlItemDTO> testItems) {
		this.testItems = testItems;
	}

	public List<DmlSampleDTO> getSamples() {
		return samples;
	}

	public void setSamples(List<DmlSampleDTO> samples) {
		this.samples = samples;
	}

	public String getReportRecipientEmail() {
		return reportRecipientEmail;
	}

	public void setReportRecipientEmail(String reportRecipientEmail) {
		this.reportRecipientEmail = reportRecipientEmail;
	}

	public List<Map> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<Map> attachments) {
		this.attachments = attachments;
	}

	public String getLabCode() {
		return labCode;
	}

	public void setLabCode(String labCode) {
		this.labCode = labCode;
	}

	public String getLineCode() {
		return lineCode;
	}

	public void setLineCode(String lineCode) {
		this.lineCode = lineCode;
	}

	public Integer getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(Integer isUrgent) {
		this.isUrgent = isUrgent;
	}

	public String getPayerTel() {
		return payerTel;
	}

	public void setPayerTel(String payerTel) {
		this.payerTel = payerTel;
	}

	public String getApplicantTel() {
		return applicantTel;
	}

	public void setApplicantTel(String applicantTel) {
		this.applicantTel = applicantTel;
	}

	public String getInvoiceRecipientEmail() {
		return invoiceRecipientEmail;
	}

	public void setInvoiceRecipientEmail(String invoiceRecipientEmail) {
		this.invoiceRecipientEmail = invoiceRecipientEmail;
	}
}
