package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.request.operator.OrderOperatorReq;
import com.sgs.ecom.order.request.operator.OrderServiceRefundReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderPayService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/business/api.v2.order/orderRefund")
public class OrderRefundController extends ControllerUtil {
	@Autowired
	private IOrderPayService orderPayService;


	/**
	 * @Description : 客服审核客户发起的退款
	 * <AUTHOR>
	 * @Date  2023/5/19
	 * @param token:
	 * @param orderOperatorReq:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "confirmCustomerRefund", method = { RequestMethod.POST })
	public ResultBody confirmCustomerRefund (
			@RequestHeader(value="accessToken") String token,
			@RequestBody OrderOperatorReq orderOperatorReq)throws Exception{
		orderPayService.confirmCustomerRefund(orderOperatorReq,getPersonInfo(token));

		return ResultBody.success();
	}

	/**
	 * @Description :客服发起的退款
	 * <AUTHOR> Zhang
	 * @Date  2023/5/19
	 * @param token:
	 * @param orderServiceRefundReq:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "csRefund", method = { RequestMethod.POST })
	public ResultBody csRefund (
			@RequestHeader(value="accessToken") String token,
			@RequestBody OrderServiceRefundReq orderServiceRefundReq)throws Exception{
		orderPayService.csRefund(orderServiceRefundReq,getPersonInfo(token),"ORDER");
		return ResultBody.success();
	}


	/**
	 * @Description : 主单发起退款时校验子单的退款状态是否一致
	 * <AUTHOR> Zhang
	 * @Date  2023/6/13
	 * @param token: 
	 * @param orderServiceRefundReq: 
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "checkSubOrderRefundState", method = { RequestMethod.POST })
	public ResultBody checkSubOrderRefundState (
			@RequestHeader(value="accessToken") String token,
			@RequestBody OrderServiceRefundReq orderServiceRefundReq)throws Exception{
		return ResultBody.newInstance(orderPayService.checkSubOrderRefundState(orderServiceRefundReq,getPersonInfo(token)));

	}

}
