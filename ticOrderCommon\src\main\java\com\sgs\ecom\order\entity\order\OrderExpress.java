package com.sgs.ecom.order.entity.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;

public class OrderExpress {
 	private String receiptPerson;

 	private String orderNo;

 	private String expressCode;

 	private String preExpressDate;

 	private String receiptTown;
 	private Long labId;
 	private Long expressId;

 	private String sendProvice;

 	private String labName;

 	private String goodsName;
 	private Integer packageNums;

 	private String sendTown;
 	private Integer deliverType;

 	private String sendAddr;

 	private String sendCity;

 	private String stateDate;

 	private String sendPerson;

 	private String receiptProvice;

 	private String expressType;

 	private String createDate;
 	private BigDecimal goodsPrice;

 	private String expressNo;

 	private String receiptPhone;
 	private String receiptEmail;

 	private String receiptAddr;

 	private String sendPhone;

 	private String receiptCity;

	private Long addressId;

	private Integer payMethond;

	private String monthlyCard;

	private Integer state;

	private Long userId;

	private String receiptCompany;

	private String sendCompany;


	private Integer goodsType;

	private Integer isCs;

	private String memo;

	public String getReceiptEmail() {
		return receiptEmail;
	}

	public void setReceiptEmail(String receiptEmail) {
		this.receiptEmail = receiptEmail;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}





	public Integer getGoodsType() {
		return goodsType;
	}

	public void setGoodsType(Integer goodsType) {
		this.goodsType = goodsType;
	}

	public void setReceiptPerson(String receiptPerson){
 		 this.receiptPerson=receiptPerson;
 	}
 	public String getReceiptPerson(){
 		 return this.receiptPerson;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setExpressCode(String expressCode){
 		 this.expressCode=expressCode;
 	}
 	public String getExpressCode(){
 		 return this.expressCode;
 	}
 
 	 
 	public void setPreExpressDate(String preExpressDate){
 		 this.preExpressDate=preExpressDate;
 	}
	@JsonSerialize(using = TimeStringFormatSerializer.class)
 	public String getPreExpressDate(){
 		 return this.preExpressDate;
 	}
 
 	 
 	public void setReceiptTown(String receiptTown){
 		 this.receiptTown=receiptTown;
 	}
 	public String getReceiptTown(){
 		 return this.receiptTown;
 	}
 
 	 
 	public void setLabId(Long labId){
 		 this.labId=labId;
 	}
 	public Long getLabId(){
 		 return this.labId;
 	}
 
 	 
 	public void setExpressId(Long expressId){
 		 this.expressId=expressId;
 	}
 	public Long getExpressId(){
 		 return this.expressId;
 	}
 
 	 
 	public void setSendProvice(String sendProvice){
 		 this.sendProvice=sendProvice;
 	}
 	public String getSendProvice(){
 		 return this.sendProvice;
 	}
 
 	 
 	public void setLabName(String labName){
 		 this.labName=labName;
 	}
 	public String getLabName(){
 		 return this.labName;
 	}
 
 	 
 	public void setGoodsName(String goodsName){
 		 this.goodsName=goodsName;
 	}
 	public String getGoodsName(){
 		 return this.goodsName;
 	}
 
 	 
 	public void setPackageNums(Integer packageNums){
 		 this.packageNums=packageNums;
 	}
 	public Integer getPackageNums(){
 		 return this.packageNums;
 	}
 
 	 
 	public void setSendTown(String sendTown){
 		 this.sendTown=sendTown;
 	}
 	public String getSendTown(){
 		 return this.sendTown;
 	}
 
 	 
 	public void setDeliverType(Integer deliverType){
 		 this.deliverType=deliverType;
 	}
 	public Integer getDeliverType(){
 		 return this.deliverType;
 	}
 
 	 
 	public void setSendAddr(String sendAddr){
 		 this.sendAddr=sendAddr;
 	}
 	public String getSendAddr(){
 		 return this.sendAddr;
 	}
 
 	 
 	public void setSendCity(String sendCity){
 		 this.sendCity=sendCity;
 	}
 	public String getSendCity(){
 		 return this.sendCity;
 	}
 
 	 
 	public void setStateDate(String stateDate){
 		 this.stateDate=stateDate;
 	}
 	public String getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setSendPerson(String sendPerson){
 		 this.sendPerson=sendPerson;
 	}
 	public String getSendPerson(){
 		 return this.sendPerson;
 	}
 
 	 
 	public void setReceiptProvice(String receiptProvice){
 		 this.receiptProvice=receiptProvice;
 	}
 	public String getReceiptProvice(){
 		 return this.receiptProvice;
 	}
 
 	 
 	public void setExpressType(String expressType){
 		 this.expressType=expressType;
 	}
 	public String getExpressType(){
 		 return this.expressType;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}


	public BigDecimal getGoodsPrice() {
		return goodsPrice;
	}

	public void setGoodsPrice(BigDecimal goodsPrice) {
		this.goodsPrice = goodsPrice;
	}

	public void setExpressNo(String expressNo){
 		 this.expressNo=expressNo;
 	}
 	public String getExpressNo(){
 		 return this.expressNo;
 	}
 
 	 
 	public void setReceiptPhone(String receiptPhone){
 		 this.receiptPhone=receiptPhone;
 	}
 	public String getReceiptPhone(){
 		 return this.receiptPhone;
 	}
 
 	 
 	public void setReceiptAddr(String receiptAddr){
 		 this.receiptAddr=receiptAddr;
 	}
 	public String getReceiptAddr(){
 		 return this.receiptAddr;
 	}
 
 	 
 	public void setSendPhone(String sendPhone){
 		 this.sendPhone=sendPhone;
 	}
 	public String getSendPhone(){
 		 return this.sendPhone;
 	}
 
 	 
 	public void setReceiptCity(String receiptCity){
 		 this.receiptCity=receiptCity;
 	}
 	public String getReceiptCity(){
 		 return this.receiptCity;
 	}

	public Long getAddressId() {
		return addressId;
	}

	public void setAddressId(Long addressId) {
		this.addressId = addressId;
	}



	public Integer getPayMethond() {
		return payMethond;
	}

	public void setPayMethond(Integer payMethond) {
		this.payMethond = payMethond;
	}

	public String getMonthlyCard() {
		return monthlyCard;
	}

	public void setMonthlyCard(String monthlyCard) {
		this.monthlyCard = monthlyCard;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getReceiptCompany() {
		return receiptCompany;
	}

	public void setReceiptCompany(String receiptCompany) {
		this.receiptCompany = receiptCompany;
	}

	public String getSendCompany() {
		return sendCompany;
	}

	public void setSendCompany(String sendCompany) {
		this.sendCompany = sendCompany;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public Integer getIsCs() {
		return isCs;
	}

	public void setIsCs(Integer isCs) {
		this.isCs = isCs;
	}
}