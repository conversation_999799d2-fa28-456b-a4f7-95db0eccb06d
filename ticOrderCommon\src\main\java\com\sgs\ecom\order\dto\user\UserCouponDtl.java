package com.sgs.ecom.order.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.util.List;

public class UserCouponDtl {
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private List<SubUserCouponDTO> userCouponList;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private String reportAmount;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private String urgentAmount;
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseQryFilter.QueryDtl.class})
	private String amount;

	public String getReportAmount() {
		return reportAmount;
	}

	public void setReportAmount(String reportAmount) {
		this.reportAmount = reportAmount;
	}

	public String getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(String urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public String getAmount() {
		return amount;
	}

	public void setAmount(String amount) {
		this.amount = amount;
	}

	public List<SubUserCouponDTO> getUserCouponList() {
		return userCouponList;
	}

	public void setUserCouponList(List<SubUserCouponDTO> userCouponList) {
		this.userCouponList = userCouponList;
	}
}
