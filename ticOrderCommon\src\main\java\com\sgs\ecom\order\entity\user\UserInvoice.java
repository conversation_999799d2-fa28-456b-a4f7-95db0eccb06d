package com.sgs.ecom.order.entity.user;

import javax.persistence.Id;

public class UserInvoice {


    private String taxNo;

    private String frontImg;

    private String bankNumber;

    private String stateDate;

    private Long userId;

    private Integer state;

    private String backImg;
    @Id
    private Long invoiceId;

    private String createDate;

    private String regAddress;

    private Integer invoiceType;

    private String invoiceTitle;

    private Integer isDefault;

    private String regPhone;

    private String bankName;

    private Integer isForeign;
    private String linkPhone;
    private String linkPerson;
    private String linkEmail;
    private String invoiceUuid;
    private String bossNo;
    private String extendFiles;
    private String country;
    private String foreignCity;
    private String postCode;
    private String contact;



    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getForeignCity() {
        return foreignCity;
    }

    public void setForeignCity(String foreignCity) {
        this.foreignCity = foreignCity;
    }

    public String getPostCode() {
        return postCode;
    }

    public void setPostCode(String postCode) {
        this.postCode = postCode;
    }

    public void setTaxNo(String taxNo){
        this.taxNo=taxNo;
    }
    public String getTaxNo(){
        return this.taxNo;
    }


    public void setFrontImg(String frontImg){
        this.frontImg=frontImg;
    }
    public String getFrontImg(){
        return this.frontImg;
    }


    public void setBankNumber(String bankNumber){
        this.bankNumber=bankNumber;
    }
    public String getBankNumber(){
        return this.bankNumber;
    }


    public void setStateDate(String stateDate){
        this.stateDate=stateDate;
    }
    public String getStateDate(){
        return this.stateDate;
    }


    public void setUserId(Long userId){
        this.userId=userId;
    }
    public Long getUserId(){
        return this.userId;
    }


    public void setState(Integer state){
        this.state=state;
    }
    public Integer getState(){
        return this.state;
    }


    public void setBackImg(String backImg){
        this.backImg=backImg;
    }
    public String getBackImg(){
        return this.backImg;
    }


    public void setInvoiceId(Long invoiceId){
        this.invoiceId=invoiceId;
    }
    public Long getInvoiceId(){
        return this.invoiceId;
    }


    public void setCreateDate(String createDate){
        this.createDate=createDate;
    }
    public String getCreateDate(){
        return this.createDate;
    }


    public void setRegAddress(String regAddress){
        this.regAddress=regAddress;
    }
    public String getRegAddress(){
        return this.regAddress;
    }


    public void setInvoiceType(Integer invoiceType){
        this.invoiceType=invoiceType;
    }
    public Integer getInvoiceType(){
        return this.invoiceType;
    }


    public void setInvoiceTitle(String invoiceTitle){
        this.invoiceTitle=invoiceTitle;
    }
    public String getInvoiceTitle(){
        return this.invoiceTitle;
    }


    public void setIsDefault(Integer isDefault){
        this.isDefault=isDefault;
    }
    public Integer getIsDefault(){
        return this.isDefault;
    }


    public void setRegPhone(String regPhone){
        this.regPhone=regPhone;
    }
    public String getRegPhone(){
        return this.regPhone;
    }


    public void setBankName(String bankName){
        this.bankName=bankName;
    }
    public String getBankName(){
        return this.bankName;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getLinkEmail() {
        return linkEmail;
    }

    public void setLinkEmail(String linkEmail) {
        this.linkEmail = linkEmail;
    }

    public String getInvoiceUuid() {
        return invoiceUuid;
    }

    public void setInvoiceUuid(String invoiceUuid) {
        this.invoiceUuid = invoiceUuid;
    }

    public String getBossNo() {
        return bossNo;
    }

    public void setBossNo(String bossNo) {
        this.bossNo = bossNo;
    }

    public String getExtendFiles() {
        return extendFiles;
    }

    public void setExtendFiles(String extendFiles) {
        this.extendFiles = extendFiles;
    }

    public Integer getIsForeign() {
        return isForeign;
    }

    public void setIsForeign(Integer isForeign) {
        this.isForeign = isForeign;
    }

}
