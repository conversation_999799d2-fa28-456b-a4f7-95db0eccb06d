package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.BeanAnno;
import com.platform.annotation.CharacterVaild;
import com.platform.annotation.CheckAnno;
import com.platform.annotation.ExplainAnno;
import com.platform.util.json.DateFormatSerializer;
import com.platform.util.json.ScienceFormatSerializer;
import com.platform.util.json.TimeFormatSerializer;

import java.sql.Timestamp;
import java.util.Date;

@ExplainAnno(value="bean",name="账单信息")
public class BOBillInfo{
 
 	public static final String SEQUENCE = "BILL_ID"; 
  
 	public static final String BO_SQL = "TB_BILL_INFO"; 
 
 	public static final String OWNER ="member";

 	public static final String CUST_ID="custId";
 	public static final String ORDER_NUM="orderNum";
 	public static final String BILL_START_DATE="billStartDate";
 	public static final String CUSTOMER_NUMBER="customerNumber";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String COMPANY_NAME="companyName";
 	public static final String INVOICE_ID="invoiceId";
 	public static final String CREATE_DATE="createDate";
 	public static final String BILL_ID="billId";
 	public static final String BU="bu";
 	public static final String STORE_NAME="storeName";
 	public static final String PAY_STATE="payState";
 	public static final String BILL_CYCLE="billCycle";
 	public static final String BILL_DATE="billDate";
 	public static final String CUST_CODE="custCode";
 	public static final String INVOICE_TITLE="invoiceTitle";
 	public static final String PAYMENT_CODE="paymentCode";
 	public static final String BILL_AMOUNT="billAmount";
 	public static final String BILL_END_DATE="billEndDate";
	public static final String LAB_NAME="labName";
 	public static final String CATEGORY_NAME="categoryName";
 	public static final String CATEGORY_ID="categoryId";

 	public static final String LAB_ID="labId";

 	@BeanAnno("CUST_ID")
 	private long custId;
 	@BeanAnno("ORDER_NUM")
 	private long orderNum;
 	@BeanAnno("bill_start_date")
 	private Date billStartDate;
 	@BeanAnno("CUSTOMER_NUMBER")
 	private String customerNumber;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("COMPANY_NAME")
 	private String companyName;
 	@BeanAnno("INVOICE_ID")
 	private long invoiceId;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("BILL_ID")
 	private long billId;
 	@BeanAnno("bu")
 	private String bu;
 	@BeanAnno("STORE_NAME")
 	private String storeName;
 	@BeanAnno("PAY_STATE")
 	private int payState;
 	@BeanAnno("BILL_CYCLE")
 	private String billCycle;
 	@BeanAnno("BILL_DATE")
 	private Timestamp billDate;
 	@BeanAnno("CUST_CODE")
 	private String custCode;
 	@BeanAnno("INVOICE_TITLE")
 	private String invoiceTitle;
 	@BeanAnno("PAYMENT_CODE")
 	private String paymentCode;
 	@BeanAnno("BILL_AMOUNT")
 	private double billAmount;
 	@BeanAnno("bill_end_date")
 	private Date billEndDate;
 	@BeanAnno("CATEGORY_NAME")
 	private String categoryName;
	@BeanAnno("LAB_NAME")
	private String labName;
	@BeanAnno("CATEGORY_ID")
	private Long labId;
	@BeanAnno("LAB_ID")
	private Long categoryId;

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setOrderNum(long orderNum){
 		 this.orderNum=orderNum;
 	}
 	public long getOrderNum(){
 		 return this.orderNum;
 	}
 
 	 
 	public void setBillStartDate(Date billStartDate){
 		 this.billStartDate=billStartDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getBillStartDate(){
 		 return this.billStartDate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCustomerNumber(String customerNumber){
 		 this.customerNumber=customerNumber;
 	}
 	@CheckAnno(len = 100) 
 	public String getCustomerNumber(){
 		 return this.customerNumber;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBillId(long billId){
 		 this.billId=billId;
 	}
 	public long getBillId(){
 		 return this.billId;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	@CheckAnno(len = 50) 
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	@CheckAnno(len = 50) 
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	public void setPayState(int payState){
 		 this.payState=payState;
 	}
 	public int getPayState(){
 		 return this.payState;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setBillCycle(String billCycle){
 		 this.billCycle=billCycle;
 	}
 	@CheckAnno(len = 20) 
 	public String getBillCycle(){
 		 return this.billCycle;
 	}
 
 	 
 	public void setBillDate(Timestamp billDate){
 		 this.billDate=billDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getBillDate(){
 		 return this.billDate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	@CheckAnno(len = 50) 
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	@CheckAnno(len = 500) 
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setPaymentCode(String paymentCode){
 		 this.paymentCode=paymentCode;
 	}
 	@CheckAnno(len = 20) 
 	public String getPaymentCode(){
 		 return this.paymentCode;
 	}
 
 	 
 	public void setBillAmount(double billAmount){
 		 this.billAmount=billAmount;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getBillAmount(){
 		 return this.billAmount;
 	}
 
 	 
 	public void setBillEndDate(Date billEndDate){
 		 this.billEndDate=billEndDate;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getBillEndDate(){
 		 return this.billEndDate;
 	}
 
 	 
}