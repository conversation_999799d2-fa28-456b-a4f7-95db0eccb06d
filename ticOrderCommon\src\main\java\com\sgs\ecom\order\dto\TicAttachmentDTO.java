package com.sgs.ecom.order.dto; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 

public class TicAttachmentDTO{
 
 	public static final String CREATE_SQL = "select CreatedBy,FileName,SystemID,AttType,OrderID,ModifiedBy,ModifiedDate,CloudID,FileUrl,BUID,CreatedDate,FileID,ID from TIC_Attachment"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATEDBY", getName="getCreatedby", setName="setCreatedby")
 	private String createdby;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="FILENAME", getName="getFilename", setName="setFilename")
 	private String filename;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SYSTEMID", getName="getSystemid", setName="setSystemid")
 	private String systemid;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="ATTTYPE", getName="getAtttype", setName="setAtttype")
 	private String atttype;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="ORDERID", getName="getOrderid", setName="setOrderid")
 	private String orderid;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="MODIFIEDBY", getName="getModifiedby", setName="setModifiedby")
 	private String modifiedby;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="MODIFIEDDATE", getName="getModifieddate", setName="setModifieddate")
 	private Timestamp modifieddate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CLOUDID", getName="getCloudid", setName="setCloudid")
 	private String cloudid;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="FILEURL", getName="getFileurl", setName="setFileurl")
 	private String fileurl;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="BUID", getName="getBuid", setName="setBuid")
 	private String buid;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CREATEDDATE", getName="getCreateddate", setName="setCreateddate")
 	private Timestamp createddate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="FILEID", getName="getFileid", setName="setFileid")
 	private String fileid;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="ID", getName="getId", setName="setId")
 	private String id;

 	public void setCreatedby(String createdby){
 		 this.createdby=createdby;
 	}
 	public String getCreatedby(){
 		 return this.createdby;
 	}
 
 	 
 	public void setFilename(String filename){
 		 this.filename=filename;
 	}
 	public String getFilename(){
 		 return this.filename;
 	}
 
 	 
 	public void setSystemid(String systemid){
 		 this.systemid=systemid;
 	}
 	public String getSystemid(){
 		 return this.systemid;
 	}
 
 	 
 	public void setAtttype(String atttype){
 		 this.atttype=atttype;
 	}
 	public String getAtttype(){
 		 return this.atttype;
 	}
 
 	 
 	public void setOrderid(String orderid){
 		 this.orderid=orderid;
 	}
 	public String getOrderid(){
 		 return this.orderid;
 	}
 
 	 
 	public void setModifiedby(String modifiedby){
 		 this.modifiedby=modifiedby;
 	}
 	public String getModifiedby(){
 		 return this.modifiedby;
 	}
 
 	 
 	public void setModifieddate(Timestamp modifieddate){
 		 this.modifieddate=modifieddate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getModifieddate(){
 		 return this.modifieddate;
 	}
 
 	 
 	public void setCloudid(String cloudid){
 		 this.cloudid=cloudid;
 	}
 	public String getCloudid(){
 		 return this.cloudid;
 	}
 
 	 
 	public void setFileurl(String fileurl){
 		 this.fileurl=fileurl;
 	}
 	public String getFileurl(){
 		 return this.fileurl;
 	}
 
 	 
 	public void setBuid(String buid){
 		 this.buid=buid;
 	}
 	public String getBuid(){
 		 return this.buid;
 	}
 
 	 
 	public void setCreateddate(Timestamp createddate){
 		 this.createddate=createddate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateddate(){
 		 return this.createddate;
 	}
 
 	 
 	public void setFileid(String fileid){
 		 this.fileid=fileid;
 	}
 	public String getFileid(){
 		 return this.fileid;
 	}
 
 	 
 	public void setId(String id){
 		 this.id=id;
 	}
 	public String getId(){
 		 return this.id;
 	}
 
 	 
}