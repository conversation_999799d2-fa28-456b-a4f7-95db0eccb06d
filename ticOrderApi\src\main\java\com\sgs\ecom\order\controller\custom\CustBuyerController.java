package com.sgs.ecom.order.controller.custom;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.request.cust.CustBuyerReq;
import com.sgs.ecom.order.service.custom.interfaces.ICustBuyerService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/business/api.v1.cust/buyer")
public class CustBuyerController extends ControllerUtil {

	@Resource
	private ICustBuyerService custBuyerService;

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryList", method = {RequestMethod.POST})
	public ResultBody qryList(
			@RequestHeader(value="system") String system,
			@RequestBody CustBuyerReq custBuyerReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(custBuyerService.qryList(custBuyerReq, getPersonInfo(token), getPower(token,system)));
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryDetail", method = {RequestMethod.POST})
	public ResultBody qryDetail(
			@RequestHeader(value="system") String system,
			@RequestBody CustBuyerReq custBuyerReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(custBuyerService.qryDetail(custBuyerReq, getPersonInfo(token), getPower(token,system)));
	}



	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "saveBuyer", method = {RequestMethod.POST})
	public ResultBody saveBuyer(
			@RequestHeader(value="system") String system,
			@Validated(BaseBean.Insert.class)
			@RequestBody CustBuyerReq custBuyerReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		custBuyerService.saveCustBuyer(custBuyerReq, getPersonInfo(token), getPower(token,system));
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "delBuyer", method = {RequestMethod.POST})
	public ResultBody delBuyer(
			@RequestHeader(value="system") String system,
			@RequestBody CustBuyerReq custBuyerReq,
			@RequestHeader(value = "accessToken") String token) throws Exception {
		custBuyerService.delCustBuyer(custBuyerReq, getPersonInfo(token), getPower(token,system));
		return ResultBody.success();
	}
}
