package com.sgs.ecom.order.dto.user;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class UserPromotionDTO extends BaseQryFilter {

	@ApiAnno(groups={Default.class})
	private Long activityId;
	@ApiAnno(groups={Default.class})
	private String userPhone;
	@ApiAnno(groups={Default.class})
	private String userEmail;
	@ApiAnno(groups={Default.class})
	private String activityCode;
	@ApiAnno(groups={Default.class})
	private int state;
	@ApiAnno(groups={Default.class})
	private Long userId;
	@ApiAnno(groups={Default.class})
	private List<UserPromotionAttrDTO> attrDTOList;
	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	private String createDate;
	@ApiAnno(groups={Default.class})
	private String promotionCode;
	@ApiAnno(groups={Default.class})
	private String personCode;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class})
	private String stateDate;
	@ApiAnno(groups={Default.class})
	private String userName;
	/**
	 * 用户微信
	 */
	@ApiAnno(groups={Default.class})
	private String userWx;
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String industry;//行业信息
	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String mainProduct;//主营产品

	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
	private String managerPersonCode;//线上业务顾问信息

	@ApiAnno(groups={Default.class,QueryList.class})
	private List<UserAccountManagerDTO> userAccountManagerDTOList;

	public String getManagerPersonCode() {
		return managerPersonCode;
	}

	public void setManagerPersonCode(String managerPersonCode) {
		this.managerPersonCode = managerPersonCode;
	}

	public List<UserAccountManagerDTO> getUserAccountManagerDTOList() {
		return userAccountManagerDTOList;
	}

	public void setUserAccountManagerDTOList(List<UserAccountManagerDTO> userAccountManagerDTOList) {
		this.userAccountManagerDTOList = userAccountManagerDTOList;
	}

	public String getIndustry() {
		return industry;
	}

	public void setIndustry(String industry) {
		this.industry = industry;
	}

	public String getMainProduct() {
		return mainProduct;
	}

	public void setMainProduct(String mainProduct) {
		this.mainProduct = mainProduct;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getActivityCode() {
		return activityCode;
	}

	public void setActivityCode(String activityCode) {
		this.activityCode = activityCode;
	}



	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public List<UserPromotionAttrDTO> getAttrDTOList() {
		return attrDTOList;
	}

	public void setAttrDTOList(List<UserPromotionAttrDTO> attrDTOList) {
		this.attrDTOList = attrDTOList;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getPromotionCode() {
		return promotionCode;
	}

	public void setPromotionCode(String promotionCode) {
		this.promotionCode = promotionCode;
	}

	public String getPersonCode() {
		return personCode;
	}

	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}
}
