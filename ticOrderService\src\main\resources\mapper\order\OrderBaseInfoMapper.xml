<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper">
    <resultMap id="BaseResultMap" type="com.sgs.ecom.order.vo.VOOrderBaseInfo">
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="QUESTION_ID" property="questionId" jdbcType="BIGINT"/>
        <result column="LAB_ID" property="labId" jdbcType="BIGINT"/>
        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="PAY_DATE" property="payDate" jdbcType="TIMESTAMP"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
        <result column="PLATFORM" property="platform" jdbcType="VARCHAR"/>
        <result column="PLATFORM_ORDER" property="platformOrder" jdbcType="VARCHAR"/>
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR"/>
        <result column="CS_BRANCH" property="csBranch" jdbcType="VARCHAR"/>
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL"/>
        <result column="OFFER_DATE" property="offerDate" jdbcType="TIMESTAMP"/>
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="TIMESTAMP"/>
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR"/>
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="TINYINT"/>
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="USER_SEX" property="userSex" jdbcType="TINYINT"/>
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR"/>
        <result column="IS_READ" property="isRead" jdbcType="TINYINT"/>
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR"/>
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR"/>
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR"/>
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER"/>
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR"/>
        <result column="LINE_ID" property="lineId" jdbcType="BIGINT"/>
        <result column="APPLICATION_LINE_ID" property="applicationLineId" jdbcType="BIGINT"/>
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="TMP_GROUP_NO" property="tmpGroupNo" jdbcType="VARCHAR"/>
        <result column="IS_REMIND" property="isRemind" jdbcType="TINYINT"/>
        <result column="CS_NAME_EN" property="csNameEn" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="CATAGORY_ID" property="catagoryId" jdbcType="BIGINT"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER"/>
        <result column="PRODUCT_IMG" property="productImg" jdbcType="VARCHAR"/>
        <result column="IS_TEST" property="isTest" jdbcType="TINYINT"/>
        <result column="IS_INVOICE" property="isInvoice" jdbcType="TINYINT"/>
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA_CODE" property="reportLuaCode" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM_CODE" property="reportFormCode" jdbcType="VARCHAR"/>
        <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER"/>
        <result column="IS_BILL" property="isBill" jdbcType="TINYINT"/>
        <result column="ABSTRACT_CUSTCODE" property="abstractCustcode" jdbcType="VARCHAR"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="orderStateNumMap" type="com.sgs.ecom.order.dto.order.OrderStateNumDTO">
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="STATE_COUNT" property="stateCount" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        ORDER_ID
        , ORDER_NO, ORDER_TYPE, ORDER_AMOUNT, QUESTION_ID, LAB_ID, LAB_NAME, DISCOUNT_AMOUNT,
    REAL_AMOUNT, STATE, PAY_STATE, USER_ID, USER_PHONE, USER_NAME, USER_EMAIL, COMPANY_NAME,
    PROVINCE, CITY, RELATE_ORDER_NO, PAY_DATE, CREATE_DATE, STATE_DATE, PLATFORM, PLATFORM_ORDER,
    CS_CODE, CS_BRANCH, SERVICE_AMOUNT, OFFER_DATE, ORDER_EXP_DATE, REPORT_LUA, REPORT_FORM,
    TEST_CYCLE, IS_URGENT, CATEGORY_PATH, BU, USER_SEX, CS_EMAIL, IS_READ, RECOMMEND_REASON,
    SAMPLE_REQUIREMENTS, GROUP_NO, HIS_STATE, CS_NAME, LINE_ID, APPLICATION_LINE_ID, BUSINESS_LINE, URGENT_AMOUNT,
    TMP_GROUP_NO, IS_REMIND, CS_NAME_EN, PRODUCT_NAME, CATAGORY_ID, SUB_STATE, TOTAL_NUMS,
    PRODUCT_IMG, IS_TEST, IS_INVOICE, AUDIT_CODE, REPORT_LUA_CODE, REPORT_FORM_CODE,
    IS_DELETE, IS_PAY_RECEIVED, PAY_METHOD, IS_BILL,IS_ELECTRON
    </sql>
    <select id="selectVOByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from ORDER_BASE_INFO
        where ORDER_ID = #{orderId,jdbcType=BIGINT}
    </select>
    <select id="qryMonthPayOrder" resultMap="resultDTO">
        select ORDER_NO,USER_NAME,USER_PHONE,USER_EMAIL,REAL_AMOUNT,ABSTRACT_CUSTCODE,STATE_DATE,ORDER_TYPE,
		(select STORE_ID from ORDER_PRODUCT op where obi.ORDER_NO=op.ORDER_NO and op.state = 1 limit 1) STORE_ID,
          (SELECT max( ( CASE oaa.ATTR_CODE WHEN 'catName' THEN oaa.ATTR_VALUE ELSE '' END ) )from order_application_attr oaa where oaa.STATE = 1 and oaa.order_no =obi.ORDER_NO) AS CATEGORY_NAME,
                 (SELECT max( ( CASE oaa.ATTR_CODE WHEN 'labName' THEN oaa.ATTR_VALUE ELSE '' END ) )from order_application_attr oaa where oaa.STATE = 1 and oaa.order_no =obi.ORDER_NO) AS LAB_NAME,
obi.LAB_ID,
        obi.CATAGORY_ID
        from ORDER_BASE_INFO obi
        where STATE_DATE between #{startDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
        and IS_TEST = 0
        and MONTH_PAY = 3
        and STATE = 80
        and ORDER_TYPE in (100000, 101000)
        and IS_BILL = 0
        and ABSTRACT_CUSTCODE is NOT NULL

    </select>


    <update id="updateVOByPrimaryKeySelective" parameterType="com.sgs.ecom.order.vo.VOOrderBaseInfo">
        update ORDER_BASE_INFO
        <set>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                ORDER_TYPE = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="questionId != null">
                QUESTION_ID = #{questionId,jdbcType=BIGINT},
            </if>
            <if test="labId != null">
                LAB_ID = #{labId,jdbcType=BIGINT},
            </if>
            <if test="labName != null">
                LAB_NAME = #{labName,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                DISCOUNT_AMOUNT = #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=INTEGER},
            </if>
            <if test="payState != null">
                PAY_STATE = #{payState,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userPhone != null">
                USER_PHONE = #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                USER_NAME = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userEmail != null">
                USER_EMAIL = #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="relateOrderNo != null">
                RELATE_ORDER_NO = #{relateOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payDate != null">
                PAY_DATE = #{payDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                STATE_DATE = #{stateDate},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="platformOrder != null">
                PLATFORM_ORDER = #{platformOrder,jdbcType=VARCHAR},
            </if>
            <if test="csCode != null">
                CS_CODE = #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csBranch != null">
                CS_BRANCH = #{csBranch,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="offerDate != null">
                OFFER_DATE = #{offerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderExpDate != null">
                ORDER_EXP_DATE = #{orderExpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportLua != null">
                REPORT_LUA = #{reportLua,jdbcType=VARCHAR},
            </if>
            <if test="reportForm != null">
                REPORT_FORM = #{reportForm,jdbcType=VARCHAR},
            </if>
            <if test="testCycle != null">
                TEST_CYCLE = #{testCycle,jdbcType=DECIMAL},
            </if>
            <if test="isUrgent != null">
                IS_URGENT = #{isUrgent,jdbcType=TINYINT},
            </if>
            <if test="categoryPath != null">
                CATEGORY_PATH = #{categoryPath,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                BU = #{bu,jdbcType=VARCHAR},
            </if>
            <if test="userSex != null">
                USER_SEX = #{userSex,jdbcType=TINYINT},
            </if>
            <if test="csEmail != null">
                CS_EMAIL = #{csEmail,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null">
                IS_READ = #{isRead,jdbcType=TINYINT},
            </if>
            <if test="recommendReason != null">
                RECOMMEND_REASON = #{recommendReason,jdbcType=VARCHAR},
            </if>
            <if test="recommendReasonImage != null">
                RECOMMEND_REASON_IMAGE = #{recommendReasonImage,jdbcType=VARCHAR},
            </if>
            <if test="sampleRequirements != null">
                SAMPLE_REQUIREMENTS = #{sampleRequirements,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                GROUP_NO = #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="hisState != null">
                HIS_STATE = #{hisState,jdbcType=INTEGER},
            </if>
            <if test="csName != null">
                CS_NAME = #{csName,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                LINE_ID = #{lineId,jdbcType=BIGINT},
            </if>
            <if test="applicationLineId != null">
                APPLICATION_LINE_ID = #{applicationLineId,jdbcType=BIGINT},
            </if>
            <if test="businessLine != null">
                BUSINESS_LINE = #{businessLine,jdbcType=VARCHAR},
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="tmpGroupNo != null">
                TMP_GROUP_NO = #{tmpGroupNo,jdbcType=VARCHAR},
            </if>
            <if test="isRemind != null">
                IS_REMIND = #{isRemind,jdbcType=TINYINT},
            </if>
            <if test="csNameEn != null">
                CS_NAME_EN = #{csNameEn,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="catagoryId != null">
                CATAGORY_ID = #{catagoryId,jdbcType=BIGINT},
            </if>
            <if test="subState != null">
                SUB_STATE = #{subState,jdbcType=INTEGER},
            </if>
            <if test="refundState != null">
                REFUND_STATE = #{refundState,jdbcType=INTEGER},
            </if>
            <if test="totalNums != null">
                TOTAL_NUMS = #{totalNums,jdbcType=INTEGER},
            </if>
            <if test="productImg != null">
                PRODUCT_IMG = #{productImg,jdbcType=VARCHAR},
            </if>
            <if test="isTest != null">
                IS_TEST = #{isTest,jdbcType=TINYINT},
            </if>
            <if test="isInvoice != null">
                IS_INVOICE = #{isInvoice,jdbcType=TINYINT},
            </if>
            <if test="auditCode != null">
                AUDIT_CODE = #{auditCode,jdbcType=VARCHAR},
            </if>
            <if test="reportLuaCode != null">
                REPORT_LUA_CODE = #{reportLuaCode,jdbcType=VARCHAR},
            </if>
            <if test="reportFormCode != null">
                REPORT_FORM_CODE = #{reportFormCode,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="isPayReceived != null">
                IS_PAY_RECEIVED = #{isPayReceived,jdbcType=TINYINT},
            </if>
            <if test="testCycleMemo != null">
                TEST_CYCLE_MEMO = #{testCycleMemo},
            </if>
            <if test="leadsCode != null">
                LEADS_CODE = #{leadsCode},
            </if>
            <if test="orderSource != null">
                ORDER_SOURCE = #{orderSource},
            </if>
            <if test="operatorCode!= null">
                OPERATOR_CODE = #{operatorCode},
            </if>
            <if test="applySubmitDate!= null">
                APPLY_SUBMIT_DATE = #{applySubmitDate},
            </if>
            <if test="testLabel!= null">
                TEST_LABEL = #{testLabel},
            </if>
            <if test="closeReason!= null">
                CLOSE_REASON = #{closeReason},
            </if>
            <if test="closeCode!= null">
                CLOSE_CODE = #{closeCode},
            </if>
            <if test="deadlineTime!= null">
                DEADLINE_TIME = #{deadlineTime},
            </if>
            <if test="noticeNum!= null">
                NOTICE_NUM = #{noticeNum},
            </if>
            <if test="isFinish!= null">
                IS_FINISH = #{isFinish},
            </if>
            <if test="isFinish!= null">
                IS_FINISH = #{isFinish},
            </if>
            <if test="isFinish!= null">
                IS_FINISH = #{isFinish},
            </if>
            <if test="currency!= null">
                CURRENCY = #{currency},
            </if>
            <if test="exchangeRate!= null">
                EXCHANGE_RATE = #{exchangeRate},
            </if>
            <if test="slimNo!= null">
                SLIM_NO = #{slimNo},
            </if>
            <if test="platformOrderNo!= null">
                PLATFORM_ORDER_NO = #{platformOrderNo},
            </if>
            <if test="platformAmount!= null">
                PLATFORM_AMOUNT = #{platformAmount},
            </if>
            <if test="downReport!= null">
                PLATFORM_AMOUNT = #{platformAmount},
            </if>
            <if test="salesCode != null">
                SALES_CODE = #{salesCode},
            </if>
            <if test="isElectron != null">
                IS_ELECTRON = #{isElectron},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=BIGINT}
    </update>

    <update id="updateOrderBaseInfo" parameterType="com.sgs.ecom.order.entity.order.OrderBaseInfo">
        update ORDER_BASE_INFO
        <set>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                ORDER_TYPE = #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT = #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="questionId != null">
                QUESTION_ID = #{questionId,jdbcType=BIGINT},
            </if>
            <if test="labId != null">
                LAB_ID = #{labId,jdbcType=BIGINT},
            </if>
            <if test="labName != null">
                LAB_NAME = #{labName,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                DISCOUNT_AMOUNT = #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT = #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                STATE = #{state,jdbcType=INTEGER},
            </if>
            <if test="payState != null">
                PAY_STATE = #{payState,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                USER_ID = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userPhone != null">
                USER_PHONE = #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                USER_NAME = #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userEmail != null">
                USER_EMAIL = #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                COMPANY_NAME = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="relateOrderNo != null">
                RELATE_ORDER_NO = #{relateOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payDate != null">
                PAY_DATE = #{payDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                STATE_DATE = #{stateDate},
            </if>
            <if test="platform != null">
                PLATFORM = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="platformOrder != null">
                PLATFORM_ORDER = #{platformOrder,jdbcType=VARCHAR},
            </if>
            <if test="csCode != null">
                CS_CODE = #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csBranch != null">
                CS_BRANCH = #{csBranch,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="offerDate != null">
                OFFER_DATE = #{offerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderExpDate != null">
                ORDER_EXP_DATE = #{orderExpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportLua != null">
                REPORT_LUA = #{reportLua,jdbcType=VARCHAR},
            </if>
            <if test="reportForm != null">
                REPORT_FORM = #{reportForm,jdbcType=VARCHAR},
            </if>
            <if test="testCycle != null">
                TEST_CYCLE = #{testCycle,jdbcType=DECIMAL},
            </if>
            <if test="isUrgent != null">
                IS_URGENT = #{isUrgent,jdbcType=TINYINT},
            </if>
            <if test="categoryPath != null">
                CATEGORY_PATH = #{categoryPath,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                BU = #{bu,jdbcType=VARCHAR},
            </if>
            <if test="userSex != null">
                USER_SEX = #{userSex,jdbcType=TINYINT},
            </if>
            <if test="csPhone != null">
                CS_PHONE = #{csPhone,jdbcType=VARCHAR},
            </if>
            <if test="csEmail != null">
                CS_EMAIL = #{csEmail,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null">
                IS_READ = #{isRead,jdbcType=TINYINT},
            </if>
            <if test="recommendReason != null">
                RECOMMEND_REASON = #{recommendReason,jdbcType=VARCHAR},
            </if>
            <if test="recommendReasonImage != null">
                RECOMMEND_REASON_IMAGE = #{recommendReasonImage,jdbcType=VARCHAR},
            </if>
            <if test="sampleRequirements != null">
                SAMPLE_REQUIREMENTS = #{sampleRequirements,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                GROUP_NO = #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="hisState != null">
                HIS_STATE = #{hisState,jdbcType=INTEGER},
            </if>
            <if test="csName != null">
                CS_NAME = #{csName,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                LINE_ID = #{lineId,jdbcType=BIGINT},
            </if>
            <if test="businessLine != null">
                BUSINESS_LINE = #{businessLine,jdbcType=VARCHAR},
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT = #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="tmpGroupNo != null">
                TMP_GROUP_NO = #{tmpGroupNo,jdbcType=VARCHAR},
            </if>
            <if test="isRemind != null">
                IS_REMIND = #{isRemind,jdbcType=TINYINT},
            </if>
            <if test="csNameEn != null">
                CS_NAME_EN = #{csNameEn,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="catagoryId != null">
                CATAGORY_ID = #{catagoryId,jdbcType=BIGINT},
            </if>
            <if test="subState != null">
                SUB_STATE = #{subState,jdbcType=INTEGER},
            </if>
            <if test="refundState != null">
                REFUND_STATE = #{refundState,jdbcType=INTEGER},
            </if>
            <if test="totalNums != null">
                TOTAL_NUMS = #{totalNums,jdbcType=INTEGER},
            </if>
            <if test="productImg != null">
                PRODUCT_IMG = #{productImg,jdbcType=VARCHAR},
            </if>
            <if test="isTest != null">
                IS_TEST = #{isTest,jdbcType=TINYINT},
            </if>
            <if test="isInvoice != null">
                IS_INVOICE = #{isInvoice,jdbcType=TINYINT},
            </if>
            <if test="auditCode != null">
                AUDIT_CODE = #{auditCode,jdbcType=VARCHAR},
            </if>
            <if test="reportLuaCode != null">
                REPORT_LUA_CODE = #{reportLuaCode,jdbcType=VARCHAR},
            </if>
            <if test="reportFormCode != null">
                REPORT_FORM_CODE = #{reportFormCode,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="isPayReceived != null">
                IS_PAY_RECEIVED = #{isPayReceived,jdbcType=TINYINT},
            </if>
            <if test="testCycleMemo != null">
                TEST_CYCLE_MEMO = #{testCycleMemo},
            </if>
            <if test="leadsCode != null">
                LEADS_CODE = #{leadsCode},
            </if>
            <if test="orderSource != null">
                ORDER_SOURCE = #{orderSource},
            </if>
            <if test="operatorCode!= null">
                OPERATOR_CODE = #{operatorCode},
            </if>
            <if test="applySubmitDate!= null">
                APPLY_SUBMIT_DATE = #{applySubmitDate},
            </if>
            <if test="testLabel!= null">
                TEST_LABEL = #{testLabel},
            </if>
            <if test="closeReason!= null">
                CLOSE_REASON = #{closeReason},
            </if>
            <if test="closeCode!= null">
                CLOSE_CODE = #{closeCode},
            </if>
            <if test="deadlineTime!= null">
                DEADLINE_TIME = #{deadlineTime},
            </if>
            <if test="noticeNum!= null">
                NOTICE_NUM = #{noticeNum},
            </if>
            <if test="isFinish!= null">
                IS_FINISH = #{isFinish},
            </if>
            <if test="isFinish!= null">
                IS_FINISH = #{isFinish},
            </if>
            <if test="isFinish!= null">
                IS_FINISH = #{isFinish},
            </if>
            <if test="currency!= null">
                CURRENCY = #{currency},
            </if>
            <if test="exchangeRate!= null">
                EXCHANGE_RATE = #{exchangeRate},
            </if>
            <if test="slimNo!= null">
                SLIM_NO = #{slimNo},
            </if>
            <if test="platformAmount!= null">
                PLATFORM_AMOUNT = #{platformAmount},
            </if>
            <if test="salesCode != null">
                SALES_CODE = #{salesCode},
            </if>
            <if test="salesEmail != null">
                SALES_EMAIL = #{salesEmail},
            </if>
            <if test="payMethod != null">
                PAY_METHOD = #{payMethod},
            </if>
            <if test="monthPay != null">
                MONTH_PAY = #{monthPay},
            </if>
            <if test="abstractCustcode != null">
                ABSTRACT_CUSTCODE = #{abstractCustcode},
            </if>
            <if test="taxRates != null">
                TAX_RATES = #{taxRates},
            </if>
            <if test="outOrderNo != null">
                OUT_ORDER_NO = #{outOrderNo},
            </if>
            <if test="businessPersonEmail != null">
                BUSINESS_PERSON_EMAIL = #{businessPersonEmail},
            </if>
        </set>
        where ORDER_ID = #{orderId,jdbcType=BIGINT}
    </update>
    <!--自定义-->





    <sql id="orderBaseSql">
        obi.ORDER_ID,obi.ORDER_NO,obi.GROUP_NO,obi.TMP_GROUP_NO,obi.STATE,obi.SUB_STATE,obi.REFUND_STATE,obi.PAY_STATE,obi.HIS_STATE,obi.USER_ID,
        obi.CS_CODE,obi.PAY_METHOD,obi.CS_EMAIL,obi.CS_NAME,obi.CS_NAME_EN,obi.ORDER_TYPE,obi.REAL_AMOUNT,obi.BU,
        IF(obi.OPERATOR_CODE IS NULL,'待更新',obi.OPERATOR_CODE) as  OPERATOR_CODE,obi.BUSINESS_LINE,obi.LINE_ID,obi.LAB_ID,obi.IS_PAY_RECEIVED,
        obi.COMPANY_NAME_EN,obi.COMPANY_ADDRESS_CN,obi.COMPANY_ADDRESS_EN,obi.TOWN,obi.COMPANY_NAME,obi.PROVINCE,obi.CITY,obi.USER_PHONE,obi.USER_NAME,
        obi.USER_EMAIL,obi.CONFIRM_ORDER_DATE,obi.SALES_CODE,obi.SALES_PHONE,IS_ELECTRON,obi.CATAGORY_ID
    </sql>

    <resultMap id="orderBaseSqlMap" type="com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO">
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR"/>
        <result column="TMP_GROUP_NO" property="tmpGroupNo" jdbcType="VARCHAR"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="SUB_STATE" property="subState" jdbcType="INTEGER"/>
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER"/>
        <result column="PAY_STATE" property="payState" jdbcType="INTEGER"/>
        <result column="HIS_STATE" property="hisState" jdbcType="INTEGER"/>
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="VARCHAR"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="COMPANY_NAME_EN" property="companyNameEn" jdbcType="VARCHAR"/>
        <result column="COMPANY_ADDRESS_CN" property="companyAddressCn" jdbcType="VARCHAR"/>
        <result column="COMPANY_ADDRESS_EN" property="companyAddressEn" jdbcType="VARCHAR"/>
        <result column="TOWN" property="town" jdbcType="VARCHAR"/>
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR"/>
        <result column="CS_EMAIL" property="csEmail" jdbcType="VARCHAR"/>
        <result column="CS_NAME" property="csName" jdbcType="VARCHAR"/>
        <result column="CS_NAME_EN" property="csNameEn" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_LINE" property="businessLine" jdbcType="VARCHAR"/>
        <result column="LINE_ID" property="lineId" jdbcType="INTEGER"/>
        <result column="LAB_ID" property="labId" jdbcType="INTEGER"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER"/>
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="TIMESTAMP"/>
        <result column="SALES_CODE" property="salesCode" jdbcType="VARCHAR"/>
        <result column="SALES_PHONE" property="salesPhone" jdbcType="VARCHAR"/>
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
        <result column="CATEGORY_ID" property="categoryId" jdbcType="INTEGER"/>
    </resultMap>


    <sql id="userAndCsSql">
        obi.USER_SEX,obi.AUDIT_CODE
    </sql>
    <!--订单相关的几个参数-->
    <sql id="orderSql">
        obi.RELATE_ORDER_NO, obi.TOTAL_NUMS, obi.PRODUCT_IMG, obi.REPORT_LUA, obi.REPORT_FORM, obi.ORDER_AMOUNT, obi.IS_TEST, obi.IS_INVOICE
    </sql>

    <resultMap id="resultDTO" type="com.sgs.ecom.order.dto.order.OrderBaseInfoDTO"
               extends="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.orderBaseSqlMap">

        <!-- userAndCsSql-->
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="INTEGER"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="USER_SEX" property="userSex" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR"/>
        <!-- userAndCsSql-->

        <!-- orderSql-->
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER"/>
        <result column="PRODUCT_IMG" property="productImg" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="IS_TEST" property="isTest" jdbcType="INTEGER"/>
        <result column="IS_INVOICE" property="isInvoice" jdbcType="INTEGER"/>
        <!-- orderSql-->

        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
        <result column="OFFER_DATE" property="offerDate" jdbcType="TIMESTAMP"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="BU" property="bu" jdbcType="VARCHAR"/>
        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR"/>
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="LEADS_CODE" property="leadsCode" jdbcType="VARCHAR"/>
        <result column="ORDER_SOURCE" property="orderSource" jdbcType="VARCHAR"/>
        <result column="APPLY_SUBMIT_DATE" property="applySubmitDate" jdbcType="TIMESTAMP"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="INTEGER"/>
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL"/>
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="TIMESTAMP"/>
        <result column="ORDER_SOURCE_FROM" property="orderSourceFrom" jdbcType="VARCHAR"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
        
        <result column="IS_BILL" property="isBill" jdbcType="TINYINT"/>
        <result column="ABSTRACT_CUSTCODE" property="abstractCustcode" jdbcType="VARCHAR"/>
        <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="INTEGER"/>
        <result column="CUST_ID" property="custId" jdbcType="BIGINT"/>
        <result column="PLATFORM_AMOUNT" property="platformAmount" jdbcType="DECIMAL"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR"/>
        <result column="FROM_SOURCE" property="fromSource" jdbcType="VARCHAR"/>
        <result column="PLATFORM_ORDER" property="platformOrder" jdbcType="VARCHAR"/>
        <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
        <result column="CATAGORY_ID" property="categoryId" jdbcType="INTEGER"/>
        <result column="LADING_NO" property="ladingNo" jdbcType="VARCHAR"/>
        <result column="OUT_ORDER_NO" property="outOrderNo" jdbcType="VARCHAR"/>
        <result column="DESTINATION_COUNTRY" property="destinationCountry" jdbcType="VARCHAR"/>
        <result column="ETA_REMAINING_DAYS" property="etaRemainingDays" jdbcType="INTEGER"/>
        <result column="BUSINESS_PERSON_EMAIL" property="businessPersonEmail" jdbcType="VARCHAR"/>
        <result column="SALES_EMAIL" property="salesEmail" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="resultMoreDTO" type="com.sgs.ecom.order.dto.order.OrderBaseInfoMoreDTO"
               extends="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.orderBaseSqlMap">


        <!-- userAndCsSql-->
        <result column="USER_ID" property="userId" jdbcType="BIGINT"/>
        <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"/>
        <result column="USER_PHONE" property="userPhone" jdbcType="INTEGER"/>
        <result column="USER_NAME" property="userName" jdbcType="VARCHAR"/>
        <result column="PROVINCE" property="province" jdbcType="VARCHAR"/>
        <result column="CITY" property="city" jdbcType="VARCHAR"/>
        <result column="USER_SEX" property="userSex" jdbcType="VARCHAR"/>
        <result column="USER_EMAIL" property="userEmail" jdbcType="VARCHAR"/>
        <result column="AUDIT_CODE" property="auditCode" jdbcType="VARCHAR"/>
        <!-- userAndCsSql-->
        <!-- orderSql-->
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR"/>
        <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER"/>
        <result column="PRODUCT_IMG" property="productImg" jdbcType="VARCHAR"/>
        <result column="REPORT_LUA" property="reportLua" jdbcType="VARCHAR"/>
        <result column="REPORT_FORM" property="reportForm" jdbcType="VARCHAR"/>
        <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL"/>
        <result column="IS_TEST" property="isTest" jdbcType="INTEGER"/>
        <result column="IS_INVOICE" property="isInvoice" jdbcType="INTEGER"/>
        <!-- orderSql-->


        <result column="LAB_NAME" property="labName" jdbcType="VARCHAR"/>
        <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP"/>
        <result column="CATEGORY_PATH" property="categoryPath" jdbcType="VARCHAR"/>
        <result column="IS_READ" property="isRead" jdbcType="VARCHAR"/>
        <result column="OFFER_DATE" property="offerDate" jdbcType="TIMESTAMP"/>
        <result column="ORDER_EXP_DATE" property="orderExpDate" jdbcType="TIMESTAMP"/>
        <result column="SHOP_DIS_AMOUNT" property="shopDisAmount" jdbcType="DECIMAL"/>
        <result column="DISCOUNT_AMOUNT" property="discountAmount" jdbcType="DECIMAL"/>
        <result column="CS_DISCOUNT_AMOUNT" property="csDiscountAmount" jdbcType="DECIMAL"/>
        <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL"/>
        <result column="URGENT_AMOUNT" property="urgentAmount" jdbcType="DECIMAL"/>
        <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="VARCHAR"/>
        <result column="RECOMMEND_REASON_IMAGE" property="recommendReasonImage" jdbcType="VARCHAR"/>
        <result column="SAMPLE_REQUIREMENTS" property="sampleRequirements" jdbcType="VARCHAR"/>
        <result column="TEST_CYCLE" property="testCycle" jdbcType="DECIMAL"/>
        <result column="STATE_DATE" property="stateDate" jdbcType="TIMESTAMP"/>
        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"/>
        <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="TINYINT"/>
        <result column="QUESTION_ID" property="questionId" jdbcType="VARCHAR"/>
        <result column="TEST_CYCLE_MEMO" property="testCycleMemo" jdbcType="VARCHAR"/>
        <result column="LEADS_CODE" property="leadsCode" jdbcType="VARCHAR"/>
        <result column="ORDER_SOURCE" property="orderSource" jdbcType="VARCHAR"/>
        <result column="IS_URGENT" property="isUrgent" jdbcType="INTEGER"/>
        <result column="TEST_LABEL" property="testLabel" jdbcType="INTEGER"/>
        <result column="CATAGORY_ID" property="categoryId" jdbcType="INTEGER"/>
        <result column="MONTH_PAY" property="monthPay" jdbcType="INTEGER"/>
        <result column="DEADLINE_TIME" property="deadlineTime" jdbcType="TIMESTAMP"/>
        <result column="ORDER_SOURCE_FROM" property="orderSourceFrom" jdbcType="VARCHAR"/>
        <result column="PAY_METHOD" property="payMethod" jdbcType="INTEGER"/>
        <result column="CONFIRM_ORDER_DATE" property="confirmOrderDate" jdbcType="VARCHAR"/>
        <result column="SALES_CODE" property="salesCode" jdbcType="VARCHAR"/>
        <result column="SALES_PHONE" property="salesPhone" jdbcType="VARCHAR"/>
        <result column="PROMO_INFO" property="promoInfo" jdbcType="VARCHAR"/>
        <result column="FROM_SOURCE" property="fromSource" jdbcType="VARCHAR"/>
        <result column="CURRENCY" property="currency" jdbcType="VARCHAR"/>
        <result column="EXCHANGE_RATE" property="exchangeRate" jdbcType="VARCHAR"/>
        <result column="PLATFORM_AMOUNT" property="platformAmount" jdbcType="DECIMAL"/>
        <result column="APPLY_SUBMIT_DATE" property="applySubmitDate" jdbcType="TIMESTAMP"/>
        <result column="IS_ELECTRON" property="isElectron" jdbcType="INTEGER"/>
        <result column="ABSTRACT_CUSTCODE" property="abstractCustcode" jdbcType="VARCHAR"/>
        <result column="TAX_RATES" property="taxRates" jdbcType="DECIMAL"/>
    </resultMap>


    <select id="selectListByMap" resultMap="resultDTO">
        select
         distinct
        <include refid="orderBaseSql"/>,
        <include refid="userAndCsSql"/>,
        <include refid="orderSql"/>,
        obi.OFFER_DATE,obi.STATE_DATE,obi.LEADS_CODE,obi.ORDER_SOURCE,obi.APPLY_SUBMIT_DATE,obi.IS_URGENT,
        obi.LAB_NAME,obi.CREATE_DATE,obi.CATEGORY_PATH,obi.PRODUCT_NAME,
        obi.TEST_LABEL,obi.CLOSE_CODE,obi.TEST_CYCLE,obi.DEADLINE_TIME,obi.ORDER_SOURCE_FROM,obi.MONTH_PAY,
        obi.CUST_ID,obi.CURRENCY,obi.PLATFORM_AMOUNT,obi.DISCOUNT_AMOUNT,obi.URGENT_AMOUNT,obi.SAMPLE_REQUIREMENTS,obi.FROM_SOURCE,obi.OUT_ORDER_NO,
        obi.BUSINESS_PERSON_EMAIL,obi.SALES_EMAIL,obi.PLATFORM_ORDER,
        (select os.LADING_NO from order_shipping os where os.STATE = 1 and os.ORDER_NO = obi.ORDER_NO) LADING_NO,
        (select datediff(os.ARRIVAL_DATE, now()) from order_shipping os where os.STATE = 1 and os.ORDER_NO = obi.ORDER_NO) ETA_REMAINING_DAYS,
        (select oaa.ATTR_VALUE from order_application_attr oaa where oaa.STATE = 1 and oaa.ATTR_CODE = 'destinationCountry' and oaa.ORDER_NO = obi.ORDER_NO) DESTINATION_COUNTRY
        from ORDER_BASE_INFO as obi

        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.baseJoinOther"/>
        <include refid="baseQueryWhere"/>
        order by obi.CREATE_DATE desc
    </select>

    <select id="selectListByOrderNo" resultMap="resultDTO">
        select
        distinct
        <include refid="orderBaseSql"/>,
        <include refid="userAndCsSql"/>,
        <include refid="orderSql"/>,
        obi.OFFER_DATE,obi.STATE_DATE,obi.LEADS_CODE,obi.ORDER_SOURCE,obi.APPLY_SUBMIT_DATE,obi.IS_URGENT,
        obi.LAB_NAME,obi.CREATE_DATE,obi.CATEGORY_PATH,obi.PRODUCT_NAME,
        obi.TEST_LABEL,obi.CLOSE_CODE,obi.TEST_CYCLE,obi.DEADLINE_TIME,obi.ORDER_SOURCE_FROM,obi.MONTH_PAY,
        obi.CUST_ID,obi.CURRENCY,obi.PLATFORM_AMOUNT,obi.DISCOUNT_AMOUNT,obi.URGENT_AMOUNT,obi.SAMPLE_REQUIREMENTS,obi.FROM_SOURCE
        from ORDER_BASE_INFO as obi

        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.baseJoinOther"/>
        where obi.ORDER_NO = #{orderNo,jdbcType=VARCHAR}
        order by obi.CREATE_DATE desc
    </select>

    <select id="selectNewListByReq" resultMap="resultDTO">
        select
        <include refid="orderBaseSql"/>,
        <include refid="userAndCsSql"/>,
        <include refid="orderSql"/>,
        obi.OFFER_DATE,obi.STATE_DATE,obi.LEADS_CODE,obi.ORDER_SOURCE,obi.APPLY_SUBMIT_DATE,obi.IS_URGENT,
        obi.LAB_NAME,obi.CREATE_DATE,obi.CATEGORY_PATH,obi.PRODUCT_NAME,
        obi.TEST_LABEL,obi.CLOSE_CODE,obi.TEST_CYCLE,obi.DEADLINE_TIME,obi.ORDER_SOURCE_FROM,obi.MONTH_PAY,
        obi.CUST_ID,obi.CURRENCY,obi.PLATFORM_AMOUNT,obi.FROM_SOURCE
        from ORDER_BASE_INFO as obi
        <include refid="baseNewQueryWhere"/>
        <if test="orderType == 200000">
            <include refid="InquiryQueryWhere"/>
        </if>
        order by obi.CREATE_DATE desc
    </select>

    <select id="selectNewListCountByReq" resultType="java.lang.Integer">
        select count(1)
        from ORDER_BASE_INFO obi
        <include refid="baseNewQueryWhere"/>
        <include refid="InquiryQueryWhere"/>
    </select>



    <select id="selectOneByMap" resultMap="resultDTO">
        select
        distinct
        <include refid="orderBaseSql"/>,
        <include refid="userAndCsSql"/>,
        <include refid="orderSql"/>,
        obi.OFFER_DATE,obi.STATE_DATE,obi.LEADS_CODE,obi.ORDER_SOURCE,obi.APPLY_SUBMIT_DATE,obi.IS_URGENT,
        obi.LAB_NAME,obi.CREATE_DATE,obi.CATEGORY_PATH,obi.PRODUCT_NAME,
        obi.TEST_LABEL,obi.CLOSE_CODE,obi.TEST_CYCLE,obi.DEADLINE_TIME,obi.ORDER_SOURCE_FROM,obi.MONTH_PAY,
        obi.CUST_ID,obi.CURRENCY,obi.PLATFORM_AMOUNT,obi.PLATFORM_ORDER
        from ORDER_BASE_INFO as obi

        <include refid="baseQueryWhere"/>
        order by obi.CREATE_DATE desc
        limit 1
    </select>

    <resultMap id="indexMap" type="com.sgs.ecom.order.dto.order.OrderIndexDTO">
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="num" property="num" jdbcType="BIGINT"/>
    </resultMap>
    <select id="getStateNum" resultMap="indexMap">
        select distinct  STATE,count(1) as num
        from ORDER_BASE_INFO as obi


        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.baseJoinOther"/>
        <include refid="baseQueryWhere"/>
        group by STATE
    </select>

    <select id="getNewStateNum" resultMap="indexMap">
        select STATE,count(1) as num
        from ORDER_BASE_INFO as obi
        <include refid="baseNewQueryWhere"/>
        <if test="orderType == 200000">
            <include refid="InquiryQueryWhere"/>
        </if>
        group by STATE
    </select>

    <select id="selectMoreDTOByOrderId" resultMap="resultMoreDTO">
        select
        distinct
        <include refid="orderBaseSql"/>,
        <include refid="userAndCsSql"/>,
        <include refid="orderSql"/>,
        LAB_NAME,CREATE_DATE,CATEGORY_PATH,IS_READ,OFFER_DATE,ORDER_EXP_DATE,IS_URGENT,SHOP_DIS_AMOUNT,
        DISCOUNT_AMOUNT,SERVICE_AMOUNT,URGENT_AMOUNT,RECOMMEND_REASON,RECOMMEND_REASON_IMAGE,SAMPLE_REQUIREMENTS,QUESTION_ID,
        TEST_CYCLE,STATE_DATE,PRODUCT_NAME,IS_PAY_RECEIVED,TEST_CYCLE_MEMO,LEADS_CODE,ORDER_SOURCE,
        OPERATOR_CODE,TEST_LABEL,CATAGORY_ID,MONTH_PAY,DEADLINE_TIME,ORDER_SOURCE_FROM,CONFIRM_ORDER_DATE,PAY_METHOD,
        SALES_CODE,SALES_PHONE,PROMO_INFO,FROM_SOURCE,CURRENCY,EXCHANGE_RATE,PLATFORM_AMOUNT,APPLY_SUBMIT_DATE,ABSTRACT_CUSTCODE,CS_DISCOUNT_AMOUNT,
        TAX_RATES
        from ORDER_BASE_INFO obi
        where ORDER_ID = #{orderId}
    </select>


    <sql id="baseQueryWhere">
        where 1=1
        <if test="orderId != null">
            AND obi.ORDER_ID=#{orderId}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND obi.ORDER_NO like concat('%',#{orderNo},'%')
        </if>
        <if test="relateOrderNo != null">
            AND obi.RELATE_ORDER_NO=#{relateOrderNo}
        </if>
        <if test="isUrgent != null">
            AND obi.IS_URGENT=#{isUrgent}
        </if>
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="userPhone != null">
            AND obi.USER_PHONE=#{userPhone}
        </if>
        <if test="labId != null">
            AND obi.LAB_ID=#{labId}
        </if>
        <if test="state != null">
            AND obi.STATE=#{state}
        </if>
        <if test="roPayState != null">
            <if test="roPayState==0">
                and (obi.PAY_METHOD is null or obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO
                where ORDER_TYPE=301000 and PAY_METHOD is null) )
            </if>
            <if test="roPayState==1">
                and (obi.PAY_METHOD is not null or obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO
                where ORDER_TYPE=301000 and PAY_METHOD is not null) )
            </if>
        </if>
        <if test="stateNot != null">
            AND obi.STATE!=#{stateNot}
        </if>
        <if test="unQuoted != null">
            <if test="unQuoted==1">
            AND obi.RELATE_ORDER_NO in (select ORDER_NO from order_base_info obb where obb.ORDER_NO=obi.RELATE_ORDER_NO and obb.STATE=2)
            </if>
            <if test="unQuoted==2">
                AND obi.RELATE_ORDER_NO in (select ORDER_NO from order_base_info obb where obb.ORDER_NO=obi.RELATE_ORDER_NO and obb.STATE in (3,4))
            </if>
        </if>


        <if test="orderType != null">
            AND obi.ORDER_TYPE=#{orderType}
        </if>
        <if test="orderExpDateEnd != null">
            AND obi.ORDER_EXP_DATE &lt; now()
        </if>
        <if test="orderSendDate != null">
            AND DATE_ADD(obi.CREATE_DATE, INTERVAL 3 HOUR) &lt; now()
        </if>
        <if test="csEmail != null">
            AND obi.CS_EMAIL=#{csEmail}
        </if>
        <if test="csCode != null">
            AND obi.CS_CODE=#{csCode}
        </if>
        <if test="city != null">
            AND obi.CITY=#{city}
        </if>
        <if test="bu != null">
            AND obi.BU=#{bu}
        </if>
        <if test="province != null">
            AND obi.PROVINCE=#{province}
        </if>
        <if test="isRemind != null">
            AND obi.IS_REMIND=#{isRemind}
        </if>
        <if test="isTest != null">
            AND obi.IS_TEST=#{isTest}
        </if>
        <if test="isFinish != null">
            AND obi.IS_FINISH=#{isFinish}
        </if>
        <if test="platform != null and platform != ''">
            AND obi.PLATFORM = #{platform}
        </if>
        <if test="platformOrder != null and platformOrder != '' ">
            AND obi.PLATFORM_ORDER = #{platformOrder}
        </if>
        <if test="orderNoList != null and orderNoList.size()>0 ">
            AND obi.ORDER_NO in
            <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="buList != null and buList.size()>0 ">
            AND obi.BU in
            <foreach collection="buList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="labCode != null and labCode != '' ">
            AND            (obi.ORDER_NO IN (select DISTINCT  ORDER_NO from order_application_attr
            where
            (ATTR_VALUE =#{labCode} AND  ATTR_CODE = 'labCode' AND AREA_CODE = 'labInfo')) )
        </if>

        <if test="orderNoList != null ">
            AND obi.ORDER_NO in
            <foreach collection="orderNoList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="useProdLabList != null  and useProdLabList.size()>0">
            AND obi.LAB_ID in
            <foreach collection="useProdLabList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="keyword != null">
            <if test="orderType == 300000">
                <![CDATA[AND (
          obi.ORDER_NO like concat('%',#{keyword},'%')
          or
          obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=301000 and ORDER_NO like concat('%',#{keyword},'%'))
          or
          (obi.ORDER_NO in (select DISTINCT ORDER_NO from ORDER_SAMPLE where (SAMPLE_NAME like concat('%',#{keyword},'%') or
          SAMPLE_NAME_CN like concat('%',#{keyword},'%') )) )
          or
          obi.ORDER_NO in (select DISTINCT ORDER_NO from ORDER_DETAIL where ITEM_NAME like concat('%',#{keyword},'%'))
          or obi.ORDER_NO in (select oaf1.ORDER_NO from ORDER_APPLICATION_FORM oaf1  where  oaf1.company_NAME_CN like concat('%',#{keyword},'%'))
          or obi.BOSS_NO  like concat('%',#{keyword},'%')
          or obi.ORDER_NO in (select ol.ORDER_NO from ORDER_LINK ol where  ol.LINK_PHONE like concat('%',#{keyword},'%') or  ol.LINK_EMAIL like concat('%',#{keyword},'%'))

        )
          ]]>
            </if>
        </if>
        <if test="orderCreateFlg != null">
            <if  test="orderCreateFlg ==0">
                AND IFNULL(obi.FROM_SOURCE,"null") !="orderCreate"
            </if>
            <if  test="orderCreateFlg ==1">
                AND obi.FROM_SOURCE="orderCreate"
            </if>
        </if>
        <if test="isPayReceived != null ">
            <if test="orderType!=100000 ">
                <if test="isPayReceived == 0 ">
                    AND obi.IS_PAY_RECEIVED=0 AND obi.PAY_STATE=1 AND obi.PAY_METHOD=300000
                </if>
                <if test="isPayReceived == 1">
                    AND obi.IS_PAY_RECEIVED=1 AND obi.PAY_METHOD=300000
                </if>
            </if>
            <if test="orderType==100000 ">
                <if test="isPayReceived == 0 ">
                    AND ((obi.IS_PAY_RECEIVED=0 AND obi.PAY_STATE=1 AND obi.PAY_METHOD=300000)
                    or( obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO o where o.ORDER_TYPE=101000
                    and  o.IS_PAY_RECEIVED=0 AND o.PAY_STATE=1 AND o.PAY_METHOD=300000)
                    ))
                </if>
                <if test="isPayReceived == 1">
                    AND ((obi.IS_PAY_RECEIVED=1 AND obi.PAY_METHOD=300000)
                    or(obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO o where o.ORDER_TYPE=101000
                    and  o.IS_PAY_RECEIVED=1 AND o.PAY_METHOD=300000 )
                    ))
                </if>
            </if>
        </if>
        <if test="subPayReceived != null">
            AND obi.IS_PAY_RECEIVED=#{subPayReceived}
        </if>
        <if test="orderSource != null">
            AND obi.ORDER_SOURCE=#{orderSource}
        </if>
        <if test="isInvoice != null">
            <if test="isInvoice == 0">
                AND obi.IS_INVOICE is null
            </if>
            <if test="isInvoice == 1">
                AND obi.IS_INVOICE=#{isInvoice}
            </if>
        </if>
        <if test="testLabel != null">
            AND obi.TEST_LABEL=#{testLabel}
        </if>
        <if test="payState != null">
            AND obi.PAY_STATE=#{payState}
        </if>
        <if test="subState != null">
            AND obi.SUB_STATE=#{subState}
        </if>
        <if test="operatorCode != null">
            AND obi.OPERATOR_CODE=#{operatorCode}
        </if>
        <if test="testLabel != null">
            AND obi.TEST_LABEL=#{testLabel}
        </if>
        <if test="closeCode != null">
            AND obi.CLOSE_CODE=#{closeCode}
        </if>
        <if test="invoiceNumber != null">
            AND obi.ORDER_NO in (SELECT ORDER_NO FROM ORDER_ATTACHMENT where INVOICE_NUMBER=#{invoiceNumber}
            and state=1 and att_Type=11 )
        </if>
        <if test="preOrderFlag == null">
        <if test="expressNo != null">
            <if test="orderType != 100000">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_EXPRESS where EXPRESS_NO like concat('%',#{expressNo},'%'))
            </if>
            <if test="orderType == 100000">
                AND obi.ORDER_NO in
                (select DISTINCT ORDER_NO from ORDER_EXPRESS where EXPRESS_NO=#{expressNo} and DELIVER_TYPE IN (20,10,11) )
            </if>
        </if>
        </if>

        <if test="monthPay != null">
            AND obi.MONTH_PAY=#{monthPay}
        </if>
        <if test="monthMorePay != null">
            <if test="monthMorePay == 1">
                AND obi.MONTH_PAY in(1,3)
            </if>
            <if test="monthMorePay != 1">
                AND obi.MONTH_PAY=#{monthMorePay}
            </if>
        </if>
        <if test="ots != null">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_RELATE_EXTERNAL where EXTERNAL_NO like
            concat('%',#{ots},'%'))
        </if>
        <if test="transNo != null">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_PAY where TRANS_NO like concat('%',#{transNo},'%'))
        </if>
        <if test="paymentNo != null">
            AND obi.ORDER_NO in (select ORDER_NO from ORDER_PAY where PAYMENT_NO like concat('%',#{paymentNo},'%'))
        </if>
        <if test="reInquiry != null">
            <if test="reInquiry ==0">
                AND obi.USER_ID not in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="INQUIRY_REPURCHASE")
            </if>
            <if test="reInquiry ==1">
                AND obi.USER_ID  in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="INQUIRY_REPURCHASE")
            </if>
        </if>
        <if test="reOrder != null">
            <if test="reOrder ==0">
                AND obi.USER_ID not in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="ORDER_REPURCHASE")
            </if>
            <if test="reOrder ==1">
                AND obi.USER_ID in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="ORDER_REPURCHASE")
            </if>
        </if>
        <if test="reTic != null">
            <if test="reTic ==0">
                AND obi.USER_ID not in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="TIC_REPURCHASE")
            </if>
            <if test="reTic ==1">
                AND obi.USER_ID in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="TIC_REPURCHASE")
            </if>
        </if>
        <if test="itemName != null">
            AND (obi.ORDER_NO in
            (select obit.ORDER_NO from ORDER_BASE_INFO obit,ORDER_DETAIL odt where obit.ORDER_NO=odt.ORDER_NO and obit.GROUP_NO=odt.GROUP_NO and odt.STATE = 1
            and obit.ORDER_TYPE in (210000,210001)
            and ITEM_NAME like concat('%',#{itemName},'%') group by obit.ORDER_NO
            )
            or obi.RELATE_ORDER_NO in
            (select obit.ORDER_NO from ORDER_BASE_INFO obit,ORDER_DETAIL odt where obit.ORDER_NO=odt.ORDER_NO and obit.GROUP_NO=odt.GROUP_NO and odt.STATE = 1
            and obit.ORDER_TYPE=200001
            and ITEM_NAME like concat('%',#{itemName},'%') group by obit.ORDER_NO
            )
            )
        </if>
        <if test="sampleName != null">
            <if test="orderType != 100000">
                AND obi.ORDER_NO in
                (select DISTINCT ORDER_NO from ORDER_SAMPLE where SAMPLE_NAME like concat('%',#{sampleName},'%') or
                SAMPLE_NAME_CN like concat('%',#{sampleName},'%') or SAMPLE_NAME_EN like concat('%',#{sampleName},'%'))
            </if>

            <if test="orderType == 100000">
                <![CDATA[
                    AND (
          obi.ORDER_NO like concat('%',#{sampleName},'%')
          or
          (obi.ORDER_NO in (select DISTINCT ORDER_NO from  ORDER_PRODUCT where  state = 1 and  PRODUCT_NAME like
           concat('%',#{sampleName},'%') ) )
           or
           (
           obi.ORDER_NO IN ( select DISTINCT  ORDER_NO from ORDER_SAMPLE_FROM
            where 1=1 and ((SAMPLE_VALUE like concat('%',#{sampleName},'%') AND  SAMPLE_KEY = 'sampleName')
            or
             (SAMPLE_VALUE like concat('%',#{sampleName},'%') AND  SAMPLE_KEY = 'sampleNameEn')
             or
             (SAMPLE_VALUE like concat('%',#{sampleName},'%') AND  SAMPLE_KEY = 'sampleDescriptionEn')
             or
             (SAMPLE_VALUE like concat('%',#{sampleName},'%') AND  SAMPLE_KEY = 'sampleDescription')
             )
              )
           )
        )
          ]]>
            </if>

        </if>
        <if test="productName != null">
            AND obi.ORDER_NO in  (select DISTINCT ORDER_NO from ORDER_PRODUCT where PRODUCT_NAME like concat('%',#{productName},'%')  and state = 1 )
        </if>

        <if test="businessLineList != null ">
            AND obi.LINE_ID in
            <foreach collection="businessLineList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="useBusinessLineList != null and useLabIdList != null">
            AND ((obi.LINE_ID in
            <foreach collection="useBusinessLineList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and obi.LAB_ID is null) or
            obi.LAB_ID in
            <foreach collection="useLabIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="stateList != null ">
            AND obi.STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="useAuthOrderState != null or useAuthOrderRefundState != null">
            AND (
            <if test="useAuthOrderState != null ">
                obi.STATE in
                <foreach collection="useAuthOrderState" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="useAuthOrderRefundState != null ">
                OR
                </if>
            </if>
            <if test="useAuthOrderRefundState != null ">
                obi.REFUND_STATE in
                <foreach collection="useAuthOrderRefundState" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or (obi.ORDER_NO in (SELECT RELATE_ORDER_NO from order_base_info where REFUND_STATE IN
                <foreach collection="useAuthOrderRefundState" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                ))
            </if>
            )
        </if>
        <if test="refundStateList != null ">
            AND obi.REFUND_STATE in
            <foreach collection="refundStateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="useAuthOrderState != null ">
            AND obi.STATE in
            <foreach collection="useAuthOrderState" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="orderIdList != null ">
            AND obi.ORDER_ID in
            <foreach collection="orderIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="payStateList != null ">
            <if test="orderType ==100000 ">
                AND (obi.PAY_STATE in
                <foreach collection="payStateList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or obi.ORDER_NO in (
                select RELATE_ORDER_NO from ORDER_BASE_INFO o where o.ORDER_TYPE=101000
                and o.PAY_STATE in
                <foreach collection="payStateList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
                )
            </if>
            <if test="orderType!=100000 ">
                AND obi.PAY_STATE in
                <foreach collection="payStateList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="monthPayList != null ">
            AND obi.MONTH_PAY in
            <foreach collection="monthPayList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="labIdList != null ">
            AND obi.LAB_ID in
            <foreach collection="labIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="labCategoryIdList != null ">
            AND obi.CATAGORY_ID in
            <foreach collection="labCategoryIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="closeCodeList != null ">
            AND obi.CLOSE_CODE in
            <foreach collection="closeCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="usePrivilegeLevel != null">
            AND (obi.STATE=1 or obi.CS_CODE=#{usePrivilegeLevel})
        </if>
        <if test="csCodeLikeSelf!=null">
                AND (obi.OPERATOR_CODE=#{csCodeLikeSelf} or obi.BUSINESS_PERSON_EMAIL =#{businessMailLikeSelf})
        </if>
        <if test="oiqCustList != null ">
            AND obi.ORDER_NO in (
            select ORDER_NO from ORDER_CUSTOMER where OBJECT_TYPE="CUST" and state=1 and OBJECT_ID in
            <foreach collection="oiqCustList" index="index" item="item" open="(" separator="," close=")">
            #{item}
            </foreach>
            )
        </if>

        <if test="ladingNo != null">
            AND obi.ORDER_NO in  (select ORDER_NO from ORDER_SHIPPING os where LADING_NO =#{ladingNo})
        </if>
        <if test="outOrderNo != null">
            AND obi.OUT_ORDER_NO=#{outOrderNo}
        </if>
        <if test="destinationCountry != null and destinationCountry != ''">
            AND EXISTS (
                select 1 from order_application_attr
                where STATE = 1
                and ATTR_CODE = 'destinationCountry'
                and ORDER_NO = obi.ORDER_NO
                and ATTR_VALUE = #{destinationCountry}
            )
        </if>
        <if test="etaRemainingDays != null and etaRemainingDays != ''">
            AND EXISTS (
                select 1 from order_shipping os
                where os.STATE = 1
                and os.ORDER_NO = obi.ORDER_NO
                and <![CDATA[ datediff(os.ARRIVAL_DATE, now()) < #{etaRemainingDays} ]]>
            )
        </if>

        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.useReFlg"/>
        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.likeKey"/>
        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.betweenKey"/>
        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoMapper.oldStateKey"/>
    </sql>

    <sql id="baseJoinOther">
        <if test="orderType == 300000">
            join (select ci.CUST_ID FROM TB_CUST_INFO ci,TB_CUST_SERVICE cs where ci.CUST_ID=cs.CUST_ID
            and  ci.BUSI_CODE="MALL" and cs.BUSI_CODE='MALL' and ci.STATE=1 and cs.bu="1302"
            <if test="roAuthList != null ">
                and (
                <if test="roPersonCode!=null">
                    cs.PERSON_CODE=#{roPersonCode} or
                </if>
                ci.BELONG_AREA in
                <foreach collection="roAuthList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                )
            </if>
            <if test="roAuthList == null and roPersonCode!=null">
                and PERSON_CODE=#{roPersonCode}
            </if>
            ) as custt on obi.CUST_ID=custt.CUST_ID
        </if>
    </sql>

    <sql id="useReFlg">
        <if test="reFlg != null">
            <if test="orderType == 100000">
                <if test="reFlg ==0">
                    AND obi.ORDER_NO not in (select ORDER_NO from ORDER_ATTRIBUTE oa
                    where oa.GROUP_NO='customGroup' and oa.ATTR_VALUE='TIC_REPURCHASE' and ATTR_NAME='1')
                </if>
                <if test="reFlg ==1">
                    AND obi.ORDER_NO in (select ORDER_NO from ORDER_ATTRIBUTE oa
                    where oa.GROUP_NO='customGroup' and oa.ATTR_VALUE='TIC_REPURCHASE' and ATTR_NAME='1')
                </if>
            </if>
        </if>
    </sql>



    <sql id="likeKey">
        <if test="likeKey != null">
            <if test="likeKey.companyName!=null">
                AND obi.COMPANY_NAME like concat('%',#{likeKey.companyName},'%')
            </if>
            <if test="likeKey.userName!=null">
                AND obi.USER_NAME like concat('%',#{likeKey.userName},'%')
            </if>
            <if test="likeKey.userEmail!=null">
                AND obi.USER_EMAIL like concat('%',#{likeKey.userEmail},'%')
            </if>
            <if test="likeKey.csCodeLike!=null">
                AND obi.CS_CODE like concat('%',#{likeKey.csCodeLike},'%') ESCAPE '$'
            </if>

            <if test="likeKey.userPhone!=null">
                AND obi.USER_PHONE like concat('%',#{likeKey.userPhone},'%')
            </if>
            <if test="likeKey.csEmail!=null">
                AND obi.CS_EMAIL like concat('%',#{likeKey.csEmail},'%')
            </if>
            <if test="likeKey.orderNo!=null">
                AND obi.ORDER_NO like concat('%',#{likeKey.orderNo},'%')
            </if>
            <if test="likeKey.orderSubNo!=null">
                AND
                (obi.ORDER_NO like concat('%',#{likeKey.orderSubNo},'%')
                or
                obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=211000 and ORDER_NO like
                concat('%',#{likeKey.orderSubNo},'%') )
                )
            </if>
            <if test="likeKey.ticSubNo!=null">
                AND
                (obi.ORDER_NO like concat('%',#{likeKey.ticSubNo},'%')
                or
                obi.ORDER_NO in (select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=101000 and ORDER_NO like
                concat('%',#{likeKey.ticSubNo},'%') )
                )
            </if>
            <if test="likeKey.auditCode!=null">
                AND obi.AUDIT_CODE like concat('%',#{likeKey.auditCode},'%')
            </if>
            <if test="likeKey.orderSourceFrom!=null">
                AND obi.ORDER_SOURCE_FROM like concat('%',#{likeKey.orderSourceFrom},'%')
            </if>
            <if test="likeKey.operatorCode != null">
                AND obi.OPERATOR_CODE like concat('%',#{likeKey.operatorCode},'%')
            </if>
            <if test="likeKey.operatorCodeUpdate != null ">
                AND (obi.OPERATOR_CODE is null or obi.OPERATOR_CODE ='')
            </if>
            <if test="likeKey.categoryPath!=null">
                AND obi.CATEGORY_PATH like concat('%',#{likeKey.categoryPath},'%')
            </if>
            <if test="likeKey.fileName!=null">
                AND obi.ORDER_NO in (select DISTINCT ORDER_NO from ORDER_ATTACHMENT where (FILE_NAME like
                concat('%',#{likeKey.fileName},'%') ) and state = 1 )
            </if>
            <if test="likeKey.testItem!=null and likeKey.testItem!=''">
                AND obi.ORDER_NO in (select DISTINCT ORDER_NO from order_detail  where  1=1 and STATE = 1
                AND ( ITEM_NAME like concat('%',#{likeKey.testItem},'%') and ITEM_TYPE = 2)
               OR ( TEST_NAME like concat('%',#{likeKey.testItem},'%') and ITEM_TYPE = 1)
                )
            </if>
        </if>
    </sql>

    <sql id="betweenKey">
        <if test="betweenKey != null">
            <if test="orderType != 100000">
                <if test="betweenKey.createDateStart!=null and betweenKey.createDateEnd!=null">
                    AND obi.CREATE_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                </if>
                <if test="betweenKey.confirmOrderDateStart!=null and betweenKey.confirmOrderDateEnd!=null">
                    AND obi.CONFIRM_ORDER_DATE between #{betweenKey.confirmOrderDateStart} and #{betweenKey.confirmOrderDateEnd}
                </if>
             </if>
            <if test="betweenKey.realAmountStart!=null and betweenKey.realAmountEnd!=null">
                AND obi.REAL_AMOUNT between #{betweenKey.realAmountStart} and #{betweenKey.realAmountEnd}
            </if>
            <if test="betweenKey.stateDateStart!=null and betweenKey.stateDateEnd!=null">
                AND obi.STATE=4 AND obi.STATE_DATE between #{betweenKey.stateDateStart} and #{betweenKey.stateDateEnd}
            </if>
            <if test="betweenKey.offerDateStart!=null and betweenKey.offerDateEnd!=null">
                AND obi.OFFER_DATE between #{betweenKey.offerDateStart} and #{betweenKey.offerDateEnd}
            </if>
            <if test="betweenKey.applySubmitDateStart!=null and betweenKey.applySubmitDateEnd!=null">
                AND obi.APPLY_SUBMIT_DATE between #{betweenKey.applySubmitDateStart} and
                #{betweenKey.applySubmitDateEnd}
            </if>
            <if test="betweenKey.deadlineTimeStart!=null and betweenKey.deadlineTimeEnd!=null">
                AND obi.DEADLINE_TIME between #{betweenKey.deadlineTimeStart} and #{betweenKey.deadlineTimeEnd}
            </if>


            <if test="betweenKey.payDateStart!=null and betweenKey.payDateEnd!=null">
                <if test="orderType == 210000">
                    AND (
                    (obi.PAY_DATE between #{betweenKey.payDateStart} and #{betweenKey.payDateEnd} )
                    or (
                        obi.ORDER_NO in (select RELATE_ORDER_NO from ORDER_BASE_INFO obi11 where obi11.ORDER_TYPE=211000 and
                        obi11.PAY_DATE between #{betweenKey.payDateStart} and #{betweenKey.payDateEnd})
                       )
                    )
                </if>
                <if test="orderType != 210000">
                    AND obi.PAY_DATE between #{betweenKey.payDateStart} and #{betweenKey.payDateEnd}
                </if>
            </if>
        </if>
    </sql>


    <sql id="newBetweenKey">
            <if test="orderType != 100000">
                <if test="createDateStart!=null and createDateEnd!=null">
                    AND obi.CREATE_DATE between #{createDateStart} and #{createDateEnd}
                </if>
            </if>
            <if test="realAmountStart!=null and realAmountEnd!=null">
                AND obi.REAL_AMOUNT between #{realAmountStart} and #{realAmountEnd}
            </if>
            <if test="stateDateStart!=null and stateDateEnd!=null">
                AND obi.STATE=4 AND obi.STATE_DATE between #{stateDateStart} and #{stateDateEnd}
            </if>
            <if test="offerDateStart!=null and offerDateEnd!=null">
                AND obi.OFFER_DATE between #{offerDateStart} and #{offerDateEnd}
            </if>
            <if test="applySubmitDateStart!=null and applySubmitDateEnd!=null">
                AND obi.APPLY_SUBMIT_DATE between #{applySubmitDateStart} and  #{applySubmitDateEnd}
            </if>
            <if test="deadlineTimeStart!=null and deadlineTimeEnd!=null">
                AND obi.DEADLINE_TIME between #{deadlineTimeStart} and #{deadlineTimeEnd}
            </if>
            <if test="payDateStart!=null and payDateEnd!=null">
                AND obi.PAY_DATE between #{payDateStart} and #{payDateEnd}
            </if>
    </sql>

    <sql id="oldStateKey">
        <if test="orderType == 100000">
            <if test="stateOrList == null and stateDateFlg==1">
                <if test="betweenKey.createDateStart!=null and betweenKey.createDateEnd!=null">
                    AND (obi.CREATE_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                    or obi.ORDER_NO in (
                        select RELATE_ORDER_NO FROM ORDER_BASE_INFO where ORDER_TYPE=101000
                        and CREATE_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                        )
                    )
                </if>
            </if>
            <if test="lvThreeLabelStateList != null">
                AND
                <foreach collection="lvThreeLabelStateList" index="index" item="item" open="(" separator=" or " close=")">
                    <if test="item != 10 and item != 51  and item != 53  and item != 73 and item != 13">
                        obi.STATE=#{item}
                    </if>
                    <if test="item == 10">
                        obi.ORDER_NO IN (
                            SELECT
                                DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO
                                else obi.ORDER_NO END) as ORDER_NO
                            FROM
                                order_base_info obi
                            WHERE
                                obi.ORDER_TYPE IN (100000, 101000)
                                AND ( obi.STATE = 10 OR (obi.RELATE_ORDER_NO IS NOT NULL AND obi.PAY_STATE = 0))
                        )
                    </if>
                    <if test="item == 51">
                        obi.ORDER_NO IN (
                            SELECT
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                            from
                                order_base_info obi
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                AND obi.PAY_STATE=2
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 13">
                        (obi.STATE=12 or  obi.STATE=13)
                    </if>
                    <if test="item == 53">
                        obi.ORDER_NO IN (
                            SELECT DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                            from
                                order_base_info obi
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                AND obi.REFUND_STATE=1
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 73">
                        (obi.DEADLINE_TIME is not null and DATE_FORMAT(obi.DEADLINE_TIME,'%Y%m%d') &lt; DATE_FORMAT(now( ) ,'%Y%m%d')
                        and obi.ORDER_NO in (select DISTINCT obi.ORDER_NO from order_base_info obi where
                        obi.ORDER_TYPE="100000"  and    ((select count(1) from order_attachment oa where oa.order_no=obi.ORDER_NO and oa.ATT_TYPE=10 )=0 and obi.STATE=14 ))
                        )
                    </if>
                </foreach>
            </if>


            <if test="stateOrList != null and stateDateFlg==0">
                AND
                <foreach collection="stateOrList" index="index" item="item" open="(" separator=" or " close=")">
                    <if test="item == 10">
                        obi.ORDER_NO IN (
                            SELECT
                                DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO
                                else obi.ORDER_NO END) as ORDER_NO
                            FROM
                                order_base_info obi
                            WHERE
                                obi.ORDER_TYPE IN (100000, 101000)
                                AND ( obi.STATE = 10 OR (obi.RELATE_ORDER_NO IS NOT NULL AND obi.PAY_STATE = 0))
                        )
                    </if>
                    <if test="item == 51">
                        obi.ORDER_NO IN (
                            SELECT
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                            from
                                order_base_info obi
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                AND obi.PAY_STATE=2
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 52">
                        obi.SUB_STATE=70
                    </if>
                    <if test="item == 53">
                        obi.ORDER_NO IN (
                            SELECT DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                            from
                                order_base_info obi
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                AND obi.REFUND_STATE=1
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 54">
                        obi.ORDER_NO  in (
                            SELECT
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                            from
                                order_base_info obi
                            join
                                ORDER_OPERATOR_LOG ool on COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) = ool.ORDER_NO
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                and obi.STATE = 91
                                and ool.OPERATOR_TYPE in (744, 2744, 36, 236, 701, 2701)
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 55">
                        obi.ORDER_NO IN (
                        SELECT
                            DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                        from
                            order_base_info obi
                        where
                            obi.ORDER_TYPE IN (100000,101000)
                            AND obi.REFUND_STATE = 4
                            <if test="likeKey != null and likeKey.ticSubNo != null">
                                AND obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                            </if>
                        )
                    </if>
                    <if test="item == 56">
                        obi.ORDER_NO IN (
                            SELECT
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) AS ORDER_NO
                            FROM
                                order_base_info obi
                            JOIN
                                ORDER_OPERATOR_LOG ool ON COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) = ool.ORDER_NO
                            WHERE
                                (
                                    (obi.REFUND_STATE = 5 AND ool.OPERATOR_TYPE IN (701, 2701))
                                    OR (obi.REFUND_STATE IN (1, 2, 3, 4, 5) AND obi.ORDER_TYPE IN (100000, 101000) AND ool.OPERATOR_TYPE IN (744, 2744))
                                )
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    AND obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 13">
                        (obi.STATE=12 or  obi.STATE=13)
                    </if>
                    <if test="item != 10 and item != 51 and item != 52 and item != 53 and item != 54  and item != 55 and item != 56 and item != 13">
                        obi.STATE=#{item}
                    </if>
                </foreach>
            </if>
            <if test="stateOrList != null and stateDateFlg==1 and betweenKey.createDateStart!=null and betweenKey.createDateEnd!=null">
                AND
                <foreach collection="stateOrList" index="index" item="item" open="(" separator=" or " close=")">
                    <if test="item == 10">
                         obi.CREATE_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                    </if>
                    <if test="item == 51">
                        obi.ORDER_NO in (
                            select
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO)
                            from
                                ORDER_OPERATOR_LOG ool
                            join
                                order_base_info obi on COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) = ool.ORDER_NO
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                AND obi.PAY_STATE IN (1, 2)
                                AND ool.OPERATOR_TYPE IN (68, 268)
                                and ool.OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 11">
                         obi.PAY_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                    </if>
                    <if test="item == 13">
                        obi.ORDER_NO in  ( select DISTINCT ORDER_NO from ORDER_OPERATOR_LOG where OPERATOR_TYPE=31
                        and OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd})
                    </if>
                    <if test="item == 14">
                        obi.ORDER_NO in  ( select DISTINCT ORDER_NO from ORDER_OPERATOR_LOG where OPERATOR_TYPE=34
                        and OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd})
                    </if>
                    <if test="item == 80">
                        obi.STATE=80 and obi.ORDER_NO in ( select DISTINCT ORDER_NO from ORDER_OPERATOR_LOG where OPERATOR_TYPE=80
                        and OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd})
                    </if>
                    <if test="item == 52">
                        obi.ORDER_NO in  ( select DISTINCT ORDER_NO from ORDER_OPERATOR_LOG where OPERATOR_TYPE=35
                        and OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd})
                    </if>
                    <if test="item == 53">
                        obi.ORDER_NO in (
                            select
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                            from
                                ORDER_OPERATOR_LOG ool
                            join
                                order_base_info obi on COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) = ool.ORDER_NO
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                AND obi.REFUND_STATE IN (1, 2, 3, 4, 5)
                                AND ool.OPERATOR_TYPE IN (33, 233)
                                and ool.OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>
                    <if test="item == 91">
                        obi.STATE=91 and obi.STATE_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                    </if>
                    <if test="item == 54">
                        obi.ORDER_NO  in (
                            SELECT
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                            from
                                order_operator_log ool
                            join
                                order_base_info obi on COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO)= ool.ORDER_NO
                            where
                                obi.ORDER_TYPE IN (100000,101000)
                                AND obi.STATE = 91
                                AND ool.OPERATOR_TYPE in (744, 2744, 36, 236, 701, 2701)
                                AND ool.OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                    and obi.ORDER_NO like concat('%',#{likeKey.ticSubNo},'%')
                                </if>
                        )
                    </if>
                    <if test="item == 55">
                        obi.ORDER_NO  in (
                        SELECT
                            DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) as ORDER_NO
                        from
                            order_operator_log ool
                        join
                            order_base_info obi on COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO)= ool.ORDER_NO
                        where
                            obi.ORDER_TYPE IN (100000,101000)
                            AND obi.REFUND_STATE IN (2, 4)
                            AND ool.OPERATOR_TYPE in (  701, 2701,36,236 )
                            AND ool.OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                            <if test="likeKey != null and likeKey.ticSubNo != null">
                                and obi.ORDER_NO like concat('%',#{likeKey.ticSubNo},'%')
                            </if>
                        )

                    </if>
                    <if test="item == 56">
                        obi.ORDER_NO IN (
                            SELECT
                                DISTINCT COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) AS ORDER_NO
                            FROM
                                order_base_info obi
                            JOIN
                                ORDER_OPERATOR_LOG ool ON COALESCE(obi.RELATE_ORDER_NO, obi.ORDER_NO) = ool.ORDER_NO
                            WHERE
                                ool.OPERATOR_DATE between #{betweenKey.createDateStart} and #{betweenKey.createDateEnd}
                                AND ((obi.REFUND_STATE = 5 AND ool.OPERATOR_TYPE IN (701, 2701))
                                    OR (obi.REFUND_STATE IN (1, 2, 3, 4, 5) AND obi.ORDER_TYPE IN (100000, 101000) AND ool.OPERATOR_TYPE IN (744, 2744)))
                                <if test="likeKey != null and likeKey.ticSubNo != null">
                                AND obi.ORDER_NO LIKE CONCAT('%', #{likeKey.ticSubNo}, '%')
                                </if>
                        )
                    </if>

                </foreach>

            </if>

        </if>
    </sql>


    <select id="checkOrderBase" resultMap="orderBaseSqlMap">
        select
        <include refid="orderBaseSql"/>,
        <include refid="orderSql"/>,obi.MONTH_PAY
        from ORDER_BASE_INFO obi
        where ORDER_ID = #{orderId}
    </select>

	<update id="modMonthPayByOrder" parameterType="com.sgs.ecom.order.vo.VOOrderBaseInfo">
        update ORDER_BASE_INFO
        <set>
        	STATE_DATE = now(),
	  		<if test="isInvoice != null" >
		        IS_INVOICE = #{isInvoice,jdbcType=TINYINT},
		    </if>
		    <if test="monthPay != null" >
		        MONTH_PAY = #{monthPay,jdbcType=TINYINT},
		    </if>
	  	</set>
	  	where ORDER_NO = #{orderNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPerson">
        update ORDER_BASE_INFO
        set OPERATOR_CODE=#{operatorCode}
        where (OPERATOR_CODE = "" or OPERATOR_CODE is null)
          and ORDER_ID = #{orderId}
    </update>
    <update id="updateCodeByPerson">
        update ORDER_BASE_INFO
        set OPERATOR_CODE=#{operatorCode}
        where (OPERATOR_CODE = "" or OPERATOR_CODE is null)
          and ORDER_NO = #{orderNo}
    </update>


    <update id="updateOrderSource">
        update ORDER_BASE_INFO
        set ORDER_SOURCE = #{orderSource}
        where ORDER_SOURCE is null
          and USER_ID = #{userId}
    </update>

    <update id="cancelPayMethod">
        update ORDER_BASE_INFO set PAY_METHOD=#{payMethod} where ORDER_NO=#{orderNo}
    </update>
    <update id="updateBatchSelective">
        update ORDER_BASE_INFO
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="is_invoice =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.isInvoice !=null">
                        when ORDER_ID=#{item.orderId} then #{item.isInvoice}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="item" index="index">
            ORDER_ID=#{item.orderId}
        </foreach>
    </update>

    <update id="updateBatchByOrderNo">
        update ORDER_BASE_INFO
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="is_invoice =case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.isInvoice !=null">
                        when ORDER_NO=#{item.orderNo} then #{item.isInvoice}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="item" index="index">
            ORDER_NO=#{item.orderNo}
        </foreach>
    </update>


    <resultMap id="permissionSqlMap" type="com.sgs.ecom.order.dto.permission.OrderPermissionDTO">
        <id column="ORDER_ID" property="orderId" jdbcType="BIGINT"/>
        <result column="STATE" property="state" jdbcType="INTEGER"/>
        <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER"/>
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"/>
        <result column="ORDER_TYPE" property="orderType" jdbcType="INTEGER"/>
        <result column="CS_CODE" property="csCode" jdbcType="VARCHAR"/>
        <result column="LINE_ID" property="lineId" jdbcType="INTEGER"/>
        <result column="PROD_ID" property="prodId" jdbcType="INTEGER"/>
        <result column="CUST_ID" property="custId" jdbcType="BIGINT"/>
        <result column="LAB_ID" property="labId" jdbcType="BIGINT"/>
        <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR"/>
        <result column="BUSINESS_PERSON_EMAIL" property="businessPersonEmail" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="basePermissionSql">
        obi.ORDER_ID,obi.STATE,obi.ORDER_NO,obi.ORDER_TYPE,obi.CS_CODE,obi.LINE_ID,op.PROD_ID,obi.CUST_ID,obi.REFUND_STATE,
        obi.LAB_ID,obi.OPERATOR_CODE,obi.BUSINESS_PERSON_EMAIL
    </sql>
    <select id="selectPermission" resultMap="permissionSqlMap">
        select
        distinct
        <include refid="basePermissionSql"/>
        from ORDER_BASE_INFO obi
        left join ORDER_PRODUCT op on obi.ORDER_NO=op.ORDER_NO and op.state = 1
        where obi.ORDER_ID = #{orderId}
        limit 1
    </select>

    <select id="selectPermissionByRelateOrderNo"
            resultMap="permissionSqlMap">
        select
        distinct
        <include refid="basePermissionSql"/>
        from ORDER_BASE_INFO obi
        left join ORDER_PRODUCT op on obi.ORDER_NO=op.ORDER_NO and op.state = 1
        where obi.RELATE_ORDER_NO = #{orderNo}
    </select>

    <update id="setBill" parameterType="java.lang.String">
        update ORDER_BASE_INFO
        set IS_BILL = 1
        where ORDER_NO = #{orderNo,jdbcType=VARCHAR}
    </update>
    <update id="closeOrderByINS" parameterType="java.lang.String">
        UPDATE ORDER_BASE_INFO
        SET STATE = 91
        WHERE
            1= 1
        <if test="orderList != null">
            AND ORDER_NO IN
            <foreach collection="orderList" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectListCountByMap" resultType="java.lang.Integer">
        select count(1)
        from ORDER_BASE_INFO obi
        <include refid="baseQueryWhere"/>
    </select>
    <select id="selectCloseOrderByINS" resultType="java.lang.String">
        SELECT
        DISTINCT obi.ORDER_NO
        FROM
        ORDER_BASE_INFO obi
        LEFT JOIN ORDER_APPLICATION_ATTR opa ON obi.ORDER_NO = opa.ORDER_NO
        WHERE
        obi.STATE = 10
        AND obi.BU = 'INS'
        AND obi.PAY_STATE = 0
        AND opa.STATE = 1
        AND opa.ATTR_CODE = 'checkDate'
        AND opa.AREA_CODE = 'levelService'
        AND opa.ATTR_VALUE &lt; DATE_FORMAT( NOW( ),'%Y-%m-%d' )
    </select>
    <select id="qryMakeUpDifference" resultMap="orderBaseSqlMap">
        select
        <include refid="orderBaseSql"/>
        from ORDER_BASE_INFO obi
        WHERE
        obi.STATE != 91
        <if test="relateOrderNo != null">
            AND obi.RELATE_ORDER_NO=#{relateOrderNo}
        </if>

    </select>
    <select id="selectRelevanceListByMap" resultMap="resultDTO">
        SELECT
        <include refid="orderBaseSql"/>,
        <include refid="userAndCsSql"/>,
        <include refid="orderSql"/>,
        obi.OFFER_DATE,obi.STATE_DATE,obi.LEADS_CODE,obi.ORDER_SOURCE,obi.APPLY_SUBMIT_DATE,obi.IS_URGENT,
        obi.LAB_NAME,obi.CREATE_DATE,obi.CATEGORY_PATH,obi.PRODUCT_NAME,
        obi.TEST_LABEL,obi.CLOSE_CODE,obi.TEST_CYCLE,obi.DEADLINE_TIME,obi.ORDER_SOURCE_FROM,obi.MONTH_PAY,
        obi.CUST_ID,obi.CURRENCY
        FROM
        order_base_info obi
        WHERE
        obi.STATE IN ( 10, 11, 12, 13, 14 )
        AND obi.ORDER_TYPE = 100000
        AND obi.ORDER_NO != #{notOrderNo}
        <if test="orderNo != null and orderNo !='' ">
            AND obi.ORDER_NO = #{orderNo}
        </if>
        <if test="userId != null and  userId != '' ">
            AND obi.USER_ID = #{userId}
        </if>
        AND obi.ORDER_NO IN (  SELECT  T.ORDER_NO FROM (
        SELECT DISTINCT
        oaa.ORDER_NO ,
        CONCAT(max( ( CASE oaa.ATTR_CODE WHEN 'province' THEN oaa.ATTR_VALUE ELSE '' END ) ),
        max( ( CASE oaa.ATTR_CODE WHEN 'city' THEN oaa.ATTR_VALUE ELSE '' END ) ),
        max( ( CASE oaa.ATTR_CODE WHEN 'town' THEN oaa.ATTR_VALUE ELSE '' END ) ),
        max( ( CASE oaa.ATTR_CODE WHEN 'labAddress' THEN oaa.ATTR_VALUE ELSE '' END ) )) LAB_ADDRESS_SHOW,
        max( ( CASE oaa.ATTR_CODE WHEN 'labId' THEN oaa.ATTR_VALUE ELSE '' END ) ) LAB_ID
        FROM
        order_application_attr oaa,
        order_base_info obis
        WHERE
        obis.order_no = oaa.order_no
        AND oaa.AREA_CODE = 'labInfo'
        AND oaa.STATE = 1
        AND
        obis.STATE IN ( 10, 11, 12, 13, 14 )
        AND obis.ORDER_TYPE = 100000
        AND obis.ORDER_NO != #{notOrderNo}
        <if test="orderNo != null and orderNo !='' ">
            AND obis.ORDER_NO = #{orderNo}
        </if>
        <if test="userId != null and  userId != '' ">
            AND obis.USER_ID = #{userId}
        </if>
        GROUP BY
        oaa.ORDER_NO ) T WHERE
        T.LAB_ADDRESS_SHOW = #{labAddressShow}
        AND
        T.LAB_ID = #{labId})
    </select>

    <select id="qryRefundTimeOverdue" resultMap="resultDTO">
        select * from order_base_info where REFUND_STATE =4
    </select>
    <select id="qryOrderNoListByMap" resultType="java.lang.String">
        SELECT
        obi.ORDER_NO
        FROM
        order_base_info obi
        LEFT JOIN order_application_form oaf ON obi.ORDER_NO = oaf.ORDER_NO
        LEFT JOIN order_express oe ON obi.ORDER_NO = oe.ORDER_NO
        WHERE
        1 = 1
        AND oaf.STATE=1
        AND oe.STATE=1
        <if test="platform != null" >
            AND  obi.PLATFORM=#{platform}
        </if>
        <if test="platformOrder != null" >
            AND  obi.PLATFORM_ORDER=#{platformOrder}
        </if>
        <if test="labCode != null" >
            AND  oaf.LAB_CODE=#{labCode}
        </if>
        <if test="expressNo != null" >
            AND  oe.EXPRESS_NO=#{expressNo}
        </if>
    </select>
    <insert id="insertVOSelective" parameterType="com.sgs.ecom.order.vo.VOOrderBaseInfo">
        <selectKey resultType="java.lang.Long" keyProperty="orderId" order="AFTER">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into ORDER_BASE_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                ORDER_ID,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="orderType != null">
                ORDER_TYPE,
            </if>
            <if test="orderAmount != null">
                ORDER_AMOUNT,
            </if>
            <if test="shopDisAmount != null">
                SHOP_DIS_AMOUNT,
            </if>
            <if test="questionId != null">
                QUESTION_ID,
            </if>
            <if test="labId != null">
                LAB_ID,
            </if>
            <if test="labName != null">
                LAB_NAME,
            </if>
            <if test="discountAmount != null">
                DISCOUNT_AMOUNT,
            </if>
            <if test="realAmount != null">
                REAL_AMOUNT,
            </if>
            <if test="state != null">
                STATE,
            </if>
            <if test="proState != null">
                PRO_STATE,
            </if>
            <if test="payState != null">
                PAY_STATE,
            </if>
            <if test="userId != null">
                USER_ID,
            </if>
            <if test="userPhone != null">
                USER_PHONE,
            </if>
            <if test="userName != null">
                USER_NAME,
            </if>
            <if test="userEmail != null">
                USER_EMAIL,
            </if>
            <if test="companyName != null">
                COMPANY_NAME,
            </if>
            <if test="province != null">
                PROVINCE,
            </if>
            <if test="city != null">
                CITY,
            </if>
            <if test="relateOrderNo != null">
                RELATE_ORDER_NO,
            </if>
            <if test="payDate != null">
                PAY_DATE,
            </if>
            <if test="createDate != null">
                CREATE_DATE,
            </if>
            <if test="stateDate != null">
                STATE_DATE,
            </if>
            <if test="platform != null">
                PLATFORM,
            </if>
            <if test="platformOrder != null">
                PLATFORM_ORDER,
            </if>
            <if test="csCode != null">
                CS_CODE,
            </if>
            <if test="csBranch != null">
                CS_BRANCH,
            </if>
            <if test="serviceAmount != null">
                SERVICE_AMOUNT,
            </if>
            <if test="offerDate != null">
                OFFER_DATE,
            </if>
            <if test="orderExpDate != null">
                ORDER_EXP_DATE,
            </if>
            <if test="reportLua != null">
                REPORT_LUA,
            </if>
            <if test="reportForm != null">
                REPORT_FORM,
            </if>
            <if test="testCycle != null">
                TEST_CYCLE,
            </if>
            <if test="isUrgent != null">
                IS_URGENT,
            </if>
            <if test="categoryPath != null">
                CATEGORY_PATH,
            </if>
            <if test="bu != null">
                BU,
            </if>
            <if test="userSex != null">
                USER_SEX,
            </if>
            <if test="csEmail != null">
                CS_EMAIL,
            </if>
            <if test="isRead != null">
                IS_READ,
            </if>
            <if test="recommendReason != null">
                RECOMMEND_REASON,
            </if>
            <if test="sampleRequirements != null">
                SAMPLE_REQUIREMENTS,
            </if>
            <if test="groupNo != null">
                GROUP_NO,
            </if>
            <if test="hisState != null">
                HIS_STATE,
            </if>
            <if test="csName != null">
                CS_NAME,
            </if>
            <if test="lineId != null">
                LINE_ID,
            </if>
            <if test="applicationLineId != null">
                APPLICATION_LINE_ID,
            </if>
            <if test="businessLine != null">
                BUSINESS_LINE,
            </if>
            <if test="urgentAmount != null">
                URGENT_AMOUNT,
            </if>
            <if test="tmpGroupNo != null">
                TMP_GROUP_NO,
            </if>
            <if test="isRemind != null">
                IS_REMIND,
            </if>
            <if test="csNameEn != null">
                CS_NAME_EN,
            </if>
            <if test="productName != null">
                PRODUCT_NAME,
            </if>
            <if test="catagoryId != null">
                CATAGORY_ID,
            </if>
            <if test="subState != null">
                SUB_STATE,
            </if>
            <if test="totalNums != null">
                TOTAL_NUMS,
            </if>
            <if test="productImg != null">
                PRODUCT_IMG,
            </if>
            <if test="isTest != null">
                IS_TEST,
            </if>
            <if test="isInvoice != null">
                IS_INVOICE,
            </if>
            <if test="auditCode != null">
                AUDIT_CODE,
            </if>
            <if test="reportLuaCode != null">
                REPORT_LUA_CODE,
            </if>
            <if test="reportFormCode != null">
                REPORT_FORM_CODE,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="isPayReceived != null">
                IS_PAY_RECEIVED,
            </if>
            <if test="testLabel != null">
                TEST_LABEL,
            </if>
            <if test="lastResponseDate != null">
                LAST_RESPONSE_DATE,
            </if>
            <if test="orderSource != null">
                ORDER_SOURCE,
            </if>
            <if test="orderSourceFrom != null">
                ORDER_SOURCE_FROM,
            </if>
            <if test="confirmOrderDate != null">
                CONFIRM_ORDER_DATE,
            </if>
            <if test="promoInfo != null">
                PROMO_INFO,
            </if>
            <if test="salesCode != null">
                SALES_CODE,
            </if>
            <if test="salesPhone != null">
                SALES_PHONE,
            </if>
            <if test="fromSource != null">
                FROM_SOURCE,
            </if>
            <if test="bossNo != null">
                BOSS_NO,
            </if>
            <if test="custId != null">
                CUST_ID,
            </if>
            <if test="monthPay != null">
                MONTH_PAY,
            </if>
            <if test="currency != null">
                CURRENCY,
            </if>
            <if test="companyNameEn != null">
                COMPANY_NAME_EN,
            </if>
            <if test="companyAddressCn != null">
                COMPANY_ADDRESS_CN,
            </if>
            <if test="companyAddressEn != null">
                COMPANY_ADDRESS_EN,
            </if>
            <if test="town != null">
                TOWN,
            </if>
            <if test="isElectron != null">
                IS_ELECTRON,
            </if>
            <if test="createCode != null">
                CREATE_CODE,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null">
                #{orderType,jdbcType=INTEGER},
            </if>
            <if test="orderAmount != null">
                #{orderAmount,jdbcType=DECIMAL},
            </if>
            <if test="shopDisAmount != null">
                #{shopDisAmount,jdbcType=DECIMAL},
            </if>
            <if test="questionId != null">
                #{questionId,jdbcType=BIGINT},
            </if>
            <if test="labId != null">
                #{labId,jdbcType=BIGINT},
            </if>
            <if test="labName != null">
                #{labName,jdbcType=VARCHAR},
            </if>
            <if test="discountAmount != null">
                #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="state != null">
                #{state,jdbcType=INTEGER},
            </if>
            <if test="proState != null">
                #{proState,jdbcType=INTEGER},
            </if>
            <if test="payState != null">
                #{payState,jdbcType=INTEGER},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userPhone != null">
                #{userPhone,jdbcType=VARCHAR},
            </if>
            <if test="userName != null">
                #{userName,jdbcType=VARCHAR},
            </if>
            <if test="userEmail != null">
                #{userEmail,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="province != null">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="relateOrderNo != null">
                #{relateOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payDate != null">
                #{payDate,jdbcType=TIMESTAMP},
            </if>
            <if test="createDate != null">
                #{createDate,jdbcType=TIMESTAMP},
            </if>
            <if test="stateDate != null">
                #{stateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="platform != null">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="platformOrder != null">
                #{platformOrder,jdbcType=VARCHAR},
            </if>
            <if test="csCode != null">
                #{csCode,jdbcType=VARCHAR},
            </if>
            <if test="csBranch != null">
                #{csBranch,jdbcType=VARCHAR},
            </if>
            <if test="serviceAmount != null">
                #{serviceAmount,jdbcType=DECIMAL},
            </if>
            <if test="offerDate != null">
                #{offerDate,jdbcType=TIMESTAMP},
            </if>
            <if test="orderExpDate != null">
                #{orderExpDate,jdbcType=TIMESTAMP},
            </if>
            <if test="reportLua != null">
                #{reportLua,jdbcType=VARCHAR},
            </if>
            <if test="reportForm != null">
                #{reportForm,jdbcType=VARCHAR},
            </if>
            <if test="testCycle != null">
                #{testCycle,jdbcType=DECIMAL},
            </if>
            <if test="isUrgent != null">
                #{isUrgent,jdbcType=TINYINT},
            </if>
            <if test="categoryPath != null">
                #{categoryPath,jdbcType=VARCHAR},
            </if>
            <if test="bu != null">
                #{bu,jdbcType=VARCHAR},
            </if>
            <if test="userSex != null">
                #{userSex,jdbcType=TINYINT},
            </if>
            <if test="csEmail != null">
                #{csEmail,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null">
                #{isRead,jdbcType=TINYINT},
            </if>
            <if test="recommendReason != null">
                #{recommendReason,jdbcType=VARCHAR},
            </if>
            <if test="sampleRequirements != null">
                #{sampleRequirements,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                #{groupNo,jdbcType=VARCHAR},
            </if>
            <if test="hisState != null">
                #{hisState,jdbcType=INTEGER},
            </if>
            <if test="csName != null">
                #{csName,jdbcType=VARCHAR},
            </if>
            <if test="lineId != null">
                #{lineId,jdbcType=BIGINT},
            </if>
            <if test="applicationLineId != null">
                #{applicationLineId,jdbcType=BIGINT},
            </if>
            <if test="businessLine != null">
                #{businessLine,jdbcType=VARCHAR},
            </if>
            <if test="urgentAmount != null">
                #{urgentAmount,jdbcType=DECIMAL},
            </if>
            <if test="tmpGroupNo != null">
                #{tmpGroupNo,jdbcType=VARCHAR},
            </if>
            <if test="isRemind != null">
                #{isRemind,jdbcType=TINYINT},
            </if>
            <if test="csNameEn != null">
                #{csNameEn,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="catagoryId != null">
                #{catagoryId,jdbcType=BIGINT},
            </if>
            <if test="subState != null">
                #{subState,jdbcType=INTEGER},
            </if>
            <if test="totalNums != null">
                #{totalNums,jdbcType=INTEGER},
            </if>
            <if test="productImg != null">
                #{productImg,jdbcType=VARCHAR},
            </if>
            <if test="isTest != null">
                #{isTest,jdbcType=TINYINT},
            </if>
            <if test="isInvoice != null">
                #{isInvoice,jdbcType=TINYINT},
            </if>
            <if test="auditCode != null">
                #{auditCode,jdbcType=VARCHAR},
            </if>
            <if test="reportLuaCode != null">
                #{reportLuaCode,jdbcType=VARCHAR},
            </if>
            <if test="reportFormCode != null">
                #{reportFormCode,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=TINYINT},
            </if>
            <if test="isPayReceived != null">
                #{isPayReceived,jdbcType=TINYINT},
            </if>
            <if test="testLabel != null">
                #{testLabel},
            </if>
            <if test="lastResponseDate != null">
                #{lastResponseDate},
            </if>
            <if test="orderSource != null">
                #{orderSource},
            </if>
            <if test="orderSourceFrom != null">
                #{orderSourceFrom},
            </if>
            <if test="confirmOrderDate != null">
                #{confirmOrderDate},
            </if>
            <if test="promoInfo != null">
                #{promoInfo},
            </if>
            <if test="salesCode != null">
                #{salesCode},
            </if>
            <if test="salesPhone != null">
                #{salesPhone},
            </if>
            <if test="fromSource != null">
                #{fromSource,jdbcType=VARCHAR},
            </if>
            <if test="bossNo != null">
                #{bossNo},
            </if>
            <if test="custId != null">
                #{custId},
            </if>
            <if test="monthPay != null">
                #{monthPay},
            </if>
            <if test="currency != null">
                #{currency},
            </if>
            <if test="companyNameEn != null">
                #{companyNameEn,jdbcType=VARCHAR},
            </if>
            <if test="companyAddressCn != null">
                #{companyAddressCn,jdbcType=VARCHAR},
            </if>
            <if test="companyAddressEn != null">
                #{companyAddressEn,jdbcType=VARCHAR},
            </if>
            <if test="town != null">
                #{town,jdbcType=VARCHAR},
            </if>
            <if test="isElectron != null">
                #{isElectron},
            </if>
            <if test="createCode != null">
                #{createCode},
            </if>
        </trim>
    </insert>







    <sql id="baseNewQueryWhere">
        where 1=1
        <if test="orderId != null">
            AND obi.ORDER_ID=#{orderId}
        </if>
        <if test="lineId != null">
            AND obi.LINE_ID=#{lineId}
        </if>

        <if test="orderNo != null">
            AND obi.ORDER_NO like concat('%',#{orderNo},'%')
        </if>
        <if test="userId != null">
            AND obi.USER_ID=#{userId}
        </if>
        <if test="stateNot != null">
            AND obi.STATE!=#{stateNot}
        </if>
        <if test="orderTypeList != null ">
            AND obi.ORDER_TYPE in
            <foreach collection="orderTypeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="csCode != null">
            AND obi.CS_CODE=#{csCode}
        </if>
        <if test="salesCode != null">
            AND obi.SALES_CODE=#{salesCode}
        </if>
        <if test="city != null">
            AND obi.CITY=#{city}
        </if>
        <if test="bu != null">
            AND obi.BU=#{bu}
        </if>
        <if test="province != null">
            AND obi.PROVINCE=#{province}
        </if>

        <if test="isTest != null">
            AND obi.IS_TEST=#{isTest}
        </if>
        <if test="itemName != null">
            AND obi.ORDER_NO in
            (select obit.ORDER_NO from ORDER_BASE_INFO obit,ORDER_DETAIL odt
            where obit.ORDER_NO=odt.ORDER_NO and obit.GROUP_NO=odt.GROUP_NO and odt.STATE = 1
            and ITEM_NAME like concat('%',#{itemName},'%') group by obit.ORDER_NO
            )
        </if>


        <if test="orderCreateFlg != null">
            <if  test="orderCreateFlg ==0">
                AND IFNULL(obi.FROM_SOURCE,"null") !="orderCreate"
            </if>
            <if  test="orderCreateFlg ==1">
                AND obi.FROM_SOURCE="orderCreate"
            </if>
        </if>

        <if test="orderSource != null">
            AND obi.ORDER_SOURCE=#{orderSource}
        </if>

        <if test="testLabel != null">
            AND obi.TEST_LABEL=#{testLabel}
        </if>



        <if test="stateList != null ">
            AND obi.STATE in
            <foreach collection="stateList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>



        <if test="orderIdList != null ">
            AND obi.ORDER_ID in
            <foreach collection="orderIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>


        <if test="labIdList != null ">
            AND obi.LAB_ID in
            <foreach collection="labIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="closeCodeList != null ">
            AND obi.CLOSE_CODE in
            <foreach collection="closeCodeList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="companyName!=null">
             AND obi.COMPANY_NAME like concat('%',#{companyName},'%')
        </if>
        <if test="userName!=null">
             AND obi.USER_NAME like concat('%',#{userName},'%')
        </if>
        <if test="userEmail!=null">
             AND obi.USER_EMAIL like concat('%',#{userEmail},'%')
        </if>
        <if test="userPhone!=null">
            AND obi.USER_PHONE like concat('%',#{userPhone},'%')
        </if>
        <if test="csEmail!=null">
            AND obi.CS_EMAIL like concat('%',#{csEmail},'%')
        </if>
        <if test="orderSourceFrom!=null">
            AND obi.ORDER_SOURCE_FROM like concat('%',#{orderSourceFrom},'%')
        </if>

    </sql>

    <sql id="InquiryQueryWhere">
        <if test="usePrivilegeLevel != null">
            AND (obi.STATE=1 or obi.CS_CODE=#{usePrivilegeLevel} )
        </if>
        <if test="crmCreateCsCode != null">
            AND obi.CREATE_CODE=#{crmCreateCsCode}
        </if>
        <if test="questionName != null">
            AND obi.QUESTION_ID in (select distinct QUESTION_ID  from TB_USER_QUESTION tuq where QUESTION_NAME like concat('%',#{questionName},'%') )
        </if>

        <if test="categoryPath!=null">
            AND obi.CATEGORY_PATH like concat('%',#{categoryPath},'%')
        </if>
        <if test="sampleBaseName!=null and sampleBaseName != ''">
            AND obi.ORDER_NO in (select distinct ORDER_NO from ORDER_SAMPLE where SAMPLE_NAME like concat('%',#{sampleBaseName},'%') and state=1 )
        </if>

        <if test="businessLineList != null ">
            AND obi.LINE_ID in
            <foreach collection="businessLineList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="useBusinessLineList != null and useLabIdList != null">
            AND ((obi.LINE_ID in
            <foreach collection="useBusinessLineList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            and obi.LAB_ID is null) or
            obi.LAB_ID in
            <foreach collection="useLabIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
            )
        </if>


        <if test="reInquiry != null">
            <if test="reInquiry ==0">
                AND obi.USER_ID not in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="INQUIRY_REPURCHASE")
            </if>
            <if test="reInquiry ==1">
                AND obi.USER_ID  in (select DISTINCT USER_ID from USER_LABEL where STATE=1 and LABEL_VALUE=1
                and  LABEL_GROUP="USER" and LABEL_CODE="INQUIRY_REPURCHASE")
            </if>
        </if>
        <if test="createDateStart!=null and createDateEnd!=null">
            AND obi.CREATE_DATE between #{createDateStart} and #{createDateEnd}
        </if>
        <if test="offerDateStart!=null and offerDateEnd!=null">
            AND obi.OFFER_DATE between #{offerDateStart} and #{offerDateEnd}
        </if>

        <if test="realAmountStart!=null and realAmountEnd!=null">
            AND obi.REAL_AMOUNT between #{realAmountStart} and #{realAmountEnd}
        </if>
        <if test="stateDateStart!=null and stateDateEnd!=null">
            AND obi.STATE=4 AND obi.STATE_DATE between #{stateDateStart} and #{stateDateEnd}
        </if>


        <if test="closeType != null and closeType != 'all'">
            <if test=" closeType == 'closeTrue'  ">
                AND obi.STATE=3
                AND obi.ORDER_EXP_DATE between #{closeQuiryDateStart} and #{closeQuiryDateEnd}
            </if>
            <if test=" closeType == 'closeFalse'  ">
                AND ( obi.STATE != 3 or  obi.ORDER_EXP_DATE &lt; #{closeQuiryDateStart} or obi.ORDER_EXP_DATE &gt; #{closeQuiryDateEnd} )
            </if>
        </if>
        <if test="fictitiousFlg != null ">
            <if test=" fictitiousFlg == 0 ">
                AND obi.ORDER_TYPE=200000
            </if>
            <if test=" fictitiousFlg == 1 ">
                AND obi.ORDER_TYPE=200001
            </if>
        </if>
    </sql>




    <update id="updateOrderBaseInfoByLeads" >
        UPDATE ORDER_BASE_INFO set ORDER_SOURCE=#{orderSource},LEADS_CODE=#{leadsCode}
        where ORDER_ID =#{orderId} and IFNULL(ORDER_SOURCE,"null")!=#{orderSource}
    </update>

    <update id="updateBaseSetLabIsNull" parameterType="java.lang.Long">
        UPDATE ORDER_BASE_INFO set LAB_ID=null,LAB_NAME=null  where ORDER_ID =#{orderId}
    </update>


    <select id="selectBaseByOrderNo"  resultMap="com.sgs.ecom.order.mapper.order.OrderBaseInfoCustomMapper.baseOrder"  >
        select
        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoCustomMapper.baseSql" />,SERVICE_AMOUNT,CS_DISCOUNT_AMOUNT
        from ORDER_BASE_INFO
        where ORDER_NO=#{orderNo}
    </select>
    <select id="selectBaseByOrderId"  resultMap="com.sgs.ecom.order.mapper.order.OrderBaseInfoCustomMapper.baseOrder"  >
        select
        <include refid="com.sgs.ecom.order.mapper.order.OrderBaseInfoCustomMapper.baseSql" />
        from ORDER_BASE_INFO
        where ORDER_ID=#{orderId}
    </select>
    <select id="qryOrderStateNum" resultMap="orderStateNumMap">
        SELECT (CASE obi.STATE
        WHEN 12 THEN 13 ELSE
        obi.STATE END
        ) STATE,count(distinct obi.ORDER_NO)  as STATE_COUNT from order_base_info obi
        <include refid="baseQueryWhere"/>
        GROUP BY obi.STATE

    </select>

    <select id="qryUsedUploadPaymentVoucher" resultMap="orderStateNumMap">
        SELECT 51 as 'STATE',count(distinct obi.ORDER_NO)  as STATE_COUNT from order_base_info obi
        <include refid="baseQueryWhere"/>
        AND obi.PAY_STATE=2
    </select>
    <select id="qryOrderReportDelay" resultMap="orderStateNumMap">
        SELECT 73 as 'STATE',count(distinct obi.ORDER_NO)  as STATE_COUNT from order_base_info obi
        <include refid="baseQueryWhere"/>
        AND
        (obi.DEADLINE_TIME is not null and DATE_FORMAT(obi.DEADLINE_TIME,'%Y%m%d') &lt; DATE_FORMAT(now( ) ,'%Y%m%d')
        and obi.ORDER_NO in (select DISTINCT obi.ORDER_NO from order_base_info obi where
        obi.ORDER_TYPE="100000"  and    ((select count(1) from order_attachment oa where oa.order_no=obi.ORDER_NO and oa.ATT_TYPE=10 )=0 and obi.STATE=14 ))
        )

    </select>
    <select id="qryOrderRefundConfirmed"  resultMap="orderStateNumMap">
        SELECT 53 as 'STATE',count(distinct obi.ORDER_NO)  as STATE_COUNT from order_base_info obi
        <include refid="baseQueryWhere"/>
        AND   obi.ORDER_NO IN (
        SELECT DISTINCT (CASE WHEN obi.RELATE_ORDER_NO is not null then obi.RELATE_ORDER_NO else 	obi.ORDER_NO END) as ORDER_NO from order_base_info obi where

        obi.ORDER_TYPE IN (100000,101000)
        AND
        obi.REFUND_STATE=1)
    </select>
    <select id="qryLatestOrderByOperatorCodeIsNotNull"
            resultMap="resultDTO">
        select * from order_base_info obi  where ORDER_TYPE  = 100000 and (operator_code is not null or operator_code != '') and USER_ID = #{userId} order by CREATE_DATE desc limit 1
    </select>



    <resultMap id="lastOrder" type="com.sgs.ecom.order.dto.order.ToOrderDTO" >
        <result column="ORDER_ID" property="orderId" jdbcType="VARCHAR" />
        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
        <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
    </resultMap>

    <select id="getLastOrderNoByInquiry"  resultMap="lastOrder" parameterType="java.util.List" >
        select ORDER_ID,ORDER_NO,RELATE_ORDER_NO from order_base_info where ORDER_ID in (
        select max(ORDER_ID) from ORDER_BASE_INFO obi where  obi.RELATE_ORDER_NO in
        <foreach collection="list"  item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by obi.RELATE_ORDER_NO
        )
    </select>
</mapper>
