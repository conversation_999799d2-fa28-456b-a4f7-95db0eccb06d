package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.platform.util.json.DateFormatSerializer;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.ecom.order.dto.bbc.TicAppFormDTO;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderReportDTO;
import com.sgs.ecom.order.dto.oiq.TfsOrderDTO;
import com.sgs.ecom.order.dto.pay.OrderPayDTO;
import com.sgs.ecom.order.dto.portal.PortalInquiryBaseDTO;
import com.sgs.ecom.order.dto.user.UserCouponDtl;
import com.sgs.ecom.order.enumtool.application.TestCycleEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.order.TestLabelEnum;
import com.sgs.ecom.order.util.order.OrderUtil;
import com.sgs.ecom.order.util.serializer.DateStringFormatSerializer;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class OrderBaseInfoMoreDTO extends OrderBaseInfoCheckDTO {

	public static final String CREATE_SQL = "select LAB_NAME,CREATE_DATE,ORDER_ID,STATE,DISCOUNT_AMOUNT,ORDER_TYPE,REAL_AMOUNT,ORDER_NO from ORDER_BASE_INFO";
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class})
	private String csCode;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String csEmail;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String csName;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String csNameEn;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class,RstsDetail.class})
	private BigDecimal realAmount;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	@BeanAnno(value="LAB_NAME", getName="getLabName", setName="setLabName")
	private String labName;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class,RstsDetail.class})
	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	private Timestamp createDate;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private int userSex;


	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class,RstsDetail.class})
	private String userPhone;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class,RstsDetail.class})
	private String userName;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,RstsDetail.class})
	private String userEmail;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String companyName;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String province;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String city;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class})
	private int monthPay;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,RstsDetail.class})
	private String promoInfo;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,RstsDetail.class})
	private String salesCode;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,RstsDetail.class})
	private String salesPhone;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String reportMemo;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String orderOperatorCode;
	@ApiAnno(groups={InquiryDetail.class,OrderDetail.class,Default.class})
	private int newTemplate=1;

	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={RstsDetail.class})
	private BigDecimal rstsAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={RstsList.class,RstsDetail.class})
	private BigDecimal platformAmount;
	@ApiAnno(groups={RstsDetail.class})
	private OrderOperatorLogDTO lastConfirmLog;
	@ApiAnno(groups={RstsDetail.class})
	private String toPayAmount;
	@ApiAnno(groups={RstsDetail.class})
	private int industryField;
	@ApiAnno(groups={RstsDetail.class})
	private String offPayAmount;
	@ApiAnno(groups={RstsDetail.class})
	private String onlinePayAmount;

	@ApiAnno(groups={RstsDetail.class})
	private List<OrderAttachmentDTO> invoiceFileList=new ArrayList<>();





	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String categoryPath;
	@ApiAnno(groups={OrderDetail.class})
	private String abstractCustcode;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private int isRead;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String offerDate;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String orderExpDate;
	@ApiAnno(groups={InquiryDetail.class})
	private String fromSource;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private TfsOrderDTO tfsOrder;


	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private List<OrderDetailDTO> orderDetailDTOList;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String sampleRequirements;//样品准备要求
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String recommendReason;//推荐理由
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String recommendReasonImage;//推荐理由

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,RstsDetail.class})
	private BigDecimal urgentAmount;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private BigDecimal testCycle;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private BigDecimal discountAmountNum;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private BigDecimal newDiscountAmountNum;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private BigDecimal urgentAmountNum;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String testCycleMemo;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String testCycleLog;

	@JsonSerialize(using = DateStringFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class,RstsDetail.class})
	private String deadlineTime;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class})
	private String deadlineMemo;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private BigDecimal taxRates;



	//订单新加参数


	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String productImg;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String relateOrderNo;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String reportForm;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String reportLua;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class,RstsDetail.class})
	private BigDecimal orderAmount;



	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class,RstsDetail.class})
	private int isTest;
	@ApiAnno(groups={InquiryDetail.class})
	private String toOrder;
	@ApiAnno(groups={InquiryDetail.class})
	private String toOrderId;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class})
	private int isUrgent;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String isUrgentShow;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private int testLabel;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String testLabelShow;
	@ApiAnno(groups={Default.class,InquiryDetail.class})
	private List<OrderSampleFromDTO> baseSampleFromList=new ArrayList<>();


	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,RstsDetail.class,TicDetail.class})
	private BigDecimal discountAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,RstsDetail.class,TicDetail.class})
	private BigDecimal serviceAmount;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={Default.class,TicDetail.class})
	private BigDecimal orderAmountPrice;//合计金额 orderAmount-storeAmount-couponAmount


	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String businessLine;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private Timestamp stateDate;

	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private String leadsCode;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class})
	private String orderSource;
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class,TicDetail.class})
	private String orderSourceShow;
	@ApiAnno(groups={Default.class,InquiryDetail.class,TicDetail.class})
	private List<OrderPayDTO> orderPayDTOList=new ArrayList<>();



	//询价单单独参数
	@ApiAnno(groups={InquiryDetail.class})
	private String questionId;
	@ApiAnno(groups={InquiryDetail.class,OrderDetail.class})
	private int priceType;
	@ApiAnno(groups={InquiryDetail.class})
	private Integer isDetermine=0;
	@ApiAnno(groups={InquiryDetail.class})
	private String determine;
	@ApiAnno(groups={InquiryDetail.class})
	private String productName;
	@ApiAnno(groups={InquiryDetail.class})
	private String orderSourceFrom;
	@ApiAnno(groups={Default.class,InquiryDetail.class})
	private int waitFlg=0;
	@ApiAnno(groups={InquiryDetail.class})
	private int temp=1;//1暂存
	@ApiAnno(groups={InquiryDetail.class})
	private int open=1;//1展开
	@ApiAnno(groups={InquiryDetail.class})
	private String testLabelMemo;
	@ApiAnno(groups={InquiryDetail.class})
	private int endState;


	//订单单独参数
	@ApiAnno(groups={OrderDetail.class})
	private Long relateOrderId;
	@ApiAnno(groups={OrderDetail.class})
	private int payNum;
	@ApiAnno(groups={OrderDetail.class})
	private int totalNums;

	@ApiAnno(groups={OrderDetail.class,TicDetail.class})
	private int isInvoice;
	@ApiAnno(groups={OrderDetail.class,TicDetail.class})
	private String operatorCode;
	@ApiAnno(groups={OrderDetail.class,TicDetail.class})
	private String auditCode;
	@ApiAnno(groups={OrderDetail.class,TicDetail.class})
	private Integer isPayReceived;
	@ApiAnno(groups={OrderDetail.class,TicDetail.class})
	private int subOrderFlg=0;
	@ApiAnno(groups={OrderDetail.class,TicDetail.class,RstsDetail.class})
	private List<SubOrderDTO> subOrderDTOList ;
	@ApiAnno(groups={RstsDetail.class})
	private SubOrderInfoDTO subOrderInfoDTO;

	//小门户
	@ApiAnno(groups={OrderDetail.class,InquiryDetail.class})
	private List<OrderAttachmentDTO> quotationFileList=new ArrayList<>();


	@ApiAnno(groups={TicDetail.class})
	private UserCouponDtl userCouponDtl;

	@ApiAnno(groups={TicDetail.class})
	private List<OrderProductDTO> orderProductDTOList;
	@ApiAnno(groups={TicDetail.class})
	private String storeId;
	@ApiAnno(groups={TicDetail.class,RstsDetail.class})
	private List<OrderAttachmentDTO> reportList=new ArrayList<>();

	@ApiAnno(groups={TicDetail.class})
	private String storeName;
	@ApiAnno(groups={TicDetail.class})
	private OrderApplicationFormDTO orderApplicationFormDTO;

	@ApiAnno(groups={TicDetail.class})
	private TicAppFormDTO ticAppFormDTO;
	@ApiAnno(groups={TicDetail.class})
	private OrderPayDTO orderServiceRefundPayDTO;
	@ApiAnno(groups={TicDetail.class})
	private OrderPayDTO refundPayDTO;
	@ApiAnno(groups={TicDetail.class})
	private int important;
	@ApiAnno(groups={TicDetail.class})
	private String orderSourceMemo;
	@ApiAnno(groups={TicDetail.class,RstsDetail.class,OrderDetail.class})
	private int payMethod;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,RstsDetail.class})
	private String confirmOrderDate;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={TicDetail.class})
	private BigDecimal shopDisAmount;

	@ApiAnno(groups={RstsDetail.class,InquiryDetail.class,OrderDetail.class})
	private String currency;
	@ApiAnno(groups={InquiryDetail.class,OrderDetail.class})
	private String currencyMark;
	@ApiAnno(groups={InquiryDetail.class,OrderDetail.class})
	private BigDecimal exchangeRate;


	@ApiAnno(groups={TicDetail.class})
	private String customType;
	@JsonSerialize(using = TimeStringFormatSerializer.class)
	@ApiAnno(groups={Default.class,InquiryDetail.class,RstsDetail.class})
	private String applySubmitDate;

	@ApiAnno(groups={OrderDetail.class,Default.class})
	private PortalInquiryBaseDTO inquiryBaseDTO;

	@ApiAnno(groups={TicDetail.class})
	private int isRefundAll;//发起的退款是全额还是部分退款  0-无退款信息 1-全部  2-部分
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是

	@ApiAnno(groups={RstsDetail.class})
	private String stageTime;

	@ApiAnno(groups={TicDetail.class})
	private LabDTO labDTO;

	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private List<OrderSampleDTO> orderSampleDTOList;
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private BigDecimal csDiscountAmount;//客服优惠金额
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private BigDecimal csDisCountRate;//客服优惠折扣
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private String model;//是否新拆单规则标识
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private BigDecimal realCsAfterAmount;//客服优惠后的金额


	@JsonSerialize(using = PriceNumFormatSerializer.class)
	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private BigDecimal oldRealAmount;//原始的订单实价格 = 订单原价-店铺优惠-优惠券优惠

	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private List<OrderDetailDTO> parentDetails;



	@ApiAnno(groups={OrderDetail.class,Default.class,TicDetail.class})
	private OrderReportDTO orderReport;

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public BigDecimal getOldRealAmount() {
		BigDecimal orderOldDecimal = !ValidationUtil.isEmpty(orderAmount) ? orderAmount : BigDecimal.ZERO;
		BigDecimal shopDecimal = !ValidationUtil.isEmpty(shopDisAmount) ? shopDisAmount : BigDecimal.ZERO;
		BigDecimal disCountDecimal = !ValidationUtil.isEmpty(discountAmount) ? discountAmount : BigDecimal.ZERO;
		return orderOldDecimal.subtract(shopDecimal).subtract(disCountDecimal);
	}

	public BigDecimal getRealCsAfterAmount() {
		BigDecimal realDecimal = !ValidationUtil.isEmpty(realAmount) ? realAmount : BigDecimal.ZERO;
		BigDecimal serviceDecimal = !ValidationUtil.isEmpty(serviceAmount) ? serviceAmount : BigDecimal.ZERO;
		return realDecimal.subtract(serviceDecimal);
	}

	public List<OrderDetailDTO> getParentDetails() {
		return parentDetails;
	}

	public void setParentDetails(List<OrderDetailDTO> parentDetails) {
		this.parentDetails = parentDetails;
	}


	public BigDecimal getCsDiscountAmount() {
		return csDiscountAmount;
	}

	public void setCsDiscountAmount(BigDecimal csDiscountAmount) {
		this.csDiscountAmount = csDiscountAmount;
	}

	public BigDecimal getCsDisCountRate() {
		BigDecimal orderOldDecimal = !ValidationUtil.isEmpty(orderAmount) ? orderAmount : BigDecimal.ZERO;
		BigDecimal shopDecimal = !ValidationUtil.isEmpty(shopDisAmount) ? shopDisAmount : BigDecimal.ZERO;
		BigDecimal disCountDecimal = !ValidationUtil.isEmpty(discountAmount) ? discountAmount : BigDecimal.ZERO;
		BigDecimal realDecimal = !ValidationUtil.isEmpty(realAmount) ? realAmount : BigDecimal.ZERO;
		BigDecimal serviceDecimal = !ValidationUtil.isEmpty(serviceAmount) ? serviceAmount : BigDecimal.ZERO;
		BigDecimal subtract = realDecimal.subtract(serviceDecimal);
		//客服折扣优惠率 = (实付金额-加急费)/(订单实付金额-店铺优惠-优惠券优惠)
		BigDecimal subtract1 = orderOldDecimal.subtract(shopDecimal).subtract(disCountDecimal);
		if(ValidationUtil.isEmpty(subtract1) || subtract1.doubleValue() == 0){
			return  BigDecimal.ZERO;
		}else {
			return subtract.divide(orderOldDecimal.subtract(shopDecimal).subtract(disCountDecimal), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
		}
	}

	@ApiAnno(groups={OrderDetail.class})
	private List<CustomerIdDTO> customerList=new ArrayList<>();


	public LabDTO getLabDTO() {
		return labDTO;
	}

	public void setLabDTO(LabDTO labDTO) {
		this.labDTO = labDTO;
	}

	public List<OrderSampleDTO> getOrderSampleDTOList() {
		return orderSampleDTOList;
	}

	public void setOrderSampleDTOList(List<OrderSampleDTO> orderSampleDTOList) {
		this.orderSampleDTOList = orderSampleDTOList;
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public PortalInquiryBaseDTO getInquiryBaseDTO() {
		return inquiryBaseDTO;
	}

	public void setInquiryBaseDTO(PortalInquiryBaseDTO inquiryBaseDTO) {
		this.inquiryBaseDTO = inquiryBaseDTO;
	}

	public int getIsRefundAll() {
		return isRefundAll;
	}

	public void setIsRefundAll(int isRefundAll) {
		this.isRefundAll = isRefundAll;
	}



	public String getCustomType() {
		return customType;
	}

	public void setCustomType(String customType) {
		this.customType = customType;
	}


	public OrderPayDTO getRefundPayDTO() {
		return refundPayDTO;
	}
	public void setRefundPayDTO(OrderPayDTO refundPayDTO) {
		this.refundPayDTO = refundPayDTO;
	}
	public OrderPayDTO getOrderServiceRefundPayDTO() {
		return orderServiceRefundPayDTO;
	}
	public void setOrderServiceRefundPayDTO(OrderPayDTO orderServiceRefundPayDTO) {
		this.orderServiceRefundPayDTO = orderServiceRefundPayDTO;
	}
	public void setLabName(String labName){
		this.labName=labName;
	}
	public String getLabName(){
		return this.labName;
	}


	public void setCreateDate(Timestamp createDate){
		this.createDate=createDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getCreateDate(){
		return this.createDate;
	}







	public List<OrderDetailDTO> getOrderDetailDTOList() {
		return orderDetailDTOList;
	}

	public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
		this.orderDetailDTOList = orderDetailDTOList;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public String getRecommendReason() {
		return recommendReason;
	}

	public void setRecommendReason(String recommendReason) {
		this.recommendReason = recommendReason;
	}
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}


	public BigDecimal getTestCycle() {
		return testCycle;
	}

	public void setTestCycle(BigDecimal testCycle) {
		this.testCycle = testCycle;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public Integer getIsDetermine() {
		return isDetermine;
	}

	public void setIsDetermine(Integer isDetermine) {
		this.isDetermine = isDetermine;
	}

	public String getDetermine() {
		return determine;
	}

	public void setDetermine(String determine) {
		this.determine = determine;
	}

	public int getIsRead() {
		return isRead;
	}

	public void setIsRead(int isRead) {
		this.isRead = isRead;
	}

	public String getOfferDate() {
		return offerDate;
	}

	public void setOfferDate(String offerDate) {
		this.offerDate = offerDate;
	}

	public String getOrderExpDate() {
		return orderExpDate;
	}

	public void setOrderExpDate(String orderExpDate) {
		this.orderExpDate = orderExpDate;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}



	public int getUserSex() {
		return userSex;
	}

	public void setUserSex(int userSex) {
		this.userSex = userSex;
	}








	public String getQuestionId() {
		return questionId;
	}

	public void setQuestionId(String questionId) {
		this.questionId = questionId;
	}



	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}



	public BigDecimal getDiscountAmountNum() {
		return discountAmountNum;
	}

	public void setDiscountAmountNum(BigDecimal discountAmountNum) {
		this.discountAmountNum = discountAmountNum;
	}

	public BigDecimal getUrgentAmountNum() {
		return urgentAmountNum;
	}

	public void setUrgentAmountNum(BigDecimal urgentAmountNum) {
		this.urgentAmountNum = urgentAmountNum;
	}

	public Long getRelateOrderId() {
		return relateOrderId;
	}

	public void setRelateOrderId(Long relateOrderId) {
		this.relateOrderId = relateOrderId;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}
	@JsonSerialize(using = DateFormatSerializer.class)
	public Timestamp getStateDate() {
		return stateDate;
	}

	public void setStateDate(Timestamp stateDate) {
		this.stateDate = stateDate;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public int getTotalNums() {
		return totalNums;
	}

	public void setTotalNums(int totalNums) {
		this.totalNums = totalNums;
	}



	public String getProductImg() {
		return productImg;
	}

	public void setProductImg(String productImg) {
		this.productImg = productImg;
	}

	public String getRelateOrderNo() {
		return relateOrderNo;
	}

	public void setRelateOrderNo(String relateOrderNo) {
		this.relateOrderNo = relateOrderNo;
	}

	public String getReportForm() {
		return reportForm;
	}

	public void setReportForm(String reportForm) {
		this.reportForm = reportForm;
	}

	public String getReportLua() {
		return reportLua;
	}

	public int getPayNum() {
		return payNum;
	}

	public void setPayNum(int payNum) {
		this.payNum = payNum;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}


	public int getIsTest() {
		return isTest;
	}

	public void setIsTest(int isTest) {
		this.isTest = isTest;
	}

	public int getIsInvoice() {
		return isInvoice;
	}

	public void setIsInvoice(int isInvoice) {
		this.isInvoice = isInvoice;
	}

	public String getAuditCode() {
		return auditCode;
	}

	public void setAuditCode(String auditCode) {
		this.auditCode = auditCode;
	}

	public Integer getIsPayReceived() {
		return isPayReceived;
	}

	public void setIsPayReceived(Integer isPayReceived) {
		this.isPayReceived = isPayReceived;
	}

	public String getTestCycleMemo() {
		return testCycleMemo;
	}

	public void setTestCycleMemo(String testCycleMemo) {
		this.testCycleMemo = testCycleMemo;
	}

	public String getLeadsCode() {
		return leadsCode;
	}

	public void setLeadsCode(String leadsCode) {
		this.leadsCode = leadsCode;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}


	public int getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(int isUrgent) {
		this.isUrgent = isUrgent;
	}

	public String getIsUrgentShow() {
		return TestCycleEnum.getNameCh(String.valueOf(isUrgent));
	}

	public String getRecommendReasonImage() {
		return recommendReasonImage;
	}

	public void setRecommendReasonImage(String recommendReasonImage) {
		this.recommendReasonImage = recommendReasonImage;
	}

	public void setIsUrgentShow(String isUrgentShow) {
		this.isUrgentShow = isUrgentShow;
	}

	public int getTestLabel() {
		return testLabel;
	}

	public void setTestLabel(int testLabel) {
		this.testLabel = testLabel;
	}

	public String getTestLabelShow() {
		return TestLabelEnum.getNameCh(testLabel);
	}

	@Override
	public String getCsCode() {
		return csCode;
	}

	@Override
	public void setCsCode(String csCode) {
		this.csCode = csCode;
	}

	@Override
	public String getCsEmail() {
		return csEmail;
	}

	@Override
	public void setCsEmail(String csEmail) {
		this.csEmail = csEmail;
	}

	@Override
	public String getCsName() {
		return csName;
	}

	@Override
	public void setCsName(String csName) {
		this.csName = csName;
	}

	@Override
	public String getCsNameEn() {
		return csNameEn;
	}

	@Override
	public void setCsNameEn(String csNameEn) {
		this.csNameEn = csNameEn;
	}


	@JsonSerialize(using = PriceNumFormatSerializer.class)
	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public String getDeadlineMemo() {
		return deadlineMemo;
	}

	public void setDeadlineMemo(String deadlineMemo) {
		this.deadlineMemo = deadlineMemo;
	}

	@Override
	public String getOperatorCode() {
		return operatorCode;
	}

	@Override
	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public String getTestCycleLog() {
		return testCycleLog;
	}

	public void setTestCycleLog(String testCycleLog) {
		this.testCycleLog = testCycleLog;
	}

	public int getSubOrderFlg() {
		return subOrderFlg;
	}

	public void setSubOrderFlg(int subOrderFlg) {
		this.subOrderFlg = subOrderFlg;
	}

	public List<SubOrderDTO> getSubOrderDTOList() {
		return subOrderDTOList;
	}

	public void setSubOrderDTOList(List<SubOrderDTO> subOrderDTOList) {
		this.subOrderDTOList = subOrderDTOList;
	}

	public String getOrderSourceFrom() {
		return orderSourceFrom;
	}

	public void setOrderSourceFrom(String orderSourceFrom) {
		this.orderSourceFrom = orderSourceFrom;
	}

	public int getWaitFlg() {
		return waitFlg;
	}

	public void setWaitFlg(int waitFlg) {
		this.waitFlg = waitFlg;
	}

	public String getOrderSourceShow() {
		 return OrderSourceEnum.getNameCh(orderSource);
	}

	public int getTemp() {
		return temp;
	}

	public void setTemp(int temp) {
		this.temp = temp;
	}

	public int getOpen() {
		return open;
	}

	public void setOpen(int open) {
		this.open = open;
	}

	public String getTestLabelMemo() {
		return testLabelMemo;
	}

	public void setTestLabelMemo(String testLabelMemo) {
		this.testLabelMemo = testLabelMemo;
	}

	public List<OrderProductDTO> getOrderProductDTOList() {
		return orderProductDTOList;
	}

	public void setOrderProductDTOList(List<OrderProductDTO> orderProductDTOList) {
		this.orderProductDTOList = orderProductDTOList;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public String getStoreName() {
		return storeName;
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public OrderApplicationFormDTO getOrderApplicationFormDTO() {
		return orderApplicationFormDTO;
	}

	public void setOrderApplicationFormDTO(OrderApplicationFormDTO orderApplicationFormDTO) {
		this.orderApplicationFormDTO = orderApplicationFormDTO;
	}



	public TicAppFormDTO getTicAppFormDTO() {
		return ticAppFormDTO;
	}

	public void setTicAppFormDTO(TicAppFormDTO ticAppFormDTO) {
		this.ticAppFormDTO = ticAppFormDTO;
	}

	public int getImportant() {
		return important;
	}

	public void setImportant(int important) {
		this.important = important;
	}

	public void setTestLabelShow(String testLabelShow) {
		this.testLabelShow = testLabelShow;
	}

	public void setOrderSourceShow(String orderSourceShow) {
		this.orderSourceShow = orderSourceShow;
	}

	public String getOrderSourceMemo() {
		return orderSourceMemo;
	}

	public void setOrderSourceMemo(String orderSourceMemo) {
		this.orderSourceMemo = orderSourceMemo;
	}



	public String getConfirmOrderDate() {
		return confirmOrderDate;
	}

	public void setConfirmOrderDate(String confirmOrderDate) {
		this.confirmOrderDate = confirmOrderDate;
	}

	public String getPromoInfo() {
		return promoInfo;
	}

	public void setPromoInfo(String promoInfo) {
		this.promoInfo = promoInfo;
	}

	public String getSalesCode() {
		return salesCode;
	}

	public void setSalesCode(String salesCode) {
		this.salesCode = salesCode;
	}

	public String getReportMemo() {
		return reportMemo;
	}

	public void setReportMemo(String reportMemo) {
		this.reportMemo = reportMemo;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}

	public UserCouponDtl getUserCouponDtl() {
		return userCouponDtl;
	}

	public void setUserCouponDtl(UserCouponDtl userCouponDtl) {
		this.userCouponDtl = userCouponDtl;
	}

	public List<OrderSampleFromDTO> getBaseSampleFromList() {
		return baseSampleFromList;
	}

	public void setBaseSampleFromList(List<OrderSampleFromDTO> baseSampleFromList) {
		this.baseSampleFromList = baseSampleFromList;
	}

	public String getFromSource() {
		return fromSource;
	}

	public void setFromSource(String fromSource) {
		this.fromSource = fromSource;
	}

	public String getSalesPhone() {
		return salesPhone;
	}

	public void setSalesPhone(String salesPhone) {
		this.salesPhone = salesPhone;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public BigDecimal getOrderAmountPrice() {
		return OrderUtil.toZero(orderAmount).subtract(OrderUtil.toZero(discountAmount)).subtract(OrderUtil.toZero(shopDisAmount));
	}

	public void setOrderAmountPrice(BigDecimal orderAmountPrice) {
		this.orderAmountPrice = orderAmountPrice;
	}

	public List<OrderAttachmentDTO> getReportList() {
		return reportList;
	}

	public void setReportList(List<OrderAttachmentDTO> reportList) {
		this.reportList = reportList;
	}

	public BigDecimal getRstsAmount() {
		return rstsAmount;
	}

	public void setRstsAmount(BigDecimal rstsAmount) {
		this.rstsAmount = rstsAmount;
	}

	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public SubOrderInfoDTO getSubOrderInfoDTO() {
		return subOrderInfoDTO;
	}

	public void setSubOrderInfoDTO(SubOrderInfoDTO subOrderInfoDTO) {
		this.subOrderInfoDTO = subOrderInfoDTO;
	}

	public List<OrderPayDTO> getOrderPayDTOList() {
		return orderPayDTOList;
	}

	public void setOrderPayDTOList(List<OrderPayDTO> orderPayDTOList) {
		this.orderPayDTOList = orderPayDTOList;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public String getOrderOperatorCode() {
		return orderOperatorCode;
	}

	public void setOrderOperatorCode(String orderOperatorCode) {
		this.orderOperatorCode = orderOperatorCode;
	}

	public int getEndState() {
		return endState;
	}

	public void setEndState(int endState) {
		this.endState = endState;
	}

	public int getPriceType() {
		return priceType;
	}

	public void setPriceType(int priceType) {
		this.priceType = priceType;
	}

	public BigDecimal getNewDiscountAmountNum() {
		return newDiscountAmountNum;
	}

	public void setNewDiscountAmountNum(BigDecimal newDiscountAmountNum) {
		this.newDiscountAmountNum = newDiscountAmountNum;
	}

	public int getNewTemplate() {
		return newTemplate;
	}

	public void setNewTemplate(int newTemplate) {
		this.newTemplate = newTemplate;
	}

	public List<OrderAttachmentDTO> getQuotationFileList() {
		return quotationFileList;
	}

	public void setQuotationFileList(List<OrderAttachmentDTO> quotationFileList) {
		this.quotationFileList = quotationFileList;
	}

	public String getStageTime() {
		return stageTime;
	}

	public void setStageTime(String stageTime) {
		this.stageTime = stageTime;
	}

	public OrderOperatorLogDTO getLastConfirmLog() {
		return lastConfirmLog;
	}

	public void setLastConfirmLog(OrderOperatorLogDTO lastConfirmLog) {
		this.lastConfirmLog = lastConfirmLog;
	}

	public String getToPayAmount() {
		return toPayAmount;
	}

	public void setToPayAmount(String toPayAmount) {
		this.toPayAmount = toPayAmount;
	}

	public int getIndustryField() {
		return industryField;
	}

	public void setIndustryField(int industryField) {
		this.industryField = industryField;
	}

	public String getOffPayAmount() {
		return offPayAmount;
	}

	public void setOffPayAmount(String offPayAmount) {
		this.offPayAmount = offPayAmount;
	}

	public String getOnlinePayAmount() {
		return onlinePayAmount;
	}

	public void setOnlinePayAmount(String onlinePayAmount) {
		this.onlinePayAmount = onlinePayAmount;
	}

	@Override
	public int getPayMethod() {
		return payMethod;
	}

	@Override
	public void setPayMethod(int payMethod) {
		this.payMethod = payMethod;
	}

	public BigDecimal getExchangeRate() {
		return exchangeRate;
	}

	public void setExchangeRate(BigDecimal exchangeRate) {
		this.exchangeRate = exchangeRate;
	}

	public String getCurrencyMark() {
		return currencyMark;
	}

	public void setCurrencyMark(String currencyMark) {
		this.currencyMark = currencyMark;
	}

	public String getAbstractCustcode() {
		return abstractCustcode;
	}

	public void setAbstractCustcode(String abstractCustcode) {
		this.abstractCustcode = abstractCustcode;
	}

	public List<CustomerIdDTO> getCustomerList() {
		return customerList;
	}

	public void setCustomerList(List<CustomerIdDTO> customerList) {
		this.customerList = customerList;
	}

	public String getToOrder() {
		return toOrder;
	}

	public void setToOrder(String toOrder) {
		this.toOrder = toOrder;
	}

	public String getToOrderId() {
		return toOrderId;
	}

	public void setToOrderId(String toOrderId) {
		this.toOrderId = toOrderId;
	}

	public BigDecimal getTaxRates() {
		return taxRates;
	}

	public void setTaxRates(BigDecimal taxRates) {
		this.taxRates = taxRates;
	}

	public List<OrderAttachmentDTO> getInvoiceFileList() {
		return invoiceFileList;
	}

	public void setInvoiceFileList(List<OrderAttachmentDTO> invoiceFileList) {
		this.invoiceFileList = invoiceFileList;
	}

	public OrderReportDTO getOrderReport() {
		return orderReport;
	}

	public void setOrderReport(OrderReportDTO orderReport) {
		this.orderReport = orderReport;
	}

	public TfsOrderDTO getTfsOrder() {
		return tfsOrder;
	}

	public void setTfsOrder(TfsOrderDTO tfsOrder) {
		this.tfsOrder = tfsOrder;
	}
}