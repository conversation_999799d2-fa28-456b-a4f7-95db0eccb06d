package com.sgs.ecom.order.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

public class UserAddressDTO {
 
 	public static final String CREATE_SQL = "select STATE_DATE,USER_ID,STATE,USER_NAME,COMPANY_ADDRESS,COMPANY_NAME,USER_PHONE,CREATE_DATE,CITY,TOWN,PROVINCE,IS_DEFAULT,ADDRESS_ID,USER_MAIL from USER_ADDRESS"; 
 

 	private long userId;

 	private int state;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String userName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String companyAddress;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String companyName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String userPhone;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String city;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String town;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String province;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private int isDefault;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private Long addressId;
	@ApiAnno(groups={BaseQryFilter.Default.class})
 	private String userMail;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String addressUuid;


 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setCompanyAddress(String companyAddress){
 		 this.companyAddress=companyAddress;
 	}
 	public String getCompanyAddress(){
 		 return this.companyAddress;
 	}
 
 	 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setUserPhone(String userPhone){
 		 this.userPhone=userPhone;
 	}
 	public String getUserPhone(){
 		 return this.userPhone;
 	}
 

 
 	 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setProvince(String province){
 		 this.province=province;
 	}
 	public String getProvince(){
 		 return this.province;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 

 	 
 	public void setUserMail(String userMail){
 		 this.userMail=userMail;
 	}
 	public String getUserMail(){
 		 return this.userMail;
 	}

	public Long getAddressId() {
		return addressId;
	}

	public void setAddressId(Long addressId) {
		this.addressId = addressId;
	}

	public String getAddressUuid() {
		return addressUuid;
	}

	public void setAddressUuid(String addressUuid) {
		this.addressUuid = addressUuid;
	}
}