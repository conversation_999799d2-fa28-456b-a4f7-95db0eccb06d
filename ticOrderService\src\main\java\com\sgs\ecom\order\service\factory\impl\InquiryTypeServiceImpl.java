package com.sgs.ecom.order.service.factory.impl;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.config.OiqApplicationProperties;
import com.sgs.ecom.order.domain.order.OrderAttachmentDO;
import com.sgs.ecom.order.domain.order.OrderSampleDO;
import com.sgs.ecom.order.domain.service.order.interfaces.*;
import com.sgs.ecom.order.dto.center.BusinessLineDTO;
import com.sgs.ecom.order.dto.center.EnumDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.custom.DetailItemNameDTO;
import com.sgs.ecom.order.dto.custom.OrderCheckStateDTO;
import com.sgs.ecom.order.dto.custom.OrderSampleNameDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.oiq.TfsOrderDTO;
import com.sgs.ecom.order.dto.order.*;
import com.sgs.ecom.order.dto.question.OrderQuestionDTO;
import com.sgs.ecom.order.dto.rpc.CenterSampleDTO;
import com.sgs.ecom.order.dto.user.UserLabelDTO;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.application.OiqFormEnum;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.enumtool.user.UserLabelCodeEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.infrastructure.handle.OrderUtilHandle;
import com.sgs.ecom.order.request.OrderBaseInfoReq;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.request.express.ExpressIdReq;
import com.sgs.ecom.order.request.operator.OrderCloseReq;
import com.sgs.ecom.order.service.custom.interfaces.ICustomLimitService;
import com.sgs.ecom.order.service.factory.interfaces.IOrderTypeService;
import com.sgs.ecom.order.service.member.interfaces.*;
import com.sgs.ecom.order.service.order.interfaces.*;
import com.sgs.ecom.order.service.user.interfaces.IUserLabelSV;
import com.sgs.ecom.order.service.util.impl.OrderServiceImpl;
import com.sgs.ecom.order.service.util.interfaces.IOrderModService;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.attr.MemoUtil;
import com.sgs.ecom.order.util.collection.MapUtil;
import com.sgs.ecom.order.util.order.OrderUtil;
import com.sgs.ecom.order.util.select.AttributeUtil;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.util.time.TimeCalendarUtil;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;


@Component("oiqInquiry")
@Slf4j
public class InquiryTypeServiceImpl extends BaseService implements IOrderTypeService {

    Logger logger= LoggerFactory.getLogger(OrderServiceImpl.class);
    @Autowired
    private IMemberRestTemplateService memberRestTemplateService;
    @Autowired
    private IOrderDetailService orderDetailService;
    @Autowired
    private IOrderAttributeService orderAttributeService;
    @Autowired
    private IOrderAttributeDomainService orderAttributeDomainService;
    @Autowired
    private ICustomLimitService customLimitService;
    @Autowired
    private IOrderBaseInfoService orderBaseInfoService;
    @Autowired
    private IOrderUtilService orderUtilService;
    @Autowired
    private IOrderLogService orderLogService;
    @Autowired
    private ISSOTemplateSV issoTemplateSV;
    @Autowired
    private IOrderMemoSV orderMemoSV;
    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Autowired
    private IUserLabelSV userLabelSV;
    @Autowired
    private IOrderSampleFromService orderSampleFromService;
    @Autowired
    private IOrderModService orderModService;
    @Autowired
    private ISmsRestTemplateService smsRestTemplateService;
    @Autowired
    private SmsEventUtil smsEventUtil;
    @Autowired
    private ApiEventUtil apiEventUtil;
    @Autowired
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Autowired
    private IOrderSampleFromDomainService orderSampleFromDomainService;
    @Autowired
    private IOrderAttachmentDomainService orderAttachmentDomainService;
    @Autowired
    private ICenterTemplateSV centerTemplateSV;
    @Autowired
    private OiqApplicationProperties oiqSampleProperties;
    @Autowired
    private IOrderApplicationFormDomainService orderApplicationFormDomainService;
    @Autowired
    private OrderUtilHandle orderUtilHandle;
    @Autowired
    private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;




    public JSONObject list(OrderBaseInfoReq orderBaseInfoReq,Map map)throws Exception {
        PageHelper.startPage(orderBaseInfoReq.getPageNum(),orderBaseInfoReq.getPageRow());
        List<OrderBaseInfoDTO> orderBaseInfoDTOS=orderBaseInfoService.selectListByMap(map);
        if(!ValidationUtil.isEmpty(orderBaseInfoDTOS)){
            listAddMore(orderBaseInfoDTOS);
        }
        PageInfo pageInfo=new PageInfo(orderBaseInfoDTOS);
        return jsonTransUtil.toJSONString(OrderBaseInfoDTO.class,orderBaseInfoDTOS,pageInfo, BaseOrderFilter.InquiryList.class);
    }


    public List<OrderBaseInfoDTO> listAddMore(List<OrderBaseInfoDTO> orderBaseInfoDTOS) {

        List<Long> userIdList = new ArrayList<>();
        for(OrderBaseInfoDTO orderBaseInfoDTO:orderBaseInfoDTOS){
            userIdList.add(orderBaseInfoDTO.getUserId());
        }
        List<UserLabelDTO> userLabelDTOList=userLabelSV.selectList(userIdList, UserLabelCodeEnum.INQUIRY_REPURCHASE);
        Map<Long, String> labelFlg=userLabelDTOList.stream().collect(Collectors.toMap(E->E.getUserId(), E->E.getLabelValue(), (key1, key2) -> key2));



        Map<String,String> map=orderUtilService.getBuNameByRedis();
        for(int n=0;n<orderBaseInfoDTOS.size();n++) {
            OrderBaseInfoDTO orderBaseInfoDTO = orderBaseInfoDTOS.get(n);
            if(labelFlg.containsKey(orderBaseInfoDTO.getUserId())){
                orderBaseInfoDTO.setUserLabelFlg(1);
            }
            //添加最后一条日志
            orderBaseInfoDTO.setLastLog(orderLogService.getLastOrderLog(orderBaseInfoDTO.getOrderNo()));

            //通过询价单查询出最后一条订单状态，如果不是关闭的给他关闭
            if(orderBaseInfoDTO.getState()==4){
                OrderCheckStateDTO orderCheckStateDTO=orderBaseInfoCustomService.selectLastOrderByRelate(orderBaseInfoDTO.getOrderNo());
                orderBaseInfoDTO.setOrderShow(OrderStateEnum.getNameCh(String.valueOf(orderCheckStateDTO.getState())));
            }

            if(StringUtils.isNotBlank(orderBaseInfoDTO.getGroupNo())){
                //算更新报价了 查询项目
                DetailItemNameDTO detailItemNameDTO=customLimitService.
                        selectFirstDetail(orderBaseInfoDTO.getOrderNo(),orderBaseInfoDTO.getGroupNo());
                if(detailItemNameDTO!=null){
                    orderBaseInfoDTO.setItemName(detailItemNameDTO.getItemName());
                    //查询关联的第一条样品
                    OrderSampleNameDTO orderSampleNameDTO=customLimitService.selectFirstSample(detailItemNameDTO.getDetailId(),orderBaseInfoDTO.getOrderNo());
                    if(orderSampleNameDTO!=null){
                        orderBaseInfoDTO.setSampleNameStr(orderSampleNameDTO.getOrderSampleName());
                    }
                }
            }
            if(map.containsKey(orderBaseInfoDTO.getBu())){
                orderBaseInfoDTO.setBuName(map.get(orderBaseInfoDTO.getBu()));
            }
            //

            String baseKey = RedisKeyUtil.ORDER_BASE_FIRSTKEY+"-"+orderBaseInfoDTO.getOrderNo();
            List<JSONObject> userAnswerDTOList = memberRestTemplateService.getUserAnswerListByRedis(baseKey, orderBaseInfoDTO.getOrderNo(), SelectMapUtil.FIRST,1);
            orderBaseInfoDTO.setUserAnswerDTOList(userAnswerDTOList);

            OrderQuestionDTO orderQuestionDTO=memberRestTemplateService.getQuestion(orderBaseInfoDTO.getOrderNo(),1);
            orderBaseInfoDTO.setCategoryPath(orderQuestionDTO.getCategoryPath());
        }
        return orderBaseInfoDTOS;
    }

    @Override
    public JSONObject detailAddMore(OrderBaseInfoMoreDTO orderBaseInfoMoreDTO,BOSysPerson boSysPerson) throws Exception {
        String orderNo=orderBaseInfoMoreDTO.getOrderNo();
        String selectGroup=StringUtils.isNotBlank(orderBaseInfoMoreDTO.getTmpGroupNo())?
                orderBaseInfoMoreDTO.getTmpGroupNo():orderBaseInfoMoreDTO.getGroupNo();

        Boolean flg=StringUtils.isBlank(selectGroup)?false:true;
        if(OrderStateEnum.WAITDISTRIBUTION.getIndex().equals(String.valueOf(orderBaseInfoMoreDTO.getState()))){
            flg=false;
        }

        if (flg){
            issoTemplateSV.getDetailPersonAttr(orderBaseInfoMoreDTO,boSysPerson.getPersonCode());
            List<OrderDetailDTO> orderDetailDTOList=orderDetailService.selectOrderDetailListByGroup(orderNo,selectGroup);
            if(!ValidationUtil.isEmpty(orderDetailDTOList)){
                int waitFlg=1;
                for(OrderDetailDTO orderDetailDTO:orderDetailDTOList){
                    if(orderDetailDTO.getPrice()!=null){
                        waitFlg=0;
                        break;
                    }
                }
                orderBaseInfoMoreDTO.setWaitFlg(waitFlg);
            }

            orderBaseInfoMoreDTO.setOrderDetailDTOList(orderDetailDTOList);

            Map mapDetail=new HashMap();
            mapDetail.put(SelectMapUtil.ORDER_NO,orderNo);
            mapDetail.put(SelectMapUtil.GROUP_NO,selectGroup);
            List<OrderAttributeDTO> listAll=orderAttributeService.selectListByMap(mapDetail);
            //重新赋值属性
            Map<String, List<OrderAttributeDTO>> groupBySex = listAll.stream()
                .filter(a -> !a.getAttrValue().equals(AttributeUtil.REPORT_LUA)
                    &&  !a.getAttrValue().equals(AttributeUtil.LAB_NAME)
                    &&  !a.getAttrValue().equals(AttributeUtil.REPORT_FORM)
                    &&  !a.getAttrValue().equals(AttributeUtil.OLD_TEST_CYCLE)
                    &&  !a.getAttrValue().equals(AttributeUtil.CS_CODE))
                .collect(Collectors.groupingBy(OrderAttributeDTO::getAttrValue));
            Map attrMap=new HashMap();
            for(String key:groupBySex.keySet()){
                if(groupBySex.get(key).size()>1){
                    continue;
                }
                OrderAttributeDTO orderAttributeDTO=groupBySex.get(key).get(0);

                if(orderAttributeDTO.getAttrValue().equals(OrderUtil.DETERMINE)){
                    //
                    orderBaseInfoMoreDTO.setIsDetermine(orderAttributeDTO.getIsDefault());
                    orderBaseInfoMoreDTO.setDetermine(orderAttributeDTO.getAttrName());
                    continue;
                }
                if(orderAttributeDTO.getAttrValue().equals(AttributeUtil.TEST_CYCLE_NUM)){
                    attrMap.put(AttributeUtil.OLD_TEST_CYCLE,orderAttributeDTO.getAttrName());
                }
                attrMap.put(key,orderAttributeDTO.getAttrName());
            }

            MapUtil.setObjectValue(orderBaseInfoMoreDTO,OrderBaseInfoMoreDTO.class,attrMap);


        }
        OrderMemoDTO orderMemoDTO=orderMemoSV.selectOrderMemoDTO(orderBaseInfoMoreDTO.getOrderNo(), MemoUtil.TEST_LABEL_MEMO);
        if(orderMemoDTO!=null){
            orderBaseInfoMoreDTO.setTestLabelMemo(orderMemoDTO.getMemoInfo());
        }
        List<OrderSampleFromDTO> baseSampleFromList=orderSampleFromDomainService.selectBaseListByOrderNo(orderBaseInfoMoreDTO.getOrderNo());

        OrderSampleDO orderSampleDO=new OrderSampleDO();
        List<CenterSampleDTO> centerSampleDTOList=centerTemplateSV.qryBusiByLine(Long.parseLong(String.valueOf(orderBaseInfoMoreDTO.getApplicationLineId())), BusinessLineDTO.LINE);
        BusinessLineDTO businessLineDTO= centerTemplateSV.qryById(Long.parseLong(String.valueOf(orderBaseInfoMoreDTO.getApplicationLineId())));
        int size=oiqSampleProperties.getSampleSizeByLine(businessLineDTO.getConfigCode());
        orderBaseInfoMoreDTO.setBaseSampleFromList(orderSampleDO.sampleFormAddCenterSample(baseSampleFromList,centerSampleDTOList,size));
        if(orderBaseInfoMoreDTO.getState()==4){
            orderBaseInfoMoreDTO.setNewTemplate(orderUtilService.getNewTemplateTemp(TimeCalendarUtil.getDateToString(orderBaseInfoMoreDTO.getStateDate())));
        }
        //
        Map<String,ToOrderDTO> lastMap=orderBaseInfoDomainService.getLastOrderNoByInquiry(Arrays.asList(orderNo));
        if(lastMap.containsKey(orderNo)){
            orderBaseInfoMoreDTO.setToOrder(lastMap.get(orderNo).getOrderNo());
            orderBaseInfoMoreDTO.setToOrderId(lastMap.get(orderNo).getOrderId());
            orderBaseInfoMoreDTO.setTfsOrder(orderApplicationFormDomainService.getTfsOrderByOrderNo(lastMap.get(orderNo).getOrderNo()));
        }
        //币种逻辑
        Map<String,String> currencyMarkMap= orderUtilService.getCurrencyMark(RedisKeyUtil.OIQ_CURRENCY);
        orderBaseInfoMoreDTO.setCurrencyMark(currencyMarkMap.getOrDefault(orderBaseInfoMoreDTO.getCurrency(),""));

        Map<String, List<OrderAttachmentDTO>> fileListMap=orderAttachmentDomainService.selectAttachmentMapByOrderNoList(Arrays.asList(orderBaseInfoMoreDTO.getOrderNo()));

        orderBaseInfoMoreDTO.setQuotationFileList(OrderAttachmentDO.getFileListByAttType(fileListMap,orderBaseInfoMoreDTO.getOrderNo(),
                OrderAttachmentTypeEnum.IND_QUOTATION_FILE));




        return jsonTransUtil.fromBoToJson(OrderBaseInfoMoreDTO.class, orderBaseInfoMoreDTO, BaseOrderFilter.InquiryDetail.class);
    }

    @Override
    public void closeOrder(OrderCloseReq orderCloseReq, OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, BOSysPerson boSysPerson) throws Exception{

        if(String.valueOf(orderBaseInfoCheckDTO.getState()).equals(OrderStateEnum.CLOSE.getIndex())){
            orderModService.editCloseMemo(orderCloseReq,boSysPerson);
            return;
        }

        VOOrderBaseInfo voOrderBaseInfoUpdate=new VOOrderBaseInfo();
        voOrderBaseInfoUpdate.setOrderId(orderBaseInfoCheckDTO.getOrderId());
        voOrderBaseInfoUpdate.setCloseCode(orderCloseReq.getCloseCode());
        voOrderBaseInfoUpdate.setState(Integer.parseInt(OrderStateEnum.CLOSE.getIndex()));

        OrderOperatorTypeEnum orderOperatorTypeEnum=OrderOperatorTypeEnum.CRM_CLOSE;;



        EnumDTO enumDTO=orderUtilService.getEnumKey(RedisKeyUtil.CRM_CLOSE,orderCloseReq.getCloseCode());
        String enumStr="";
        if(enumDTO!=null){
            enumStr=enumDTO.getEnumName();
            voOrderBaseInfoUpdate.setCloseReason(enumStr);
        }


        voOrderBaseInfoUpdate.setStateDate(UseDateUtil.getDateString(new Date()));
        voOrderBaseInfoUpdate.setHisState(orderBaseInfoCheckDTO.getState());
        orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfoUpdate);

        //查看有没有历史的没取消的单子
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(orderBaseInfoCheckDTO.getOrderNo());

        String memo=orderCloseReq.getMemo();
        String  operatorText=enumStr+" "+orderCloseReq.getMemo();
        orderLogService.addOrderOperatorLogByOiqInquiry(orderBaseInfoCheckDTO.getOrderNo(),boSysPerson.getPersonCode(),orderOperatorTypeEnum,operatorText,"",memo,"1", SelectMapUtil.CLOSE);
        if("reason7".equals(orderCloseReq.getCloseCode())){
            smsEventUtil.sendSms(baseOrderDTO.getOrderNo(),baseOrderDTO.getOrderType(), OiqSmsEnum.CLOSE);
            apiEventUtil.sendWechatMsg(baseOrderDTO.getOrderNo(), EventEnum.SEND_WECHAT_MSG, WechatEnum.CLOSE);
        }
        orderCloseReq.setEventFlg(1);
        orderCloseReq.setEventOrderNo(baseOrderDTO.getOrderNo());

    }

    @Override
    public void updatePublicPrice(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, OrderPriceReq orderPriceReq, BOSysPerson boSysPerson) {

    }

    @Override
    public JSONObject selectFormInfo(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) {
        return null;
    }

    @Override
    public void batchCloseOrder(OrderCloseReq orderCloseReq, BOSysPerson boSysPerson) {

    }

    @Override
    public void batchUpdatePublicPrice(OrderPriceReq orderPriceReq, BOSysPerson personInfo) {

    }



    @Override
    public JSONObject qryExpressList(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, ExpressIdReq expressIdReq, BOSysPerson boSysPerson) {
        return null;
    }

}
