package com.sgs.ecom.order.controller.custom;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.service.custom.interfaces.ICustInvoiceSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOCustInfo;
import com.sgs.ecom.order.vo.VOCustInvoice;

@RestController
@RequestMapping("/business/api.v1.cust/invoice")
public class CustInvoiceController extends ControllerUtil {

	@Autowired
	private ICustInvoiceSV custInvoiceSV;
	
    /**   
	* @Function: addInvoice
	* @Description: 新增月结发票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "add", method = { RequestMethod.POST })
    public ResultBody addInvoice(@RequestBody VOCustInfo custInfo,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		custInvoiceSV.addInvoice(custInfo, getPersonInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: modInvoice
	* @Description: 修改月结发票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "mod", method = { RequestMethod.POST })
    public ResultBody modInvoice(@RequestBody VOCustInvoice custInvoice,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		custInvoiceSV.modInvoice(custInvoice, getPersonInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: delInvoice
	* @Description: 删除月结发票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "del", method = { RequestMethod.POST })
    public ResultBody delInvoice(@RequestBody VOCustInvoice custInvoice,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		custInvoiceSV.delInvoice(custInvoice, getPersonInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: qryInvoice
	* @Description: 查询月结发票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qry", method = { RequestMethod.POST })
    public ResultBody qryInvoice(@RequestBody VOCustInvoice custInvoice) throws Exception {
		return ResultBody.newInstance(custInvoiceSV.qryInvoice(custInvoice));
	}
    
    /**   
	* @Function: qryInvoiceDtl
	* @Description: 查询月结发票详情
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryDtl", method = { RequestMethod.POST })
    public ResultBody qryInvoiceDtl(@RequestBody VOCustInvoice custInvoice) throws Exception {
		return ResultBody.newInstance(custInvoiceSV.qryInvoiceDtl(custInvoice));
	}
    
    /**   
	* @Function: qryUnbindInvoice
	* @Description: 查询未关联发票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryUnbind", method = { RequestMethod.POST })
    public ResultBody qryUnbindInvoice() throws Exception {
    	return ResultBody.newInstance(custInvoiceSV.qryUnbindInvoice());
	}
}
