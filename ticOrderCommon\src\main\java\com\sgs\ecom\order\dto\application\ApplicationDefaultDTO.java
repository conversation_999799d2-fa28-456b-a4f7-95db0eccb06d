package com.sgs.ecom.order.dto.application;

import com.sgs.ecom.order.request.oiq.OiqUserInvoiceReq;
import lombok.Data;

@Data
public class ApplicationDefaultDTO {
    /**
     * 报告抬头
     */
    private String reportTitleType;
    /**
     * 报告语言
     */
    private String reportLuaCode;
    /**
     * 报告形式
     */
    private String reportFormCode;
    /**
     * 报告出具要求
     */
    private String reportMethod;
    /**
     * 资质要求
     */
    private String qualifications;
    /**
     * 样品分类
     */
    private String sampleCategory;
    /**
     * 报告显示名
     */
    private String reportShowName;
    /**
     * 周期要求
     */
    private String cycleRequirements;
    /**
     * 样品接收地
     */
    private String sampleReceivingLocation;
    /**
     * 销售经理
     */
    private String salesEmail;
    /**
     * 业务顾问
     */
    private String businessPersonEmail;
    /**
     * 默认发票
     */
    private OiqUserInvoiceReq invoice;
}
