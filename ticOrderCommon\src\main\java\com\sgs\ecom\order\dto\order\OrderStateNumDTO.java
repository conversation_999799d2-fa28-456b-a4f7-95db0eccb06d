package com.sgs.ecom.order.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.enums.QryOrderStateNumSortEnum;

/**
 * <AUTHOR>
 * @Description :
 * @date 2023/11/22
 */
public class OrderStateNumDTO extends BaseOrderFilter {

    @ApiAnno(groups={OrderStateNumList.class})
    private int state;
    @ApiAnno(groups={OrderStateNumList.class})
    private int stateCount;

    @ApiAnno(groups={OrderStateNumList.class})
    private String stateName;

    private int sortShow;

    public int getSortShow() {
        return sortShow;
    }

    public void setSortShow(int sortShow) {
        this.sortShow = sortShow;
    }

    public String getStateName() {
        if(ValidationUtil.isEmpty(state))
            return "";

        return QryOrderStateNumSortEnum.getName(state);
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public int getStateCount() {
        return stateCount;
    }

    public void setStateCount(int stateCount) {
        this.stateCount = stateCount;
    }
}
