package com.sgs.ecom.order.dto.order; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OrderSampleDTO extends BaseQryFilter {

	public static final String CREATE_SQL = "select CREATE_DATE,PRODUCT_BATCH,GROUP_NO,SAMPLE_NAME,STATE_DATE,SAMPLE_NAME_EN,STATE,ORDER_NO,PRODUCT_INFO,MATERIAL_GRADE,REMARK,SAMPLE_ID from ORDER_SAMPLE";


	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
	private Timestamp createDate;
	@BeanAnno(value="PRODUCT_BATCH", getName="getProductBatch", setName="setProductBatch")
	private String productBatch;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SAMPLE_NAME", getName="getSampleName", setName="setSampleName")
	private String sampleName;
	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
	private Timestamp stateDate;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SAMPLE_NAME_EN", getName="getSampleNameEn", setName="setSampleNameEn")
	private String sampleNameEn;
	@BeanAnno(value="STATE", getName="getState", setName="setState")
	private int state;
	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
	private String orderNo;
	@BeanAnno(value="PRODUCT_INFO", getName="getProductInfo", setName="setProductInfo")
	private String productInfo;
	@BeanAnno(value="MATERIAL_GRADE", getName="getMaterialGrade", setName="setMaterialGrade")
	private String materialGrade;
	@BeanAnno(value="REMARK", getName="getRemark", setName="setRemark")
	private String remark;
	@ApiAnno(groups={Default.class})
	@BeanAnno(value="SAMPLE_ID", getName="getSampleId", setName="setSampleId")
	private long sampleId;

	@ApiAnno(groups={Default.class})
	private String sampleNo;
	@ApiAnno(groups={Default.class})
	private String row;
	@ApiAnno(groups={Default.class})
	private String groupNo;
	@ApiAnno(groups={Default.class})
	private String sampleNameCn;
	private String supplierName;
	private String buyersName;

	private String buyersNameEn;
	private String supplierNameEn;
	private String remarkEn;
	private String materialGradeEn;
	private String productInfoEn;
	private String productBatchEn;
	@ApiAnno(groups={Default.class})
	private int isSampleReportShow;

	@ApiAnno(groups={Default.class})
	private List<OrderSampleFromDTO> sampleFromDTOList=new ArrayList<>();
	@ApiAnno(groups={Default.class})
	private Map<String,Object> sampleFromDTOMap;

	@ApiAnno(groups={Default.class})//查询样品下的附件信息
	private List<OrderAttachmentDTO> orderAttachmentDTOList=new ArrayList<>();

	private Long detailId;
	@ApiAnno(groups={Default.class})
	private int buyNums;

	public int getBuyNums() {
		return buyNums;
	}

	public void setBuyNums(int buyNums) {
		this.buyNums = buyNums;
	}

	public Long getDetailId() {
		return detailId;
	}

	public void setDetailId(Long detailId) {
		this.detailId = detailId;
	}

	public int getIsSampleReportShow() {
		return isSampleReportShow;
	}

	public void setIsSampleReportShow(int isSampleReportShow) {
		this.isSampleReportShow = isSampleReportShow;
	}

	public List<OrderAttachmentDTO> getOrderAttachmentDTOList() {
		return orderAttachmentDTOList;
	}

	public void setOrderAttachmentDTOList(List<OrderAttachmentDTO> orderAttachmentDTOList) {
		this.orderAttachmentDTOList = orderAttachmentDTOList;
	}

	public void setCreateDate(Timestamp createDate){
		this.createDate=createDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getCreateDate(){
		return this.createDate;
	}


	public void setProductBatch(String productBatch){
		this.productBatch=productBatch;
	}
	public String getProductBatch(){
		return this.productBatch;
	}

	public void setSampleName(String sampleName){
		this.sampleName=sampleName;
	}
	public String getSampleName(){
		return this.sampleName;
	}


	public void setStateDate(Timestamp stateDate){
		this.stateDate=stateDate;
	}
	@JsonSerialize(using = TimeFormatSerializer.class)
	public Timestamp getStateDate(){
		return this.stateDate;
	}


	public void setSampleNameEn(String sampleNameEn){
		this.sampleNameEn=sampleNameEn;
	}
	public String getSampleNameEn(){
		return this.sampleNameEn;
	}


	public void setState(int state){
		this.state=state;
	}
	public int getState(){
		return this.state;
	}


	public void setOrderNo(String orderNo){
		this.orderNo=orderNo;
	}
	public String getOrderNo(){
		return this.orderNo;
	}


	public void setProductInfo(String productInfo){
		this.productInfo=productInfo;
	}
	public String getProductInfo(){
		return this.productInfo;
	}


	public void setMaterialGrade(String materialGrade){
		this.materialGrade=materialGrade;
	}
	public String getMaterialGrade(){
		return this.materialGrade;
	}


	public void setRemark(String remark){
		this.remark=remark;
	}
	public String getRemark(){
		return this.remark;
	}


	public void setSampleId(long sampleId){
		this.sampleId=sampleId;
	}
	public long getSampleId(){
		return this.sampleId;
	}

	public String getSampleNo() {
		return sampleNo;
	}

	public void setSampleNo(String sampleNo) {
		this.sampleNo = sampleNo;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public String getSampleNameCn() {
		return sampleNameCn;
	}

	public void setSampleNameCn(String sampleNameCn) {
		this.sampleNameCn = sampleNameCn;
	}


	public String getRow() {
		return row;
	}

	public void setRow(String row) {
		this.row = row;
	}

	public String getSupplierName() {
		return supplierName;
	}

	public void setSupplierName(String supplierName) {
		this.supplierName = supplierName;
	}

	public String getBuyersName() {
		return buyersName;
	}

	public void setBuyersName(String buyersName) {
		this.buyersName = buyersName;
	}

	public String getBuyersNameEn() {
		return buyersNameEn;
	}

	public void setBuyersNameEn(String buyersNameEn) {
		this.buyersNameEn = buyersNameEn;
	}

	public String getSupplierNameEn() {
		return supplierNameEn;
	}

	public void setSupplierNameEn(String supplierNameEn) {
		this.supplierNameEn = supplierNameEn;
	}

	public String getRemarkEn() {
		return remarkEn;
	}

	public void setRemarkEn(String remarkEn) {
		this.remarkEn = remarkEn;
	}

	public String getMaterialGradeEn() {
		return materialGradeEn;
	}

	public void setMaterialGradeEn(String materialGradeEn) {
		this.materialGradeEn = materialGradeEn;
	}

	public String getProductInfoEn() {
		return productInfoEn;
	}

	public void setProductInfoEn(String productInfoEn) {
		this.productInfoEn = productInfoEn;
	}

	public String getProductBatchEn() {
		return productBatchEn;
	}

	public void setProductBatchEn(String productBatchEn) {
		this.productBatchEn = productBatchEn;
	}

	public List<OrderSampleFromDTO> getSampleFromDTOList() {
		return sampleFromDTOList;
	}

	public void setSampleFromDTOList(List<OrderSampleFromDTO> sampleFromDTOList) {
		this.sampleFromDTOList = sampleFromDTOList;
	}

	public Map<String,Object> getSampleFromDTOMap() {
		if(ValidationUtil.isEmpty(sampleFromDTOList)){
			return new HashMap();
		}
		return sampleFromDTOList.stream().collect(Collectors.toMap(OrderSampleFromDTO::getSampleKey, OrderSampleFromDTO::getSampleValue, (key1, key2) -> key2));
	}


}