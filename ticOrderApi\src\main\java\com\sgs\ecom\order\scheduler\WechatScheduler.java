package com.sgs.ecom.order.scheduler;

import com.platform.util.SysCommonConstant;
import com.platform.util.ValidationUtil;
import com.platform.util.date.DateFormat;
import com.sgs.ecom.order.infrastructure.wechat.OaWechatAppPropertiesUtils;
import com.sgs.ecom.order.service.user.interfaces.IOfficialUserSV;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.wechat.VOWechatApp;
import com.sgs.redis.RedisClient;
import java.util.Date;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class WechatScheduler {

    @Autowired
    private RedisClient redisClient;
    @Autowired
    private IOfficialUserSV iOfficialUserSV;
    @Autowired
    ApplicationArguments applicationArguments;
    @Resource
    private OaWechatAppPropertiesUtils oaWechatAppPropertiesUtils;

    private final static int MAX_RETRY = 100;
    private static final Logger log = LoggerFactory.getLogger(WechatScheduler.class);

    /**
     * 每2小时轮询获取公众号粉丝信息最后一页数据
     *
     * @throws Exception
     */
    @Scheduled(cron = "0 0 0/2 * * ?")
    public void getOfficialFansIncrement() throws Exception {
        boolean isGray = applicationArguments.containsOption("gray");
        if (isGray) {
            return;
        }
        qryOfficialFans("1");
    }

    /**
     * 获取公众号关注粉丝
     *
     * @param resetFlag
     * @throws Exception
     */
    private void qryOfficialFans(String resetFlag) throws Exception {
        String key = "GET_OFFICIAL_FANS_THREADS";

        String value = UUID.randomUUID().toString();
        try {
            log.info("获取公众号粉丝信息处理开始 {} ", DateFormat.dateToString(new Date(),
                SysCommonConstant.DateFormat.DatetimeFormat));
            //使用redis分布式锁处理负载均衡
            boolean locked = redisClient.setLock(key, value, 600000);

            if (locked) {
                Map<String, VOWechatApp> wechatAppMap = oaWechatAppPropertiesUtils.getWxMap();
                wechatAppMap.forEach((appId, wechatApp) -> {
                    if (!ValidationUtil.isEmpty(wechatApp.getMonitorFollow()) &&
                        StringUtils.equalsAny(wechatApp.getMonitorFollow(), "1", "2")) {
                        String nextOpenIdRedisKey = RedisKeyUtil.getNextOpenIdRedisKey(appId);
                        String fullOpenIdRedisKey = RedisKeyUtil.getFullOpenIdRedisKey(appId);

                        if (StringUtils.equals("1", resetFlag)) {
                            // 先清空redisKey，相当于全部重新跑
                            redisClient.del(nextOpenIdRedisKey);
                        }
                        // 从Redis里获取存量OPENID
                        Set<String> fullOpenIdSet = new HashSet<>(
                            redisClient.lrange(fullOpenIdRedisKey, 0, -1));

                        // 获取公众号粉丝信息
                        try {
                            int count;
                            int currentCount = 0;
                            do {
                                count = iOfficialUserSV.getAndPushOfficialUserList(appId,
                                    wechatApp, fullOpenIdSet);
                                currentCount++;
                            } while (count > 0 && currentCount < MAX_RETRY);
                        } catch (Exception e) {
                            log.error("获取公众号粉丝信息 失败 {}, {}", appId, e.getMessage());
                        }
                    }
                });
            }
        } catch (InterruptedException e) {
            log.debug("获取公众号粉丝信息 error", e);
            Thread.currentThread().interrupt();

        } finally {
            try {
                redisClient.releaseLock(key, value);
                log.info("获取公众号粉丝信息处理完成 {} ", DateFormat.dateToString(new Date(),
                    SysCommonConstant.DateFormat.DatetimeFormat));
            } catch (Exception e) {
                log.debug("获取公众号粉丝信息 error", e);
            }
        }
    }
}
