package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.bo.BOSysPerson;
import com.platform.util.SysException;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.dto.send.TicOtherApiDTO;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.request.OrderFileReq;
import com.sgs.ecom.order.request.operator.OrderReportReq;
import com.sgs.ecom.order.request.operator.UpdateReportReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.service.order.interfaces.IOrderFileService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/file")
public class OrderFileController extends ControllerUtil {

    @Resource
    private IOrderBaseInfoService orderBaseInfoService;
    @Resource
    private IOrderFileService orderFileService;
    @Resource
    private ApiEventUtil eventApiUtil;
    @Resource
    private MailEventUtil mailEventUtil;
    @Resource
    private SmsEventUtil smsEventUtil;
    @Resource
    private ApiEventUtil apiEventUtil;
    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;


    /**
    *@Function: saveFile
    *@Description  上传报告
    *@param: [orderReportReq, token]
    *@author: Xiwei_Qiu @date: 2021/5/14 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4306","4511","4519","4521"})
    @RequestMapping(value = "saveFile", method = { RequestMethod.POST })
    public ResultBody saveFile(
            @RequestBody OrderReportReq orderReportReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        BOSysPerson boSysPerson=getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO=gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderReportReq.getOrderId(),boSysPerson,privilegeLevelDTO);
        orderFileService.saveFile(orderBaseInfoCheckDTO,boSysPerson,orderReportReq);
        return ResultBody.success();
    }

    /**
     * @Description: 发票/报告批量上传文件
     * @Author: bowen zhang
     * @Date: 2022/5/10
     * @param orderReportReq:
     * @param token:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "saveBatchPublicFile", method = { RequestMethod.POST })
    public ResultBody saveBatchPublicFile(
            @Validated(value = BaseBean.Default.class)
            @RequestBody OrderReportReq orderReportReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        orderFileService.saveBatchPublicFile(orderReportReq,getPersonInfo(token),getPower(token));

        if(!ValidationUtil.isEmpty(orderReportReq.getApiOrderList())){
            for(String orderNo:orderReportReq.getApiOrderList()){
                eventApiUtil.saveEvent(orderNo, EventEnum.SAVE_ORDER_TO_END,new TicOtherApiDTO(orderReportReq.getFileReqList()));
            }

        }

        return ResultBody.success();
    }

    /**
     *@Function: fileList
     *@Description 订单的报告或者发票
     *@param: [orderReportReq, token]
     *
     *@version:
     *@author: Xiwei_Qiu
     *@date: 2020/12/18
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "fileList", method = { RequestMethod.POST })
    public ResultBody fileList(
            @RequestBody OrderReportReq orderReportReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.success(orderFileService.qryFileList(orderReportReq,getPersonInfo(token),gePrivilegeLevelValue(token)));
    }

    /**
     * @Description : LV3-订单报告
     * <AUTHOR> Zhang
     * @Date  2023/10/23
     * @param orderReportReq:
     * @param token:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryFileListBySample", method = { RequestMethod.POST })
    public ResultBody qryFileListBySample(
            @RequestBody OrderReportReq orderReportReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.success(orderFileService.qryFileListBySample(orderReportReq,getPersonInfo(token),gePrivilegeLevelValue(token)));
    }

    /**
    *@Function: updateFile
    *@Description
    *@param: [updatePeportReq, token]
    *@author: Xiwei_Qiu @date: 2021/5/14 @version:
    **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true,permission={"4522" })
    @RequestMapping(value = "updateFile", method = { RequestMethod.POST })
    public ResultBody updateFile(
            @RequestBody UpdateReportReq updatePeportReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        BOSysPerson boSysPerson=getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO=gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(updatePeportReq.getOrderId(),boSysPerson,privilegeLevelDTO);
        orderFileService.updateFile(orderBaseInfoCheckDTO,boSysPerson,updatePeportReq);
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(orderBaseInfoCheckDTO.getOrderNo());
        //替换的发送逻辑在外面，新增在里面
        if(updatePeportReq.getEventFlg()==1 && !ValidationUtil.isEmpty(updatePeportReq.getOiqOtherDTO())){
            //短信和消息推送逻辑逻辑
            smsEventUtil.sendSms(orderBaseInfoCheckDTO.getOrderNo(),updatePeportReq.getOrderType(), OiqSmsEnum.EDIT_REPORT);
            apiEventUtil.sendWechatMsg(orderBaseInfoCheckDTO.getOrderNo(), EventEnum.SEND_WECHAT_MSG,WechatEnum.EDIT_REPORT);

            if(OrderTypeEnum.OIQ_ORDER.getIndex().equals(updatePeportReq.getOrderType())){
                updatePeportReq.getOiqOtherDTO().setSendOperatorCodeFlg(1);
                mailEventUtil.sendMail(baseOrderDTO,OiqMailEnum.UPDATE_REPORT,1L,updatePeportReq.getOiqOtherDTO());
            }
            if(OrderTypeEnum.OIQ_SMALL_ORDER.getIndex().equals(updatePeportReq.getOrderType())){
                mailEventUtil.sendPortalMail(baseOrderDTO.getOrderNo(),updatePeportReq.getOrderType(), OiqMailEnum.PORTAL_REPORT_CHANGE,1L,updatePeportReq.getOiqOtherDTO());
            }
        }
        return ResultBody.success();
    }

    /**
     * @Description: 新开接口TIC和OIQ的报告和发票替换功能都能使用
     * @Author: bowen zhang
     * @Date: 2022/5/23
     * @param token:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @CheckType
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "updatePublicFile", method = { RequestMethod.POST })
    public ResultBody updatePublicFile(
        @RequestHeader(value="accessToken") String token,
            @RequestBody UpdateReportReq updateReportReq) throws Exception {
        orderFileService.updatePublicFile(getPersonInfo(token),updateReportReq);
        if(StringUtils.isNotBlank(updateReportReq.getApiOrderNo())){
            eventApiUtil.saveEvent(updateReportReq.getApiOrderNo(), EventEnum.SAVE_ORDER_TO_END,updateReportReq.getTicOtherApiDTO());
        }

        return ResultBody.success();
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = false)
    @RequestMapping(value = "delFile", method = { RequestMethod.POST })
    public ResultBody delFile(
            @RequestBody UpdateReportReq updatePeportReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        BOSysPerson boSysPerson=getPersonInfo(token);
        PrivilegeLevelDTO privilegeLevelDTO=gePrivilegeLevelValue(token);
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(updatePeportReq.getOrderId(),boSysPerson,privilegeLevelDTO);
        orderFileService.delFile(orderBaseInfoCheckDTO,boSysPerson,updatePeportReq);
        return ResultBody.success();
    }

    /**
     * @Description: 新开接口TIC和OIQ的报告和发票删除功能都能使用
     * @Author: bowen zhang
     * @Date: 2022/5/23
     * @param updatePeportReq:
     * @param token:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "delPublicFile", method = { RequestMethod.POST })
    public ResultBody delPublicFile(
            @Validated(value = {BaseBean.Delete.class,BaseBean.Default.class})
            @RequestBody UpdateReportReq updatePeportReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        orderFileService.delPublicFile(updatePeportReq,getPersonInfo(token),gePrivilegeLevelValue(token));
        return ResultBody.success();
    }

    /**
     *@Function: saveBatchPayFile
     *@Description 批量上传支付文件
     *@param: [orderReportReq, personInfo, power]
     *@author: Xiwei_Qiu @date: 2022/5/26 @version:
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "saveBatchPayFile", method = { RequestMethod.POST })
    public ResultBody saveBatchPayFile(
        @RequestHeader(value="accessToken") String token,
        @RequestBody OrderFileReq orderReportReq) throws Exception {
        orderFileService.saveBatchPayFile(orderReportReq,getPersonInfo(token));
        return ResultBody.success();
    }

    /**
     * @Description : 客服代上传支付凭证
     * <AUTHOR> Zhang
     * @Date  2023/2/22
     * @param orderPayReq:
     * @param token:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "csSavePayFile", method = { RequestMethod.POST })
    public ResultBody csSavePayFile(
            @RequestBody OrderFileReq orderPayReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        orderFileService.csSavePayFile(orderPayReq,getPersonInfo(token),getPower(token));
        return ResultBody.success();
    }

    /**
     * @Description : 查询AFL和RSTS-default申请表的关联关系
     * <AUTHOR> Zhang
     * @Date  2023/9/7
     * @param orderPayReq:
     * @param token:
     * @return: com.sgs.ecom.order.util.ResultBody
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qrySampleAndReportData", method = { RequestMethod.POST })
    public ResultBody qrySampleAndReportData(
            @RequestBody OrderFileReq orderPayReq,
            @RequestHeader(value="accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderFileService.qrySampleAndReportData(orderPayReq));
    }
}
