package com.sgs.ecom.order.service.order.impl;

import com.platform.annotation.Master;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.domain.order.OrderAttachmentDO;
import com.sgs.ecom.order.domain.order.OrderBaseInfoDO;
import com.sgs.ecom.order.domain.order.service.interfaces.IOrderRelateExternalDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationAttrDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationFormDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderAttachmentDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderAttributeDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderDetailDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderExpressDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderInvoiceDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderLinkDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderMemoDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderOperatorLogDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderProductDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderReportDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleFromDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleRelateDomainService;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.dto.order.OrderApplicationAttrDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoDTO;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.order.OrderMemoDTO;
import com.sgs.ecom.order.dto.order.OrderProductDTO;
import com.sgs.ecom.order.dto.order.OrderSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleFromDTO;
import com.sgs.ecom.order.dto.order.OrderSampleRelateDTO;
import com.sgs.ecom.order.dto.rpc.dml.CreateInquiryRpcDTO;
import com.sgs.ecom.order.dto.rpc.dml.DmlApplicationDTO;
import com.sgs.ecom.order.dto.rpc.dml.PayerInfoDTO;
import com.sgs.ecom.order.dto.rpc.dml.QuotationFileDTO;
import com.sgs.ecom.order.entity.order.OrderBaseInfo;
import com.sgs.ecom.order.enums.PayMethod;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.order.BaseOrderStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.factory.OrderDomainFactory;
import com.sgs.ecom.order.infrastructure.rpc.res.CenterInfoDTO;
import com.sgs.ecom.order.infrastructure.rpc.res.CenterLabInfoDTO;
import com.sgs.ecom.order.infrastructure.rpc.res.CenterLineInfoDTO;
import com.sgs.ecom.order.infrastructure.rpc.template.CenterRespository;
import com.sgs.ecom.order.request.FileReq;
import com.sgs.ecom.order.request.order.OrderUploadReportReq;
import com.sgs.ecom.order.request.rpc.InvoiceInfoReq;
import com.sgs.ecom.order.request.rpc.InvoiceReq;
import com.sgs.ecom.order.request.rpc.PayMonthReq;
import com.sgs.ecom.order.request.rpc.PayReceivedReq;
import com.sgs.ecom.order.request.rsts.OperatorReq;
import com.sgs.ecom.order.request.rsts.QryOrderReq;
import com.sgs.ecom.order.response.order.QryOrderRes;
import com.sgs.ecom.order.response.order.other.OrderAfterRes;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoRpcService;
import com.sgs.ecom.order.service.order.interfaces.IOrderOperatorLogService;
import com.sgs.ecom.order.service.order.interfaces.IOrderPayService;
import com.sgs.ecom.order.service.order.interfaces.ISubOrderService;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.check.CheckOpenUtil;
import com.sgs.ecom.order.util.check.OrderCheckUtil;
import com.sgs.ecom.order.util.file.FileUtils;
import com.sgs.ecom.order.util.select.SelectListUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.vo.VOOrderOperatorLog;
import com.sgs.ecom.order.vo.VOOrderSample;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class OrderBaseInfoRpcServiceImpl extends BaseService implements IOrderBaseInfoRpcService {



    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Autowired
    private OrderDomainFactory orderDomainFactory;
    @Autowired
    private IOrderUtilService orderUtilService;
    @Autowired
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Autowired
    private IOrderOperatorLogDomainService orderOperatorLogDomainService;
    @Autowired
    private IOrderDetailDomainService orderDetailDomainService;
    @Autowired
    private IOrderSampleDomainService orderSampleDomainService;
    @Autowired
    private IOrderAttributeDomainService orderAttributeDomainService;
    @Autowired
    private IOrderExpressDomainService  orderExpressDomainService;
    @Autowired
    private IOrderProductDomainService orderProductDomainService;
    @Autowired
    private IOrderApplicationFormDomainService orderApplicationFormDomainService;
    @Autowired
    private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;
    @Autowired
    private IOrderInvoiceDomainService orderInvoiceDomainService;
    @Autowired
    private IOrderSampleFromDomainService orderSampleFromDomainService;
    @Autowired
    private IOrderAttachmentDomainService orderAttachmentDomainService;
    @Autowired
    private IOrderMemoDomainService orderMemoDomainService;
    @Autowired
    private CenterRespository centerTemplateRpc;
    @Autowired
    private IOrderReportDomainService orderReportDomainService;
    @Autowired
    private IOrderLinkDomainService orderLinkDomainService;
    @Resource
    private IOrderOperatorLogService orderOperatorLogService;
    @Resource
    private ISubOrderService subOrderService;
    @Resource
    private IOrderPayService orderPayService;
    @Resource
    private IOrderRelateExternalDomainService orderRelateExternalDomainService;
    @Resource
    private IOrderSampleRelateDomainService orderSampleRelateDomainService;




    /**
     * @params [operatorReq]
     * @return void
     * @description 变更状态接口 oiq的确认报价
     * <AUTHOR> || created at 2023/5/16 10:36
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void operatorOrder(OperatorReq operatorReq) throws Exception{

        String dateStr = UseDateUtil.getDateString(new Date());
        //订单号校验
        BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(operatorReq.getOrderNo());
        if(!OrderTypeEnum.OIQ_SMALL_ORDER.getIndex().equals(baseOrderDTO.getOrderType())){
            return;
        }
        // 已取消订单不处理
        if (OrderStateEnum.CANCELLED.getIndex().equals(String.valueOf(baseOrderDTO.getState()))) {
            return;
        }

        BaseOrderDTO baseOrderDTOLog =baseOrderDTO;

        baseOrderDTO.setDateStr(dateStr);
        //当状态为4的时候 是询价单业务的确认报价
        if(operatorReq.getOrderState()==4 ){
            baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());
        }
        if(!CheckOpenUtil.checkOpenOperatorReq(baseOrderDTO,operatorReq)){
            throw new BusinessException(ResultEnumCode.REQ_ERROR);
        }
        // 订单状态为已完成的情况下,且不是确认动作
        if(operatorReq.getOrderState()!=BaseOrderStateEnum.CONFIRM.getIndex() && baseOrderDTO.getState()== BaseOrderStateEnum.END.getIndex()){
            return;
        }

        if(operatorReq.getOrderState()==14){
            BaseOrderDTO inquiry=orderBaseInfoCustomService.selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());
            if("1900".equals(inquiry.getBu())&& inquiry.getState()==3){
                OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
                orderBaseInfo.setOrderId(inquiry.getOrderId());
                orderBaseInfo.setState(4);
                orderBaseInfo.setStateDate(UseDateUtil.getDateString(new Date()));
                orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);
                orderOperatorLogDomainService.addLog(baseOrderDTO,OrderOperatorTypeEnum.DML_CONFIRM_ORDER,operatorReq.getPlatform());
            }
            orderRelateExternalDomainService.saveExternalByPlatform(operatorReq.getOrderNo(),operatorReq.getPlatform(),operatorReq.getPlatformOrder());
        }



        //询价单更新状态  订单涉及取消和修改
        orderBaseInfoDomainService.updateOrderBaseInfoByOperatorReq(baseOrderDTO,operatorReq);

        //todo 当需要处理额外业务调用
        //orderDomainFactory.createDomainService(baseOrderDTO.getOrderType()).operatorOrder(operatorReq);

        orderOperatorLogDomainService.addLogByOperator(baseOrderDTOLog,operatorReq);

        if(operatorReq.getOrderState()==91){
            operatorReq.setEventOrderNo(baseOrderDTO.getOrderNo());
            operatorReq.setModLabelFlg(1);
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createInquiry(CreateInquiryRpcDTO createInquiryRpcDTO) throws Exception{
        //先查询出订单号下面的询价单
        //订单号校验
        BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(createInquiryRpcDTO.getOrderNo());
        if(!OrderTypeEnum.OIQ_SMALL_ORDER.getIndex().equals(baseOrderDTO.getOrderType())){
            return;
        }
        if(StringUtils.isBlank(createInquiryRpcDTO.getBaseInfo().getCurrency())){
            createInquiryRpcDTO.getBaseInfo().setCurrency("CNY");
        }

        if(createInquiryRpcDTO.getBaseInfo().getMonthPay()==null){
            createInquiryRpcDTO.getBaseInfo().setMonthPay(0);
        }

        //订单是非月结的，然后已经支付了，报错
        if(baseOrderDTO.getPayState()!=0 & baseOrderDTO.getMonthPay()==0 ){
            throw new BusinessException(ResultEnumCode.LEADS_ORDER_PAY_ERROR);
        }


        BaseOrderDTO baseInquiryOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());

        //groupNo调整
        createInquiryRpcDTO.getDmlRpcReqDTO().setNewGroupNo(orderUtilService.getNewGroupNo());
        //主表数据处理 返回新groupNo CreateInquiryRpcDTO里的DmlRpcReqDTO
        OrderBaseInfo infoInquiry=orderBaseInfoDomainService.updateInquiryRpcInfo(baseInquiryOrderDTO,createInquiryRpcDTO);

        //更新订单的金额等数据
        orderBaseInfoDomainService.updateOrderByInquiry(baseOrderDTO,infoInquiry,createInquiryRpcDTO);

        //checkPayerInfo(createInquiryRpcDTO.getPayerInfo());
        //更新订单发票里面的公司名称
        orderInvoiceDomainService.updatePayInfo(createInquiryRpcDTO.getPayerInfo(),baseOrderDTO.getOrderNo());


        //项目处理
        orderDetailDomainService.insertByItems(baseInquiryOrderDTO,createInquiryRpcDTO);
        //样品处理 纵表数据关联
        orderSampleDomainService.addSampleRpc(baseInquiryOrderDTO,createInquiryRpcDTO,baseOrderDTO.getApplicationLineId());
        //把实验室 报告语言和形式同步到attr
        orderAttributeDomainService.insertByAttr(baseInquiryOrderDTO,createInquiryRpcDTO);
        //询价单和订单都生产报价单附件 报价单附件处理
        OrderAttachmentDO orderAttachmentDO=new OrderAttachmentDO();
        orderAttachmentDomainService.delByOrderNo(baseInquiryOrderDTO.getOrderNo(), OrderAttachmentTypeEnum.IND_QUOTATION_FILE);
        orderAttachmentDomainService.delByOrderNo(baseOrderDTO.getOrderNo(), OrderAttachmentTypeEnum.IND_QUOTATION_FILE);
        String dateStr=UseDateUtil.getDateString(new Date());

        if(!ValidationUtil.isEmpty(createInquiryRpcDTO.getQuotations())){
            orderAttachmentDomainService.insertForeach(orderAttachmentDO.getBaseFileReq(QuotationFileDTO.getBase(createInquiryRpcDTO.getQuotations()),
                    baseInquiryOrderDTO.getOrderNo(), OrderAttachmentTypeEnum.IND_QUOTATION_FILE,createInquiryRpcDTO.getDmlRpcReqDTO().getNewGroupNo(),dateStr));
            orderAttachmentDomainService.insertForeach(orderAttachmentDO.getBaseFileReq(QuotationFileDTO.getBase(createInquiryRpcDTO.getQuotations()),
                    baseOrderDTO.getOrderNo(), OrderAttachmentTypeEnum.IND_QUOTATION_FILE,baseOrderDTO.getGroupNo(),dateStr));
        }

        createInquiryRpcDTO.setEventFlg(1);
        createInquiryRpcDTO.setEventOrder(baseOrderDTO);
        //添加日志 用的订单号
        orderOperatorLogDomainService.addLog(baseOrderDTO, OrderOperatorTypeEnum.DML_WAIT_CONFIRM,createInquiryRpcDTO.getPlatform());

        if("1900".equals(baseOrderDTO.getBu())){
            createInquiryRpcDTO.setMinOrder(baseOrderDTO);
        }
    }



    @Override
    public JSONObject qryOrder(QryOrderReq qryOrderReq) throws Exception {
        System.out.println("qryOrder接口请求数据==>"+ com.alibaba.fastjson.JSONObject.toJSONString(qryOrderReq));
        checkParams(qryOrderReq);
        List<QryOrderRes> resList = new ArrayList<>();
        Map<String, Object> qryMap = getStringObjectMap(qryOrderReq);
//        List<String> orderNoList =  orderBaseInfoDomainService.qryOrderNoListByMap(qryMap);
//        if(ValidationUtil.isEmpty(orderNoList))
//            return new JSONObject();

        List<OrderBaseInfoDTO> baseInfoDTOList =  orderBaseInfoDomainService.selectListByMap(qryMap);
        if(ValidationUtil.isEmpty(baseInfoDTOList))
            throw new BusinessException(ResultEnumCode.ORDER_NULL);

//        List<Integer> integerList = baseInfoDTOList.stream().map(OrderBaseInfoCheckDTO::getState).distinct().collect(Collectors.toList());
//        Integer state = integerList.get(0);
//        if(!String.valueOf(state).equals(OrderStateEnum.WAITEXAMINE.getIndex()) &&
//                !String.valueOf(state).equals(OrderStateEnum.WAITSEND.getIndex()) )
//            return new JSONObject();

        Map<String, List<OrderExpressDTO>>  orderExpressListMap =orderExpressDomainService.selectListMapByMap(qryMap);
        Map<String, List<OrderProductDTO>> orderProductListMap = orderProductDomainService.selectListMapByMap(qryMap);
        Map<String, List<OrderDetailDTO>> orderDetailListMap = orderDetailDomainService.selectListMapByMap(qryMap);
        Map<String, List<OrderApplicationFormDTO>> applicationFormListMap =orderApplicationFormDomainService.selectListMapByMap(qryMap);
        Map<String, List<OrderApplicationAttrDTO>> orderApplicationAttrListMap =orderApplicationAttrDomainService.selectListMapByMap(qryMap);
        Map<String, List<OrderInvoiceDTO>> orderInvoiceListMap = orderInvoiceDomainService.selectListMapByMap(qryMap);
        Map<String, List<OrderSampleFromDTO>> sampleFromListMap =  orderSampleFromDomainService.selectListMapByMap(qryMap);
        List<OrderAttachmentDTO> orderAttachmentDTOS = orderAttachmentDomainService.qryList(qryMap);
        Map<String, List<OrderAttachmentDTO>> attachmentGroupMap = new HashMap<>();
        if(!ValidationUtil.isEmpty(orderAttachmentDTOS)){
            attachmentGroupMap = orderAttachmentDTOS.stream().collect(Collectors.groupingBy(E -> E.getOrderNo() + E.getAttType()+E.getGroupNo()));//以订单号+附件类型+组(存的是itemType =1的 detailId)
        }
        Map<String, List<OrderAttachmentDTO>> attListMap = new HashMap<>();
        if(!ValidationUtil.isEmpty(orderAttachmentDTOS)){
            attListMap = orderAttachmentDTOS.stream().collect(Collectors.groupingBy(E -> E.getOrderNo()));
        }
        List<OrderSampleRelateDTO> orderSampleRelateDTOS = orderSampleRelateDomainService.selectListByOrderNo(qryOrderReq.getOrderNo(), null);
//        Map<String, List<OrderAttachmentDTO>> attListMap = orderAttachmentDomainService.selectListMapByMap(qryMap);
        //查询套餐关联样品的信息
        VOOrderSample voOrderSample = new VOOrderSample(qryOrderReq.getOrderNo());
        List<OrderSampleDTO> samplePackageRelationList =orderSampleDomainService.qryPackageAndSampleRelation(voOrderSample);
        Map<String, List<OrderSampleDTO>> samplePackageRelationMap = new HashMap<>();
        if(!ValidationUtil.isEmpty(samplePackageRelationList)){
            samplePackageRelationMap = samplePackageRelationList.stream().collect(Collectors.groupingBy(q -> q.getOrderNo() + q.getDetailId()));
        }
        Map<String, List<OrderMemoDTO>> memoListMap = orderMemoDomainService.selectListMapByMap(qryMap);
        resList = orderBaseInfoDomainService.addData(baseInfoDTOList,orderExpressListMap,orderProductListMap,orderDetailListMap,
                applicationFormListMap,orderApplicationAttrListMap,orderInvoiceListMap,sampleFromListMap,resList,
                attListMap,memoListMap,attachmentGroupMap,orderSampleRelateDTOS,samplePackageRelationMap);
        System.out.println("qryOrder接口返回数据==>"+ com.alibaba.fastjson.JSONObject.toJSONString(resList));
        //更新主表的subState，标识soda已经拉去过订单信息
        updateOrderBaseInfo(baseInfoDTOList);
        orderOperatorLogService.saveOperatorLog(new VOOrderOperatorLog(baseInfoDTOList.get(0).getOrderNo(), Integer.valueOf(OrderTypeEnum.TIC.getIndex()),
                Integer.valueOf(OrderOperatorTypeEnum.IMPORT_TRF.getIndex()),
                "pre-order", null, 1));
        return jsonTransUtil.transJSONString(QryOrderRes.class, resList, BaseOrderFilter.OrderQryList.class);
    }

    private void updateOrderBaseInfo(List<OrderBaseInfoDTO> baseInfoDTOList) {
        if(ValidationUtil.isEmpty(baseInfoDTOList))
            return;

        baseInfoDTOList.stream().forEach(orderBaseInfoDTO -> {
            OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
            orderBaseInfo.setUserId(orderBaseInfoDTO.getUserId());
            orderBaseInfo.setOrderId(orderBaseInfoDTO.getOrderId());
            orderBaseInfo.setOrderNo(orderBaseInfoDTO.getOrderNo());
            orderBaseInfo.setSubState(OrderSubStateEnum.ORDER_IMPORT_STATUS.getIndex());
            orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);
        });
    }

    @Override
    @Transactional
    public void operatorOrderState(OperatorReq operatorReq) throws Exception {
        System.out.println("operatorOrderState接口请求数据==>"+ com.alibaba.fastjson.JSONObject.toJSONString(operatorReq));
        String dateStr = UseDateUtil.getDateString(new Date());
        //订单号校验

        BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(operatorReq.getOrderNo());

        if(String.valueOf(baseOrderDTO.getState()).equals(OrderStateEnum.END.getIndex()))
            return;


        orderBaseInfoDomainService.checkData(operatorReq,baseOrderDTO);

//        orderBaseInfoDomainService.updateOrderBaseInfoByState(baseOrderDTO,operatorReq.getOrderState());

        //todo 当需要处理额外业务调用
        orderDomainFactory.createDomainService(baseOrderDTO.getOrderType()).operatorOrder(operatorReq,baseOrderDTO);
//        orderOperatorLogDomainService.addLogByOperator(baseOrderDTO,operatorReq);


    }

    @Override
    public OrderAfterRes uploadReport(OrderUploadReportReq uploadReportReq) {
        System.out.println("uploadReports接口请求数据==>"+ com.alibaba.fastjson.JSONObject.toJSONString(uploadReportReq));
        OrderAfterRes res = new OrderAfterRes();
        Map<String, Object> qryMap = new HashMap<>();
        qryMap.put(SelectMapUtil.ORDER_NO, uploadReportReq.getOrderNo());
        qryMap.put(SelectMapUtil.PLATFORM, uploadReportReq.getPlatform());
        qryMap.put(SelectMapUtil.PLATFORM_ORDER, uploadReportReq.getPlatformOrder());
        List<OrderBaseInfoDTO> orderBaseInfoDTOList = orderBaseInfoDomainService.selectListByMap(qryMap);
        if (ValidationUtil.isEmpty(orderBaseInfoDTOList))
            throw new BusinessException(ResultEnumCode.ORDER_NULL);


        OrderBaseInfoDO orderBaseInfoDO = new OrderBaseInfoDO();
        OrderBaseInfo orderBaseInfo = orderBaseInfoDO.copyOrderBaseInfo(orderBaseInfoDTOList,uploadReportReq);
        OrderCheckUtil.checkUploadFileState(orderBaseInfo,uploadReportReq);
//        orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);
        res = orderAttachmentDomainService.uploadReport(uploadReportReq,orderBaseInfo,res);
        return res;
    }

    @Override
    @Transactional
    public void updateApplicationForm(DmlApplicationDTO dmlApplicationDTO) throws Exception{

        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(dmlApplicationDTO.getOrderNo());
        if(!baseOrderDTO.getOrderType().equals(OrderTypeEnum.OIQ_SMALL_ORDER.getIndex())){
            return;
        }
        //业务线相关数据
        CenterLineInfoDTO centerLineInfoDTO=centerTemplateRpc.getLineInfoByPlatformCode(dmlApplicationDTO.getBaseInfo().getLineCode());
        CenterLabInfoDTO centerLabInfoDTO=centerTemplateRpc.getLabInfoByPlatformCode(dmlApplicationDTO.getBaseInfo().getLabCode());
        CenterInfoDTO centerInfoDTO=new CenterInfoDTO(centerLineInfoDTO,centerLabInfoDTO);
        checkApplication(dmlApplicationDTO,centerInfoDTO);

        OrderBaseInfo orderBaseInfo=orderBaseInfoDomainService.updateOrderBaseInfoByDmlApplication(baseOrderDTO,dmlApplicationDTO,centerInfoDTO);

        orderApplicationFormDomainService.updateApplicationForm(dmlApplicationDTO.getOrderNo(),dmlApplicationDTO);
        //申请表attr
        orderApplicationAttrDomainService.saveDmlForm(dmlApplicationDTO.getOrderNo(),dmlApplicationDTO.getApplicationForm(),centerLabInfoDTO);
        //保存报告
        orderReportDomainService.saveDmlReport(dmlApplicationDTO.getOrderNo(),dmlApplicationDTO.getApplicationForm(),orderBaseInfo);
        //保存寄送物流
        orderExpressDomainService.saveDmlOrderExpress(dmlApplicationDTO.getOrderNo(),dmlApplicationDTO.getDelivers());
        //保存发票
        orderInvoiceDomainService.saveByDmlInvoice(dmlApplicationDTO.getInvoice(),dmlApplicationDTO.getPayerInfo(),baseOrderDTO.getOrderNo());
        //保存项目
        orderDetailDomainService.saveItemByDmlForm(dmlApplicationDTO.getItems(),baseOrderDTO.getOrderNo(),baseOrderDTO.getGroupNo());
        //保存样品
        orderSampleDomainService.dmlAddSampleList(dmlApplicationDTO.getSamples(),baseOrderDTO.getOrderNo(),baseOrderDTO.getGroupNo(),centerLineInfoDTO.getConfigId());
        //orderLink
        orderLinkDomainService.saveOrderLinkByDmlOrder(dmlApplicationDTO,baseOrderDTO.getOrderNo());
        //写日志
        orderOperatorLogDomainService.addLog(baseOrderDTO,OrderOperatorTypeEnum.DML_CRM_UPDATE_FORM,dmlApplicationDTO.getPlatform());
        //发邮件
        dmlApplicationDTO.setEventFlg(1);
        dmlApplicationDTO.setEventOrder(baseOrderDTO);
    }


    @Override
    @Master
    @Transactional(rollbackFor = Throwable.class)
    public void payReceived(PayReceivedReq payReceivedReq)throws Exception {

        OrderCheckUtil.checkPayReceived(payReceivedReq.getPayReceived());


        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseOrder(payReceivedReq.getOrderNo());
        if(payReceivedReq.getPayReceived()==1){
            if(baseOrderDTO.getMonthPay()==2){
                return;
            }
        /*    if(baseOrderDTO.getRealAmount()==null){
                throw new BusinessException(ResultEnumCode.REAL_AMOUNT_IS_NULL);
            }*/
            if(baseOrderDTO.getPayState()==1 && !PayMethodEnum.OFFLINE.getIndex().equals(baseOrderDTO.getPayMethod())){
                if(baseOrderDTO.getIsPayReceived()==1){
                    return;
                }
                if(baseOrderDTO.getIsPayReceived()==0){
                    throw new BusinessException(ResultEnumCode.DML_PAY_NOT_TO_RECEIVED);
                }

            }
            checkSub(baseOrderDTO.getRelateOrderNo());
            OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
            orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
            orderBaseInfo.setPayState(1);
            orderBaseInfo.setPayMethod(Integer.parseInt(PayMethodEnum.OFFLINE.getIndex()));
            orderBaseInfo.setIsPayReceived(1);
            if(baseOrderDTO.getPayState()==0||baseOrderDTO.getPayState()==2){
                orderBaseInfo.setPayDate(payReceivedReq.getOperatorDate());
                orderPayService.updatePayDateByMap(baseOrderDTO.getOrderNo(),payReceivedReq.getOperatorDate());
            }
            orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);


            orderOperatorLogDomainService.addBaseLogDetailNo(baseOrderDTO,OrderOperatorTypeEnum.ISPAYRECEIVEDTRUE,payReceivedReq.getPlatform(),payReceivedReq.getPlatformOrder());

            Boolean sendReportFlg=baseOrderDTO.getPayState()==0||baseOrderDTO.getPayState()==2||(PayMethodEnum.OFFLINE.getIndex().equals(baseOrderDTO.getPayMethod()) && baseOrderDTO.getPayState()==1
                    &&baseOrderDTO.getIsPayReceived()==0);
            if(sendReportFlg){
                orderAttachmentDomainService.sendReportFileByEnd(baseOrderDTO);
            }
            return;
        }
        if(payReceivedReq.getPayReceived()==0){
            if(baseOrderDTO.getPayState()==1 && (PayMethodEnum.ZFB.getIndex().equals(baseOrderDTO.getPayMethod())
            ||PayMethodEnum.WX.getIndex().equals(baseOrderDTO.getPayMethod()))){
                throw new BusinessException(ResultEnumCode.DML_PAY_METHOD_NOT_TO_RECEIVED);
            }

            OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
            orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
            if(baseOrderDTO.getMonthPay()==2){
                orderBaseInfo.setMonthPay(0);
                orderBaseInfo.setPayMethod(Integer.parseInt(PayMethodEnum.OFFLINE.getIndex()));
            }
            if (!ValidationUtil.isEmpty(baseOrderDTO.getPayMethod())) {
                orderBaseInfo.setPayMethod(Integer.parseInt(PayMethodEnum.OFFLINE.getIndex()));
            }
            orderBaseInfo.setIsPayReceived(0);
            orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);
            orderOperatorLogDomainService.addBaseLogDetailNo(baseOrderDTO,OrderOperatorTypeEnum.ISPAYRECEIVEDFALSE, payReceivedReq.getPlatform(), payReceivedReq.getPlatformOrder());
        }

        if(payReceivedReq.getPayReceived()==2){
           if(baseOrderDTO.getPayState()==1  && baseOrderDTO.getMonthPay()==0 && !PayMethodEnum.OFFLINE.getIndex().equals(baseOrderDTO.getPayMethod())){
              return;
           }
           if(baseOrderDTO.getPayState()==1 && baseOrderDTO.getIsPayReceived()==1 && PayMethodEnum.OFFLINE.getIndex().equals(baseOrderDTO.getPayMethod())){
               return;
           }

           if(baseOrderDTO.getPayState()==0 || baseOrderDTO.getPayState()==2 || (baseOrderDTO.getPayState()==1 &&
                   baseOrderDTO.getIsPayReceived()==0)){
               checkSub(baseOrderDTO.getRelateOrderNo());
               OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
               orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
               orderBaseInfo.setPayState(1);
               orderBaseInfo.setIsPayReceived(1);
               orderBaseInfo.setMonthPay(0);
               orderBaseInfo.setPayDate(payReceivedReq.getOperatorDate());
               orderBaseInfo.setPayMethod(PayMethod.ADVANCE_PAYMENT.getIndex());
               orderPayService.updatePayDateByMap(baseOrderDTO.getOrderNo(),payReceivedReq.getOperatorDate());

               orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);
               orderOperatorLogDomainService.addBaseLogDetailNo(baseOrderDTO,OrderOperatorTypeEnum.PAY_ADVANCE_PAYMENT, payReceivedReq.getPlatform(), payReceivedReq.getPlatformOrder());
               orderAttachmentDomainService.sendReportFileByEnd(baseOrderDTO);
           }
        }
    }

    /**
     * 通用上传发票
     * @param invoiceReq
     * @return void
     * <AUTHOR> || created at 2024/9/9 14:45
     * @throws Exception 抛出错误
     */
    @Override
    @Master
    @Transactional(rollbackFor=Exception.class)
    public void modInvoiceFile(InvoiceReq invoiceReq) throws Exception {
        String dateStr=UseDateUtil.getDateString(new Date());
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(invoiceReq.getOrderNo());
        Map<String,List<InvoiceInfoReq>> invoiceMap=invoiceReq.getInvoices().stream().collect(Collectors.groupingBy(E->E.getStatus()));
        BOSysPerson boSysPerson=new BOSysPerson();
        boSysPerson.setPersonCode(invoiceReq.getPlatform());
        if(invoiceMap.containsKey("1")){
            List<InvoiceInfoReq> invoiceList=invoiceMap.get("1");
            List<FileReq> list=new ArrayList<>();
            for(int n=0;n<invoiceList.size();n++){
                InvoiceInfoReq invoiceInfoReq=invoiceList.get(n);
                FileReq fileReq=new FileReq();
                baseCopyObj.copyWithNull(fileReq,invoiceInfoReq);
                fileReq.setInvoiceNumber(invoiceInfoReq.getInvoiceNo());
                list.add(fileReq);
            }
            orderAttachmentDomainService.saveInvoiceFileList(list,baseOrderDTO.getOrderNo(),boSysPerson);
        }
        if(invoiceMap.containsKey("2")){
            List<InvoiceInfoReq> invoiceList=invoiceMap.get("2");
            List<String> invoiceNumberList=invoiceList.stream().map(InvoiceInfoReq::getInvoiceNo).collect(Collectors.toList());
            List<OrderAttachmentDTO> list=orderAttachmentDomainService.getListByInvoice(invoiceNumberList,baseOrderDTO.getOrderNo(),OrderAttachmentTypeEnum.OIQ_EXPRESS);
            //获取删除发票日志文案
            StringJoiner operatorText=new StringJoiner(" ");
            for(int n=0;n<list.size();n++){
                OrderAttachmentDTO attachmentDTO=list.get(n);
                operatorText.add(FileUtils.getLogFileByNameOrUrl(attachmentDTO.getFileName(),attachmentDTO.getFileUrl()));
            }

            List<Long> idList=list.stream().map(OrderAttachmentDTO::getAttachmentId).collect(Collectors.toList());
            if(!ValidationUtil.isEmpty(idList)){
                orderAttachmentDomainService.updateBatchDelState(idList);
                //发票删除日志
                orderOperatorLogDomainService.addLog(baseOrderDTO,OrderOperatorTypeEnum.DEL_INVOICE_FILE,boSysPerson.getPersonCode(),operatorText.toString(),1,dateStr);
            }
        }
    }

    @Override
    public void payMonth(PayMonthReq payMonthReq) throws Exception{
        if(payMonthReq.getMonthPay()==null){
            throw new BusinessException(ResultEnumCode.INT_FLG_ERROR);
        }
        if(!(payMonthReq.getMonthPay()==0 ||payMonthReq.getMonthPay()==2)){
            throw new BusinessException(ResultEnumCode.INT_FLG_ERROR);
        }
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(payMonthReq.getOrderNo());
        if(baseOrderDTO.getPayState()==1  && baseOrderDTO.getMonthPay()==0 && !PayMethodEnum.OFFLINE.getIndex().equals(baseOrderDTO.getPayMethod())){
            return;
        }
        if(payMonthReq.getMonthPay()==2 && baseOrderDTO.getPayState()==1 && baseOrderDTO.getIsPayReceived()==1 && PayMethodEnum.OFFLINE.getIndex().equals(baseOrderDTO.getPayMethod())){
            return;
        }

        OrderBaseInfoDO orderBaseInfoDO=new OrderBaseInfoDO();
        if(payMonthReq.getMonthPay()==0 ){
            if(baseOrderDTO.getMonthPay()==2){
                OrderBaseInfo orderBaseInfo=orderBaseInfoDO.EntitySetUnMonthPay(baseOrderDTO);
                orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);
            }
            orderOperatorLogDomainService.addBaseLogDetailNo(baseOrderDTO,OrderOperatorTypeEnum.PAY_UN_MON, payMonthReq.getPlatform(), payMonthReq.getPlatformOrder());
        }
        if(payMonthReq.getMonthPay()==2 && baseOrderDTO.getMonthPay()==0){
            OrderBaseInfo orderBaseInfo=orderBaseInfoDO.EntitySetMonthPay(baseOrderDTO);
            orderBaseInfo.setPayDate(payMonthReq.getOperatorDate());
            orderBaseInfoDomainService.updateOrderBaseInfo(orderBaseInfo);
            orderAttachmentDomainService.sendReportFileByEnd(baseOrderDTO);
            orderOperatorLogDomainService.addBaseLogDetailNo(baseOrderDTO,OrderOperatorTypeEnum.PAY_MON, payMonthReq.getPlatform(), payMonthReq.getPlatformOrder());
        }

    }


    private Map<String, Object> getStringObjectMap(QryOrderReq qryOrderReq) {
        Map<String,Object> qryMap = new HashMap<>();
        qryMap.put(SelectMapUtil.PLATFORM, qryOrderReq.getPlatform());
        qryMap.put(SelectMapUtil.PLATFORM_ORDER, qryOrderReq.getPlatformOrder());
        qryMap.put(SelectMapUtil.LAB_CODE, qryOrderReq.getLabCode());
        qryMap.put(SelectListUtil.ORDER_NO_LIST,ValidationUtil.isEmpty(qryOrderReq.getOrderNo())?null: Arrays.asList(qryOrderReq.getOrderNo()));
        qryMap.put(SelectMapUtil.EXPRESS_NO, qryOrderReq.getExpressNo());
        qryMap.put(SelectMapUtil.PRE_ORDER_FLAG, "pre-order");
        List<Integer> stateList = new ArrayList<>();
        List<Integer> refundStateList = new ArrayList<>();
        stateList.add(Integer.parseInt(OrderStateEnum.WAITSEND.getIndex()));
        stateList.add(Integer.parseInt(OrderStateEnum.WAITEXAMINE.getIndex()));
        refundStateList.add(OrderRefundStateEnum.NOOPERATOR.getIndex());
        refundStateList.add(OrderRefundStateEnum.FAIL.getIndex());
        refundStateList.add(OrderRefundStateEnum.PART_REFUND_CONFIRMATION.getIndex());
        qryMap.put(SelectListUtil.STATE_LIST,stateList);
        qryMap.put(SelectListUtil.REFUND_STATE_LIST,refundStateList);
        qryMap.put(SelectMapUtil.PRE_ORDER_FLAG, "pre-order");
        return qryMap;
    }
    private void checkParams(QryOrderReq qryOrderReq) {
        if(ValidationUtil.isEmpty(qryOrderReq) || (ValidationUtil.isEmpty(qryOrderReq.getOrderNo()) && ValidationUtil.isEmpty(qryOrderReq.getExpressNo()))){
            throw new BusinessException(ResultEnumCode.PARAMS_NULL);
        }
    }

    private void checkPayerInfo(PayerInfoDTO payerInfoDTO){
        if(ValidationUtil.isEmpty(payerInfoDTO)){
            throw new BusinessException(ResultEnumCode.PAYER_INFO_ERROR);
        }
        if(StringUtils.isBlank(payerInfoDTO.getPayerCompany())){
            throw new BusinessException(ResultEnumCode.PAYER_INFO_COMPANY_ERROR);
        }
        if(StringUtils.isBlank(payerInfoDTO.getPayerName())){
            throw new BusinessException(ResultEnumCode.PAYER_INFO_NAME_ERROR);
        }
        if(StringUtils.isBlank(payerInfoDTO.getPayerPhone())){
            throw new BusinessException(ResultEnumCode.PAYER_INFO_PHONE_ERROR);
        }
        if(StringUtils.isBlank(payerInfoDTO.getPayerEmail())){
            throw new BusinessException(ResultEnumCode.PAYER_INFO_EMAIL_ERROR);
        }
    }

    private void checkApplication(DmlApplicationDTO dmlApplicationDTO, CenterInfoDTO centerInfoDTO){
        //
        if(ValidationUtil.isEmpty(centerInfoDTO.getCenterLineInfoDTO())){
            throw new BusinessException(ResultEnumCode.DML_LINE_CODE_ERROR);
        }
        if(ValidationUtil.isEmpty(centerInfoDTO.getCenterLabInfoDTO())){
            throw new BusinessException(ResultEnumCode.DML_LAB_CODE_ERROR);
        }

        checkPayerInfo(dmlApplicationDTO.getPayerInfo());
    }

    public void checkSub(String relateOrderNo){
        int n = subOrderService.selectCountByReceived(relateOrderNo);
        if (n > 0) {
            throw new BusinessException(ResultEnumCode.SUB_ORDER_RECEIVED);
        }
    }



}
