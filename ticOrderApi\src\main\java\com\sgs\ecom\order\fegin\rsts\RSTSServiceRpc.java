package com.sgs.ecom.order.fegin.rsts;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.request.rsts.*;
import com.sgs.ecom.order.service.rsts.interfaces.IRSTSService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/business/rpc.v1.rsts/order")
@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
public class RSTSServiceRpc extends ControllerUtil {

	@Resource
	private IRSTSService irstsService;
	@Resource
	private MailEventUtil mailEventUtil;
	@Resource
	private ApiEventUtil apiEventUtil;

	/**测试
	 * @Function: operatorOrder
	 * @Description 审核接口
	 * @param: [operatorReq]
	 * @author: Xiwei_Qiu @date: 2021/11/19 @version:
	 **/
	@HystrixCommand
	@RequestMapping(value = "operator", method = {RequestMethod.POST})
	public ResultBody operatorOrder(
		@Validated(BaseBean.Update.class)
		@RequestBody OperatorReq operatorReq) throws Exception {
		irstsService.operatorOrder(operatorReq);
		if(operatorReq.getEventFlg()==1){
			mailEventUtil.sendMail(operatorReq.getEventOrderNo(),operatorReq.getOrderType(),operatorReq.getOiqMailEnum(),1L,operatorReq.getRoOtherDTO());
		}
		return ResultBody.success();
	}

	/**
	 * 临时接口，修复数据用
	 *
	 * @Function: operatorOrder
	 * @Description 审核接口
	 * @param: [operatorReq]
	 * @author: Xiwei_Qiu @date: 2021/11/19 @version:
	 **/
	@HystrixCommand
	@RequestMapping(value = "operatorTmp", method = {RequestMethod.POST})
	public ResultBody operatorOrderTmp(
		@Validated(BaseBean.Update.class)
		@RequestBody OperatorReq operatorReq) throws Exception {
		irstsService.operatorOrderTmp(operatorReq);
		if(operatorReq.getEventFlg()==1){
			mailEventUtil.sendMail(operatorReq.getEventOrderNo(),operatorReq.getOrderType(),operatorReq.getOiqMailEnum(),1L,operatorReq.getRoOtherDTO());
		}
		return ResultBody.success();
	}


	/**
	 * @Function: updatePrice
	 * @Description 改价接口
	 * @param: [operatorPriceReq]
	 * @author: Xiwei_Qiu @date: 2021/11/19 @version:
	 **/
	@HystrixCommand
	@RequestMapping(value = "updatePrice", method = {RequestMethod.POST})
	public ResultBody updatePrice(
		@Validated(BaseBean.Update.class)
		@RequestBody OperatorPriceReq operatorPriceReq) throws Exception {
		irstsService.updatePrice(operatorPriceReq);
		if(operatorPriceReq.getRoChangePrice()==1){
			mailEventUtil.sendMail(operatorPriceReq.getEventOrderNo(),operatorPriceReq.getOrderType(), OiqMailEnum.RO_UPDATE_PRICE,1L,operatorPriceReq.getRoOtherDTO());
		}
		return ResultBody.success();
	}

	/**
	 * @Function: createSubOrder
	 * @Description 创建补差价订单
	 * @param: [operatorCreateReq]
	 * @author: Xiwei_Qiu @date: 2021/11/19 @version:
	 **/
	@HystrixCommand
	@RequestMapping(value = "createSubOrder", method = {RequestMethod.POST})
	public ResultBody createSubOrder(
		@Validated(BaseBean.Insert.class)
		@RequestBody SubCreateReq subCreateReq) throws Exception {
		irstsService.createSubOrder(subCreateReq);
		if(subCreateReq.getRoCreateSub()==1){
			mailEventUtil.sendMail(subCreateReq.getEventOrderNo(),subCreateReq.getOrderType(), OiqMailEnum.RO_CREATE_SUB,1L,subCreateReq.getRoOtherDTO());
		}
		return ResultBody.newInstance(subCreateReq.getRoOtherDTO().getSubOrder().getOrderNo());
	}

	/**
	 * @Function: received
	 * @Description 到账
	 * @param: [operatorReceivedReq]
	 * @author: Xiwei_Qiu @date: 2021/11/19 @version:
	 **/
	@HystrixCommand
	@RequestMapping(value = "received", method = {RequestMethod.POST})
	public ResultBody received(
		@Validated(BaseBean.Insert.class)
		@RequestBody OperatorReceivedReq operatorReceivedReq) throws Exception {
		irstsService.received(operatorReceivedReq);
		return ResultBody.success();
	}

	/**
	 * @Function: saveExpress
	 * @Description 保存物流信息
	 * @param: [expressReq]
	 * @author: Xiwei_Qiu @date: 2021/11/19 @version:
	 **/
	@HystrixCommand
	@RequestMapping(value = "saveExpress", method = {RequestMethod.POST})
	public ResultBody saveExpress(
		@Validated(BaseBean.Insert.class)
		@RequestBody OperatorExpressReq expressReq) throws Exception {
		irstsService.saveExpress(expressReq);
		if(expressReq.getEventFlg()==1){
			mailEventUtil.sendMail(expressReq.getEventOrderNo(),expressReq.getOrderType(),OiqMailEnum.RO_EXPRESS,1L,expressReq.getRoOtherDTO());
		}
		return ResultBody.success();
	}

	/**
	*@Function: cancelReceived
	*@Description 核销支付记录
	*@param: [cancelReceivedReq]
	*@author: Xiwei_Qiu @date: 2021/12/6 @version:
	**/
	@HystrixCommand
	@RequestMapping(value = "cancelReceived", method = {RequestMethod.POST})
	public ResultBody cancelReceived(
		@Validated(BaseBean.Insert.class)
		@RequestBody CancelReceivedReq cancelReceivedReq) throws Exception {
		irstsService.cancelReceived(cancelReceivedReq);
		if(cancelReceivedReq.getEventFlg()==1){
			apiEventUtil.saveEvent(cancelReceivedReq.getEventOrderNo(), EventEnum.RO_CANCEL_PAY_METHOD);
		}
		return ResultBody.success();
	}





}


