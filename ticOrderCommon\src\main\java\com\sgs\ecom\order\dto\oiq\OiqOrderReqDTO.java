package com.sgs.ecom.order.dto.oiq;


import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.center.BusinessLineDTO;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.dml.DmlMainReqDTO;
import com.sgs.ecom.order.dto.user.UserDTO;
import com.sgs.ecom.order.entity.order.OrderReport;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.request.oiq.OiqOrderReq;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
* @params
* @return 传递参数对象
* @description
* <AUTHOR> || created at 2023/8/21 17:36
*/
public class OiqOrderReqDTO {

    private String dateStr;


    private Long inquiryOrderId;
    private String inquiryOrderNo;
    private String inquiryGroupNo;
    private String groupNo;
    private Boolean isAdd;
    private Boolean isPortal=true;

    private LabDTO labDTO;
    private BusinessLineDTO businessLineDTO;
    private Long lineId;
    private OrderReport orderReport;

    private Long orderId;
    private String orderNo;

    //修改的时候有值 小门户和询报价查询的 旧的状态
    private BaseOrderDTO orderDTO;
    //订单类型
    private String orderType;

    //入参数据
    private OiqOrderReq oiqOrderReq;

    private Map<String,String> codeToKey=new HashMap<>();

    private Map<String,Integer> codeToRow=new HashMap<>();

    private UserDTO userDTO;

    private SysPersonDTO businessPerson;
    private int addInquiry;
    private DmlMainReqDTO dmlMainReqDTO=new DmlMainReqDTO();

    public OiqOrderReqDTO() {
    }

    public OiqOrderReqDTO(String inquiryOrderNo, String inquiryGroupNo, String orderNo, String groupNo, Boolean isAdd, UserDTO userDTO) {
        this.inquiryOrderNo = inquiryOrderNo;
        this.inquiryGroupNo = inquiryGroupNo;
        this.orderNo = orderNo;
        this.groupNo = groupNo;
        this.isAdd = isAdd;
        this.userDTO=userDTO;
        this.dateStr= UseDateUtil.getDateString(new Date());
        //初始是小门
        this.orderType=OrderTypeEnum.OIQ_SMALL_ORDER.getIndex();
    }

    public static void addOrder(OiqOrderReqDTO oiqOrderReqDTO, BaseOrderDTO inquiryDTO,BaseOrderDTO baseOrderDTO){
        oiqOrderReqDTO.setInquiryOrderId(inquiryDTO.getOrderId());
        oiqOrderReqDTO.setInquiryOrderNo(inquiryDTO.getOrderNo());
        //修改的情况下 可能是普通询价单
        if(OrderTypeEnum.OIQ_ORDER.getIndex().equals(baseOrderDTO.getOrderType())){
            oiqOrderReqDTO.setPortal(false);
            oiqOrderReqDTO.setOrderType(OrderTypeEnum.OIQ_ORDER.getIndex());
            //普通询价单group不变
            oiqOrderReqDTO.setGroupNo(baseOrderDTO.getGroupNo());
            oiqOrderReqDTO.setLineId(baseOrderDTO.getLineId());
        }
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public Boolean getAdd() {
        return isAdd;
    }

    public void setAdd(Boolean add) {
        isAdd = add;
    }

    public String getInquiryOrderNo() {
        return inquiryOrderNo;
    }

    public void setInquiryOrderNo(String inquiryOrderNo) {
        this.inquiryOrderNo = inquiryOrderNo;
    }

    public String getInquiryGroupNo() {
        return inquiryGroupNo;
    }

    public void setInquiryGroupNo(String inquiryGroupNo) {
        this.inquiryGroupNo = inquiryGroupNo;
    }

    public Long getInquiryOrderId() {
        return inquiryOrderId;
    }

    public void setInquiryOrderId(Long inquiryOrderId) {
        this.inquiryOrderId = inquiryOrderId;
    }

    public UserDTO getUserDTO() {
        return userDTO;
    }

    public void setUserDTO(UserDTO userDTO) {
        this.userDTO = userDTO;
    }

    public LabDTO getLabDTO() {
        return labDTO;
    }

    public void setLabDTO(LabDTO labDTO) {
        this.labDTO = labDTO;
    }

    public String getDateStr() {
        return dateStr;
    }

    public void setDateStr(String dateStr) {
        this.dateStr = dateStr;
    }

    public BusinessLineDTO getBusinessLineDTO() {
        return businessLineDTO;
    }

    public void setBusinessLineDTO(BusinessLineDTO businessLineDTO) {
        this.businessLineDTO = businessLineDTO;
    }

    public OrderReport getOrderReport() {
        return orderReport;
    }

    public void setOrderReport(OrderReport orderReport) {
        this.orderReport = orderReport;
    }

    public Boolean getPortal() {
        return isPortal;
    }

    public void setPortal(Boolean portal) {
        isPortal = portal;
    }

    public BaseOrderDTO getOrderDTO() {
        return orderDTO;
    }

    public void setOrderDTO(BaseOrderDTO orderDTO) {
        this.orderDTO = orderDTO;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public OiqOrderReq getOiqOrderReq() {
        return oiqOrderReq;
    }

    public void setOiqOrderReq(OiqOrderReq oiqOrderReq) {
        this.oiqOrderReq = oiqOrderReq;
    }

    public Map<String, String> getCodeToKey() {
        return codeToKey;
    }

    public void setCodeToKey(Map<String, String> codeToKey) {
        this.codeToKey = codeToKey;
    }

    public Map<String, Integer> getCodeToRow() {
        return codeToRow;
    }

    public void setCodeToRow(Map<String, Integer> codeToRow) {
        this.codeToRow = codeToRow;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public SysPersonDTO getBusinessPerson() {
        return businessPerson;
    }

    public void setBusinessPerson(SysPersonDTO businessPerson) {
        this.businessPerson = businessPerson;
    }

    public void setAddInquiry(int addInquiry) {
        this.addInquiry = addInquiry;
    }

    public int getAddInquiry() {
        return addInquiry;
    }

    public DmlMainReqDTO getDmlMainReqDTO() {
        return dmlMainReqDTO;
    }

    public void setDmlMainReqDTO(DmlMainReqDTO dmlMainReqDTO) {
        this.dmlMainReqDTO = dmlMainReqDTO;
    }
}
