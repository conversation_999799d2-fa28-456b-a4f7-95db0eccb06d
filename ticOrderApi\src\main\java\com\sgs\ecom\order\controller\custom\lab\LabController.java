package com.sgs.ecom.order.controller.custom.lab;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.lab.LabReq;
import com.sgs.ecom.order.service.member.interfaces.IMemberRestTemplateService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.lab/lab")
public class LabController {

    @Autowired
    private IMemberRestTemplateService memberRestTemplateService;



    /**
     *@Function: qryLabBankDTO
     *@Description 获取实验室信息
     *@param: [orderNo]
     *@author: Xiwei_Qiu @date: 2022/5/31 @version:
     **/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryLabBankByLabId", method = { RequestMethod.POST })
    public ResultBody qryLabBankByLabId(
            @RequestBody LabReq labReq) throws Exception{
        return ResultBody.newInstance(memberRestTemplateService.qryLabBankByLabId(labReq.getLabId()));
    }




}
