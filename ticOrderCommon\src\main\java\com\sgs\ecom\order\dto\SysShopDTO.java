package com.sgs.ecom.order.dto; 
 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 

public class SysShopDTO{
 
 	public static final String CREATE_SQL = "select qq,shopuser_name,shop_area,wangwang,mobile,shop_addr,open_time,shop_cate,close_time,shop_name,shop_logo,shop_id,shop_type,shopuser_identity_img,close_reason,shopuser_identity,shop_descript,seller_id,email,bulletin,status from sysshop_shop"; 
 
 
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="QQ", getName="getQq", setName="setQq")
 	private String qq;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOPUSER_NAME", getName="getShopuserName", setName="setShopuserName")
 	private String shopuserName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOP_AREA", getName="getShopArea", setName="setShopArea")
 	private String shopArea;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="WANGWANG", getName="getWangwang", setName="setWangwang")
 	private String wangwang;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="MOBILE", getName="getMobile", setName="setMobile")
 	private String mobile;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOP_ADDR", getName="getShopAddr", setName="setShopAddr")
 	private String shopAddr;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="OPEN_TIME", getName="getOpenTime", setName="setOpenTime")
 	private long openTime;
 	@ApiAnno(serviceName={"qryShop"})
 	@BeanAnno(value="SHOP_CATE", getName="getShopCate", setName="setShopCate")
 	private String shopCate;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CLOSE_TIME", getName="getCloseTime", setName="setCloseTime")
 	private long closeTime;
 	@ApiAnno(serviceName={"qryShop"})
 	@BeanAnno(value="SHOP_NAME", getName="getShopName", setName="setShopName")
 	private String shopName;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOP_LOGO", getName="getShopLogo", setName="setShopLogo")
 	private String shopLogo;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOP_ID", getName="getShopId", setName="setShopId")
 	private long shopId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOP_TYPE", getName="getShopType", setName="setShopType")
 	private String shopType;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOPUSER_IDENTITY_IMG", getName="getShopuserIdentityImg", setName="setShopuserIdentityImg")
 	private String shopuserIdentityImg;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="CLOSE_REASON", getName="getCloseReason", setName="setCloseReason")
 	private String closeReason;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOPUSER_IDENTITY", getName="getShopuserIdentity", setName="setShopuserIdentity")
 	private String shopuserIdentity;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SHOP_DESCRIPT", getName="getShopDescript", setName="setShopDescript")
 	private String shopDescript;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="SELLER_ID", getName="getSellerId", setName="setSellerId")
 	private long sellerId;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="EMAIL", getName="getEmail", setName="setEmail")
 	private String email;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="BULLETIN", getName="getBulletin", setName="setBulletin")
 	private String bulletin;
 	@ApiAnno(serviceName={})
 	@BeanAnno(value="STATUS", getName="getStatus", setName="setStatus")
 	private String status;

 	public void setQq(String qq){
 		 this.qq=qq;
 	}
 	public String getQq(){
 		 return this.qq;
 	}
 
 	 
 	public void setShopuserName(String shopuserName){
 		 this.shopuserName=shopuserName;
 	}
 	public String getShopuserName(){
 		 return this.shopuserName;
 	}
 
 	 
 	public void setShopArea(String shopArea){
 		 this.shopArea=shopArea;
 	}
 	public String getShopArea(){
 		 return this.shopArea;
 	}
 
 	 
 	public void setWangwang(String wangwang){
 		 this.wangwang=wangwang;
 	}
 	public String getWangwang(){
 		 return this.wangwang;
 	}
 
 	 
 	public void setMobile(String mobile){
 		 this.mobile=mobile;
 	}
 	public String getMobile(){
 		 return this.mobile;
 	}
 
 	 
 	public void setShopAddr(String shopAddr){
 		 this.shopAddr=shopAddr;
 	}
 	public String getShopAddr(){
 		 return this.shopAddr;
 	}
 
 	 
 	public void setOpenTime(long openTime){
 		 this.openTime=openTime;
 	}
 	public long getOpenTime(){
 		 return this.openTime;
 	}
 
 	 
 	public void setShopCate(String shopCate){
 		 this.shopCate=shopCate;
 	}
 	public String getShopCate(){
 		 return this.shopCate;
 	}
 
 	 
 	public void setCloseTime(long closeTime){
 		 this.closeTime=closeTime;
 	}
 	public long getCloseTime(){
 		 return this.closeTime;
 	}
 
 	 
 	public void setShopName(String shopName){
 		 this.shopName=shopName;
 	}
 	public String getShopName(){
 		 return this.shopName;
 	}
 
 	 
 	public void setShopLogo(String shopLogo){
 		 this.shopLogo=shopLogo;
 	}
 	public String getShopLogo(){
 		 return this.shopLogo;
 	}
 
 	 
 	public void setShopId(long shopId){
 		 this.shopId=shopId;
 	}
 	public long getShopId(){
 		 return this.shopId;
 	}
 
 	 
 	public void setShopType(String shopType){
 		 this.shopType=shopType;
 	}
 	public String getShopType(){
 		 return this.shopType;
 	}
 
 	 
 	public void setShopuserIdentityImg(String shopuserIdentityImg){
 		 this.shopuserIdentityImg=shopuserIdentityImg;
 	}
 	public String getShopuserIdentityImg(){
 		 return this.shopuserIdentityImg;
 	}
 
 	 
 	public void setCloseReason(String closeReason){
 		 this.closeReason=closeReason;
 	}
 	public String getCloseReason(){
 		 return this.closeReason;
 	}
 
 	 
 	public void setShopuserIdentity(String shopuserIdentity){
 		 this.shopuserIdentity=shopuserIdentity;
 	}
 	public String getShopuserIdentity(){
 		 return this.shopuserIdentity;
 	}
 
 	 
 	public void setShopDescript(String shopDescript){
 		 this.shopDescript=shopDescript;
 	}
 	public String getShopDescript(){
 		 return this.shopDescript;
 	}
 
 	 
 	public void setSellerId(long sellerId){
 		 this.sellerId=sellerId;
 	}
 	public long getSellerId(){
 		 return this.sellerId;
 	}
 
 	 
 	public void setEmail(String email){
 		 this.email=email;
 	}
 	public String getEmail(){
 		 return this.email;
 	}
 
 	 
 	public void setBulletin(String bulletin){
 		 this.bulletin=bulletin;
 	}
 	public String getBulletin(){
 		 return this.bulletin;
 	}
 
 	 
 	public void setStatus(String status){
 		 this.status=status;
 	}
 	public String getStatus(){
 		 return this.status;
 	}
 
 	 
}