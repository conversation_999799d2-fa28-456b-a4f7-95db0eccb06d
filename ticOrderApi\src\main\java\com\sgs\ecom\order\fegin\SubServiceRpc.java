package com.sgs.ecom.order.fegin;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.dml.DmlReportReq;
import com.sgs.ecom.order.service.order.interfaces.ISubOrderService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/business/rpc.v1.order/sub")
public class SubServiceRpc {

	@Autowired
	private ISubOrderService subOrderService;

	@HystrixCommand
	@RequestMapping(value = "qryQuotationData", method = {RequestMethod.POST})
	public ResultBody saveReport(
		@RequestBody OrderNoReq orderNoReq) throws Exception { ;
		return ResultBody.newInstance(subOrderService.qryQuotationData(orderNoReq.getOrderNo()));
	}


}
