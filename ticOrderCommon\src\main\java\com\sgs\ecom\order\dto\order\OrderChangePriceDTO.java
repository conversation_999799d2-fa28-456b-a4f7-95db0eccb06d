package com.sgs.ecom.order.dto.order;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;

/**
 * <AUTHOR>
 * @Description :
 * @date 2024/8/2
 */
public class OrderChangePriceDTO {
    private Boolean isMonth;
    private BaseOrderDTO baseOrderDTO;

    public OrderChangePriceDTO(Boolean isMonth, BaseOrderDTO baseOrderDTO) {
        this.isMonth = isMonth;
        this.baseOrderDTO = baseOrderDTO;
    }

    public Boolean getMonth() {
        return isMonth;
    }

    public void setMonth(Boolean month) {
        isMonth = month;
    }

    public BaseOrderDTO getBaseOrderDTO() {
        return baseOrderDTO;
    }

    public void setBaseOrderDTO(BaseOrderDTO baseOrderDTO) {
        this.baseOrderDTO = baseOrderDTO;
    }
}
