package com.sgs.ecom.order.dto.detail;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.UserCompanyDTO;
import com.sgs.ecom.order.dto.bbc.TicAppFormCodeDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.OrderLinkDTO;
import com.sgs.ecom.order.dto.user.UserAddressDTO;
import com.sgs.ecom.order.dto.user.UserInvoiceDTO;
import com.sgs.ecom.order.enumtool.application.ReportMethodEnum;
import com.sgs.ecom.order.response.order.TicAttrInfo;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class OrderApplicationFormDTO extends BaseOrderFilter {
 
 	public static final String CREATE_SQL = "select STATE_DATE,STATE,LINK_PHONE,FORM_TEMPLATE,CREATE_DATE,LINK_PERSON,COMPANY_ADDRESS_EN,TEST_CYCLE,COMPANY_NAME_CN,COMPANY_ADDRESS_CN,LINK_EMAIL,FORM_ID,COMPANY_NAME_EN from ORDER_APPLICATION_FORM"; 
 
 
 	@ApiAnno(groups={Default.class, TicForm.class, TicForm.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class, TicForm.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="LINK_PHONE", getName="getLinkPhone", setName="setLinkPhone")
 	private String linkPhone;
 	@ApiAnno(groups={Default.class, TicForm.class})
 	@BeanAnno(value="FORM_TEMPLATE", getName="getFormTemplate", setName="setFormTemplate")
 	private String formTemplate;
 	@ApiAnno(groups={Default.class, TicForm.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="LINK_PERSON", getName="getLinkPerson", setName="setLinkPerson")
 	private String linkPerson;
 	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="COMPANY_ADDRESS_EN", getName="getCompanyAddressEn", setName="setCompanyAddressEn")
 	private String companyAddressEn;
 	@ApiAnno(groups={Default.class, TicForm.class})
 	@BeanAnno(value="TEST_CYCLE", getName="getTestCycle", setName="setTestCycle")
 	private int testCycle;
 	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="COMPANY_NAME_CN", getName="getCompanyNameCn", setName="setCompanyNameCn")
 	private String companyNameCn;
 	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="COMPANY_ADDRESS_CN", getName="getCompanyAddressCn", setName="setCompanyAddressCn")
 	private String companyAddressCn;
 	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="LINK_EMAIL", getName="getLinkEmail", setName="setLinkEmail")
 	private String linkEmail;
 	@ApiAnno(groups={Default.class, TicForm.class})
 	@BeanAnno(value="FORM_ID", getName="getFormId", setName="setFormId")
 	private long formId;
 	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
	private Integer isRefundSample;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String reportSendCc;
	@ApiAnno(groups={Default.class, TicForm.class})
	private OrderExpressDTO backAddress;
	@ApiAnno(groups={Default.class, TicForm.class})
	private OrderExpressDTO detectionAddress;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String detectionAddressStr;
	@ApiAnno(groups={Default.class, TicForm.class})
	private OrderExpressDTO reportAddress;
	@ApiAnno(groups={Default.class, TicForm.class})
	private OrderExpressDTO teachingMaterialAddress;//ACA客户-教材寄送地址
	@ApiAnno(groups={Default.class, TicForm.class})
	private OrderExpressDTO certificateAddress;//ACA客户-证书寄送地址
	@ApiAnno(groups={Default.class, TicForm.class})
	private String backAddressStr;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String reportAddressStr;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String teachingMaterialAddressStr;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String certificateAddressStr;
	@ApiAnno(groups={Default.class, TicForm.class,BaseOrderFilter.OrderToOther.class})
	private String testMemo;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String receiveName;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String receivePhone;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String receiveEmail;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String province;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String city;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String town;
	@ApiAnno(groups={Default.class, TicForm.class})
	private int reportMethod;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String reportMethodValue;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String reportMethodShow;
	@ApiAnno(groups={Default.class, TicForm.class})
	private int isTransfer;





	@ApiAnno(groups={Default.class, TicForm.class})
	private String orderNo;
	@ApiAnno(groups={Default.class, TicForm.class})
	private List<TicAppFormCodeDTO> formCodeList=new ArrayList<>();//ins专用
	@ApiAnno(groups={Default.class, TicForm.class})
	private List<TicAppFormCodeDTO> formList=new ArrayList<>();//非ins专用
	@ApiAnno(groups={Default.class, TicForm.class})
	private OrderReportDTO orderReportDTO;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String bu;
	@ApiAnno(groups={Default.class, TicForm.class})
	private List<OrderLinkDTO> orderLinkDTOList;

	@ApiAnno(groups={Default.class, TicForm.class})
	private OrderInvoiceDTO orderInvoiceDTO;
	@ApiAnno(groups={Default.class, TicForm.class})
	private String subBuCode;

	@ApiAnno(groups={Default.class, TicForm.class})
	private List<TicAttrInfo> attrInfo;

	@ApiAnno(groups={Default.class, TicForm.class})
	private List<OrderAttachmentDTO> allFileList = new ArrayList<>();

	@ApiAnno(groups={Default.class})
	private UserAddressDTO invoiceAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO buyAddress;
	@ApiAnno(groups={Default.class, TicForm.class})
	private  String reportLua;


	public void setReportMethodShow(String reportMethodShow) {
		this.reportMethodShow = reportMethodShow;
	}

	public String getReportLua() {
		return reportLua;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}

	public List<OrderAttachmentDTO> getAllFileList() {
		return allFileList;
	}

	public void setAllFileList(List<OrderAttachmentDTO> allFileList) {
		this.allFileList = allFileList;
	}

	public UserAddressDTO getInvoiceAddress() {
		return invoiceAddress;
	}

	public void setInvoiceAddress(UserAddressDTO invoiceAddress) {
		this.invoiceAddress = invoiceAddress;
	}

	public UserAddressDTO getBuyAddress() {
		return buyAddress;
	}

	public void setBuyAddress(UserAddressDTO buyAddress) {
		this.buyAddress = buyAddress;
	}

	public UserAddressDTO getFactoryAddress() {
		return factoryAddress;
	}

	public void setFactoryAddress(UserAddressDTO factoryAddress) {
		this.factoryAddress = factoryAddress;
	}

	public UserAddressDTO getSupplierAddress() {
		return supplierAddress;
	}

	public void setSupplierAddress(UserAddressDTO supplierAddress) {
		this.supplierAddress = supplierAddress;
	}

	public UserInvoiceDTO getInvoice() {
		return invoice;
	}

	public void setInvoice(UserInvoiceDTO invoice) {
		this.invoice = invoice;
	}

	public String getTestMemoImg() {
		return testMemoImg;
	}

	public void setTestMemoImg(String testMemoImg) {
		this.testMemoImg = testMemoImg;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public String getRegPhone() {
		return regPhone;
	}

	public void setRegPhone(String regPhone) {
		this.regPhone = regPhone;
	}

	public String getBankName() {
		return bankName;
	}

	public void setBankName(String bankName) {
		this.bankName = bankName;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public String getImg() {
		return img;
	}

	public void setImg(String img) {
		this.img = img;
	}

	public String getReportCompanyNameCn() {
		return reportCompanyNameCn;
	}

	public void setReportCompanyNameCn(String reportCompanyNameCn) {
		this.reportCompanyNameCn = reportCompanyNameCn;
	}

	public String getReportCompanyNameEn() {
		return reportCompanyNameEn;
	}

	public void setReportCompanyNameEn(String reportCompanyNameEn) {
		this.reportCompanyNameEn = reportCompanyNameEn;
	}

	public String getReportAddressCn() {
		return reportAddressCn;
	}

	public void setReportAddressCn(String reportAddressCn) {
		this.reportAddressCn = reportAddressCn;
	}

	public String getReportAddressEn() {
		return reportAddressEn;
	}

	public void setReportAddressEn(String reportAddressEn) {
		this.reportAddressEn = reportAddressEn;
	}

	@ApiAnno(groups={Default.class})
	private UserAddressDTO factoryAddress;
	@ApiAnno(groups={Default.class})
	private UserAddressDTO supplierAddress;
	@ApiAnno(groups={Default.class})
	private UserInvoiceDTO invoice;

	@ApiAnno(groups={Default.class})
	private String testMemoImg;


	//税号
	@ApiAnno(groups={Default.class})
	private String taxNo;
	//公司电话
	@ApiAnno(groups={Default.class})
	private String regPhone;
	//开户银行
	@ApiAnno(groups={Default.class})
	private String bankName;
	@ApiAnno(groups={Default.class})
	//银行账户
	private String bankNumber;
	@ApiAnno(groups={Default.class})
	//营业执照图片
	private String img;

	@ApiAnno(groups={Default.class})
	private String reportCompanyNameCn;
	@ApiAnno(groups={Default.class})
	private String reportCompanyNameEn;
	@ApiAnno(groups={Default.class})
	private String reportAddressCn;
	@ApiAnno(groups={Default.class})
	private String reportAddressEn;


	public OrderApplicationFormDTO( UserCompanyDTO userCompanyDTO) {
		this.province = userCompanyDTO.getProvice();
		this.city = userCompanyDTO.getCity();
		this.town = userCompanyDTO.getTown();
		this.companyNameCn = userCompanyDTO.getCompanyName();
		this.companyNameEn = userCompanyDTO.getCompanyNameEn();
		this.companyAddressCn = userCompanyDTO.getAddress();
		this.companyAddressEn = userCompanyDTO.getCompanyNameEn();
	}



	public OrderExpressDTO getTeachingMaterialAddress() {
		return teachingMaterialAddress;
	}

	public void setTeachingMaterialAddress(OrderExpressDTO teachingMaterialAddress) {
		this.teachingMaterialAddress = teachingMaterialAddress;
	}

	public OrderExpressDTO getCertificateAddress() {
		return certificateAddress;
	}

	public void setCertificateAddress(OrderExpressDTO certificateAddress) {
		this.certificateAddress = certificateAddress;
	}

	public String getTeachingMaterialAddressStr() {
		return teachingMaterialAddressStr;
	}

	public void setTeachingMaterialAddressStr(String teachingMaterialAddressStr) {
		this.teachingMaterialAddressStr = teachingMaterialAddressStr;
	}

	public String getCertificateAddressStr() {
		return certificateAddressStr;
	}

	public void setCertificateAddressStr(String certificateAddressStr) {
		this.certificateAddressStr = certificateAddressStr;
	}

	public OrderApplicationFormDTO(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO) {
		this.linkPerson = orderBaseInfoCheckDTO.getUserName();
		this.linkPhone = orderBaseInfoCheckDTO.getUserPhone();
		this.linkEmail = orderBaseInfoCheckDTO.getUserEmail();
		this.province = orderBaseInfoCheckDTO.getProvince();
		this.city = orderBaseInfoCheckDTO.getCity();
		this.town = orderBaseInfoCheckDTO.getTown();
		this.companyNameCn = orderBaseInfoCheckDTO.getCompanyName();
		this.companyNameEn = orderBaseInfoCheckDTO.getCompanyNameEn();
		this.companyAddressCn = orderBaseInfoCheckDTO.getCompanyAddressCn();
		this.companyAddressEn = orderBaseInfoCheckDTO.getCompanyAddressEn();
	}
	public OrderApplicationFormDTO() {


	}

	public List<TicAttrInfo> getAttrInfo() {
		return attrInfo;
	}

	public void setAttrInfo(List<TicAttrInfo> attrInfo) {
		this.attrInfo = attrInfo;
	}

	public OrderExpressDTO getDetectionAddress() {
		return detectionAddress;
	}

	public void setDetectionAddress(OrderExpressDTO detectionAddress) {
		this.detectionAddress = detectionAddress;
	}

	public String getDetectionAddressStr() {
		return detectionAddressStr;
	}

	public void setDetectionAddressStr(String detectionAddressStr) {
		this.detectionAddressStr = detectionAddressStr;
	}

	public String getSubBuCode() {
		return subBuCode;
	}

	public void setSubBuCode(String subBuCode) {
		this.subBuCode = subBuCode;
	}

	public OrderInvoiceDTO getOrderInvoiceDTO() {
		return orderInvoiceDTO;
	}

	public void setOrderInvoiceDTO(OrderInvoiceDTO orderInvoiceDTO) {
		this.orderInvoiceDTO = orderInvoiceDTO;
	}

	public List<OrderLinkDTO> getOrderLinkDTOList() {
		return orderLinkDTOList;
	}

	public void setOrderLinkDTOList(List<OrderLinkDTO> orderLinkDTOList) {
		this.orderLinkDTOList = orderLinkDTOList;
	}

	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class)
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setLinkPhone(String linkPhone){
 		 this.linkPhone=linkPhone;
 	}
 	public String getLinkPhone(){
 		 return this.linkPhone;
 	}
 
 	 
 	public void setFormTemplate(String formTemplate){
 		 this.formTemplate=formTemplate;
 	}
 	public String getFormTemplate(){
 		 return this.formTemplate;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class)
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setLinkPerson(String linkPerson){
 		 this.linkPerson=linkPerson;
 	}
 	public String getLinkPerson(){
 		 return this.linkPerson;
 	}
 
 	 
 	public void setCompanyAddressEn(String companyAddressEn){
 		 this.companyAddressEn=companyAddressEn;
 	}
 	public String getCompanyAddressEn(){
 		 return this.companyAddressEn;
 	}
 
 	 
 	public void setTestCycle(int testCycle){
 		 this.testCycle=testCycle;
 	}
 	public int getTestCycle(){
 		 return this.testCycle;
 	}
 
 	 
 	public void setCompanyNameCn(String companyNameCn){
 		 this.companyNameCn=companyNameCn;
 	}
 	public String getCompanyNameCn(){
 		 return this.companyNameCn;
 	}
 
 	 
 	public void setCompanyAddressCn(String companyAddressCn){
 		 this.companyAddressCn=companyAddressCn;
 	}
 	public String getCompanyAddressCn(){
 		 return this.companyAddressCn;
 	}
 
 	 
 	public void setLinkEmail(String linkEmail){
 		 this.linkEmail=linkEmail;
 	}
 	public String getLinkEmail(){
 		 return this.linkEmail;
 	}
 
 	 
 	public void setFormId(long formId){
 		 this.formId=formId;
 	}
 	public long getFormId(){
 		 return this.formId;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}

	public Integer getIsRefundSample() {
		return isRefundSample;
	}

	public void setIsRefundSample(Integer isRefundSample) {
		this.isRefundSample = isRefundSample;
	}

	public String getReportSendCc() {
		return reportSendCc;
	}

	public void setReportSendCc(String reportSendCc) {
		this.reportSendCc = reportSendCc;
	}

	public OrderExpressDTO getBackAddress() {
		return backAddress;
	}

	public void setBackAddress(OrderExpressDTO backAddress) {
		this.backAddress = backAddress;
	}

	public OrderExpressDTO getReportAddress() {
		return reportAddress;
	}

	public void setReportAddress(OrderExpressDTO reportAddress) {
		this.reportAddress = reportAddress;
	}

	public String getBackAddressStr() {
		return backAddressStr;
	}

	public void setBackAddressStr(String backAddressStr) {
		this.backAddressStr = backAddressStr;
	}

	public String getReportAddressStr() {
		return reportAddressStr;
	}

	public void setReportAddressStr(String reportAddressStr) {
		this.reportAddressStr = reportAddressStr;
	}

	public String getTestMemo() {
		return testMemo;
	}

	public void setTestMemo(String testMemo) {
		this.testMemo = testMemo;
	}

	public String getReceiveName() {
		return receiveName;
	}

	public void setReceiveName(String receiveName) {
		this.receiveName = receiveName;
	}

	public String getReceivePhone() {
		return receivePhone;
	}

	public void setReceivePhone(String receivePhone) {
		this.receivePhone = receivePhone;
	}

	public String getReceiveEmail() {
		return receiveEmail;
	}

	public void setReceiveEmail(String receiveEmail) {
		this.receiveEmail = receiveEmail;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public int getReportMethod() {
		return reportMethod;
	}

	public void setReportMethod(int reportMethod) {
		this.reportMethod = reportMethod;
	}

	public String getReportMethodValue() {
		return reportMethodValue;
	}

	public void setReportMethodValue(String reportMethodValue) {
		this.reportMethodValue = reportMethodValue;
	}

	public String getReportMethodShow() {
		if(ReportMethodEnum.OTHER.getIndex()==reportMethod){
			return reportMethodValue;
		}
		return ReportMethodEnum.getNameCh(reportMethod);
	}

	public int getIsTransfer() {
		return isTransfer;
	}

	public void setIsTransfer(int isTransfer) {
		this.isTransfer = isTransfer;
	}


	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public List<TicAppFormCodeDTO> getFormCodeList() {
		return formCodeList;
	}

	public void setFormCodeList(List<TicAppFormCodeDTO> formCodeList) {
		this.formCodeList = formCodeList;
	}

	public OrderReportDTO getOrderReportDTO() {
		return orderReportDTO;
	}

	public void setOrderReportDTO(OrderReportDTO orderReportDTO) {
		this.orderReportDTO = orderReportDTO;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public List<TicAppFormCodeDTO> getFormList() {
		return formList;
	}

	public void setFormList(List<TicAppFormCodeDTO> formList) {
		this.formList = formList;
	}


}