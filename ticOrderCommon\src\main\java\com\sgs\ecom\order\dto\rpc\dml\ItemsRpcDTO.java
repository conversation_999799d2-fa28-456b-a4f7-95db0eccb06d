package com.sgs.ecom.order.dto.rpc.dml;

import java.math.BigDecimal;
import java.util.List;

public class ItemsRpcDTO {
    private String testLineId;
    private String itemName;
    private String standardCode;
    private BigDecimal originalPrice;
    private BigDecimal totalPrice;
    private BigDecimal price;
    private int buyNums;
    private int testDays;
    private String testMemo;
    private String unit;
    private String otherExplain;
    private String memoExplain;
    private String sampleRequirements;
    private String cmaLab;
    private String cnasLab;
    private String labelName;
    private int urgentType;
    private List<SampleFormRpcDTO> relate;

    public String getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(String testLineId) {
        this.testLineId = testLineId;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }


    public int getBuyNums() {
        return buyNums;
    }

    public void setBuyNums(int buyNums) {
        this.buyNums = buyNums;
    }

    public int getTestDays() {
        return testDays;
    }

    public void setTestDays(int testDays) {
        this.testDays = testDays;
    }

    public String getTestMemo() {
        return testMemo;
    }

    public void setTestMemo(String testMemo) {
        this.testMemo = testMemo;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getOtherExplain() {
        return otherExplain;
    }

    public void setOtherExplain(String otherExplain) {
        this.otherExplain = otherExplain;
    }

    public String getMemoExplain() {
        return memoExplain;
    }

    public void setMemoExplain(String memoExplain) {
        this.memoExplain = memoExplain;
    }

    public String getSampleRequirements() {
        return sampleRequirements;
    }

    public void setSampleRequirements(String sampleRequirements) {
        this.sampleRequirements = sampleRequirements;
    }

    public String getCmaLab() {
        return cmaLab;
    }

    public void setCmaLab(String cmaLab) {
        this.cmaLab = cmaLab;
    }

    public String getCnasLab() {
        return cnasLab;
    }

    public void setCnasLab(String cnasLab) {
        this.cnasLab = cnasLab;
    }

    public String getLabelName() {
        return labelName;
    }

    public void setLabelName(String labelName) {
        this.labelName = labelName;
    }

    public int getUrgentType() {
        return urgentType;
    }

    public void setUrgentType(int urgentType) {
        this.urgentType = urgentType;
    }

    public List<SampleFormRpcDTO> getRelate() {
        return relate;
    }

    public void setRelate(List<SampleFormRpcDTO> relate) {
        this.relate = relate;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}
