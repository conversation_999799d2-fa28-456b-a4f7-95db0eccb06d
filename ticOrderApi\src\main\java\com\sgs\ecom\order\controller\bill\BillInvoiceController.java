package com.sgs.ecom.order.controller.bill;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.bill.BillFileReq;
import com.sgs.ecom.order.service.bill.interfaces.IBillInvoiceService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v2.bill/invoice")
public class BillInvoiceController extends ControllerUtil {

    @Autowired
    private IBillInvoiceService billInvoiceService;

    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "save", method = { RequestMethod.POST })
    public ResultBody saveInvoice(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody BillFileReq billFileReq) throws Exception {
        billInvoiceService.saveInvoice(billFileReq,getPersonInfo(token));
        return ResultBody.success();
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "fileList", method = { RequestMethod.POST })
    public ResultBody fileList(
            @RequestBody BillFileReq billFileReq) throws Exception {
        return ResultBody.newInstance(billInvoiceService.selectFile(billFileReq));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "delFile", method = { RequestMethod.POST })
    public ResultBody delFile(
            @RequestHeader(value = "accessToken") String token,
            @RequestBody BillFileReq billFileReq) throws Exception {
        billInvoiceService.delFile(billFileReq,getPersonInfo(token));
        return ResultBody.success();
    }

}
