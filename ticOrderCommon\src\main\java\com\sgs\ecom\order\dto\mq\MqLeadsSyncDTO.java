package com.sgs.ecom.order.dto.mq;

import java.util.List;

public class MqLeadsSyncDTO {
    //{"ticketId":"20230703000007","personPhone":"17312190752","personEmail":"<EMAIL>","operatorDate":1688374504864,
    // "action":"create","bu":"LEADS系统QA","systems":["OIQ"]}
    private String ticketId;
    private String personPhone;
    private String personEmail;
    private String emailCode;
    private String createDate;
    private String operatorDate;
    private List<String> systems;
    private String action;

    public String getTicketId() {
        return ticketId;
    }

    public void setTicketId(String ticketId) {
        this.ticketId = ticketId;
    }

    public String getPersonPhone() {
        return personPhone;
    }

    public void setPersonPhone(String personPhone) {
        this.personPhone = personPhone;
    }

    public String getPersonEmail() {
        return personEmail;
    }

    public void setPersonEmail(String personEmail) {
        this.personEmail = personEmail;
    }

    public String getOperatorDate() {
        return operatorDate;
    }

    public void setOperatorDate(String operatorDate) {
        this.operatorDate = operatorDate;
    }

    public List<String> getSystems() {
        return systems;
    }

    public void setSystems(List<String> systems) {
        this.systems = systems;
    }

    public String getEmailCode() {
        return emailCode;
    }

    public void setEmailCode(String emailCode) {
        this.emailCode = emailCode;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }
}
