package com.sgs.ecom.order.dto.rpc.dml;


import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.oiq.SalesInfoDTO;
import com.sgs.ecom.order.dto.pay.PaymentChannelsDTO;
import com.sgs.ecom.order.request.base.OiqEventReq;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CreateInquiryRpcDTO extends OiqEventReq {

    private String orderNo;
    private String platform;
    private String platformOrder;
    private String operatorDate;
    private String memo;
    @Valid
    private BaseInfoRpcDTO baseInfo;
    private List<SampleFormRpcDTO> sampleForm;
    private List<ItemsRpcDTO> items;
    private List<QuotationFileDTO> quotations;
    private PayerInfoDTO payerInfo;
    private SalesInfoDTO salesInfo;

    private List<PaymentChannelsDTO> paymentChannels=new ArrayList<>();

    //用来转递参数对象 业务处理
    private DmlRpcReqDTO dmlRpcReqDTO=new DmlRpcReqDTO();
    private BaseOrderDTO minOrder;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPlatformOrder() {
        return platformOrder;
    }

    public void setPlatformOrder(String platformOrder) {
        this.platformOrder = platformOrder;
    }

    public String getOperatorDate() {
        return operatorDate;
    }

    public void setOperatorDate(String operatorDate) {
        this.operatorDate = operatorDate;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public BaseInfoRpcDTO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseInfoRpcDTO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public List<SampleFormRpcDTO> getSampleForm() {
        return sampleForm;
    }

    public void setSampleForm(List<SampleFormRpcDTO> sampleForm) {
        this.sampleForm = sampleForm;
    }

    public List<ItemsRpcDTO> getItems() {
        return items;
    }

    public void setItems(List<ItemsRpcDTO> items) {
        this.items = items;
    }

    public DmlRpcReqDTO getDmlRpcReqDTO() {
        return dmlRpcReqDTO;
    }

    public void setDmlRpcReqDTO(DmlRpcReqDTO dmlRpcReqDTO) {
        this.dmlRpcReqDTO = dmlRpcReqDTO;
    }

    public List<QuotationFileDTO> getQuotations() {
        return quotations;
    }

    public void setQuotations(List<QuotationFileDTO> quotations) {
        this.quotations = quotations;
    }

    public PayerInfoDTO getPayerInfo() {
        return payerInfo;
    }

    public void setPayerInfo(PayerInfoDTO payerInfo) {
        this.payerInfo = payerInfo;
    }

    public List<PaymentChannelsDTO> getPaymentChannels() {
        return paymentChannels;
    }

    public void setPaymentChannels(List<PaymentChannelsDTO> paymentChannels) {
        this.paymentChannels = paymentChannels;
    }

    public BaseOrderDTO getMinOrder() {
        return minOrder;
    }

    public void setMinOrder(BaseOrderDTO minOrder) {
        this.minOrder = minOrder;
    }

    public SalesInfoDTO getSalesInfo() {
        return salesInfo;
    }

    public void setSalesInfo(SalesInfoDTO salesInfo) {
        this.salesInfo = salesInfo;
    }
}
