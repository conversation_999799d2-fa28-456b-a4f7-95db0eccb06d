package com.sgs.ecom.order.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;


import java.util.List;

/**
 * <AUTHOR>
 */
public class OiqItemDTO {
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String itemName;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String standardCode;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private String testMemo;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Integer isDetermine;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqSampleDTO> sampleList;

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class,BaseOrderFilter.OrderToOther.class})
    private Integer buyNums;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private OiqOtherExplainDTO otherExplainDTO=new OiqOtherExplainDTO();
    private String categoryPath;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private Long itemId;
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private String testLineId;

    public OiqItemDTO() {
    }


    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }

    public String getStandardCode() {
        return standardCode;
    }

    public void setStandardCode(String standardCode) {
        this.standardCode = standardCode;
    }

    public String getTestMemo() {
        return testMemo;
    }

    public void setTestMemo(String testMemo) {
        this.testMemo = testMemo;
    }

    public Integer getIsDetermine() {
        return isDetermine;
    }

    public void setIsDetermine(Integer isDetermine) {
        this.isDetermine = isDetermine;
    }



    public Integer getBuyNums() {
        return buyNums;
    }

    public void setBuyNums(Integer buyNums) {
        this.buyNums = buyNums;
    }

    public List<OiqSampleDTO> getSampleList() {
        return sampleList;
    }

    public void setSampleList(List<OiqSampleDTO> sampleList) {
        this.sampleList = sampleList;
    }

    public OiqOtherExplainDTO getOtherExplainDTO() {
        return otherExplainDTO;
    }

    public void setOtherExplainDTO(OiqOtherExplainDTO otherExplainDTO) {
        this.otherExplainDTO = otherExplainDTO;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public Long getItemId() {
        return itemId;
    }

    public void setItemId(Long itemId) {
        this.itemId = itemId;
    }

    public String getTestLineId() {
        return testLineId;
    }

    public void setTestLineId(String testLineId) {
        this.testLineId = testLineId;
    }
}
