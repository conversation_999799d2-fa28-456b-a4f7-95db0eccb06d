package com.sgs.ecom.order.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.dto.detail.OrderReportDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.order.OrderApplicationAttrDTO;
import com.sgs.ecom.order.dto.rpc.dml.ApplicationInfoDTO;
import com.sgs.ecom.order.entity.order.OrderBaseInfo;
import com.sgs.ecom.order.entity.order.OrderReport;
import com.sgs.ecom.order.enumtool.application.OiqReportFormEnum;
import com.sgs.ecom.order.enumtool.application.ReportLuaEnum;
import com.sgs.ecom.order.enumtool.application.ReportMethodEnum;
import com.sgs.ecom.order.enumtool.dml.BusinessCodeEnum;
import com.sgs.ecom.order.request.oiq.OiqApplicationReq;
import com.sgs.ecom.order.request.oiq.OiqReportReq;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class OrderReportDO extends OrderReport {

    private OrderReport orderReport = new OrderReport();
    private BaseCopyObj baseCopy = new BaseCopyObj();

    public void addIsUploadProject(OrderReportDTO orderReportDTO, List<OrderApplicationAttrDTO> isUploadProjectList) {
        if(ValidationUtil.isEmpty(isUploadProjectList))
            return;
        orderReportDTO.setIsUploadProject(ValidationUtil.isEmpty(isUploadProjectList.get(0))?"":isUploadProjectList.get(0).getAttrValue());
    }

    public OrderReport getReportByReq(OiqReportReq oiqReportReq, String orderNo,String bu){
        baseCopy.copyWithNull(orderReport,oiqReportReq);

        orderReport.setReportLua(ReportLuaEnum.getNameChByName(oiqReportReq.getReportLuaCode()));
        orderReport.setReportForm(OiqReportFormEnum.getNameCh(oiqReportReq.getReportFormCode()));
        if("1900".equals(bu)){
            orderReport.setReportLua(ReportLuaEnum.getMinNameChByName(oiqReportReq.getReportLuaCode()));
            orderReport.setReportForm(OiqReportFormEnum.getMinNameCh(oiqReportReq.getReportFormCode()));
        }

        orderReport.setReportPerson(JSON.toJSONString(new ArrayList<>()));
        orderReport.setOrderNo(orderNo);
        orderReport.setState(1);
        return this.orderReport;
    }

    public OrderReport getReportByReq(OiqReportReq oiqReportReq, String orderNo, OiqOrderReqDTO oiqOrderReqDTO){
        if(ValidationUtil.isEmpty(oiqReportReq)){
            return null;
        }
        //同申请方 付款方
        if(oiqReportReq.getReportTitleType()!=null){
            if(oiqReportReq.getReportTitleType()==0){
                OiqApplicationReq applicationReq=oiqOrderReqDTO.getOiqOrderReq().getApplication();
                oiqReportReq.setReportCompanyNameCn(applicationReq.getCompanyNameCn());
                oiqReportReq.setReportCompanyNameEn(applicationReq.getCompanyNameEn());
                oiqReportReq.setReportAddressCn(applicationReq.getCompanyAddressCn());
                oiqReportReq.setReportAddressEn(applicationReq.getCompanyAddressEn());
            }
            if(oiqReportReq.getReportTitleType()==1) {
                OrderInvoiceDTO orderInvoiceDTO=oiqOrderReqDTO.getDmlMainReqDTO().getOrderInvoiceDTO();
                if(!ValidationUtil.isEmpty(orderInvoiceDTO) && orderInvoiceDTO.getInvoiceType()!=2){
                    oiqReportReq.setReportCompanyNameCn(orderInvoiceDTO.getInvoiceTitle());
                    oiqReportReq.setReportAddressCn(orderInvoiceDTO.getRegAddress());
                }
            }
        }

        baseCopy.copyWithNull(orderReport,oiqReportReq);
        orderReport.setReportLua(ReportLuaEnum.getNameChByName(oiqReportReq.getReportLuaCode()));
        orderReport.setReportForm(OiqReportFormEnum.getNameCh(oiqReportReq.getReportFormCode()));
        if(oiqOrderReqDTO.getPortal() && BusinessCodeEnum.toMin(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode())){
            orderReport.setReportLua(ReportLuaEnum.getMinNameChByName(oiqReportReq.getReportLuaCode()));
            orderReport.setReportForm(OiqReportFormEnum.getMinNameCh(oiqReportReq.getReportFormCode()));
        }



        if(StringUtils.isNotBlank(oiqReportReq.getReportFormCode()) && oiqReportReq.getReportFormCode().toLowerCase().equals("other")){
            orderReport.setReportForm(oiqReportReq.getReportFormValue());
        }


        orderReport.setReportPerson(JSON.toJSONString(new ArrayList<>()));
        orderReport.setOrderNo(orderNo);
        orderReport.setState(1);
        if(oiqReportReq.getReportMethod()!=null && ReportMethodEnum.OTHER.getIndex()!=oiqReportReq.getReportMethod()){
            orderReport.setReportMethodMemo("");
        }
        return this.orderReport;
    }

    public OrderReport getReportByDmlReq(ApplicationInfoDTO applicationInfoDTO, String orderNo, OrderBaseInfo orderBaseInfo){
        OrderReport baseReport = new OrderReport();
        baseReport.setReportCompanyNameCn(applicationInfoDTO.getReportTitle());
        baseReport.setReportCompanyNameEn(applicationInfoDTO.getReportTitleEn());
        baseReport.setReportAddressCn(applicationInfoDTO.getReportAddress());
        baseReport.setReportAddressEn(applicationInfoDTO.getReportAddressEn());
        baseReport.setReportMethod(applicationInfoDTO.getReportMethod());
        baseReport.setReportMethodMemo(applicationInfoDTO.getReportMethodValue());
        baseReport.setReportTitleType(applicationInfoDTO.getReportTitleType());
        baseReport.setReportLuaCode(orderBaseInfo.getReportLuaCode());
        baseReport.setReportLua(orderBaseInfo.getReportLua());
        baseReport.setReportFormCode(orderBaseInfo.getReportFormCode());
        baseReport.setReportForm(orderBaseInfo.getReportForm());
        baseReport.setReportPerson(JSON.toJSONString(new ArrayList<>()));
        baseReport.setOrderNo(orderNo);
        baseReport.setState(1);
        return baseReport;
    }

}
