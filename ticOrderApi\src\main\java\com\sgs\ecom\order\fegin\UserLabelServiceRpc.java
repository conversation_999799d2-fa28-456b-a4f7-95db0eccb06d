package com.sgs.ecom.order.fegin;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.rpc.BbcUserLabelReq;
import com.sgs.ecom.order.service.user.interfaces.IUserLabelSV;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/rpc.v1.order/label")
public class UserLabelServiceRpc {

	@Autowired
	private IUserLabelSV userLabelSV;

	@RequestMapping(value = "updateUserLabel", method = { RequestMethod.POST })
	public ResultBody updateUserLabel (
		@RequestBody BbcUserLabelReq bbcUserLabelReq)throws Exception{
		userLabelSV.updateUserLabel(bbcUserLabelReq);
		return ResultBody.success();
	}
}
