package com.sgs.ecom.order.dto.user; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class UserCompanyDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select PROVICE,STATE_DATE,USER_ID,STATE,COMPANY_ADDR_EN,COMPANY_NAME,CREATE_DATE,COUNTRY,CITY,TOWN,ADDRESS,COMPANY_ID,COMPANY_NAME_EN from USER_COMPANY"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="PROVICE", getName="getProvice", setName="setProvice")
 	private String provice;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COMPANY_ADDR_EN", getName="getCompanyAddrEn", setName="setCompanyAddrEn")
 	private String companyAddrEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COMPANY_NAME", getName="getCompanyName", setName="setCompanyName")
 	private String companyName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COUNTRY", getName="getCountry", setName="setCountry")
 	private String country;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CITY", getName="getCity", setName="setCity")
 	private String city;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TOWN", getName="getTown", setName="setTown")
 	private String town;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ADDRESS", getName="getAddress", setName="setAddress")
 	private String address;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COMPANY_ID", getName="getCompanyId", setName="setCompanyId")
 	private long companyId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="COMPANY_NAME_EN", getName="getCompanyNameEn", setName="setCompanyNameEn")
 	private String companyNameEn;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_NICK", getName="getUserNick", setName="setUserNick")
 	private String userNick;

 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setCompanyAddrEn(String companyAddrEn){
 		 this.companyAddrEn=companyAddrEn;
 	}
 	public String getCompanyAddrEn(){
 		 return this.companyAddrEn;
 	}
 
 	 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	public void setCompanyId(long companyId){
 		 this.companyId=companyId;
 	}
 	public long getCompanyId(){
 		 return this.companyId;
 	}
 
 	 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
	public String getUserNick() {
		return userNick;
	}
	public void setUserNick(String userNick) {
		this.userNick = userNick;
	}
 
 	 
}