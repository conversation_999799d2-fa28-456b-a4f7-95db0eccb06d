package com.sgs.ecom.order.entity.order;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.springframework.data.annotation.Id;

/**
 * @ClassName OrderProduct
 * <AUTHOR>
 * @Date 2024-06-23 17:33
 */
@Data
public class OrderProduct {

    @Id
    private Long productId;
    private String orderNo;
    private Long prodId;
    private String productName;
    private String subTitle;
    private String storeId;
    private String storeName;
    private BigDecimal price;
    private Integer quantity;
    private BigDecimal totalPrice;
    private String createDate;
    private Date stateDate;
    private String subBuCode;
    private BigDecimal realTotalPrice;

    private String productMemo;
    private String skuAttr;
    private String productImg;
    private String customType;
    private Integer state;

    private BigDecimal shopDisAmount;
    private BigDecimal subDiscountAmount;
    private BigDecimal subCsDiscountAmount;
    private BigDecimal subServiceAmount;

    /**
     * 最低售价
     */
    private BigDecimal lowestPrice;

    /**
     * SKU费用
     */
    private BigDecimal skuPrice;

    /**
     * 最低售价补收差额
     */
    private BigDecimal lowestPriceMargin;

    /**
     * 触发最低售价
     */
    private boolean triggerLowestPrice;

    /**
     * 触发最低售价描述
     */
    private String triggerLowestPriceStr;

    public BigDecimal getSubDiscountAmount() {
        return subDiscountAmount;
    }

    public void setSubDiscountAmount(BigDecimal subDiscountAmount) {
        this.subDiscountAmount = subDiscountAmount;
    }

    public BigDecimal getSubCsDiscountAmount() {
        return subCsDiscountAmount;
    }

    public void setSubCsDiscountAmount(BigDecimal subCsDiscountAmount) {
        this.subCsDiscountAmount = subCsDiscountAmount;
    }

    public BigDecimal getSubServiceAmount() {
        return subServiceAmount;
    }

    public void setSubServiceAmount(BigDecimal subServiceAmount) {
        this.subServiceAmount = subServiceAmount;
    }

    public BigDecimal getShopDisAmount() {
        return shopDisAmount;
    }

    public void setShopDisAmount(BigDecimal shopDisAmount) {
        this.shopDisAmount = shopDisAmount;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Long getProdId() {
        return prodId;
    }

    public void setProdId(Long prodId) {
        this.prodId = prodId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getStoreId() {
        return storeId;
    }

    public void setStoreId(String storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public Date getStateDate() {
        return stateDate;
    }

    public void setStateDate(Date stateDate) {
        this.stateDate = stateDate;
    }

    public String getSubBuCode() {
        return subBuCode;
    }

    public void setSubBuCode(String subBuCode) {
        this.subBuCode = subBuCode;
    }

    public BigDecimal getRealTotalPrice() {
        return realTotalPrice;
    }

    public void setRealTotalPrice(BigDecimal realTotalPrice) {
        this.realTotalPrice = realTotalPrice;
    }

    public String getProductMemo() {
        return productMemo;
    }

    public void setProductMemo(String productMemo) {
        this.productMemo = productMemo;
    }

    public String getSkuAttr() {
        return skuAttr;
    }

    public void setSkuAttr(String skuAttr) {
        this.skuAttr = skuAttr;
    }

    public String getProductImg() {
        return productImg;
    }

    public void setProductImg(String productImg) {
        this.productImg = productImg;
    }

    public String getCustomType() {
        return customType;
    }

    public void setCustomType(String customType) {
        this.customType = customType;
    }
}
