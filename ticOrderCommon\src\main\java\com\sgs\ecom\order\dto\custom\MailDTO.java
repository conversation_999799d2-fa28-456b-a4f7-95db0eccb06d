package com.sgs.ecom.order.dto.custom;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sgs.ecom.order.util.serializer.TimeStringFormatSerializer;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

public class MailDTO {
    private String userName;
    private Long userId;
    private String userPhone;
    private String userEmail;
    private Integer userSex;
    private String csCode;
    private String csEmail;
    private String csName;
    private String orderNo;
    private String businessLine;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    private String createDate;

    private String operatorCode;
    private String auditCode;
    private BigDecimal realAmount;
    private String companyName;
    private String groupNo;
    private BigDecimal testCycle;
    private Long orderId;
    private String categoryPath;
    private Integer isDelete;
    private Integer isTest;
    private String lineId;
    private String bu;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    private String lastResponseDate;
    private String orderType;
    private String orderAmount;
    private String relateOrderNo;
    @JsonSerialize(using = TimeStringFormatSerializer.class)
    private String confirmOrderDate;
    private String custId;

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getConfirmOrderDate() {
        return confirmOrderDate;
    }

    public void setConfirmOrderDate(String confirmOrderDate) {
        this.confirmOrderDate = confirmOrderDate;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public Integer getUserSex() {
        return userSex;
    }

    public void setUserSex(Integer userSex) {
        this.userSex = userSex;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public String getCsName() {
        return csName;
    }

    public void setCsName(String csName) {
        this.csName = csName;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getCreateDate() {
        if(StringUtils.isNotBlank(createDate) && createDate.contains(".")) {
            createDate = createDate.split("\\.")[0];
        }
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public void setAuditCode(String auditCode) {
        this.auditCode = auditCode;
    }

    public BigDecimal getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(BigDecimal realAmount) {
        this.realAmount = realAmount;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public BigDecimal getTestCycle() {
        return testCycle;
    }

    public void setTestCycle(BigDecimal testCycle) {
        this.testCycle = testCycle;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getCategoryPath() {
        if(categoryPath==null){
            return "";
        }
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsTest() {
        return isTest;
    }

    public void setIsTest(Integer isTest) {
        this.isTest = isTest;
    }

    public String getLineId() {
        return lineId;
    }

    public void setLineId(String lineId) {
        this.lineId = lineId;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getLastResponseDate() {
        if(StringUtils.isNotBlank(lastResponseDate) && lastResponseDate.contains(".")) {
            lastResponseDate = lastResponseDate.split("\\.")[0];
        }
        return lastResponseDate;
    }


    public void setLastResponseDate(String lastResponseDate) {
        this.lastResponseDate = lastResponseDate;
    }

    public void setIsTest(int isTest) {
        this.isTest = isTest;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }
}
