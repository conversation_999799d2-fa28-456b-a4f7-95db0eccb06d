package com.sgs.ecom.order.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;

public class UserInfoOiqDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Long userId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userEmail;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userPhone;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String companyName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String province;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String city;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String town;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String address;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}
