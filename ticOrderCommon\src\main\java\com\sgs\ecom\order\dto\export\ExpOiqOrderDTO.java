package com.sgs.ecom.order.dto.export;


import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.TfsStateEnum;
import com.sgs.ecom.order.enumtool.application.TestCycleEnum;
import com.sgs.ecom.order.enumtool.order.ConfirmSourceEnum;
import com.sgs.ecom.order.enumtool.order.OrderPayStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.util.collection.StrUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

public class ExpOiqOrderDTO {
    //订单编号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderNo;
    //询价单编号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String relateOrderNo;
    //补差价订单号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String subOrderNo;

    //询价单来源
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String orderSource;

    //LEADS_CODE
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String leadsCode;
    //ots单号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String otsOrderNo;
    //操作人
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String operatorCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String businessPersonEmail;
    //报价人
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String csCode;
    //审核人
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String auditCode;
    //订单状态
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String state;
    //支付状态
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payState;
    //订单价格
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String realAmountTotal;
    //测试服务项目
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String itemNameAll;
    //报告编号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportNameAll;//?
    //报告抬头
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String companyNameCn;//?
    //发票抬头
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String invoiceTitle;//?
    //业务线
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String businessLine;
    //实验室
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labName;
    //订单创建时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String createDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payDate;
    //审核通过时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String applySubmitDate;
    //报告上传时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reportTime;
    //报告deadline时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String deadlineTime;
    //订单关闭时间
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String closeTime;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userId;
    //客户姓名
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userName;
    //客户电话
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userPhone;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String userEmail;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String companyName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String reOrder;
    //紧急程度
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String isUrgent;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String isUrgentShow;
    //服务周期天数
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String testCycle;
    //订单关闭理由
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String closeReason;
    //订单关闭备注
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String closeMemo;
    //订单关闭人
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String closePerson;
    //开户银行
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankAddr;
    //开户行号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String bankNumber;
    //是否月结
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String monthPay;
    //支付方式
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String payMethod;
    //商户订单号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String paymentNo;
    //交易流水号
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String transNo;

    private String invoiceShow;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String confirmSource;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String confirmSourceShow;

    private String currency;

    private String bu;

    private Integer stateInt;



    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getRelateOrderNo() {
        return relateOrderNo;
    }

    public void setRelateOrderNo(String relateOrderNo) {
        this.relateOrderNo = relateOrderNo;
    }

    public String getOtsOrderNo() {
        return otsOrderNo;
    }

    public void setOtsOrderNo(String otsOrderNo) {
        this.otsOrderNo = otsOrderNo;
    }

    public String getOperatorCode() {
        return operatorCode;
    }

    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    public String getAuditCode() {
        return auditCode;
    }

    public void setAuditCode(String auditCode) {
        this.auditCode = auditCode;
    }



    public void setState(String state) {
        this.state = state;
    }


    public void setPayState(String payState) {
        this.payState = payState;
    }

    public String getRealAmount() {
        return realAmount;
    }

    public void setRealAmount(String realAmount) {
        this.realAmount = realAmount;
    }

    public String getReportNameAll() {
        return reportNameAll;
    }

    public void setReportNameAll(String reportNameAll) {
        this.reportNameAll = reportNameAll;
    }

    public String getCompanyNameCn() {
        return companyNameCn;
    }

    public void setCompanyNameCn(String companyNameCn) {
        this.companyNameCn = companyNameCn;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getBusinessLine() {
        return businessLine;
    }

    public void setBusinessLine(String businessLine) {
        this.businessLine = businessLine;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public String getItemNameAll() {
        return itemNameAll;
    }

    public void setItemNameAll(String itemNameAll) {
        this.itemNameAll = itemNameAll;
    }



    public void setReportTime(String reportTime) {
        this.reportTime = reportTime;
    }



    public void setCloseTime(String closeTime) {
        this.closeTime = closeTime;
    }

    public void setIsUrgentShow(String isUrgentShow) {
        this.isUrgentShow = isUrgentShow;
    }

    public String getCloseReason() {
        return closeReason;
    }

    public void setCloseReason(String closeReason) {
        this.closeReason = closeReason;
    }

    public String getCloseMemo() {
        return closeMemo;
    }

    public void setCloseMemo(String closeMemo) {
        this.closeMemo = closeMemo;
    }

    public String getClosePerson() {
        return closePerson;
    }

    public void setClosePerson(String closePerson) {
        this.closePerson = closePerson;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }



    public void setApplySubmitDate(String applySubmitDate) {
        this.applySubmitDate = applySubmitDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(String isUrgent) {
        this.isUrgent = isUrgent;
    }

    public String getBankAddr() {
        return bankAddr;
    }

    public void setBankAddr(String bankAddr) {
        this.bankAddr = bankAddr;
    }

    public String getBankNumber() {
        return bankNumber;
    }

    public void setBankNumber(String bankNumber) {
        this.bankNumber = bankNumber;
    }

    public String getState() {
        if(stateInt==null){
            return "";
        }
        if("2700".equals(bu)){
            return TfsStateEnum.getNameCh(stateInt);
        }
        return OrderStateEnum.getNameCh(String.valueOf(stateInt));
    }

    public String getDeadlineTime() {
        return StrUtil.isTime(deadlineTime);
    }

    public void setDeadlineTime(String deadlineTime) {
        this.deadlineTime = deadlineTime;
    }

    public String getCreateDate() {
        return StrUtil.isTime(createDate);
    }

    public String getApplySubmitDate() {
        return StrUtil.isTime(applySubmitDate);
    }

    public String getReportTime() {
        return StrUtil.isTime(reportTime);
    }

    public String getCloseTime() {
        return StrUtil.isTime(closeTime);
    }

    public void setMonthPay(String monthPay) {
        this.monthPay = monthPay;
    }

    public void setTestCycle(String testCycle) {
        this.testCycle = testCycle;
    }

    public String getPayMethod() {
        return PayMethodEnum.getNameCh(payMethod);
    }

    public void setPayMethod(String payMethod) {
        this.payMethod = payMethod;
    }

    public String getPaymentNo() {
        return paymentNo;
    }

    public void setPaymentNo(String paymentNo) {
        this.paymentNo = paymentNo;
    }

    public String getTransNo() {
        return transNo;
    }

    public void setTransNo(String transNo) {
        this.transNo = transNo;
    }

    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getPayState() {
        if(StringUtils.isNotBlank(payState)){
            return OrderPayStateEnum.getNameCh(Integer.parseInt(payState));
        }
        return "";
    }
    public String getIsUrgentShow() {
        if(StringUtils.isBlank(isUrgent)){
            return "";
        }
        return TestCycleEnum.getNameCh(isUrgent);
    }








    public String getTestCycle() {
        if(StringUtils.isNotBlank(testCycle)){
            BigDecimal b=new BigDecimal(testCycle);
            return String.valueOf(b.intValue());

        }
        return testCycle;
    }

    public String getMonthPay() {
        if(StringUtils.isNotBlank(monthPay)){
           return monthPay.equals("2")?"是":"否";
        }
        return "否";
    }

    public String getSubOrderNo() {
        return subOrderNo;
    }

    public void setSubOrderNo(String subOrderNo) {
        this.subOrderNo = subOrderNo;
    }

    public String getLeadsCode() {
        return leadsCode;
    }

    public void setLeadsCode(String leadsCode) {
        this.leadsCode = leadsCode;
    }

    public String getPayDate() {
        return StrUtil.isTime(payDate);
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public String getReOrder() {
        return reOrder;
    }

    public void setReOrder(String reOrder) {
        this.reOrder = reOrder;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRealAmountTotal() {
        return realAmountTotal;
    }

    public void setRealAmountTotal(String realAmountTotal) {
        this.realAmountTotal = realAmountTotal;
    }

    public String getInvoiceShow() {
        return invoiceShow;
    }

    public void setInvoiceShow(String invoiceShow) {
        this.invoiceShow = invoiceShow;
    }

    public String getOrderSource() {
        if(StringUtils.isNotBlank(orderSource)){
            return OrderSourceEnum.getNameCh(orderSource);
        }
        return "";
    }


    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getConfirmSource() {
        return confirmSource;
    }

    public void setConfirmSource(String confirmSource) {
        this.confirmSource = confirmSource;
    }

    public String getConfirmSourceShow() {
        return ConfirmSourceEnum.getNameCh(confirmSource);
    }

    public void setConfirmSourceShow(String confirmSourceShow) {
        this.confirmSourceShow = confirmSourceShow;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getBusinessPersonEmail() {
        return businessPersonEmail;
    }

    public void setBusinessPersonEmail(String businessPersonEmail) {
        this.businessPersonEmail = businessPersonEmail;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public Integer getStateInt() {
        return stateInt;
    }

    public void setStateInt(Integer stateInt) {
        this.stateInt = stateInt;
    }
}

