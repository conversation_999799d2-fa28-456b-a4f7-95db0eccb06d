package com.sgs.ecom.order.service.util.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.platform.bo.BOSysPerson;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.domain.order.OrderExpressDO;
import com.sgs.ecom.order.domain.order.OrderSampleFromDO;
import com.sgs.ecom.order.domain.order.service.interfaces.IOrderRelateExternalDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.*;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.*;
import com.sgs.ecom.order.dto.dml.*;
import com.sgs.ecom.order.dto.oiq.OiqOrderDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqItemDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.order.dto.order.*;
import com.sgs.ecom.order.dto.send.mail.OiqOtherDTO;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.enumtool.application.DeliverTypeEnum;
import com.sgs.ecom.order.enumtool.application.OiqFormEnum;
import com.sgs.ecom.order.enumtool.dml.ReportFormCodeEnum;
import com.sgs.ecom.order.enumtool.dml.ReportLuaCodeEnum;
import com.sgs.ecom.order.request.FileReq;
import com.sgs.ecom.order.request.base.BaseFileReq;
import com.sgs.ecom.order.request.dml.DmlReportListReq;
import com.sgs.ecom.order.request.dml.DmlReportReq;
import com.sgs.ecom.order.request.oiq.OiqApplicationAttrReq;
import com.sgs.ecom.order.request.oiq.OiqOrderLinkAttrReq;
import com.sgs.ecom.order.request.operator.OrderReportReq;
import com.sgs.ecom.order.request.operator.UpdateReportReq;
import com.sgs.ecom.order.service.order.interfaces.*;
import com.sgs.ecom.order.service.util.interfaces.IDmlSV;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.collection.StrUtil;
import com.sgs.ecom.order.util.select.AttributeUtil;
import com.sgs.ecom.order.util.select.SelectListUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import com.sgs.ecom.wechat.util.ValidationUtil;
import com.sgs.util.calendar.LunarWorkday;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DmlSVImpl extends BaseService implements IDmlSV {



	@Autowired
	private IOrderBaseInfoCustomService orderBaseInfoCustomService;
	@Autowired
	private IOrderBaseInfoService orderBaseInfoService;
	@Autowired
	private IOrderSampleService orderSampleService;
	@Autowired
	private IOrderAttributeService orderAttributeService;
	@Autowired
	private IOrderReportSV orderReportSV;
	@Autowired
	private IOrderApplicationFormService orderApplicationFormService;
	@Autowired
	private IOrderExpressService orderExpressService;
	@Autowired
	private IOrderAttachmentService orderAttachmentService;
	@Autowired
	private IOrderFileService orderFileService;
	@Autowired
	private LunarWorkday lunarWorkday;
	@Autowired
	private IOrderBaseInfoDomainService orderBaseInfoDomainService;
	@Autowired
	private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;
	@Autowired
	private IOrderInvoiceDomainService orderInvoiceDomainService;
	@Autowired
	private IOrderLinkDomainService orderLinkDomainService;
	@Autowired
	private IOrderDetailDomainService orderDetailDomainService;
	@Autowired
	private IOrderSampleDomainService orderSampleDomainService;
	@Autowired
	private IOrderRelateExternalDomainService orderRelateExternalDomainService;
	@Autowired
	private IOrderSampleFromDomainService orderSampleFromDomainService;



	@Override
	public DmlMainDTO getDmlByOrderNo(String orderNo, BOSysPerson boSysPerson,String line)throws Exception {
		OrderApplicationFormDTO formDTO = orderApplicationFormService.selectFormDTOByOrderNo(orderNo);
		OrderInvoiceDTO orderInvoiceDTO =orderInvoiceDomainService.qryOneOrderInvoiceByOrder(orderNo);
		OrderReportDTO orderReportDTO = orderReportSV.selectByOrderNo(orderNo);

		BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseOrder(orderNo);
		List<OrderSampleMoreDTO> orderSampleDTOList = orderSampleService.orderSampleList(orderNo, baseOrderDTO.getGroupNo());

		OrderAttributeDTO imgAttr = orderAttributeService.selectCustom(orderNo,  baseOrderDTO.getGroupNo(), AttributeUtil.RECOMMEND_REASON_IMAGE);


		//List<OrderDetailDTO> orderDetailDTOList = orderDetailService.selectOrderDetailListByGroup(orderNo, baseOrderDTO.getGroupNo());

		//先样品在项目 样品的值有用
		OiqOrderDTO oiqOrderDTO=new OiqOrderDTO();
		oiqOrderDTO.setCurrentOrder(baseOrderDTO);
		List<OiqSampleDTO> sampleDTOList=orderSampleDomainService.getOiqOrderSample(baseOrderDTO.getOrderNo(),baseOrderDTO.getGroupNo(),oiqOrderDTO.getCurrentOrder().getApplicationLineId(),true);
		oiqOrderDTO.setSampleList(sampleDTOList);
		//增加项目
		orderDetailDomainService.getItemByOrderNo(orderNo,baseOrderDTO.getGroupNo(),oiqOrderDTO);



		Map<String, OrderApplicationAttrDTO> attrDTOMap=orderApplicationAttrDomainService.getAttrDTOMap(orderNo);
		OiqApplicationAttrReq oiqApplicationAttrReq=new OiqApplicationAttrReq(attrDTOMap);





		Map<String, List<OrderExpressDTO>> orderExpressMap = orderExpressService.selectExpressMapByOrderNo(orderNo);
		OrderExpressDTO reportDTO = orderExpressService.getOrderExpressDTOByDeliverType(orderExpressMap, DeliverTypeEnum.REPORT);
		OrderExpressDTO back = orderExpressService.getOrderExpressDTOByDeliverType(orderExpressMap, DeliverTypeEnum.BACK);


		DmlMainReqDTO dmlMainReqDTO=new DmlMainReqDTO();
		dmlMainReqDTO.setOrderNo(orderNo);
		dmlMainReqDTO.setLine(line);
		dmlMainReqDTO.setOwnerEmail(boSysPerson.getPersonMail());
		dmlMainReqDTO.setFormDTO(formDTO);
		dmlMainReqDTO.setOrderInvoiceDTO(orderInvoiceDTO);
		dmlMainReqDTO.setOrderReportDTO(orderReportDTO);
		dmlMainReqDTO.setOrderSampleDTOList(orderSampleDTOList);
		dmlMainReqDTO.setItemDTOList(oiqOrderDTO.getItemList());
		dmlMainReqDTO.setImgAttr(imgAttr);
		dmlMainReqDTO.setReport(reportDTO);
		dmlMainReqDTO.setBack(back);
		dmlMainReqDTO.setBaseOrderDTO(baseOrderDTO);
		DmlOtherDTO dmlOtherDTO=new DmlOtherDTO();
		OiqOrderLinkAttrReq oiqOrderLinkAttrReq=new OiqOrderLinkAttrReq();
		oiqOrderLinkAttrReq.setLinkEmail(formDTO.getLinkEmail());
		oiqOrderLinkAttrReq.setLinkMap(orderLinkDomainService.qryOiqOrderLinkMap(orderNo));
		dmlOtherDTO.setOiqOrderLinkAttrReq(oiqOrderLinkAttrReq);
		dmlOtherDTO.setAcceptSubcon(formDTO.getIsTransfer());


		dmlOtherDTO.setOiqApplicationAttrReq(oiqApplicationAttrReq);
		if(attrDTOMap.containsKey(OiqFormEnum.TEST_MEMO_IMG.getName())){
			dmlOtherDTO.setTestMemoImg(JSON.parseArray(attrDTOMap.get(OiqFormEnum.TEST_MEMO_IMG.getName()).getAttrValue(), JSONObject.class));;
		}
		dmlOtherDTO.setLabCode(attrDTOMap.getOrDefault(OiqFormEnum.LAB_CODE.getName(),new OrderApplicationAttrDTO()).getAttrValue());
		dmlMainReqDTO.setDmlOther(dmlOtherDTO);
		return getBaseDmlMainDTO(dmlMainReqDTO);
	}



	@Override
	public DmlMainDTO getBaseDmlMainDTO(DmlMainReqDTO dmlMainReqDTO)throws Exception {

		System.out.println(dmlMainReqDTO.getItemDTOList().size());

		OrderApplicationFormDTO formDTO = dmlMainReqDTO.getFormDTO();
		OrderInvoiceDTO orderInvoiceDTO = dmlMainReqDTO.getOrderInvoiceDTO()==null?new OrderInvoiceDTO():dmlMainReqDTO.getOrderInvoiceDTO();
		OrderReportDTO orderReportDTO =dmlMainReqDTO.getOrderReportDTO();
		BaseOrderDTO baseOrderDTO=dmlMainReqDTO.getBaseOrderDTO();
		List<OrderSampleMoreDTO> orderSampleDTOList = dmlMainReqDTO.getOrderSampleDTOList();


		OrderAttributeDTO imgAttr = dmlMainReqDTO.getImgAttr();

		DmlOtherDTO dmlOtherDTO=dmlMainReqDTO.getDmlOther();



		List<OiqItemDTO> orderDetailDTOList =dmlMainReqDTO.getItemDTOList();

		String ownerEmail=dmlMainReqDTO.getOwnerEmail();

		OrderExpressDTO report=dmlMainReqDTO.getReport();
		OrderExpressDTO back=dmlMainReqDTO.getBack();

		DmlMainDTO mainDTO = new DmlMainDTO();
		mainDTO.setOIQNo(dmlMainReqDTO.getOrderNo());
		mainDTO.setLineCode(dmlMainReqDTO.getLine());

		if(StringUtils.isBlank(dmlOtherDTO.getLabCode())){
			throw new BusinessException(ResultEnumCode.DML_LAB_CODE);
		}
		mainDTO.setLabCode(dmlOtherDTO.getLabCode());
		mainDTO.setDispute(dmlOtherDTO.getOiqApplicationAttrReq().getIsDispute());
		mainDTO.setDisputeRemark(dmlOtherDTO.getOiqApplicationAttrReq().getIsDisputeMemo());
		mainDTO.setAcceptSubcon(dmlMainReqDTO.getDmlOther().getAcceptSubcon());//


		mainDTO.setApplicant(formDTO.getCompanyNameCn());
		mainDTO.setApplicantEn(formDTO.getCompanyNameEn());
		mainDTO.setApplicantAddress(formDTO.getCompanyAddressCn());
		mainDTO.setApplicantAddressEn(formDTO.getCompanyAddressEn());

		mainDTO.setApplicantContact(formDTO.getLinkPerson());
		mainDTO.setApplicantContactEmail(formDTO.getLinkEmail());


		//申请表订单会添加订单抄送人数据

		mainDTO.setApplicantContactMobile(formDTO.getLinkPhone());


		mainDTO.setApplicantTaxRegistrationNO(orderInvoiceDTO.getTaxNo());
		mainDTO.setSameAsApplicant(0);

		mainDTO.setPayer(orderInvoiceDTO.getInvoiceTitle());
		mainDTO.setPayerBankName(orderInvoiceDTO.getBankAddr());
		mainDTO.setPayerBankAccount(orderInvoiceDTO.getBankNumber());
		mainDTO.setPayerTaxRegistrationNO(orderInvoiceDTO.getTaxNo());

		//地址逻辑不变
		mainDTO.setPayerAddress(orderInvoiceDTO.getRegisterAddr());
		if(StringUtils.isBlank(mainDTO.getPayerAddress())){
			mainDTO.setPayerAddress(orderInvoiceDTO.getRegAddress());
		}
		mainDTO.setPayerTel(orderInvoiceDTO.getRegisterPhone());
		if(StringUtils.isBlank(mainDTO.getPayerAddress())){
			mainDTO.setPayerTel(orderInvoiceDTO.getRegPhone());
		}

		mainDTO.setPayerContact(orderInvoiceDTO.getPayerName());
		mainDTO.setPayerContactEmail(orderInvoiceDTO.getPayerEmail());
		mainDTO.setPayerContactMobile(orderInvoiceDTO.getPayerPhone());
		mainDTO.setPayerTaxRegistrationNO(orderInvoiceDTO.getTaxNo());
		mainDTO.setIsUrgent(baseOrderDTO.getIsUrgent());

		mainDTO.setInvoiceRecipientEmail(dmlOtherDTO.getOiqOrderLinkAttrReq().getInvoiceEmail());
		//报告抄送有些
		mainDTO.setReportRecipientEmail(dmlOtherDTO.getOiqOrderLinkAttrReq().getReportEmail());
		mainDTO.setInvoiceType(orderInvoiceDTO.getInvoiceType() == 3 ? 0 : 1);
		mainDTO.setInvoiceExpress(2);
		/*if (mainDTO.getInvoiceType() == 0) {//专票有地址
			mainDTO.setInvoiceExpress(2);
			mainDTO.setInvoiceRecipient(orderInvoiceDTO.getDeliveryName());
			mainDTO.setInvoiceRecipientTel(orderInvoiceDTO.getDeliveryPhone());
			StringJoiner stringJoiner=new StringJoiner("");
			stringJoiner.add(StrUtil.toStr(orderInvoiceDTO.getDeliveryProvince()));
			stringJoiner.add(StrUtil.toStr(orderInvoiceDTO.getDeliveryCity()));
			stringJoiner.add(StrUtil.toStr(orderInvoiceDTO.getDeliveryTown()));
			stringJoiner.add(StrUtil.toStr(orderInvoiceDTO.getDeliveryAddr()));
			mainDTO.setInvoiceRecipientAddress(stringJoiner.toString());
		}*/



		mainDTO.setReportLanguage(ReportLuaCodeEnum.getDmlName(orderReportDTO.getReportLuaCode()));
		mainDTO.setReportType(ReportFormCodeEnum.getDmlName(orderReportDTO.getReportFormCode()));



		int n = "0".equals(mainDTO.getReportType())?0:1;//单电子报告数量为0
		int sampleNum = orderSampleDTOList.size();
		mainDTO.setReportProvide(dmlMainReqDTO.getOrderReportDTO().getReportMethod() - 1);
		mainDTO.setReportProvideRemark(dmlMainReqDTO.getOrderReportDTO().getReportMethodMemo());
		if (mainDTO.getReportProvide() == 0) {//相当于只要一份报告
			sampleNum = 1;
		}





		mainDTO.setReportQty(n * sampleNum);
		String reportTitleType="2";
		if(orderReportDTO.getReportTitleType()!=null && orderReportDTO.getReportTitleType()==0){
			reportTitleType="0";
		}

		mainDTO.setReportTitleType(reportTitleType);
		//当报告抬头不为0的时候传递
		if (!"".equals(mainDTO.getReportTitleType())) {
			mainDTO.setReportTitle(orderReportDTO.getReportCompanyNameCn());
			mainDTO.setReportTitleEn(orderReportDTO.getReportCompanyNameEn());
			mainDTO.setReportAddress(orderReportDTO.getReportAddressCn());
			mainDTO.setReportAddressEn(orderReportDTO.getReportAddressEn());
		}

		mainDTO.setReportDeliver("2");
		if (!ValidationUtil.isEmpty(report)) {
			mainDTO.setReportRecipient(report.getReceiptPerson());
			mainDTO.setReportRecipientTel(report.getReceiptPhone());
			mainDTO.setReportRecipientAddress(OrderExpressDO.getAddressByExpressDTO(report));
		}
		mainDTO.setSampleReturn(formDTO.getIsRefundSample() == 0 ? 0 : 4);
		if (mainDTO.getSampleReturn() == 4) {
			mainDTO.setSampleRecipient(back.getReceiptPerson());
			mainDTO.setSampleRecipientTel(back.getReceiptPhone());
			mainDTO.setSampleRecipientAddress(OrderExpressDO.getAddressByExpressDTO(back));
		}
		//(OIQ 订单总金额+测试周期
		String testStr=baseOrderDTO.getTestCycle()==null?"":"测试周期:" + baseOrderDTO.getTestCycle().intValue();
		if(baseOrderDTO.getRealAmount()!=null){
			mainDTO.setOtherRequest("OIQ 订单总金额:" + baseOrderDTO.getRealAmount() +"("+baseOrderDTO.getCurrency()+")" + testStr);
		}
		mainDTO.setOwnerEmail(ownerEmail);
		//申请表订单使用
		mainDTO.setTestRequest(formDTO.getTestMemo());
		mainAddDetailAndSample(mainDTO,orderDetailDTOList, orderSampleDTOList);
		mainAddFile(mainDTO,imgAttr,dmlOtherDTO.getTestMemoImg());
		return mainDTO;
	}




	public void mainAddDetailAndSample(DmlMainDTO mainDTO,List<OiqItemDTO> orderDetailDTOList, List<OrderSampleMoreDTO> orderSampleDTOList) {
		//获取判断的数据 是否判定：是，备注：

		mainDTO.setTestItems(getDmlItemList(orderDetailDTOList));
		//0 中文 1英文
		Boolean znFlg="1".equals(mainDTO.getReportLanguage())?false:true;
		Boolean enFlg="0".equals(mainDTO.getReportLanguage())?false:true;
		Map<String,String> chemistryMap=new HashMap<>();
		List<DmlSampleDTO> list=getDmlSampleList(orderSampleDTOList,znFlg,enFlg,chemistryMap);
	/*	if(StringUtils.isNotBlank(chemistryMap.getOrDefault(OrderSampleFromDO.OIQ_USING_CHEMISTRY,""))){
			mainDTO.setOtherRequest(StrUtil.toStr(mainDTO.getOtherRequest())+chemistryMap.getOrDefault(OrderSampleFromDO.OIQ_USING_CHEMISTRY,""));
		}*/

		mainDTO.setSamples(list);
/*		if("1".equals(mainDTO.getReportLanguage())){
			mainDTO.setApplicant(mainDTO.getApplicantEn());
			mainDTO.setApplicantAddress(mainDTO.getApplicantAddressEn());
		}*/
	}

	public List<DmlSampleDTO> getDmlSampleList(List<OrderSampleMoreDTO> orderSampleDTOList,Boolean znFlg,Boolean enFlg,Map<String,String> chemistryMap) {
		if (ValidationUtil.isEmpty(orderSampleDTOList)) {
			return new ArrayList<>();
		}
		List<DmlSampleDTO> list = new ArrayList<>();
		//tringJoiner chemistryStr=new StringJoiner(";");
		for (int n = 0; n < orderSampleDTOList.size(); n++) {
			OrderSampleMoreDTO orderSampleDTO = orderSampleDTOList.get(n);
			List<OrderSampleFromDTO> fromDTOList=orderSampleDTO.getSampleFromDTOList();
			//chemistryStr.add(orderSampleFromDomainService.getChemistryValueStr(fromDTOList,n));
			Map<String,String> keyMap=fromDTOList.stream().collect(Collectors.toMap(OrderSampleFromDTO::getSampleKey,OrderSampleFromDTO::getSampleValue, (key1, key2) -> key2));;

			DmlSampleDTO dmlSampleDTO = new DmlSampleDTO();
			dmlSampleDTO.setSampleNo(orderSampleDTO.getRow()+"#");
			dmlSampleDTO.setRemark(orderSampleFromDomainService.getChemistryValueStr(fromDTOList,n));
			if(znFlg){
				dmlSampleDTO.setName(orderSampleDTO.getSampleNameCn());
				//材质牌号/Material and Mark
				dmlSampleDTO.setMaterial(keyMap.getOrDefault(OrderSampleFromDO.OIQ_MATERIAL_GRADE,""));
				// 炉号/heatNo
				dmlSampleDTO.setHeatNo(keyMap.getOrDefault(OrderSampleFromDO.OIQ_HEAT_NO,""));
				//产品规格/Product Specification
				dmlSampleDTO.setSpecification(keyMap.getOrDefault(OrderSampleFromDO.OIQ_PRODUCT_INFO,""));
				//货号或批号/Product or Lot No.   PRODUCT_BATCH
				dmlSampleDTO.setLotNo(keyMap.getOrDefault(OrderSampleFromDO.OIQ_PRODUCT_BATCH,""));
				//购买商/品牌/Buyer/Brand
				dmlSampleDTO.setBuyer(keyMap.getOrDefault(OrderSampleFromDO.OIQ_BUYERS_NAME,""));
				//生产商/Manufacturer
				dmlSampleDTO.setManufacturer(keyMap.getOrDefault(OrderSampleFromDO.OIQ_PRODUCTER_NAME,""));
				//其他备注/Other  REMARK
				dmlSampleDTO.setOthers(keyMap.getOrDefault(OrderSampleFromDO.OIQ_REMARK,""));
			}
			if(enFlg){
				dmlSampleDTO.setNameEn(orderSampleDTO.getSampleNameEn());
				dmlSampleDTO.setMaterialEn(keyMap.getOrDefault(OrderSampleFromDO.OIQ_MATERIAL_GRADE_EN,""));
				dmlSampleDTO.setHeatNoEn(keyMap.getOrDefault(OrderSampleFromDO.OIQ_HEAT_NO_EN,""));
				dmlSampleDTO.setSpecificationEn(keyMap.getOrDefault(OrderSampleFromDO.OIQ_PRODUCT_INFO_EN,""));
				dmlSampleDTO.setLotNoEn(keyMap.getOrDefault(OrderSampleFromDO.OIQ_PRODUCT_BATCH_EN,""));
				dmlSampleDTO.setBuyerEn(keyMap.getOrDefault(OrderSampleFromDO.OIQ_BUYERS_NAME_EN,""));
				dmlSampleDTO.setManufacturerEn(keyMap.getOrDefault(OrderSampleFromDO.OIQ_PRODUCTER_NAME_EN,""));
				dmlSampleDTO.setOthersEn(keyMap.getOrDefault(OrderSampleFromDO.OIQ_REMARK_EN,""));
			}

			list.add(dmlSampleDTO);
		}
		//chemistryMap.put(OrderSampleFromDO.OIQ_USING_CHEMISTRY,chemistryStr.toString());
		return list;
	}

	public List<DmlItemDTO> getDmlItemList(List<OiqItemDTO> orderDetailDTOList) {
		if (ValidationUtil.isEmpty(orderDetailDTOList)) {
			return new ArrayList<>();
		}
		List<DmlItemDTO> list = new ArrayList<>();
		for (int n = 0; n < orderDetailDTOList.size(); n++) {
			OiqItemDTO orderDetailDTO = orderDetailDTOList.get(n);
			DmlItemDTO dmlItemDTO = new DmlItemDTO();
			dmlItemDTO.setName(orderDetailDTO.getItemName());
			if (!ValidationUtil.isEmpty(orderDetailDTO.getSampleList())) {
				StringBuilder str = new StringBuilder();
				for (int t = 0; t < orderDetailDTO.getSampleList().size(); t++) {
					OiqSampleDTO sampleDTO = orderDetailDTO.getSampleList().get(t);
					str.append(sampleDTO.getRow()+"#");
					str.append(",");
				}
				dmlItemDTO.setSampleNo(StrUtil.subLastChar(str));
			}

			dmlItemDTO.setStandard(orderDetailDTO.getStandardCode());
			dmlItemDTO.setCondition(orderDetailDTO.getTestMemo());
			dmlItemDTO.setQty(orderDetailDTO.getBuyNums()==null?1:orderDetailDTO.getBuyNums());
			dmlItemDTO.setEvaluation(orderDetailDTO.getIsDetermine()==null?0:orderDetailDTO.getIsDetermine());
			//dmlItemDTO.setEvaluationStandard(orderAttributeDTO.getAttrName());
			list.add(dmlItemDTO);
		}
		return list;

	}




	public void mainAddFile(DmlMainDTO mainDTO,OrderAttributeDTO imgAttr,List<JSONObject> applicationAttrImgList) {
		List<Map> dmlMapList = new ArrayList<>();
		if (!ValidationUtil.isEmpty(imgAttr)) {
			String json = imgAttr.getAttrName();
			List<Map> mapList = JSON.parseArray(json, Map.class);
			if (!ValidationUtil.isEmpty(mapList)) {
				for (Map map : mapList) {
					if (map.containsKey("name") && map.containsKey("url")) {
						Map dmlMap = new HashMap();
						String name = map.get("name").toString();
						dmlMap.put("attachmentName", name);
						name = name.substring(name.lastIndexOf(".") + 1);
						dmlMap.put("attachmentType", name);
						dmlMap.put("cloudId", map.get("url").toString());
						dmlMapList.add(dmlMap);
					}
				}
			}
		}


		if (!ValidationUtil.isEmpty(applicationAttrImgList)) {
				for (JSONObject map : applicationAttrImgList) {
					if(map.containsKey("name")&& map.containsKey("uploadType") && map.get("uploadType").toString().equals("2")){
						Map dmlMap = new HashMap();
						String name = map.get("name").toString();
						dmlMap.put("attachmentName", name);
						name = name.substring(name.lastIndexOf(".") + 1);
						dmlMap.put("attachmentType", name);
						dmlMap.put("cloudId", map.get("cloudId").toString());
						dmlMapList.add(dmlMap);
						continue;
					}
					if (map.containsKey("name") && map.containsKey("src")) {
						Map dmlMap = new HashMap();
						String name = map.get("name").toString();
						dmlMap.put("attachmentName", name);
						name = name.substring(name.lastIndexOf(".") + 1);
						dmlMap.put("attachmentType", name);
						dmlMap.put("cloudId", map.get("src").toString());
						dmlMapList.add(dmlMap);
					}
				}
		}

		mainDTO.setAttachments(dmlMapList);

	}

	@Override
	public void saveReport(DmlReportReq dmlReportReq) throws Exception{

		String dateStr= UseDateUtil.getDateString(new Date());
		BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(dmlReportReq.getOrderNo());
		dmlReportReq.setBaseOrderDTO(baseOrderDTO);
		OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.qryBase(baseOrderDTO.getOrderId());
		BOSysPerson boSysPerson=new BOSysPerson();
		boSysPerson.setPersonCode(dmlReportReq.getPlatform());

		//更新主表数据
		VOOrderBaseInfo update=new VOOrderBaseInfo();
		update.setOrderId(baseOrderDTO.getOrderId());
		update.setStateDate(dateStr);
		update.setPlatform(dmlReportReq.getPlatform());
		update.setPlatformOrder(dmlReportReq.getPlatformOrder());
		List<DmlReportListReq> list=dmlReportReq.getReports();
		if(ValidationUtil.isEmpty(list)){
			throw new BusinessException(ResultEnumCode.FILE_NULL);
		}

		orderRelateExternalDomainService.saveExternalByPlatform(dmlReportReq.getOrderNo(),dmlReportReq.getPlatform(),dmlReportReq.getPlatformOrder());

		//1 add 2mod 3del
		Map<Integer,List<DmlReportListReq>> reportMap=list.stream().collect(Collectors.groupingBy(E->E.getStatus()));
		//校验更新报告是否符合要求
		List<OrderAttachmentDTO> updateList=new ArrayList<>();
		if(reportMap.containsKey(2)){
			List<DmlReportListReq> reportList=reportMap.get(2);
			List<String> oldReport=reportList.stream().map(DmlReportListReq::getOldNo).collect(Collectors.toList());
			updateList=getListByReport(oldReport,baseOrderDTO.getOrderNo());
			Map<String,List<OrderAttachmentDTO>> updateMap=updateList.stream().collect(Collectors.groupingBy(E ->E.getReportNo()));
			for(String str:oldReport){
				if(!updateMap.containsKey(str)){
					throw new BusinessException(ResultEnumCode.DML_REPORT_UPDATE_ERROR.getIndex(),"报告编号"+str+"不存在，无法替换，更新失败。");
				}
			}
		}




		if(reportMap.containsKey(1)){
			List<DmlReportListReq> reportList=reportMap.get(1);
			List<FileReq> fileReqList=new ArrayList<>();
			for(DmlReportListReq dmlReportListReq:reportList){
				fileReqList.add(new FileReq(dmlReportListReq.getFileId(),dmlReportListReq.getFileName(),dmlReportListReq.getCloudId(),dmlReportListReq.getReportNo()));
			}
			OrderReportReq reportReq=new OrderReportReq();
			reportReq.setFileReqList(fileReqList);
			reportReq.setAttType(OrderAttachmentTypeEnum.OIQ_REPORT.getIndex());
			orderFileService.saveFile(orderBaseInfoCheckDTO,boSysPerson,reportReq);
		}

		if(reportMap.containsKey(2)){
			List<DmlReportListReq> reportList=reportMap.get(2);
			Map<String,OrderAttachmentDTO> map=updateList.stream().collect(Collectors.toMap(OrderAttachmentDTO::getReportNo, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
			dmlReportReq.setEventFlg(1);
			dmlReportReq.setEventOrderNo(baseOrderDTO.getOrderNo());
			dmlReportReq.setOrderType(baseOrderDTO.getOrderType());
			List<BaseFileReq> fileReqList=new ArrayList<>();
			StringJoiner stringJoiner=new StringJoiner(" ");
			for(DmlReportListReq dmlReportListReq:reportList){
				if(map.containsKey(dmlReportListReq.getOldNo())){
					OrderAttachmentDTO orderAttachmentDTO=map.get(dmlReportListReq.getOldNo());
					UpdateReportReq updateReportReq=new UpdateReportReq(dmlReportListReq.getFileId(),
							dmlReportListReq.getFileName(),dmlReportListReq.getCloudId(),String.valueOf(orderBaseInfoCheckDTO.getOrderId()),dmlReportListReq.getReportNo(),orderAttachmentDTO.getAttachmentId());
					orderFileService.updateFile(orderBaseInfoCheckDTO,boSysPerson,updateReportReq);
					if(updateReportReq.getEventFlg()==1 && !ValidationUtil.isEmpty(updateReportReq.getOiqOtherDTO())){
						fileReqList.addAll(updateReportReq.getOiqOtherDTO().getFileReqList());
						stringJoiner.add(updateReportReq.getReportNo());
					}
				}
			}
			OiqOtherDTO oiqOtherDTO=new OiqOtherDTO();
			oiqOtherDTO.setFileReqList(fileReqList);
			oiqOtherDTO.setOrderId(baseOrderDTO.getOrderId());
			oiqOtherDTO.setReportNoStr(stringJoiner.toString());
			dmlReportReq.setOiqOtherDTO(oiqOtherDTO);
		}
		if(reportMap.containsKey(3)){
			List<DmlReportListReq> reportList=reportMap.get(3);
			List<String> reportNo=reportList.stream().map(DmlReportListReq::getReportNo).collect(Collectors.toList());
			List<OrderAttachmentDTO> delList=getListByReport(reportNo,baseOrderDTO.getOrderNo());
			for(OrderAttachmentDTO orderAttachmentDTO:delList){
				orderFileService.delFile(orderBaseInfoCheckDTO,boSysPerson,new UpdateReportReq(orderAttachmentDTO.getAttachmentId()));
			}
		}
	}

	@Override
	public String getDeadlineTimeByDate(Date date,int testCycle) throws Exception{

		int addDate = testCycle + 1;
		Calendar now =Calendar.getInstance();
		now.setTime(date);
		int hour=now.get(Calendar.HOUR_OF_DAY);
		if(hour<12){
			addDate=addDate-1;
		}
		if(addDate==0){
			addDate=1;
		}

		Date deadlineTime = lunarWorkday.getWordday(date, addDate);
		System.out.println("deadlineTime="+deadlineTime);
		return UseDateUtil.getDateString(deadlineTime);
	}


	private  List<OrderAttachmentDTO>  getListByReport(List<String> reportList,String orderNo){
		Map<String,Object> map=new HashMap<>();
		map.put(SelectMapUtil.ORDER_NO,orderNo);
		map.put(SelectListUtil.REPORT_NO_LIST,reportList);
		return orderAttachmentService.selectListByMap(map);
	}
}
