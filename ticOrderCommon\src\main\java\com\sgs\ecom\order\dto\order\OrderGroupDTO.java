package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;

public class OrderGroupDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String groupNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private Timestamp createDate;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currency;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String currencyMark;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String realAmount;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String sampleName;


    @JsonSerialize(using = TimeFormatSerializer.class)
    public Timestamp getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Timestamp createDate) {
        this.createDate = createDate;
    }

    public String getGroupNo() {
        return groupNo;
    }

    public void setGroupNo(String groupNo) {
        this.groupNo = groupNo;
    }

    public String getRealAmount() {
        if(StringUtils.isNotBlank(realAmount)){
            BigDecimal bigDecimal=new BigDecimal(realAmount);
            DecimalFormat decimalFormat = new DecimalFormat("0.00");
            return decimalFormat.format(bigDecimal);
        }
        return realAmount;
    }

    public void setRealAmount(String realAmount) {
        this.realAmount = realAmount;
    }

    public String getSampleName() {
        return sampleName;
    }

    public void setSampleName(String sampleName) {
        this.sampleName = sampleName;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyMark() {
        return currencyMark;
    }

    public void setCurrencyMark(String currencyMark) {
        this.currencyMark = currencyMark;
    }
}
