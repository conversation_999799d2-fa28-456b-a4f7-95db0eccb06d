package com.sgs.ecom.order.controller.rsts;

import com.alibaba.fastjson.JSONObject;
import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.service.rsts.interfaces.IRSTSPayService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.rsts/pay")
public class RSTSPayController extends ControllerUtil {

    @Resource
    private IRSTSPayService rstsPayService;



    /**
    * @params [orderPayTransReq, token]
    * @return com.sgs.ecom.member.util.ResultBody
    * @description
    * <AUTHOR> || created at 2023/9/12 15:50
    */
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryList", method = { RequestMethod.POST })
    public ResultBody qryList(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderIdReq orderIdReq) throws Exception {
        return ResultBody.newInstance(rstsPayService.qryList(orderIdReq));
    }

}
