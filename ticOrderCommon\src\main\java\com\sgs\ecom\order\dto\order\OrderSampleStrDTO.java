package com.sgs.ecom.order.dto.order;

import com.sgs.ecom.order.util.collection.StrUtil;

public class OrderSampleStrDTO {
    private String orderNo;
    private String sampleNo;
    private String sampleNameCn;
    private String sampleNameEn;
    private String sampleAttrStr;
    private String sampleNameStr;

    public String getSampleNameCn() {
        return sampleNameCn;
    }

    public void setSampleNameCn(String sampleNameCn) {
        this.sampleNameCn = sampleNameCn;
    }

    public String getSampleNameEn() {
        return sampleNameEn;
    }

    public void setSampleNameEn(String sampleNameEn) {
        this.sampleNameEn = sampleNameEn;
    }

    public String getSampleAttrStr() {
        return sampleAttrStr;
    }

    public void setSampleAttrStr(String sampleAttrStr) {
        this.sampleAttrStr = sampleAttrStr;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getSampleNo() {
        return sampleNo;
    }

    public void setSampleNo(String sampleNo) {
        this.sampleNo = sampleNo;
    }

    public String getSampleNameStr() {
        return StrUtil.strAddStr(sampleNameCn,sampleNameEn,'|');
    }

    public void setSampleNameStr(String sampleNameStr) {
        this.sampleNameStr = sampleNameStr;
    }
}
