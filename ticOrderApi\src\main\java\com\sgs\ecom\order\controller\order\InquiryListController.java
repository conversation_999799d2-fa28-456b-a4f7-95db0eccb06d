package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.list.InquiryListReq;
import com.sgs.ecom.order.service.list.interfaces.IInquiryListService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/business/api.v2.order/list")
public class InquiryListController extends ControllerUtil {

    @Resource
    private IInquiryListService orderListService;

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryListAll", method = {RequestMethod.POST})
    public ResultBody qryListAll(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderListService.qryListAll(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryListUserCreate", method = {RequestMethod.POST})
    public ResultBody qryListUserCreate(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderListService.qryListUserCreate(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryListCsCreate", method = {RequestMethod.POST})
    public ResultBody qryListCsCreate(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderListService.qryListCsCreate(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryListCsFollow", method = {RequestMethod.POST})
    public ResultBody qryListCsFollow(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderListService.qryListCsFollow(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }



    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "getStateNumAll", method = {RequestMethod.POST})
    public ResultBody getStateNumAll(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderListService.getStateNumAll(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "getStateNumUserCreate", method = {RequestMethod.POST})
    public ResultBody getStateNumUserCreate(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {
        return ResultBody.newInstance(orderListService.getStateNumUserCreate(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "getStateNumCsCreate", method = {RequestMethod.POST})
    public ResultBody getStateNumCsCreate(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {

        return ResultBody.newInstance(orderListService.getStateNumCsCreate(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }

    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "getStateNumCsFollow", method = {RequestMethod.POST})
    public ResultBody getStateNumCsFollow(
            @RequestHeader(value="system") String system,
            @RequestBody InquiryListReq inquiryListReq,
            @RequestHeader(value = "accessToken") String token) throws Exception {

        return ResultBody.newInstance(orderListService.getStateNumCsFollow(inquiryListReq, getPersonInfo(token), getPower(token,system)));
    }



    /**
     * 询价单订单导出excel
     * @throws Exception
     */
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "expOrderListAll", method = {RequestMethod.POST})
    public void expOrderListAll(
            @RequestHeader(value="system") String system,
            @RequestHeader(value = "accessToken") String token,
            @RequestBody InquiryListReq inquiryListReq, HttpServletResponse res) throws Exception {
        orderListService.expOrderListAll(inquiryListReq, getPersonInfo(token), getPower(token,system), res);
    }

    /**
     * 询价单订单导出excel
     * @throws Exception
     */
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "expOrderListUserCreate", method = {RequestMethod.POST})
    public void expOrderListUserCreate(
            @RequestHeader(value="system") String system,
            @RequestHeader(value = "accessToken") String token,
            @RequestBody InquiryListReq inquiryListReq, HttpServletResponse res) throws Exception {
        orderListService.expOrderListUserCreate(inquiryListReq, getPersonInfo(token), getPower(token,system), res);
    }

    /**
     * 询价单订单导出excel
     * @throws Exception
     */
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "expOrderListCsCreate", method = {RequestMethod.POST})
    public void expOrderListCsCreate(
            @RequestHeader(value="system") String system,
            @RequestHeader(value = "accessToken") String token,
            @RequestBody InquiryListReq inquiryListReq, HttpServletResponse res) throws Exception {
        orderListService.expOrderListCsCreate(inquiryListReq, getPersonInfo(token), getPower(token,system), res);
    }

   /**
     * 询价单订单导出excel
     * @throws Exception
     */
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "expOrderListCsFollow", method = {RequestMethod.POST})
    public void expOrderListCsFollow(
            @RequestHeader(value="system") String system,
            @RequestHeader(value = "accessToken") String token,
            @RequestBody InquiryListReq inquiryListReq, HttpServletResponse res) throws Exception {
        orderListService.expOrderListCsFollow(inquiryListReq, getPersonInfo(token), getPower(token,system), res);
    }
}
