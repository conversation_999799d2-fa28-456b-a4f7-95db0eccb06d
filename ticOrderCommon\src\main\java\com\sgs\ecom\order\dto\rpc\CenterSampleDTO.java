package com.sgs.ecom.order.dto.rpc;

/**
 * <AUTHOR>
 */
public class CenterSampleDTO {
	private String sampleKey;
	private String sampleKeyName;
	private int sortShow;
	private int isMerge;
	//是否必输
	private int isMust;
	//字段定义页面类型 0-输入框 1-前缀方形勾选框 2-圆形前缀勾选' AFTER `SORT_SHOW`
	private int type;
	private String lua;
	private String sampleGroup;
	private int fillLen;


	private String regexRule;
	private String attrRules;
	private String remarkEnumConfig;
	private String enumConfig;
	private int rowNum;
	private String showAttr;
	private String fillNotice;

	public int getIsMust() {
		return isMust;
	}

	public void setIsMust(int isMust) {
		this.isMust = isMust;
	}

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

	public String getSampleKey() {
		return sampleKey;
	}

	public void setSampleKey(String sampleKey) {
		this.sampleKey = sampleKey;
	}

	public String getSampleKeyName() {
		return sampleKeyName;
	}

	public void setSampleKeyName(String sampleKeyName) {
		this.sampleKeyName = sampleKeyName;
	}

	public int getSortShow() {
		return sortShow;
	}

	public void setSortShow(int sortShow) {
		this.sortShow = sortShow;
	}

	public int getIsMerge() {
		return isMerge;
	}

	public void setIsMerge(int isMerge) {
		this.isMerge = isMerge;
	}

	public String getLua() {
		return lua;
	}

	public void setLua(String lua) {
		this.lua = lua;
	}

	public String getSampleGroup() {
		return sampleGroup;
	}

	public void setSampleGroup(String sampleGroup) {
		this.sampleGroup = sampleGroup;
	}

	public int getFillLen() {
		return fillLen;
	}

	public void setFillLen(int fillLen) {
		this.fillLen = fillLen;
	}

	public String getRegexRule() {
		return regexRule;
	}

	public void setRegexRule(String regexRule) {
		this.regexRule = regexRule;
	}

	public String getAttrRules() {
		return attrRules;
	}

	public void setAttrRules(String attrRules) {
		this.attrRules = attrRules;
	}

	public String getRemarkEnumConfig() {
		return remarkEnumConfig;
	}

	public void setRemarkEnumConfig(String remarkEnumConfig) {
		this.remarkEnumConfig = remarkEnumConfig;
	}

	public String getEnumConfig() {
		return enumConfig;
	}

	public void setEnumConfig(String enumConfig) {
		this.enumConfig = enumConfig;
	}

	public int getRowNum() {
		return rowNum;
	}

	public void setRowNum(int rowNum) {
		this.rowNum = rowNum;
	}

	public String getShowAttr() {
		return showAttr;
	}

	public void setShowAttr(String showAttr) {
		this.showAttr = showAttr;
	}

	public String getFillNotice() {
		return fillNotice;
	}

	public void setFillNotice(String fillNotice) {
		this.fillNotice = fillNotice;
	}
}
