package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class BOCustInvoice{
 
 	public static final String SEQUENCE = "INVOICE_ID"; 
  
 	public static final String BO_SQL = "TB_CUST_INVOICE"; 
 
 	public static final String OWNER ="member";

 	public static final String CUST_ID="custId";
 	public static final String TAX_NO="taxNo";
 	public static final String PROVICE="provice";
 	public static final String BANK_NUMBER="bankNumber";
 	public static final String CUSTOMER_NUMBER="customerNumber";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String MEMO="memo";
 	public static final String INVOICE_ID="invoiceId";
 	public static final String CREATE_DATE="createDate";
 	public static final String REG_ADDRESS="regAddress";
 	public static final String INVOICE_TYPE="invoiceType";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String INVOICE_TITLE="invoiceTitle";
 	public static final String IS_DEFAULT="isDefault";
 	public static final String PAYMENT_CODE="paymentCode";
 	public static final String REG_PHONE="regPhone";
 	public static final String BANK_NAME="bankName";
 	public static final String BUSI_CODE="busiCode";
 	
 	@BeanAnno("CUST_ID")
 	private long custId;
 	@BeanAnno("TAX_NO")
 	private String taxNo;
 	@BeanAnno("PROVICE")
 	private String provice;
 	@BeanAnno("BANK_NUMBER")
 	private String bankNumber;
 	@BeanAnno("customer_number")
 	private String customerNumber;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("memo")
 	private String memo;
 	@BeanAnno("INVOICE_ID")
 	private long invoiceId;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("REG_ADDRESS")
 	private String regAddress;
 	@BeanAnno("INVOICE_TYPE")
 	private int invoiceType;
 	@BeanAnno("COUNTRY")
 	private String country;
 	@BeanAnno("CITY")
 	private String city;
 	@BeanAnno("TOWN")
 	private String town;
 	@BeanAnno("INVOICE_TITLE")
 	private String invoiceTitle;
 	@BeanAnno("IS_DEFAULT")
 	private int isDefault;
 	@BeanAnno("PAYMENT_CODE")
 	private String paymentCode;
 	@BeanAnno("REG_PHONE")
 	private String regPhone;
 	@BeanAnno("BANK_NAME")
 	private String bankName;
 	@BeanAnno("BUSI_CODE")
 	private String busiCode;

 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCustomerNumber(String customerNumber){
 		 this.customerNumber=customerNumber;
 	}
 	public String getCustomerNumber(){
 		 return this.customerNumber;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setRegAddress(String regAddress){
 		 this.regAddress=regAddress;
 	}
 	public String getRegAddress(){
 		 return this.regAddress;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setPaymentCode(String paymentCode){
 		 this.paymentCode=paymentCode;
 	}
 	public String getPaymentCode(){
 		 return this.paymentCode;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setRegPhone(String regPhone){
 		 this.regPhone=regPhone;
 	}
 	public String getRegPhone(){
 		 return this.regPhone;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBankName(String bankName){
 		 this.bankName=bankName;
 	}
 	public String getBankName(){
 		 return this.bankName;
 	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
 
 	 
}