package com.sgs.ecom.order.dto.center;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.json.TimeFormatSerializer;
import com.sgs.base.BaseQryFilter;
import net.sf.json.JSONArray;

import java.sql.Timestamp;

public class SysAuthGroupDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select GROUP_NAME,CREATE_DATE,SHOP_ID,SHOP_CODE,STATE_DATE,STATE,PRODUCT_ID,GROUP_ID from SYS_AUTH_GROUP"; 
 
 
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="GROUP_NAME", getName="getGroupName", setName="setGroupName")
 	private String groupName;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="SHOP_ID", getName="getShopId", setName="setShopId")
 	private long shopId;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="SHOP_CODE", getName="getShopCode", setName="setShopCode")
 	private String shopCode;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class,QuerySummary.class})
 	@BeanAnno(value="PRODUCT_ID", getName="getProductId", setName="setProductId")
 	private long productId;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="GROUP_ID", getName="getGroupId", setName="setGroupId")
 	private long groupId;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="SHOP_NAME", getName="getShopName", setName="setShopName")
 	private String shopName;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="PRODUCT_NAME", getName="getProductName", setName="setProductName")
 	private String productName;

 	@ApiAnno(groups={QuerySummary.class})
 	private JSONArray lstProduct;

 	public void setGroupName(String groupName){
 		 this.groupName=groupName;
 	}
 	public String getGroupName(){
 		 return this.groupName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setShopId(long shopId){
 		 this.shopId=shopId;
 	}
 	public long getShopId(){
 		 return this.shopId;
 	}
 
 	 
 	public void setShopCode(String shopCode){
 		 this.shopCode=shopCode;
 	}
 	public String getShopCode(){
 		 return this.shopCode;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setProductId(long productId){
 		 this.productId=productId;
 	}
 	public long getProductId(){
 		 return this.productId;
 	}
 
 	 
 	public void setGroupId(long groupId){
 		 this.groupId=groupId;
 	}
 	public long getGroupId(){
 		 return this.groupId;
 	}
	public String getShopName() {
		return shopName;
	}
	public void setShopName(String shopName) {
		this.shopName = shopName;
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public JSONArray getLstProduct() {
		return lstProduct;
	}
	public void setLstProduct(JSONArray lstProduct) {
		this.lstProduct = lstProduct;
	}
 	 
}