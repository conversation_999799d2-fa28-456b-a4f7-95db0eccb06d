package com.sgs.ecom.order.controller.custom;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.service.custom.interfaces.ICustServiceSV;
import com.sgs.ecom.order.service.user.interfaces.IUserInfoSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOCustService;
import com.sgs.ecom.order.vo.VOUserInfo;

@RestController
@RequestMapping("/business/api.v1.cust/cs")
public class CustServiceController extends ControllerUtil {

	@Autowired
	private ICustServiceSV custServiceSV;
	
}
