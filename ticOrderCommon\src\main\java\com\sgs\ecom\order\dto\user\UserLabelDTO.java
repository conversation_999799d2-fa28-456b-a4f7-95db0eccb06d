package com.sgs.ecom.order.dto.user;
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class UserLabelDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select LABEL_GROUP,CREATE_DATE,LABEL_VALUE,LABEL_ID,STATE_DATE,USER_ID,STATE,LABEL_CODE from USER_LABEL"; 
 
 
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LABEL_GROUP", getName="getLabelGroup", setName="setLabelGroup")
 	private String labelGroup;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LABEL_VALUE", getName="getLabelValue", setName="setLabelValue")
 	private String labelValue;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LABEL_ID", getName="getLabelId", setName="setLabelId")
 	private long labelId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="USER_ID", getName="getUserId", setName="setUserId")
 	private long userId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="LABEL_CODE", getName="getLabelCode", setName="setLabelCode")
 	private String labelCode;

 	public void setLabelGroup(String labelGroup){
 		 this.labelGroup=labelGroup;
 	}
 	public String getLabelGroup(){
 		 return this.labelGroup;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setLabelValue(String labelValue){
 		 this.labelValue=labelValue;
 	}
 	public String getLabelValue(){
 		 return this.labelValue;
 	}
 
 	 
 	public void setLabelId(long labelId){
 		 this.labelId=labelId;
 	}
 	public long getLabelId(){
 		 return this.labelId;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setLabelCode(String labelCode){
 		 this.labelCode=labelCode;
 	}
 	public String getLabelCode(){
 		 return this.labelCode;
 	}
 
 	 
}