package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.enumtool.application.TestCycleEnum;
import com.sgs.ecom.order.util.collection.NumUtil;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class OrderDetailDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select DETAIL_NO,DETAIL_ID,PRICE,TEST_NAME,TEST_DAYS,BUY_NUMS,MEMO_EXPLAIN,ORDER_NO,NUMS_EXPLAIN,MEMO,ITEM_NAME from ORDER_DETAIL"; 
 
 

 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="DETAIL_ID", getName="getDetailId", setName="setDetailId")
 	private long detailId;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="PRICE", getName="getPrice", setName="setPrice")
 	private BigDecimal price;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
    private String priceShow;
	@ApiAnno(groups={Default.class,BaseOrderFilter.TicDetail.class, BaseOrderFilter.OrderQryList.class})
	private BigDecimal totalPrice;
	@ApiAnno(groups={ BaseOrderFilter.OrderQryList.class})
	private BigDecimal disPrice;
	private String totalPriceShow;
	@ApiAnno(groups={Default.class})
	private BigDecimal originalPrice;
	@ApiAnno(groups={Default.class})
	@JsonSerialize(using = PriceNumFormatSerializer.class)
	private BigDecimal originalTotalPrice;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="TEST_NAME", getName="getTestName", setName="setTestName")
 	private String testName;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="TEST_DAYS", getName="getTestDays", setName="setTestDays")
 	private BigDecimal testDays;
 	@BeanAnno(value="BUY_NUMS", getName="getBuyNums", setName="setBuyNums")
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	private Integer buyNums;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="MEMO_EXPLAIN", getName="getMemoExplain", setName="setMemoExplain")
 	private String memoExplain;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="ORDER_NO", getName="getOrderNo", setName="setOrderNo")
 	private String orderNo;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="NUMS_EXPLAIN", getName="getNumsExplain", setName="setNumsExplain")
 	private String otherExplain;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="MEMO", getName="getMemo", setName="setMemo")
 	private String memo;
 	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	@BeanAnno(value="ITEM_NAME", getName="getItemName", setName="setItemName")
 	private String itemName;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	private Long itemId;


	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
 	private String standardCode;
	@ApiAnno(groups={Default.class})
 	private String businessLine;
	@ApiAnno(groups={Default.class})
 	private String labName;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String unit;

	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String testMemo;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String sampleRequirements;

	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String cnasLab;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String cmaLab;
	@ApiAnno(groups={Default.class})
	private int isOptional;//是否可选，1是0否
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String labelName;//标签
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private int isDefault;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String itemAlias;
	@ApiAnno(groups={Default.class})
	private List<OrderSampleDTO> orderSampleDTOList = new ArrayList<>();
	@ApiAnno(groups={Default.class})
	private BigDecimal priceNum= new BigDecimal("100");

	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	@BeanAnno(value="PRODUCT_ID", getName="getProductId", setName="setProductId")
	private Long productId;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private Integer itemType;

	//补差价使用
	@ApiAnno(groups={Default.class})
	private String sampleName;
	@ApiAnno(groups={Default.class})
	private Integer isDetermine;//0,1

	@ApiAnno(groups={Default.class})
	private Integer urgentType;
	@ApiAnno(groups={Default.class})
	private BigDecimal urgentAmount;
	@ApiAnno(groups={Default.class})
	private String urgentTypeShow;
	@ApiAnno(groups={Default.class})
	private BigDecimal baseOriginalPrice;
	@ApiAnno(groups={Default.class})
	private List<OrderDetailDTO> subDetails = new ArrayList<>();
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private Integer isCs;
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private List<OrderAttachmentDTO> attachments = new ArrayList<>();

	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private String sampleNameInfo;//套餐关联的样品信息
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private Long parentDetailId;
	@ApiAnno(groups={Default.class})
	private String createDate;

	@ApiAnno(groups={Default.class})
	private String itemNameGroup;
	@ApiAnno(groups={Default.class})
	private String packageType;

	@ApiAnno(groups={Default.class})
	private String extendName;

	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private List<OrderSampleDTO> sampleNameInfoList = new ArrayList<>();

	@ApiAnno(groups={Default.class})
	private Integer orderState;//当前订单的状态
	@ApiAnno(groups={Default.class, BaseOrderFilter.OrderQryList.class})
	private List<OrderSampleRelateDTO> orderSampleList;

	@ApiAnno(groups={Default.class})
	private String pointsNum;

	/**
	 * 最低售价
	 */
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPrice;

	/**
	 * SKU价格
	 */
	@ApiAnno(groups={Default.class})
	private BigDecimal skuPrice;

	/**
	 * 最低售价补收差额
	 */
	@ApiAnno(groups={Default.class})
	private BigDecimal lowestPriceMargin;

	/**
	 * 最低收费明细标志
	 */
	private boolean lowestPriceDetail;

	public List<OrderSampleDTO> getSampleNameInfoList() {
		return sampleNameInfoList;
	}

	public Integer getOrderState() {
		return orderState;
	}

	public void setOrderState(Integer orderState) {
		this.orderState = orderState;
	}

	public void setSampleNameInfoList(List<OrderSampleDTO> sampleNameInfoList) {
		this.sampleNameInfoList = sampleNameInfoList;
	}

	public String getExtendName() {
		return extendName;
	}

	public void setExtendName(String extendName) {
		this.extendName = extendName;
	}

	public String getPackageType() {
		return packageType;
	}

	public void setPackageType(String packageType) {
		this.packageType = packageType;
	}

	public String getItemNameGroup() {
		return itemNameGroup;
	}

	public void setItemNameGroup(String itemNameGroup) {
		this.itemNameGroup = itemNameGroup;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public Long getParentDetailId() {
		return parentDetailId;
	}

	public void setParentDetailId(Long parentDetailId) {
		this.parentDetailId = parentDetailId;
	}

	public List<OrderAttachmentDTO> getAttachments() {
		return attachments;
	}

	public String getSampleNameInfo() {
		return sampleNameInfo;
	}

	public void setSampleNameInfo(String sampleNameInfo) {
		this.sampleNameInfo = sampleNameInfo;
	}

	public void setAttachments(List<OrderAttachmentDTO> attachments) {
		this.attachments = attachments;
	}
	public List<OrderDetailDTO> getSubDetails() {
		return subDetails;
	}

	public void setSubDetails(List<OrderDetailDTO> subDetails) {
		this.subDetails = subDetails;
	}

	public Integer getIsCs() {
		return isCs;
	}

	public void setIsCs(Integer isCs) {
		this.isCs = isCs;
	}

	public BigDecimal getDisPrice() {
		return disPrice;
	}

	public void setDisPrice(BigDecimal disPrice) {
		this.disPrice = disPrice;
	}

	public Integer getItemType() {
		return itemType;
	}

	public void setItemType(Integer itemType) {
		this.itemType = itemType;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public void setDetailId(long detailId){
 		 this.detailId=detailId;
 	}
 	public long getDetailId(){
 		 return this.detailId;
 	}
 
 	 

 
 	 
 	public void setTestName(String testName){
 		 this.testName=testName;
 	}
 	public String getTestName(){
 		 return this.testName;
 	}
 
 	 

 

 	public void setBuyNums(Integer buyNums){
 		 this.buyNums=buyNums;
 	}
 	public Integer getBuyNums(){
 		 return this.buyNums;
 	}
 
 	 
 	public void setMemoExplain(String memoExplain){
 		 this.memoExplain=memoExplain;
 	}
 	public String getMemoExplain(){
 		 return this.memoExplain;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}


	public String getOtherExplain() {
		return otherExplain;
	}

	public void setOtherExplain(String otherExplain) {
		this.otherExplain = otherExplain;
	}

	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	public void setItemName(String itemName){
 		 this.itemName=itemName;
 	}
 	public String getItemName(){
 		 return this.itemName;
 	}

	public String getStandardCode() {
		return standardCode;
	}

	public void setStandardCode(String standardCode) {
		this.standardCode = standardCode;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}


	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getTestMemo() {
		return testMemo;
	}

	public void setTestMemo(String testMemo) {
		this.testMemo = testMemo;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public String getCnasLab() {
		return cnasLab;
	}

	public void setCnasLab(String cnasLab) {
		this.cnasLab = cnasLab;
	}

	public String getCmaLab() {
		return cmaLab;
	}

	public void setCmaLab(String cmaLab) {
		this.cmaLab = cmaLab;
	}

	public BigDecimal getTestDays() {
		return testDays;
	}

	public void setTestDays(BigDecimal testDays) {
		this.testDays = testDays;
	}

	public List<OrderSampleDTO> getOrderSampleDTOList() {
		return orderSampleDTOList;
	}

	public void setOrderSampleDTOList(List<OrderSampleDTO> orderSampleDTOList) {
		this.orderSampleDTOList = orderSampleDTOList;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public BigDecimal getTotalPrice() {
		return totalPrice;
	}

	public void setTotalPrice(BigDecimal totalPrice) {
		this.totalPrice = totalPrice;
	}

	public int getIsOptional() {
		return isOptional;
	}

	public void setIsOptional(int isOptional) {
		this.isOptional = isOptional;
	}

	public String getLabelName() {
		return labelName;
	}

	public void setLabelName(String labelName) {
		this.labelName = labelName;
	}

	public int getIsDefault() {
		return isDefault;
	}

	public void setIsDefault(int isDefault) {
		this.isDefault = isDefault;
	}

	public String getItemAlias() {
		return itemAlias;
	}

	public void setItemAlias(String itemAlias) {
		this.itemAlias = itemAlias;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}


	@ApiAnno(groups={Default.class})
	private int isGive;

	private String bu;
	@ApiAnno(groups={Default.class})
	private String detailNo;
	@ApiAnno(groups={Default.class})
	private int state=1;



	@ApiAnno(groups={Default.class})
	private String categoryPath;
	@ApiAnno(groups={Default.class})
	private String itemNameShow;

	@ApiAnno(groups={Default.class})
	private List<OrderDetailDTO> subOrderDetailDTO = new ArrayList<OrderDetailDTO>();

	public int getIsGive() {
		return isGive;
	}

	public void setIsGive(int isGive) {
		this.isGive = isGive;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public String getDetailNo() {
		return detailNo;
	}

	public void setDetailNo(String detailNo) {
		this.detailNo = detailNo;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public List<OrderDetailDTO> getSubOrderDetailDTO() {
		return subOrderDetailDTO;
	}

	public void setSubOrderDetailDTO(List<OrderDetailDTO> subOrderDetailDTO) {
		this.subOrderDetailDTO = subOrderDetailDTO;
	}

	public void setPriceNum(BigDecimal priceNum) {
		this.priceNum = priceNum;
	}


	public BigDecimal getOriginalTotalPrice() {
		if(originalPrice==null){
			return null;
		}

		return originalPrice.multiply(new BigDecimal(buyNums).setScale(2, BigDecimal.ROUND_HALF_UP));
	}

	public void setOriginalTotalPrice(BigDecimal originalTotalPrice) {
		this.originalTotalPrice = originalTotalPrice;
	}

	public String getSampleName() {
		return sampleName;
	}

	public void setSampleName(String sampleName) {
		this.sampleName = sampleName;
	}

	public Integer getIsDetermine() {
		return isDetermine;
	}

	public void setIsDetermine(Integer isDetermine) {
		this.isDetermine = isDetermine;
	}

	public Integer getUrgentType() {
		return urgentType;
	}

	public void setUrgentType(Integer urgentType) {
		this.urgentType = urgentType;
	}

	public String getUrgentTypeShow() {
		return urgentType==null?"":TestCycleEnum.getNameCh(urgentType.toString());
	}

	public void setUrgentTypeShow(String urgentTypeShow) {
		this.urgentTypeShow = urgentTypeShow;
	}

	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public BigDecimal getPriceNum() {
		if(originalPrice==null||price==null){
			return new BigDecimal("100");
		}
		if(originalPrice.doubleValue()==0 || price.doubleValue()==0){
			return new BigDecimal("100");
		}
		BigDecimal addUrgentAmount=(urgentAmount==null)?BigDecimal.ZERO:urgentAmount;
		return price.divide(originalPrice.add(addUrgentAmount),4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));

	}


	public String getPriceShow() {
		return NumUtil.toFormat(price);
	}

	public String getTotalPriceShow() {
		return NumUtil.toFormat(totalPrice);
	}

	public BigDecimal getBaseOriginalPrice() {
		return baseOriginalPrice;
	}

	public void setBaseOriginalPrice(BigDecimal baseOriginalPrice) {
		this.baseOriginalPrice = baseOriginalPrice;
	}

	public String getPointsNum() {
		if(ValidationUtil.isEmpty(labelName))
			return "";

		return labelName;
	}

	public List<OrderSampleRelateDTO> getOrderSampleList() {
		return orderSampleList;
	}

	public void setOrderSampleList(List<OrderSampleRelateDTO> orderSampleList) {
		this.orderSampleList = orderSampleList;
	}
}