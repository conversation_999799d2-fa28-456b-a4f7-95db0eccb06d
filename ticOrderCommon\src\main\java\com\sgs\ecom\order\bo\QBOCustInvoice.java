package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

public class QBOCustInvoice{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.CUST_ID,a.TAX_NO,a.PROVICE,a.BANK_NUMBER,a.customer_number,a.STATE_DATE,a.STATE,a.memo,a.INVOICE_ID,a.CREATE_DATE,a.REG_ADDRESS,a.INVOICE_TYPE,a.COUNTRY,a.CITY,a.TOWN,a.INVOICE_TITLE,a.IS_DEFAULT,a.PAYMENT_CODE,a.REG_PHONE,a.BANK_NAME,b.COMPANY_NAME,b.CUST_CODE from TB_CUST_INVOICE a LEFT JOIN TB_CUST_INFO b ON a.cust_id=b.cust_id"; 
 
 	public static final String OWNER ="member";

 	public static final String CUST_ID="custId";
 	public static final String TAX_NO="taxNo";
 	public static final String PROVICE="provice";
 	public static final String BANK_NUMBER="bankNumber";
 	public static final String CUSTOMER_NUMBER="customerNumber";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String MEMO="memo";
 	public static final String COMPANY_NAME="companyName";
 	public static final String INVOICE_ID="invoiceId";
 	public static final String CREATE_DATE="createDate";
 	public static final String REG_ADDRESS="regAddress";
 	public static final String INVOICE_TYPE="invoiceType";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String CUST_CODE="custCode";
 	public static final String INVOICE_TITLE="invoiceTitle";
 	public static final String IS_DEFAULT="isDefault";
 	public static final String PAYMENT_CODE="paymentCode";
 	public static final String REG_PHONE="regPhone";
 	public static final String BANK_NAME="bankName";
 	public static final String BUSI_CODE="busiCode";

 	@BeanAnno(value="CUST_ID",table="a")
 	private long custId;
 	@BeanAnno(value="TAX_NO",table="a")
 	private String taxNo;
 	@BeanAnno(value="PROVICE",table="a")
 	private String provice;
 	@BeanAnno(value="BANK_NUMBER",table="a")
 	private String bankNumber;
 	@BeanAnno(value="customer_number",table="a")
 	private String customerNumber;
 	@BeanAnno(value="STATE_DATE",table="a")
 	private Timestamp stateDate;
 	@BeanAnno(value="STATE",table="a")
 	private int state;
 	@BeanAnno(value="memo",table="a")
 	private String memo;
 	@BeanAnno(value="COMPANY_NAME",table="b")
 	private String companyName;
 	@BeanAnno(value="INVOICE_ID",table="a")
 	private long invoiceId;
 	@BeanAnno(value="CREATE_DATE",table="a")
 	private Timestamp createDate;
 	@BeanAnno(value="REG_ADDRESS",table="a")
 	private String regAddress;
 	@BeanAnno(value="INVOICE_TYPE",table="a")
 	private int invoiceType;
 	@BeanAnno(value="COUNTRY",table="a")
 	private String country;
 	@BeanAnno(value="CITY",table="a")
 	private String city;
 	@BeanAnno(value="TOWN",table="a")
 	private String town;
 	@BeanAnno(value="CUST_CODE",table="b")
 	private String custCode;
 	@BeanAnno(value="INVOICE_TITLE",table="a")
 	private String invoiceTitle;
 	@BeanAnno(value="IS_DEFAULT",table="a")
 	private int isDefault;
 	@BeanAnno(value="PAYMENT_CODE",table="a")
 	private String paymentCode;
 	@BeanAnno(value="REG_PHONE",table="a")
 	private String regPhone;
 	@BeanAnno(value="BANK_NAME",table="a")
 	private String bankName;
 	@BeanAnno(value="BUSI_CODE",table="a")
 	private String busiCode;

 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setTaxNo(String taxNo){
 		 this.taxNo=taxNo;
 	}
 	public String getTaxNo(){
 		 return this.taxNo;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	@CharacterVaild(len = 32) 
 	public void setBankNumber(String bankNumber){
 		 this.bankNumber=bankNumber;
 	}
 	public String getBankNumber(){
 		 return this.bankNumber;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCustomerNumber(String customerNumber){
 		 this.customerNumber=customerNumber;
 	}
 	public String getCustomerNumber(){
 		 return this.customerNumber;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setMemo(String memo){
 		 this.memo=memo;
 	}
 	public String getMemo(){
 		 return this.memo;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setInvoiceId(long invoiceId){
 		 this.invoiceId=invoiceId;
 	}
 	public long getInvoiceId(){
 		 return this.invoiceId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setRegAddress(String regAddress){
 		 this.regAddress=regAddress;
 	}
 	public String getRegAddress(){
 		 return this.regAddress;
 	}
 
 	 
 	public void setInvoiceType(int invoiceType){
 		 this.invoiceType=invoiceType;
 	}
 	public int getInvoiceType(){
 		 return this.invoiceType;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCustCode(String custCode){
 		 this.custCode=custCode;
 	}
 	public String getCustCode(){
 		 return this.custCode;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setInvoiceTitle(String invoiceTitle){
 		 this.invoiceTitle=invoiceTitle;
 	}
 	public String getInvoiceTitle(){
 		 return this.invoiceTitle;
 	}
 
 	 
 	public void setIsDefault(int isDefault){
 		 this.isDefault=isDefault;
 	}
 	public int getIsDefault(){
 		 return this.isDefault;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setPaymentCode(String paymentCode){
 		 this.paymentCode=paymentCode;
 	}
 	public String getPaymentCode(){
 		 return this.paymentCode;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setRegPhone(String regPhone){
 		 this.regPhone=regPhone;
 	}
 	public String getRegPhone(){
 		 return this.regPhone;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setBankName(String bankName){
 		 this.bankName=bankName;
 	}
 	public String getBankName(){
 		 return this.bankName;
 	}
	public String getBusiCode() {
		return busiCode;
	}
	public void setBusiCode(String busiCode) {
		this.busiCode = busiCode;
	}
 
 	 
}