package com.sgs.ecom.order.entity.order;

import javax.persistence.Id;
import java.math.BigDecimal;


public class OrderReport {



 	private String reportFormCode;

 	private String reportCompanyNameEn;

 	private BigDecimal reportAmount;

 	private String reportRequirements;

 	private String reportCompanyNameCn;

 	private String stateDate;

 	private String reportAddressEn;

 	private Integer state;

 	private String orderNo;
	@Id
 	private Long reportId;

 	private String reportPerson;

 	private String reportForm;

 	private String createDate;

 	private String reportLua;

 	private String reportAddressCn;

 	private Integer isOpen;

 	private String reportMethodMemo;

 	private String reportLuaCode;

 	private Integer reportMethod;

 	private Integer isJudge;//是否需要评判
	private String judgeMemo;//是的时候取值

	private Integer isPicture;//是否需要照片

	private String province;

	private String city;

	private String town;
	//报告是否需要流程图  0-否 1-是

	private String isFlow;
	////检测场所类别

	private String detectionSiteCategory;
	//检测地址(地址簿)Id
	private Long detectionsite;

	private int reportRequirementsFlg;

	private Integer reportType;

	private String reportSendCc;

	private Integer reportTitleType;

	public String getReportSendCc() {
		return reportSendCc;
	}

	public void setReportSendCc(String reportSendCc) {
		this.reportSendCc = reportSendCc;
	}

	public Integer getReportType() {
		return reportType;
	}

	public void setReportType(Integer reportType) {
		this.reportType = reportType;
	}


	public int getReportRequirementsFlg() {
		return reportRequirementsFlg;
	}

	public void setReportRequirementsFlg(int reportRequirementsFlg) {
		this.reportRequirementsFlg = reportRequirementsFlg;
	}

	public String getIsFlow() {
		return isFlow;
	}

	public void setIsFlow(String isFlow) {
		this.isFlow = isFlow;
	}

	public String getDetectionSiteCategory() {
		return detectionSiteCategory;
	}

	public void setDetectionSiteCategory(String detectionSiteCategory) {
		this.detectionSiteCategory = detectionSiteCategory;
	}

	public Long getDetectionsite() {
		return detectionsite;
	}

	public void setDetectionsite(Long detectionsite) {
		this.detectionsite = detectionsite;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public Integer getIsJudge() {
		return isJudge;
	}

	public void setIsJudge(Integer isJudge) {
		this.isJudge = isJudge;
	}

	public Integer getIsPicture() {
		return isPicture;
	}

	public void setIsPicture(Integer isPicture) {
		this.isPicture = isPicture;
	}


	public void setReportFormCode(String reportFormCode){
 		 this.reportFormCode=reportFormCode;
 	}
 	public String getReportFormCode(){
 		 return this.reportFormCode;
 	}
 
 	 
 	public void setReportCompanyNameEn(String reportCompanyNameEn){
 		 this.reportCompanyNameEn=reportCompanyNameEn;
 	}
 	public String getReportCompanyNameEn(){
 		 return this.reportCompanyNameEn;
 	}


	public BigDecimal getReportAmount() {
		return reportAmount;
	}

	public void setReportAmount(BigDecimal reportAmount) {
		this.reportAmount = reportAmount;
	}

	public void setReportRequirements(String reportRequirements){
 		 this.reportRequirements=reportRequirements;
 	}
 	public String getReportRequirements(){
 		 return this.reportRequirements;
 	}
 
 	 
 	public void setReportCompanyNameCn(String reportCompanyNameCn){
 		 this.reportCompanyNameCn=reportCompanyNameCn;
 	}
 	public String getReportCompanyNameCn(){
 		 return this.reportCompanyNameCn;
 	}
 
 	 
 	public void setStateDate(String stateDate){
 		 this.stateDate=stateDate;
 	}
 	public String getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setReportAddressEn(String reportAddressEn){
 		 this.reportAddressEn=reportAddressEn;
 	}
 	public String getReportAddressEn(){
 		 return this.reportAddressEn;
 	}
 
 	 
 	public void setState(Integer state){
 		 this.state=state;
 	}
 	public Integer getState(){
 		 return this.state;
 	}
 
 	 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	public void setReportId(Long reportId){
 		 this.reportId=reportId;
 	}
 	public Long getReportId(){
 		 return this.reportId;
 	}
 
 	 
 	public void setReportPerson(String reportPerson){
 		 this.reportPerson=reportPerson;
 	}
 	public String getReportPerson(){
 		 return this.reportPerson;
 	}
 
 	 
 	public void setReportForm(String reportForm){
 		 this.reportForm=reportForm;
 	}
 	public String getReportForm(){
 		 return this.reportForm;
 	}
 
 	 
 	public void setCreateDate(String createDate){
 		 this.createDate=createDate;
 	}
 	public String getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setReportLua(String reportLua){
 		 this.reportLua=reportLua;
 	}
 	public String getReportLua(){
 		 return this.reportLua;
 	}
 
 	 
 	public void setReportAddressCn(String reportAddressCn){
 		 this.reportAddressCn=reportAddressCn;
 	}
 	public String getReportAddressCn(){
 		 return this.reportAddressCn;
 	}
 
 	 
 	public void setIsOpen(Integer isOpen){
 		 this.isOpen=isOpen;
 	}
 	public Integer getIsOpen(){
 		 return this.isOpen;
 	}
 
 	 
 	public void setReportMethodMemo(String reportMethodMemo){
 		 this.reportMethodMemo=reportMethodMemo;
 	}
 	public String getReportMethodMemo(){
 		 return this.reportMethodMemo;
 	}
 
 	 
 	public void setReportLuaCode(String reportLuaCode){
 		 this.reportLuaCode=reportLuaCode;
 	}
 	public String getReportLuaCode(){
 		 return this.reportLuaCode;
 	}
 

 	public void setReportMethod(Integer reportMethod){
 		 this.reportMethod=reportMethod;
 	}
 	public Integer getReportMethod(){
 		 return this.reportMethod;
 	}

	public String getJudgeMemo() {
		return judgeMemo;
	}

	public void setJudgeMemo(String judgeMemo) {
		this.judgeMemo = judgeMemo;
	}

	public Integer getReportTitleType() {
		return reportTitleType;
	}

	public void setReportTitleType(Integer reportTitleType) {
		this.reportTitleType = reportTitleType;
	}
}