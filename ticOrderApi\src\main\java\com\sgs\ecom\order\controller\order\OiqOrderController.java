package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.pdf.OrderWordPrintReq;
import com.sgs.ecom.order.service.oiq.interfaces.IOiqOrderService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/order")
public class OiqOrderController extends ControllerUtil {

    @Autowired
    private IOiqOrderService oiqOrderService;




    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "getWordStream", method = { RequestMethod.POST })
    public void getWordStream (
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderWordPrintReq orderWordPrintReq,
            HttpServletResponse httpServletResponse)throws Exception{
        oiqOrderService.getWordStream(orderWordPrintReq,getPersonInfo(token),httpServletResponse);
    }


    @RequestMapping(value = "getWordStreamTest", method = { RequestMethod.GET })
    public void getWordStreamTest (
            @RequestParam(value="orderNo") String orderNo,
            HttpServletResponse httpServletResponse)throws Exception{
        OrderWordPrintReq orderWordPrintReq=new OrderWordPrintReq(orderNo,"form");
        orderWordPrintReq.setBu("2700");
        oiqOrderService.getWordStream(orderWordPrintReq,null,httpServletResponse);
    }

}
