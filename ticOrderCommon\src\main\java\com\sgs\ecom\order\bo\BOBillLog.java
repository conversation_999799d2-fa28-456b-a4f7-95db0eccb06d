package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild;
import com.platform.annotation.ExplainAnno;

import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="账单备注")
public class BOBillLog{
 
 	public static final String SEQUENCE = "LOG_ID"; 
  
 	public static final String BO_SQL = "TB_BILL_LOG"; 
 
 	public static final String OWNER ="member";

 	public static final String CS_CODE="csCode";
 	public static final String CREATE_DATE="createDate";
 	public static final String BILL_ID="billId";
 	public static final String BILL_CYCLE="billCycle";
 	public static final String STATE_DATE="stateDate";
 	public static final String STATE="state";
 	public static final String LOG_ID="logId";
 	public static final String REMARK="remark";
 	public static final String CS_ID="csId";

 	@BeanAnno("CS_CODE")
 	private String csCode;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("BILL_ID")
 	private long billId;
 	@BeanAnno("BILL_CYCLE")
 	private String billCycle;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("LOG_ID")
 	private long logId;
 	@BeanAnno("REMARK")
 	private String remark;
 	@BeanAnno("CS_ID")
 	private long csId;

 	@CharacterVaild(len = 50) 
 	public void setCsCode(String csCode){
 		 this.csCode=csCode;
 	}
 	public String getCsCode(){
 		 return this.csCode;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBillId(long billId){
 		 this.billId=billId;
 	}
 	public long getBillId(){
 		 return this.billId;
 	}
 
 	 
 	public void setBillCycle(String billCycle){
 		 this.billCycle=billCycle;
 	}
 	public String getBillCycle(){
 		 return this.billCycle;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setLogId(long logId){
 		 this.logId=logId;
 	}
 	public long getLogId(){
 		 return this.logId;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
 	public void setCsId(long csId){
 		 this.csId=csId;
 	}
 	public long getCsId(){
 		 return this.csId;
 	}
 
 	 
}