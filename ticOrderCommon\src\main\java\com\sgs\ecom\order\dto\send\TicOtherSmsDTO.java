package com.sgs.ecom.order.dto.send;

import com.sgs.ecom.order.request.FileReq;
import com.sgs.ecom.order.request.express.OrderTicExpress;

import java.util.List;
import java.util.Map;

public class TicOtherSmsDTO {
    private String sendCC;
    private String orderNo;
    private String userName;//客户名称
    private List<FileReq> fileReqList;
    private List<OrderTicExpress> orderTicExpressList;// 快递信息

    private String sendPhone;
    private Boolean sendPhoneFlag=false;

    private Integer state;
    private Map<String,Object> paramMap ;

    public Map<String, Object> getParamMap() {
        return paramMap;
    }

    public void setParamMap(Map<String, Object> paramMap) {
        this.paramMap = paramMap;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getSendPhone() {
        return sendPhone;
    }

    public void setSendPhone(String sendPhone) {
        this.sendPhone = sendPhone;
    }

    public Boolean getSendPhoneFlag() {
        return sendPhoneFlag;
    }

    public void setSendPhoneFlag(Boolean sendPhoneFlag) {
        this.sendPhoneFlag = sendPhoneFlag;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public List<OrderTicExpress> getOrderTicExpressList() {
		return orderTicExpressList;
	}

	public void setOrderTicExpressList(List<OrderTicExpress> orderTicExpressList) {
		this.orderTicExpressList = orderTicExpressList;
	}

	public List<FileReq> getFileReqList() {
        return fileReqList;
    }

    public void setFileReqList(List<FileReq> fileReqList) {
        this.fileReqList = fileReqList;
    }

    public String getSendCC() {
        return sendCC;
    }

    public void setSendCC(String sendCC) {
        this.sendCC = sendCC;
    }
}
