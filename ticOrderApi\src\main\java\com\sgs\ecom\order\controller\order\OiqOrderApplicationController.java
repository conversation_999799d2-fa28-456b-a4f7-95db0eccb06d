package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.oiq.OiqLineReq;
import com.sgs.ecom.order.request.oiq.OiqOrderReq;
import com.sgs.ecom.order.service.oiq.interfaces.IOiqOrderApplicationService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import net.sf.json.JSONObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.oiq/application")
public class OiqOrderApplicationController extends ControllerUtil {

    @Resource
    private IOiqOrderApplicationService oiqOrderApplicationService;

    /**
     *@Function: save
     *@Description 保存申请表
     *@param: [token, applicationReq]
     *@version:
     *@author: Xiwei_Qiu
     *@date: 2021/3/9
     **/
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "saveForm", method = { RequestMethod.POST })
    public ResultBody saveForm (
            @RequestHeader(value="accessToken") String token,
            @RequestHeader(value = "system") String system,
            @Validated(BaseBean.Update.class)
            @RequestBody OiqOrderReq oiqOrderReq)throws Exception{
        oiqOrderReq.setSystem(system);
        oiqOrderReq.setSysPerson(getPersonInfo(token));
        oiqOrderReq.setPersonBuMap(getPersonBu(token, system));
        String jsonStr = oiqOrderApplicationService.checkFormToSendFlg(oiqOrderReq);
        JSONObject jsonObject = oiqOrderApplicationService.saveForm(oiqOrderReq);
        oiqOrderApplicationService.sendNotifications(oiqOrderReq, jsonStr);
        return ResultBody.newInstance(jsonObject);
    }


    /**
    * @params [token, orderNoReq]
    * @return ResultBody
    * @description 查询申请表信息 询报价或申请表订单
    * <AUTHOR> || created at 2024/3/8 9:22
    */
    @HystrixCommand
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryInfo", method = { RequestMethod.POST })
    public ResultBody qryInfo(
            @RequestHeader(value="accessToken") String token,
            @RequestBody OrderNoReq orderNoReq) throws Exception {
        return ResultBody.newInstance(oiqOrderApplicationService.qryInfo(orderNoReq,getPersonInfo(token)));
    }

    @HystrixCommand
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryLineByLineCode", method = { RequestMethod.POST })
    public ResultBody qryLineByLineCode (
            @RequestBody OiqLineReq oiqLineReq)throws Exception{
        return ResultBody.newInstance(oiqOrderApplicationService.qryLineByLineCode(oiqLineReq));
    }



}
