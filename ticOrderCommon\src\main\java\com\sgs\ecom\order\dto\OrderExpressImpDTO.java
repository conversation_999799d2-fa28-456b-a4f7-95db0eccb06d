package com.sgs.ecom.order.dto;

import com.platform.annotation.BeanAnno;
import com.platform.annotation.ExplainAnno;
import com.sgs.base.BaseBean;

import javax.validation.constraints.Size;

public class OrderExpressImpDTO extends BaseBean {

 	@Size(min = 0,max =30,message = "ORDER_NO参数长度超出范围,最大长度为:30", groups = {Insert.class,Update.class}) 
 	@BeanAnno("ORDER_NO")
 	private String orderNo;
 	@Size(min = 0,max =50,message = "EXPRESS_CODE参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class}) 
 	@BeanAnno("EXPRESS_CODE")
 	private String expressCode;

 	@Size(min = 0,max =200,message = "GOODS_NAME参数长度超出范围,最大长度为:200", groups = {Insert.class,Update.class}) 
 	@BeanAnno("GOODS_NAME")
 	private String goodsName;

 	@Size(min = 0,max =20,message = "EXPRESS_NO参数长度超出范围,最大长度为:20", groups = {Insert.class,Update.class}) 
 	@BeanAnno("EXPRESS_NO")
 	private String expressNo;

 	@BeanAnno("SEND_PHONE")
 	private String sendPhone;
 	@Size(min = 0,max =20,message = "RECEIPT_CITY参数长度超出范围,最大长度为:20", groups = {Insert.class,Update.class}) 

	@BeanAnno("GOODS_TYPE")
	private String goodsType;

	@BeanAnno("IS_SHOW")
 	private String isShow;

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getExpressCode() {
		return expressCode;
	}

	public void setExpressCode(String expressCode) {
		this.expressCode = expressCode;
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName;
	}

	public String getExpressNo() {
		return expressNo;
	}

	public void setExpressNo(String expressNo) {
		this.expressNo = expressNo;
	}

	public String getSendPhone() {
		return sendPhone;
	}

	public void setSendPhone(String sendPhone) {
		this.sendPhone = sendPhone;
	}

	public String getGoodsType() {
		return goodsType;
	}

	public void setGoodsType(String goodsType) {
		this.goodsType = goodsType;
	}

	public String getIsShow() {
		return isShow;
	}

	public void setIsShow(String isShow) {
		this.isShow = isShow;
	}
}