package com.sgs.ecom.order.dto.export;

import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.enumtool.activity.PromotionStateEnum;
import com.sgs.ecom.order.enumtool.application.InvoiceTypeEnum;
import com.sgs.ecom.order.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.order.enumtool.bbc.StoreEnum;
import com.sgs.ecom.order.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderSourceEnum;
import com.sgs.ecom.order.enumtool.order.OrderSubStateEnum;
import com.sgs.ecom.order.enumtool.pay.MonthPayEnum;
import com.sgs.ecom.order.enumtool.pay.OrderPayRefundEnum;
import com.sgs.ecom.order.enumtool.pay.PayMethodEnum;
import com.sgs.ecom.order.util.attr.AttrUtil;
import com.sgs.ecom.order.util.order.OrderUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

public class ExpTicOrderDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int row;//编号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String selectState;//筛选状态
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String selectTime;//筛选时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String nowTime;//导出时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int state;//当前状态
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String stateShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int subState;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String subStateShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int refundState;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String refundStateShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String isTest;//订单类型
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderNo;//订单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String subOrderNo;//补差价订单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderShow="主订单";//主订单
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reFlg;//0否 1是

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String createDate;//下单时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String productName;//服务名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sku;//价格属性
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String skuAttr;//
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String testItem;//检测项目
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String checkDate;//最终检验日期（InspectionOnly）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal price;//单价
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer quantity;//数量
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String productMemo;//商品备注
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payMethod;//支付方式
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String accountName;//商户名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String buyerInfo;//付款账号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal orderAmount;//总计原价 (没有优惠的)
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal storeAmount;//店铺优惠
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal couponAmount;//优惠券金额
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal orderAmountPrice;//合计金额 orderAmount-storeAmount
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal orderRealAmount;//订单总金额 realAmount
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal orderRealPrice;//应付金额 realAmount
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal realAmount;//实付金额 realAmount
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal subTotalAmount;//补差价订单总金额
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal refundAmount;//退款金额 第一次退款成功的最新金额
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String refundReason;//退款原因
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String refundMemo;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String refundReasonShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String couponName;//优惠券使用 优惠券名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payDate;//付款时间
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String transNo;//支付平台交易号 （TRANS_NO）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String paymentNo;//商户订单号（PAYMENT_NO）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payAgain="否";//重复支付（是否）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bu;//服务类型
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String catName;//实验室类别
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String labName;//实验室名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userName;//用户名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formUserName;//申请人姓名
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formUserPhone;//申请人电话
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formUserEmail;//申请人邮箱
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer proState;//Pro会员状态 （ ,已通过、待通过、已驳回、未提交）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String proStateName;//Pro会员状态 （ ,已通过、待通过、已驳回、未提交）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyAddressCn;//公司地址
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyAddressEn;//公司地址（英文）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyNameCn;//公司名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyNameEn;//公司名称（英文）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int invoiceType;//发票类型
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceTypeShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceTitle;//公司抬头
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bossTag;//boss系统的标记
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String taxNo;//纳税人识别号，
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bankAddr;//开户银行
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bankNumber;//开户行号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String registerAddr;//注册地址
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String registerPhone;//注册电话
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reTic;//标记
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userId;


	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String idCode ;//身份证 taxNo
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String phone;//联系电话
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendCompanyName;//发票邮寄公司名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendPerson;//发票寄送收件人
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendPhone;//发票寄送收件人电话
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendAddress;//发票寄送公司地址
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceSendEmail;//发票寄送收件人邮箱
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderPayNo;//合并支付订单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String operatorCode;//订单操作人
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sampleName;//样品信息（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportName;//BU report no（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int monthPay;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String monthPayShow;//支付方式 0现付（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String monthMorePay;//月结支付状态（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String isInvoice;//已出票（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderSource;//订单来源（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderSourceMemo;//订单来源备注（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String businessLicense;//营业执照
	//private String ;//其他文件
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String brandName;//品牌信息 （申请表里的品牌信息formAttr）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formCreateTime;//申请表提交时间（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String formCommitTime;//订单确认时间（申请表确认时间）（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportCreateDate;//上传报告时间（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String expressOff;//快递单号和快递录入时间  1（未提交）（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String expressOn;//快递单号和提交客户端时间 1（已提交）（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceFileName;//电子普票 （发票文件）（√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceFileTime;//电子普票上传时间 （发票上传时间） （√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String isPayReceived;//收款状态 （√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String changePriceMemo;//改价理由和备注 （√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String otsSystem;//业务系统  （√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String otsNo;//业务系统订单号 （√）
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String hangUp;//是否挂账
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String hangUpReason;//挂账原因

	//下面字段不体现在导出
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int subCount;//
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String sortDate;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是


	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String foreignCountry;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String foreignAddr;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String foreignConcat;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String refundLastPerson;//退款最后一次发起人
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payerName;//付款方名称
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payerPhone;//付款方手机号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payerEmail;//付款方邮箱

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String customerPackage;//套餐

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer customerPackageNum;//套餐数量

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String customerFlexi;//自选

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer customerFlexiNum;//自选数量

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String csPackage;//客服定制

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer csPackageNum;//客服定制数量

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String csDisCountRate;//客服优惠比例

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String urgentName;//加急选项
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal serviceAmount;//加急费

	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public String getCsDisCountRate() {
		BigDecimal orderOldDecimal = !ValidationUtil.isEmpty(orderAmount) ? orderAmount : BigDecimal.ZERO;
		BigDecimal shopDecimal = !ValidationUtil.isEmpty(storeAmount) ? storeAmount : BigDecimal.ZERO;
		BigDecimal disCountDecimal = !ValidationUtil.isEmpty(couponAmount) ? couponAmount : BigDecimal.ZERO;
		BigDecimal realDecimal = !ValidationUtil.isEmpty(realAmount) ? realAmount : BigDecimal.ZERO;
		BigDecimal serviceDecimal = !ValidationUtil.isEmpty(serviceAmount) ? serviceAmount : BigDecimal.ZERO;
		BigDecimal subtract = realDecimal.subtract(serviceDecimal);
		//客服折扣优惠率 = (实付金额-加急费)/(订单实付金额-店铺优惠-优惠券优惠)
		BigDecimal subtract1 = orderOldDecimal.subtract(shopDecimal).subtract(disCountDecimal);
		if(ValidationUtil.isEmpty(subtract1) || subtract1.doubleValue() == 0){
			return  "";
		}else {
			BigDecimal multiply = subtract.divide(orderOldDecimal.subtract(shopDecimal).subtract(disCountDecimal), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP);
			if(!ValidationUtil.isEmpty(multiply) && multiply.doubleValue() !=0){
				return multiply.toString()+"%";
			}
			return  "";
		}
	}



	public String getUrgentName() {
		return urgentName;
	}

	public void setUrgentName(String urgentName) {
		this.urgentName = urgentName;
	}

	public String getCustomerPackage() {
		return customerPackage;
	}

	public void setCustomerPackage(String customerPackage) {
		this.customerPackage = customerPackage;
	}

	public Integer getCustomerPackageNum() {
		return customerPackageNum;
	}

	public void setCustomerPackageNum(Integer customerPackageNum) {
		this.customerPackageNum = customerPackageNum;
	}

	public String getCustomerFlexi() {
		return customerFlexi;
	}

	public void setCustomerFlexi(String customerFlexi) {
		this.customerFlexi = customerFlexi;
	}

	public Integer getCustomerFlexiNum() {
		return customerFlexiNum;
	}

	public void setCustomerFlexiNum(Integer customerFlexiNum) {
		this.customerFlexiNum = customerFlexiNum;
	}

	public String getCsPackage() {
		return csPackage;
	}

	public void setCsPackage(String csPackage) {
		this.csPackage = csPackage;
	}

	public Integer getCsPackageNum() {
		return csPackageNum;
	}

	public void setCsPackageNum(Integer csPackageNum) {
		this.csPackageNum = csPackageNum;
	}

	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	public String getPayerPhone() {
		return payerPhone;
	}

	public void setPayerPhone(String payerPhone) {
		this.payerPhone = payerPhone;
	}

	public String getPayerEmail() {
		return payerEmail;
	}

	public void setPayerEmail(String payerEmail) {
		this.payerEmail = payerEmail;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getRefundLastPerson() {
		return refundLastPerson;
	}

	public void setRefundLastPerson(String refundLastPerson) {
		this.refundLastPerson = refundLastPerson;
	}

	public String getForeignCountry() {
		return foreignCountry;
	}

	public void setForeignCountry(String foreignCountry) {
		this.foreignCountry = foreignCountry;
	}

	public String getForeignAddr() {
		return foreignAddr;
	}

	public void setForeignAddr(String foreignAddr) {
		this.foreignAddr = foreignAddr;
	}

	public String getForeignConcat() {
		return foreignConcat;
	}

	public void setForeignConcat(String foreignConcat) {
		this.foreignConcat = foreignConcat;
	}

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public String getBossTag() {
		return bossTag;
	}

	public void setBossTag(String bossTag) {
		this.bossTag = bossTag;
	}

	public String getBusinessLicense() {
		return businessLicense;
	}

	public void setBusinessLicense(String businessLicense) {
		this.businessLicense = businessLicense;
	}

	public String getProStateName() {
		if(ValidationUtil.isEmpty(proState)){
			return "未提交";
		}
		return PromotionStateEnum.getNameCh(proState);

	}

	public void setProStateName(String proStateName) {
		this.proStateName = proStateName;
	}

	public int getRow() {
		return row;
	}

	public void setRow(int row) {
		this.row = row;
	}

	public String getNowTime() {
		String[] newstr = nowTime.split("\\.");
		return newstr[0];
	}

	public void setNowTime(String nowTime) {
		this.nowTime = nowTime;
	}


	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getStateShow() {
		return BbcStateEnum.getNameCh(state);
	}

	public void setStateShow(String stateShow) {
		this.stateShow = stateShow;
	}

	public String getIsTest() {
		if("1".equals(isTest)){
			return "测试订单";
		}
		if("0".equals(isTest)){
			return "真实订单";
		}
		return isTest;
	}

	public void setIsTest(String isTest) {
		this.isTest = isTest;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getSubOrderNo() {
		return subOrderNo;
	}

	public void setSubOrderNo(String subOrderNo) {
		this.subOrderNo = subOrderNo;
	}

	public String getCreateDate() {

		String[] newstr = createDate.split("\\.");
		return newstr[0];
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getSkuAttr() {
		return skuAttr;
	}

	public void setSkuAttr(String skuAttr) {
		this.skuAttr = skuAttr;
	}

	public void setSku(String sku) {
		this.sku = sku;
	}

	public String getTestItem() {
		return testItem;
	}

	public void setTestItem(String testItem) {
		this.testItem = testItem;
	}

	public String getCheckDate() {
		return checkDate;
	}

	public void setCheckDate(String checkDate) {
		this.checkDate = checkDate;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public String getProductMemo() {
		return productMemo;
	}

	public void setProductMemo(String productMemo) {
		this.productMemo = productMemo;
	}

	public String getPayMethod() {
		return PayMethodEnum.getNameCh(payMethod);
	}

	public void setPayMethod(String payMethod) {
		this.payMethod = payMethod;
	}

	public String getAccountName() {
		return accountName;
	}

	public void setAccountName(String accountName) {
		this.accountName = accountName;
	}

	public String getBuyerInfo() {
		return buyerInfo;
	}

	public void setBuyerInfo(String buyerInfo) {
		this.buyerInfo = buyerInfo;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public BigDecimal getStoreAmount() {
		if("主订单".equals(orderShow)){
			return storeAmount;
		}
		return null;
	}

	public void setStoreAmount(BigDecimal storeAmount) {
		this.storeAmount = storeAmount;
	}

	public BigDecimal getCouponAmount() {
		if("主订单".equals(orderShow)){
			return couponAmount;
		}
		return null;
	}

	public void setCouponAmount(BigDecimal couponAmount) {
		this.couponAmount = couponAmount;
	}

	public BigDecimal getOrderAmountPrice() {
		if("主订单".equals(orderShow)){
			return OrderUtil.toZero(orderAmount).subtract(OrderUtil.toZero(storeAmount)).subtract(OrderUtil.toZero(couponAmount));
		}
		return null;
	}

	public void setOrderAmountPrice(BigDecimal orderAmountPrice) {
		this.orderAmountPrice = orderAmountPrice;
	}

	public BigDecimal getOrderRealAmount() {
		return orderRealAmount;
	}

	public void setOrderRealAmount(BigDecimal orderRealAmount) {
		this.orderRealAmount = orderRealAmount;
	}

	public BigDecimal getOrderRealPrice() {
		return orderRealPrice;
	}

	public void setOrderRealPrice(BigDecimal orderRealPrice) {
		this.orderRealPrice = orderRealPrice;
	}

	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public BigDecimal getSubTotalAmount() {

		return subTotalAmount;
	}

	public void setSubTotalAmount(BigDecimal subTotalAmount) {
		this.subTotalAmount = subTotalAmount;
	}

	public BigDecimal getRefundAmount() {
		return refundAmount;
	}

	public void setRefundAmount(BigDecimal refundAmount) {
		this.refundAmount = refundAmount;
	}

	public String getRefundReason() {
		return refundReason;
	}

	public String getRefundReasonShow() {
		if(StringUtils.isBlank(refundReason)){
			return "";
		}
		if(Integer.parseInt(refundReason)==91){
			return refundMemo;
		}

		return OrderPayRefundEnum.getNameCh(Integer.parseInt(refundReason));
	}

	public void setRefundReasonShow(String refundReasonShow) {
		this.refundReasonShow = refundReasonShow;
	}

	public void setRefundReason(String refundReason) {
		this.refundReason = refundReason;
	}

	public String getCouponName() {
		return couponName;
	}

	public void setCouponName(String couponName) {
		this.couponName = couponName;
	}

	public String getPayDate() {
		if(StringUtils.isNotBlank(payDate)){
			String[] newstr = payDate.split("\\.");
			return  newstr[0];
		}
		return payDate;
	}

	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}

	public String getTransNo() {
		return transNo;
	}

	public void setTransNo(String transNo) {
		this.transNo = transNo;
	}

	public String getPaymentNo() {
		return paymentNo;
	}

	public void setPaymentNo(String paymentNo) {
		this.paymentNo = paymentNo;
	}

	public String getPayAgain() {
		return payAgain;
	}

	public void setPayAgain(String payAgain) {
		this.payAgain = payAgain;
	}

	public String getBu() {
		return StoreEnum.getNameCh(bu);
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getFormUserName() {
		return formUserName;
	}

	public void setFormUserName(String formUserName) {
		this.formUserName = formUserName;
	}

	public String getFormUserPhone() {
		return formUserPhone;
	}

	public void setFormUserPhone(String formUserPhone) {
		this.formUserPhone = formUserPhone;
	}

	public String getFormUserEmail() {
		return formUserEmail;
	}

	public void setFormUserEmail(String formUserEmail) {
		this.formUserEmail = formUserEmail;
	}

	public Integer getProState() {
		return proState;
	}

	public void setProState(Integer proState) {
		this.proState = proState;
	}

	public String getCompanyAddressCn() {
		return companyAddressCn;
	}

	public void setCompanyAddressCn(String companyAddressCn) {
		this.companyAddressCn = companyAddressCn;
	}

	public String getCompanyAddressEn() {
		return companyAddressEn;
	}

	public void setCompanyAddressEn(String companyAddressEn) {
		this.companyAddressEn = companyAddressEn;
	}

	public String getCompanyNameCn() {
		return companyNameCn;
	}

	public void setCompanyNameCn(String companyNameCn) {
		this.companyNameCn = companyNameCn;
	}

	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public int getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(int invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getInvoiceTypeShow() {
//		if( !ValidationUtil.isEmpty(isElectron) && isElectron == 1)
//			return InvoiceTypeEnum.COMMON_COMPANY.getNameCh();

		return InvoiceTypeEnum.getNameCh(invoiceType);
	}

	public void setInvoiceTypeShow(String invoiceTypeShow) {
		this.invoiceTypeShow = invoiceTypeShow;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public String getBankAddr() {
		return bankAddr;
	}

	public void setBankAddr(String bankAddr) {
		this.bankAddr = bankAddr;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public String getRegisterAddr() {
		return registerAddr;
	}

	public void setRegisterAddr(String registerAddr) {
		this.registerAddr = registerAddr;
	}

	public String getRegisterPhone() {
		return registerPhone;
	}

	public void setRegisterPhone(String registerPhone) {
		this.registerPhone = registerPhone;
	}

	public String getIdCode() {
		return idCode;
	}

	public void setIdCode(String idCode) {
		this.idCode = idCode;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getInvoiceSendCompanyName() {
		return invoiceSendCompanyName;
	}

	public void setInvoiceSendCompanyName(String invoiceSendCompanyName) {
		this.invoiceSendCompanyName = invoiceSendCompanyName;
	}

	public String getInvoiceSendPerson() {
		return invoiceSendPerson;
	}

	public void setInvoiceSendPerson(String invoiceSendPerson) {
		this.invoiceSendPerson = invoiceSendPerson;
	}

	public String getInvoiceSendPhone() {
		return invoiceSendPhone;
	}

	public void setInvoiceSendPhone(String invoiceSendPhone) {
		this.invoiceSendPhone = invoiceSendPhone;
	}

	public String getInvoiceSendAddress() {
		return invoiceSendAddress;
	}

	public void setInvoiceSendAddress(String invoiceSendAddress) {
		this.invoiceSendAddress = invoiceSendAddress;
	}

	public String getInvoiceSendEmail() {
		return invoiceSendEmail;
	}

	public void setInvoiceSendEmail(String invoiceSendEmail) {
		this.invoiceSendEmail = invoiceSendEmail;
	}

	public String getOrderPayNo() {
		return orderPayNo;
	}

	public void setOrderPayNo(String orderPayNo) {
		this.orderPayNo = orderPayNo;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public String getSampleName() {
		return sampleName;
	}

	public void setSampleName(String sampleName) {
		this.sampleName = sampleName;
	}

	public String getReportName() {
		if(StringUtils.isBlank(reportName)){
			return "";
		}
		String replace = reportName.replace(".jpg", "")
				.replace(".JPG", "")
				.replace(".jpeg", "")
				.replace(".JPEG", "")
				.replace(".png", "")
				.replace(".png", "")
				.replace(".PNG", "")
				.replace(".pdf", "")
				.replace(".PDF", "")
				.replace(".gif", "")
				.replace(".GIF", "");
		return replace;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	public int getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(int monthPay) {
		this.monthPay = monthPay;
	}





	public String getIsInvoice() {
		return "1".equals(isInvoice)?"已出票":"未出票";
	}

	public void setIsInvoice(String isInvoice) {
		this.isInvoice = isInvoice;
	}

	public String getOrderSource() {
		return OrderSourceEnum.getNameCh(orderSource);
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public String getOrderSourceMemo() {
		return orderSourceMemo;
	}

	public void setOrderSourceMemo(String orderSourceMemo) {
		this.orderSourceMemo = orderSourceMemo;
	}

	public String getExpressOff() {
		return expressOff;
	}

	public void setExpressOff(String expressOff) {
		this.expressOff = expressOff;
	}

	public String getExpressOn() {
		return expressOn;
	}

	public void setExpressOn(String expressOn) {
		this.expressOn = expressOn;
	}

	public String getInvoiceFileName() {
		return invoiceFileName;
	}

	public void setInvoiceFileName(String invoiceFileName) {
		this.invoiceFileName = invoiceFileName;
	}

	public String getInvoiceFileTime() {
		if(StringUtils.isNotBlank(invoiceFileTime)){
			String[] newstr = invoiceFileTime.split("\\.");
			return  newstr[0];
		}
		return invoiceFileTime;
	}

	public void setInvoiceFileTime(String invoiceFileTime) {
		this.invoiceFileTime = invoiceFileTime;
	}

	public String getIsPayReceived() {
		return "0".equals(isPayReceived)?"未到账":"已到账";
	}

	public void setIsPayReceived(String isPayReceived) {
		this.isPayReceived = isPayReceived;
	}

	public String getChangePriceMemo() {
		return changePriceMemo;
	}

	public void setChangePriceMemo(String changePriceMemo) {
		this.changePriceMemo = changePriceMemo;
	}

	public String getOtsSystem() {
		return otsSystem;
	}

	public void setOtsSystem(String otsSystem) {
		this.otsSystem = otsSystem;
	}

	public String getOtsNo() {
		return otsNo;
	}

	public void setOtsNo(String otsNo) {
		this.otsNo = otsNo;
	}

	public String getHangUp() {
		return hangUp;
	}

	public void setHangUp(String hangUp) {
		this.hangUp = hangUp;
	}

	public String getHangUpReason() {
		return hangUpReason;
	}

	public void setHangUpReason(String hangUpReason) {
		this.hangUpReason = hangUpReason;
	}


	//


	public String getSku() {
		if(StringUtils.isBlank(skuAttr)){
			return "";
		}
		return AttrUtil.skuAttrToSku(skuAttr);
	}


	public String getMonthPayShow() {
		if(!"主订单".equals(orderShow)){
			if(StringUtils.isBlank(payMethod)){
				return "";
			}
			return "现付";
		}


		if(monthPay==1 ||monthPay==3){
			return "月结";
		}


		return MonthPayEnum.getNameCh(monthPay);
	}

	public void setMonthPayShow(String monthPayShow) {
		this.monthPayShow = monthPayShow;
	}

	public String getMonthMorePay() {
		if(monthPay==1 ||monthPay==3){
			return MonthPayEnum.getNameCh(monthPay);
		}
		return "";
	}

	public void setMonthMorePay(String monthMorePay) {
		this.monthMorePay = monthMorePay;
	}

	public String getReportCreateDate() {
		if(StringUtils.isNotBlank(reportCreateDate)){
			String[] newstr = reportCreateDate.split("\\.");
			return  newstr[0];
		}
		return reportCreateDate;
	}

	public void setReportCreateDate(String reportCreateDate) {
		this.reportCreateDate = reportCreateDate;
	}

	public String getFormCreateTime() {

		if(StringUtils.isNotBlank(formCreateTime)){
			String[] newstr = formCreateTime.split("\\.");
			return  newstr[0];
		}
		return formCreateTime;
	}

	public void setFormCreateTime(String formCreateTime) {
		this.formCreateTime = formCreateTime;
	}

	public String getFormCommitTime() {
		if(StringUtils.isNotBlank(formCommitTime)){
			String[] newstr = formCommitTime.split("\\.");
			return  newstr[0];
		}
		return formCommitTime;
	}

	public void setFormCommitTime(String formCommitTime) {
		this.formCommitTime = formCommitTime;
	}

	public int getSubState() {
		return subState;
	}

	public void setSubState(int subState) {
		this.subState = subState;
	}

	public String getSubStateShow() {
		if(subState==70){
			return OrderSubStateEnum.getNameCh(subState);
		}
		return "";
	}

	public void setSubStateShow(String subStateShow) {
		this.subStateShow = subStateShow;
	}

	public int getRefundState() {
		return refundState;
	}

	public void setRefundState(int refundState) {
		this.refundState = refundState;
	}

	public String getRefundStateShow() {
		return OrderRefundStateEnum.getNameCh(refundState);
	}

	public void setRefundStateShow(String refundStateShow) {
		this.refundStateShow = refundStateShow;
	}

	public String getRefundMemo() {
		return refundMemo;
	}

	public void setRefundMemo(String refundMemo) {
		this.refundMemo = refundMemo;
	}

	public String getOrderShow() {
		return orderShow;
	}

	public void setOrderShow(String orderShow) {
		this.orderShow = orderShow;
	}

	public int getSubCount() {
		return subCount;
	}

	public void setSubCount(int subCount) {
		this.subCount = subCount;
	}

	public String getSortDate() {
		return sortDate;
	}

	public void setSortDate(String sortDate) {
		this.sortDate = sortDate;
	}

	public String getSelectState() {
		return selectState;
	}

	public void setSelectState(String selectState) {
		this.selectState = selectState;
	}

	public String getSelectTime() {
		return selectTime;
	}

	public void setSelectTime(String selectTime) {
		this.selectTime = selectTime;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public String getReTic() {
		return reTic;
	}

	public void setReTic(String reTic) {
		this.reTic = reTic;
	}

	public String getReFlg() {
		return reFlg;
	}

	public void setReFlg(String reFlg) {
		this.reFlg = reFlg;
	}

	public String getCatName() {
		return catName;
	}

	public void setCatName(String catName) {
		this.catName = catName;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}
}
