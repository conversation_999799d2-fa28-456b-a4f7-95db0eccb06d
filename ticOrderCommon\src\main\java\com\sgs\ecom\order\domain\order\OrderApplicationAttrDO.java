package com.sgs.ecom.order.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.entity.order.OrderApplicationAttr;
import com.sgs.ecom.order.enumtool.application.OiqFormEnum;
import com.sgs.ecom.order.request.oiq.OiqApplicationReq;
import com.sgs.ecom.order.request.order.OrderApplicationAttrReq;
import com.sgs.ecom.order.util.UseDateUtil;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class OrderApplicationAttrDO {

    public static final String BASE_FORM="baseForm";
    public static final String DESTINATION_LIST="destinationList";
    public static final String DESTINATION_COUNTRY="destinationCountry";
    public static final String COST_INFORMATION_LIST="costInformationList";
    public static final String GOODS="goods";
    public static final String DOCUMENTS_INFO="documentsInfo";
    public static final String RO="RO";
    public static final String LCL="LCL";
    public static final String FCL="FCL";
    public static final String BULK_GOODS="BULK_GOODS";
    public static final String TFSTransport="TFSTransport";
    public static final String ADD_VEHICLE_MODEL="addVehicleModel";
    public static final String ADD_VEHICLE_LT5="addVehicleLt5";
    public static final String ADD_VEHICLE_GE5="addVehicleGe5";
    public static final String ADD_CONTAINERS="addContainers";
    public static final String ADD_CONTAINERS20="addContainers20";
    public static final String ADD_CONTAINERS40="addContainers40";
    public static final String CONTAINER_SIZE = "containerSize";
    public static final String HS_CODE="hsCode";
    public static final String VEHICLE_TYPE="vehicleType";
    public static final String VEHICLE_WEIGHT_LT5_TOTAL_QUANTITY="vehicleWeightLt5Quantity";
    public static final String VEHICLE_WEIGHT_LT5_TOTAL_VOLUME="vehicleWeightLt5TotalVolume";
    public static final String VEHICLE_WEIGHT_LT5_TOTAL_GROSS_WEIGHT="vehicleWeightLt5TotalGrossWeight";
    public static final String VEHICLE_WEIGHT_GE5_TOTAL_QUANTITY="vehicleWeightGe5Quantity";
    public static final String VEHICLE_WEIGHT_GE5_TOTAL_VOLUME="vehicleWeightGe5TotalVolume";
    public static final String VEHICLE_WEIGHT_GE5_TOTAL_GROSS_WEIGHT="vehicleWeightGe5TotalGrossWeight";
    /**车辆附表*/
    public static final String ADD_VEHICLE_MODEL_DTO_LIST="addVehicleModelDTOList";
    public static final String ADD_VEHICLE="addVehicle";
    public static final String ADD_GOODS="addGoods";
    public static final String CONTAINER_NUMBER="containerNumber";
    /**货物附表*/
    public static final String ADD_CONTAINER_DTO_LIST="addContainerDTOList";

    public static final String BASE_ADD_CONTAINER_DTO_LIST="baseAddContainerDTOList";


    public static final String TRANSPORT_MODE="transportMode";

    /**
     * 运输方式拼接
     */
    public static final String TRANSPORT_STR="transportStr";
    /**
     * 贝宁-品名拼接
     */
    public static final String GOODS_NAME_STR_INNER="goodsNameStrInner";
    /**
     * 加蓬-货物总毛重拼接
     */
    public static final String GOODS_GROSS_WEIGHT_STR="goodsGrossWeightStr";
    /**
     * 加蓬-货物总体积拼接
     */
    public static final String GOODS_VOLUME_STR="goodsVolumeStr";
    /**
     * 品名拼接
     */
    public static final String GOODS_NAME_STR="goodsNameStr";
    /**
     * 品名key
     */
    public static final String GOODS_NAME="goodsName";
    /**
     * 货物总毛重拼接
     */
    public static final String GROSS_WEIGHT_STR="grossWeightStr";
    /**
     * 货物总毛重key
     */
    public static final String GROSS_WEIGHT="grossWeight";
    /**
     * 货物总毛重拼接
     */
    public static final String TOTAL_GROSS_WEIGHT_STR="totalGrossWeightStr";
    /**
     * 货物总毛重key
     */
    public static final String TOTAL_GROSS_WEIGHT="totalGrossWeight";
    /**
     * 货物总体积拼接
     */
    public static final String TOTAL_VOLUME_STR="totalVolumeStr";
    /**
     * 货物总体积key
     */
    public static final String TOTAL_VOLUME="totalVolume";
    /**
     * 货物总件数拼接
     */
    public static final String PACKAGES_NUMBER_STR="packagesNumberStr";
    /**
     * 货物总件数key
     */
    public static final String PACKAGES_NUMBER="packagesNumber";
    /**
     * 海运费拼接
     */
    public static final String OCEAN_FREIGHT_STR="oceanFreightStr";
    /**
     * 货物价值拼接
     */
    public static final String FOB_VALUE_STR="fobValueStr";
    /**
     * 产品类别拼接
     */
    public static final String PRODUCTS_TYPE_STR="productsTypeStr";
    /**
     * 产品类别key
     */
    public static final String PRODUCTS_TYPE="productsType";
    /**
     * 车辆数量拼接
     */
    public static final String VEHICLES_QUANTITY_STR ="vehiclesQuantityStr";
    /**
     * 车辆数量key
     */
    public static final String VEHICLES_QUANTITY ="vehiclesQuantity";
    /**
     * 货物总净重拼接
     */
    public static final String TOTAL_NET_WEIGHT_STR ="totalNetWeightStr";
    /**
     * 货物总净重key
     */
    public static final String TOTAL_NET_WEIGHT ="totalNetWeight";
    /**
     * 20/10尺集装箱数量
     */
    public static final String TOTAL_QUANTITY_CONTAINER_20_OR_10 = "totalQuantityContainer20Or10";
    /**
     * 40/45尺集装箱数量
     */
    public static final String TOTAL_QUANTITY_CONTAINER_40_OR_45 = "totalQuantityContainer40Or45";

    public static final String LADING_BILL_TYPE="ladingBillType";


    public static final String draftOfBL="申请预开号";

    // 报价单页面展示字段
    public static final String ORDER_SHIPPING = "orderShipping";
    public static final String LADING_NO = "ladingNo";
    public static final String APPLICATION_FORM = "applicationForm";
    public static final String COMPANY_NAME_CN = "companyNameCn";
    public static final String BASE_INFO = "baseInfo";
    public static final String OUT_ORDER_NO = "outOrderNo";
    public static final String TOTAL_QUANTITY_OF_CONTAINER_10 = "totalQuantityOfContainers10";
    public static final String TOTAL_QUANTITY_OF_CONTAINER_20 = "totalQuantityOfContainers20";
    public static final String TOTAL_QUANTITY_OF_CONTAINER_40 = "totalQuantityOfContainers40";
    public static final String TOTAL_QUANTITY_OF_CONTAINER_45 = "totalQuantityOfContainers45";
    public static final String VEHICLE_QUANTITY = "vehicleQuantity";
    public static final String VEHICLE_QUANTITY_WEIGHT_LT_5 = "vehicleQuantityWeightLt5";
    public static final String VEHICLE_QUANTITY_WEIGHT_GE_5 = "vehicleQuantityWeightGe5";


    public static class SHOW_NAME {
        public static final String CONTAINER_SIZE_20_10 = "20/10尺货柜数量";
        public static final String CONTAINER_SIZE_40_45 = "40/45尺货柜数量";
        public static final String CONTAINER_SIZE_1O = "10尺货柜数量";
        public static final String CONTAINER_SIZE_2O = "20尺货柜数量";
        public static final String CONTAINER_SIZE_4O = "40尺货柜数量";
        public static final String CONTAINER_SIZE_45 = "45尺货柜数量";
        public static final String TOTAL_GROSS_WEIGHT = "总毛重";
        public static final String TOTAL_GROSS_WEIGHT_LT_5 = "总毛重（车毛重<5吨）";
        public static final String TOTAL_GROSS_WEIGHT_GE_5 = "总毛重（车毛重>5吨）";
        public static final String TOTAL_VOLUME = "总体积";
        public static final String TOTAL_VOLUME_LT_5 = "总体积（车毛重<5吨）";
        public static final String TOTAL_VOLUME_GE_5 = "总体积（车毛重>5吨）";
        public static final String VEHICLE_QUANTITY = "车辆数量";
        public static final String VEHICLE_QUANTITY_WEIGHT_LT_5 = "车辆数量（车毛重<5吨）";
        public static final String VEHICLE_QUANTITY_WEIGHT_GE_5 = "车辆数量（车毛重>=5吨）";
    }


    public static String getOceanFreightKey(String key){
        return key+"_"+"oceanFreight";
    }

    public static String getFobValue(String key){
        return key+"_"+"fobValue";
    }

    public static String getGoodsName(String key){
        return key+"_"+"goodsName";
    }















    public OrderApplicationAttr portalToForm(String key,String value,String orderNo,String dateStr){
        OrderApplicationAttr orderApplicationAttr = new OrderApplicationAttr();
        orderApplicationAttr.setOrderNo(orderNo);
        orderApplicationAttr.setState(1);
        orderApplicationAttr.setCreateDate(dateStr);
        orderApplicationAttr.setAttrCode(key);
        orderApplicationAttr.setAttrCode(key);
        orderApplicationAttr.setAttrValue(value);
        orderApplicationAttr.setAttrName(OiqFormEnum.getNameCh(key));
        orderApplicationAttr.setAreaCode(OiqFormEnum.getFormCode(key));
        return orderApplicationAttr;
    }



    public OrderApplicationAttr repToFormAttr(String key,String value,String orderNo,String dateStr){
        if(value==null){
            value="";
        }
        OrderApplicationAttr orderApplicationAttr = new OrderApplicationAttr();
        orderApplicationAttr.setOrderNo(orderNo);
        orderApplicationAttr.setState(1);
        orderApplicationAttr.setCreateDate(dateStr);
        orderApplicationAttr.setAttrCode(key);
        orderApplicationAttr.setAttrValue(value);
        orderApplicationAttr.setAttrName(OiqFormEnum.getNameCh(key));
        orderApplicationAttr.setAreaCode(OiqFormEnum.getFormCode(key));
        return orderApplicationAttr;
    }

    public List<OrderApplicationAttr> reqToAttrList(OiqApplicationReq oiqApplicationReq, String orderNo, String dateStr){
        List<OrderApplicationAttr> list=new ArrayList<>();
        OrderApplicationAttr testImg=portalToForm(OiqFormEnum.TEST_MEMO_IMG.getName(), JSON.toJSONString(oiqApplicationReq.getTestMemoImg()),orderNo,dateStr);
        String json=JSON.toJSONString(oiqApplicationReq.getApplicationAttr());

        Map<String, Object> mapKey = JSON.parseObject(json, Map.class);
        for(String key:mapKey.keySet()){
            if(!ValidationUtil.isEmpty(mapKey.get(key))){
                OrderApplicationAttr attr=repToFormAttr(key,mapKey.get(key).toString(),orderNo,dateStr);
                list.add(attr);
            }
        }
        list.add(testImg);
        return list;
    }

    public  List<OrderApplicationAttr> getEntityListByReqList(List<OrderApplicationAttrReq> reqList, String orderNo){
        if(ValidationUtil.isEmpty(reqList)){
            return new ArrayList<>();
        }
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        String dateStr= UseDateUtil.getDateString(new Date());
        List<OrderApplicationAttr> list=new ArrayList<>();
        for(int n=0;n<reqList.size();n++){
            OrderApplicationAttr orderApplicationAttr=new OrderApplicationAttr();
            OrderApplicationAttrReq orderApplicationAttrReq=reqList.get(n);
            baseCopyObj.copyWithNull(orderApplicationAttr,orderApplicationAttrReq);
            if(orderApplicationAttrReq.getAttrText()!=null){
                orderApplicationAttr.setAttrText(JSON.toJSONString(orderApplicationAttrReq.getAttrText()));
            }
            if(StringUtils.isBlank(orderApplicationAttrReq.getAttrValue())){
                orderApplicationAttr.setAttrValue("");
            }
            orderApplicationAttr.setCreateDate(dateStr);
            orderApplicationAttr.setOrderNo(orderNo);
            orderApplicationAttr.setState(1);
            list.add(orderApplicationAttr);
        }
        return list;
    }

    public static List<String> addTfsFormList(){
        List<String> list=new ArrayList<>();
        list.add(DESTINATION_LIST);
        list.add(COST_INFORMATION_LIST);
        list.add(GOODS);
        list.add(DOCUMENTS_INFO);
        return list;
    }
}
