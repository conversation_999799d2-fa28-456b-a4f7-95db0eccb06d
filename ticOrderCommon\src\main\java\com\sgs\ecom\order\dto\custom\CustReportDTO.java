package com.sgs.ecom.order.dto.custom; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.util.json.ScienceFormatSerializer; 
import com.platform.annotation.ApiAnno; 
import com.platform.annotation.BeanAnno; 
import com.sgs.base.BaseQryFilter; 

public class CustReportDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CURRENCY,CUST_ID,CREATE_DATE,BU,TAX_RATE,STATE_DATE,STATE,REPORT_PRICE,REPORT_ID from TB_CUST_REPORT"; 
 
 
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="CURRENCY", getName="getCurrency", setName="setCurrency")
 	private String currency;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private long custId;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="CREATE_DATE", getName="getCreateDate", setName="setCreateDate")
 	private Timestamp createDate;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="BU", getName="getBu", setName="setBu")
 	private String bu;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="TAX_RATE", getName="getTaxRate", setName="setTaxRate")
 	private double taxRate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE_DATE", getName="getStateDate", setName="setStateDate")
 	private Timestamp stateDate;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="STATE", getName="getState", setName="setState")
 	private int state;
 	@ApiAnno(groups={Default.class,QueryList.class})
 	@BeanAnno(value="REPORT_PRICE", getName="getReportPrice", setName="setReportPrice")
 	private double reportPrice;
 	@ApiAnno(groups={Default.class})
 	@BeanAnno(value="REPORT_ID", getName="getReportId", setName="setReportId")
 	private long reportId;

 	public void setCurrency(String currency){
 		 this.currency=currency;
 	}
 	public String getCurrency(){
 		 return this.currency;
 	}
 
 	 
 	public void setCustId(long custId){
 		 this.custId=custId;
 	}
 	public long getCustId(){
 		 return this.custId;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	public void setBu(String bu){
 		 this.bu=bu;
 	}
 	public String getBu(){
 		 return this.bu;
 	}
 
 	 
 	public void setTaxRate(double taxRate){
 		 this.taxRate=taxRate;
 	}
 	public double getTaxRate(){
 		 return this.taxRate;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	public void setReportPrice(double reportPrice){
 		 this.reportPrice=reportPrice;
 	}
 	@JsonSerialize(using = ScienceFormatSerializer.class) 
 	public double getReportPrice(){
 		 return this.reportPrice;
 	}
 
 	 
 	public void setReportId(long reportId){
 		 this.reportId=reportId;
 	}
 	public long getReportId(){
 		 return this.reportId;
 	}
 
 	 
}