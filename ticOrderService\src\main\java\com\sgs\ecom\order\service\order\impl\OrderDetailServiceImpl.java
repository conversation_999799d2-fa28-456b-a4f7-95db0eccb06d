package com.sgs.ecom.order.service.order.impl;


import com.alibaba.fastjson.JSON;
import com.platform.annotation.Master;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.domain.order.OrderSampleFromDO;
import com.sgs.ecom.order.domain.service.order.interfaces.*;
import com.sgs.ecom.order.dto.center.EnumDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.log.BaseLog;
import com.sgs.ecom.order.dto.order.*;
import com.sgs.ecom.order.dto.send.TicOtherMailDTO;
import com.sgs.ecom.order.entity.order.OrderAttachment;
import com.sgs.ecom.order.entity.order.OrderSampleFrom;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.order.GroupTypeEnum;
import com.sgs.ecom.order.enumtool.pay.OrderPayReceivedEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.event.EventMailUtil;
import com.sgs.ecom.order.event.EventSmsUtil;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.mapper.order.OrderDetailMapper;
import com.sgs.ecom.order.request.OrderDetailAddReq;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.request.OrderSampleNoReq;
import com.sgs.ecom.order.request.OrderSampleReq;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.request.exp.ExpDetailReq;
import com.sgs.ecom.order.service.custom.interfaces.ICustomCodeService;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.IMemberRestTemplateService;
import com.sgs.ecom.order.service.member.interfaces.ISSOTemplateSV;
import com.sgs.ecom.order.service.order.interfaces.*;
import com.sgs.ecom.order.service.util.interfaces.IOrderService;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.*;
import com.sgs.ecom.order.util.collection.StrUtil;
import com.sgs.ecom.order.util.select.AttributeUtil;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.vo.*;
import com.sgs.ecom.order.vo.order.VOChangeOrderPrice;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class OrderDetailServiceImpl extends BaseService implements IOrderDetailService {

    Logger logger = LoggerFactory.getLogger(OrderDetailServiceImpl.class);
    @Autowired
    public IOrderBaseInfoService orderBaseInfoService;
    @Autowired
    public IOrderService orderService;
    @Autowired
    public IOrderUtilService orderUtilService;
    @Autowired
    public IOrderAttributeService orderAttributeService;
    @Autowired
    public IOrderSampleService orderSampleService;
    @Autowired
    public IOrderDetailService orderDetailService;
    @Autowired
    public IOrderSampleRelateService orderSampleRelateService;
    @Autowired
    public IMemberRestTemplateService memberRestTemplateService;
    @Autowired
    public ISSOTemplateSV issoRestTemplateService;
    @Autowired
    public ICenterTemplateSV centerRestTemplateService;
    @Autowired
    public IOrderLogService orderLogService;
    @Autowired
    public OrderDetailMapper orderDetailMapper;
    @Autowired
    private IOrderGroupService orderGroupService;
    @Autowired
	private IOrderOperatorLogService orderOperatorLogService;
    @Autowired
    private SmsEventUtil smsEventUtil;
    @Autowired
    private MailEventUtil mailEventUtil;
    @Autowired
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Autowired
    private IOrderSampleFromDomainService orderSampleFromDomainService;
    @Resource
    private IOrderProductDomainService orderProductDomainService;
    @Autowired
    private IOrderOperatorLogDomainService orderOperatorLogDomainService;


    @Autowired
    private ApiEventUtil apiEventUtil;
	@Autowired
	private ICustomCodeService customCodeService;
    @Autowired
    private IOrderSampleFromService orderSampleFromService;
    @Resource
    private IOrderDetailDomainService orderDetailsService;

    @Resource
    private IOrderAttachmentDomainService orderAttachmentDomainService;
    @Resource
    private UtilTools utilTools;
    @Resource
    private IOrderOperatorLogDomainService operatorLogDomainService;
    @Autowired
    private EventMailUtil eventMailUtil;
    @Autowired
    private EventSmsUtil eventSmsUtil;
    @Resource
    private RedisUtils redisUtils;

    @Override
    public void addOrderDetail(List<OrderDetailAddReq> orderDetailAddList, OrderPriceDTO orderPriceDTO,Boolean checkSample,BaseOrderDTO baseOrderDTO) throws Exception{


        String orderNo=orderPriceDTO.getOrderNo();
        String useOldGroup=orderPriceDTO.getOldGroup();
        String groupNo= orderPriceDTO.getNewGroup();
        if(ValidationUtil.isEmpty(orderDetailAddList)){
            orderPriceDTO.setOrderAmount(BigDecimal.ZERO);
            orderPriceDTO.setTotalUrgentAmount(BigDecimal.ZERO);
            return;
        }
        List<String> sampleNoList=orderSampleService.selectSampleNo(orderNo,useOldGroup);
        List<String> checkSampleNoList=new ArrayList<>(sampleNoList);
        BigDecimal orderAmount=BigDecimal.ZERO;
        List<VOOrderSampleRelate> orderSampleRelateList=new ArrayList<>();


        //加急费总额是通过测试项目算的
        Map<String, EnumDTO> enumDTOMap=orderUtilService.getEnumByRedisKey(RedisKeyUtil.OIQ_TEST_CYCLE);
        BigDecimal totalUrgentAmount=BigDecimal.ZERO;
        for(OrderDetailAddReq orderDetailAdd:orderDetailAddList) {
            if (orderDetailAdd.getPrice() == null && orderPriceDTO.getPriceType()==1) {
                throw new BusinessException("3111", "有价格待定的项目，请联系工程师修改后再确认");
            }

            VOOrderDetail voOrderDetailAdd = new VOOrderDetail();
            baseCopyObj.copyWithNull(voOrderDetailAdd,orderDetailAdd);

            voOrderDetailAdd.setUrgentType(voOrderDetailAdd.getUrgentType()==null?1:voOrderDetailAdd.getUrgentType());

            voOrderDetailAdd.setOrderNo(orderNo);
            voOrderDetailAdd.setGroupNo(groupNo);
            voOrderDetailAdd.setItemId(orderDetailAdd.getItemId());
            voOrderDetailAdd.setBuyNums(orderDetailAdd.getBuyNums());
            voOrderDetailAdd.setOriginalPrice(StrUtil.toBigDecimal(orderDetailAdd.getOriginalPrice()));

            if(StringUtils.isNotBlank(orderDetailAdd.getPrice())){
                BigDecimal price=new BigDecimal(orderDetailAdd.getPrice());
                voOrderDetailAdd.setPrice(price);
                voOrderDetailAdd.setTotalPrice(price.multiply(new BigDecimal(orderDetailAdd.getBuyNums())));
                if(voOrderDetailAdd.getUrgentType()!=null){
                    EnumDTO enumDTO=enumDTOMap.get(voOrderDetailAdd.getUrgentType().toString());
                    if(!ValidationUtil.isEmpty(enumDTO) && voOrderDetailAdd.getOriginalPrice()!=null){
                        BigDecimal urgentNum=new BigDecimal(enumDTO.getRemark()).divide(new BigDecimal("100"));
                        BigDecimal urgentAmount=voOrderDetailAdd.getOriginalPrice().multiply(urgentNum);
                        voOrderDetailAdd.setUrgentAmount(urgentAmount);
                        totalUrgentAmount=totalUrgentAmount.add(urgentAmount.multiply(new BigDecimal(voOrderDetailAdd.getBuyNums())).
                                setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }

                if(voOrderDetailAdd.getTotalPrice().doubleValue()>=1000000000L){
                   throw new BusinessException(ResultEnumCode.ORDER_PRICE_MAX);
                }

            }

            String otherExplain=(orderDetailAdd.getOtherExplain()==null?JSON.toJSONString(new OtherExplainDTO()):JSON.toJSONString(orderDetailAdd.getOtherExplain()));
            voOrderDetailAdd.setOtherExplain(otherExplain);
            voOrderDetailAdd.setTestDays(new BigDecimal(orderDetailAdd.getTestDays()));
            String date = UseDateUtil.getDateString(new Date());
            voOrderDetailAdd.setCreateDate(date);
            voOrderDetailAdd.setStateDate(date);
            voOrderDetailAdd.setState(1);
            if(voOrderDetailAdd.getIsDefault()==1 && voOrderDetailAdd.getTotalPrice()!=null){//默认加入计算价格
                orderAmount=orderAmount.add(voOrderDetailAdd.getTotalPrice());
            }
            voOrderDetailAdd.setIsDetermine(orderDetailAdd.getIsDetermine());
            if(voOrderDetailAdd.getIsDetermine()==null ||voOrderDetailAdd.getIsDetermine()==-1){
                voOrderDetailAdd.setIsDetermine(0);
            }

            orderDetailMapper.insertVOSelective(voOrderDetailAdd);
            Long detailId=voOrderDetailAdd.getDetailId();

            if(orderDetailAdd.getOrderSampleDTOList()!=null && orderDetailAdd.getOrderSampleDTOList().size()>0){

                for(OrderSampleNoReq sampleNoReq:orderDetailAdd.getOrderSampleDTOList()){
                    //保存样品的关联关系
                    orderSampleRelateList.add(orderSampleRelateService.getVOOrderSampleRelate(detailId,orderNo,groupNo,sampleNoReq.getSampleNo()));
                    if(checkSample && !checkSampleNoList.contains(sampleNoReq.getSampleNo())){
                        logger.info("checkSampleNoList=="+JSON.toJSONString(checkSampleNoList));
                        throw new BusinessException(ResultEnumCode.SAMPLE_INFO_CHANGE);
                    }

                    if(!ValidationUtil.isEmpty(sampleNoList) && sampleNoList.contains(sampleNoReq.getSampleNo())){
                        sampleNoList.remove(sampleNoReq.getSampleNo());
                    }
                }

            }
        }

        if(orderAmount.doubleValue()>=1000000000L){
            throw new BusinessException(ResultEnumCode.ORDER_PRICE_MAX);
        }
        if((!"2700".equals(baseOrderDTO.getBu())) && checkSample && ValidationUtil.isEmpty(orderSampleRelateList)  ){
			throw new BusinessException(ResultEnumCode.DETAIL_SAMPLE_NULL);
		}

        if(!ValidationUtil.isEmpty(orderSampleRelateList)){
            orderSampleRelateService.insertForeach(orderSampleRelateList);
        }

        //当样品未被关联的时候 把状态改成0；
        if (!ValidationUtil.isEmpty(sampleNoList)){
            orderSampleService.updateStateByRelate(orderNo,groupNo,sampleNoList);
        }
        orderPriceDTO.setOrderAmount(orderAmount);

        orderPriceDTO.setTotalUrgentAmount(totalUrgentAmount);
    }



	public void addSubOrderDetail(VOSubOrder voSubOrder,List<OrderDetailAddReq> orderDetailAddList) throws Exception{

		String dateStr = UseDateUtil.getDateString(new Date());
		String orderNo=voSubOrder.getOrderNo();
		String groupNo= voSubOrder.getGroupNo();
		for(OrderDetailAddReq orderDetailAdd:orderDetailAddList) {
			VOOrderDetail voOrderDetailAdd = new VOOrderDetail();
			baseCopyObj.copyWithNull(voOrderDetailAdd,orderDetailAdd);
			voOrderDetailAdd.setOrderNo(orderNo);
			voOrderDetailAdd.setGroupNo(groupNo);
			voOrderDetailAdd.setItemId(orderDetailAdd.getItemId());
			voOrderDetailAdd.setBuyNums(orderDetailAdd.getBuyNums());
			if(StringUtils.isNotBlank(orderDetailAdd.getPrice())){
				BigDecimal price=new BigDecimal(orderDetailAdd.getPrice());
				voOrderDetailAdd.setPrice(price);
				voOrderDetailAdd.setTotalPrice(price.multiply(new BigDecimal(orderDetailAdd.getBuyNums())));
				if(voOrderDetailAdd.getTotalPrice().doubleValue()>=1000000000L){
					throw new BusinessException(ResultEnumCode.ORDER_PRICE_MAX);
				}
            }

			voOrderDetailAdd.setCreateDate(dateStr);
			if(StringUtils.isNotBlank(orderDetailAdd.getOriginalPrice())){
				voOrderDetailAdd.setOriginalPrice(new BigDecimal(orderDetailAdd.getOriginalPrice()));
			}
			voOrderDetailAdd.setStateDate(dateStr);
			voOrderDetailAdd.setState(1);
            voOrderDetailAdd.setIsDetermine(0);
			orderDetailMapper.insertVOSelective(voOrderDetailAdd);
			Long detailId=voOrderDetailAdd.getDetailId();

			//样品
			VOOrderSample voOrderSample=new VOOrderSample();
			voOrderSample.setSampleNo(customCodeService.getNewSampleNo());
			voOrderSample.setSampleName(orderDetailAdd.getSampleName());
			voOrderSample.setState(1);
			voOrderSample.setOrderNo(orderNo);
			voOrderSample.setGroupNo(groupNo);
			voOrderSample.setCreateDate(dateStr);
			orderSampleService.insertSelective(voOrderSample);

			orderSampleRelateService.insertSelective(detailId,orderNo,groupNo,voOrderSample.getSampleNo());
		}
	}

    public VOOrderDetail selectByPrimaryKey(long parseLong) {
        return orderDetailMapper.selectVOByPrimaryKey(parseLong);
    }

    public List<OrderDetailDTO> selectListByMap(Map map){
    	return   orderDetailMapper.selectListByMap(map);
    }

	public List<OrderDetailDTO> selectSubListByMap(Map map){
		return   orderDetailMapper.selectSubListByMap(map);
	}

    public Long insertSelective(VOOrderDetail voOrderDetail){
        if(voOrderDetail.getIsDetermine()==null){
            voOrderDetail.setIsDetermine(0);
        }
		orderDetailMapper.insertVOSelective(voOrderDetail);
		return voOrderDetail.getDetailId();
	}

    public void updateByPrimaryKeySelective(VOOrderDetail voOrderDetail){
    	orderDetailMapper.updateVOByPrimaryKeySelective(voOrderDetail);
    }

    @Override
    public void addQuestionTmpGroup(BaseOrderDTO baseOrderDTO, JSONObject jsonObject) throws Exception{
        //第一次生成初始的偏好及推荐项目数据
        String oldGroup=StringUtils.isBlank(baseOrderDTO.getTmpGroupNo())?baseOrderDTO.getGroupNo():baseOrderDTO.getTmpGroupNo();
        String tmpGroup=orderUtilService.getNewGroupNo();

        if(jsonObject.containsKey("centerItemReq")){
            List<VOOrderDetail> detailList=centerRestTemplateService.getAnswerItem(baseOrderDTO.getOrderNo(),tmpGroup,jsonObject.get("centerItemReq"),"");
            if(!ValidationUtil.isEmpty(detailList)){
                insertForeach(detailList);
            }
        }

        Map map=new HashMap();
        map.put(SelectMapUtil.GROUP_NO,oldGroup);
        List<VOOrderAttribute> list=orderAttributeService.selectVOListByMap(map);
        for(VOOrderAttribute voOrderAttribute:list){
            voOrderAttribute.setGroupNo(tmpGroup);
            voOrderAttribute.setCreateDate(UseDateUtil.getDateString(new Date()));
            //当他是币种的情况下 去重新赋值
            if(AttributeUtil.CURRENCY.equals(voOrderAttribute.getAttrValue())){
                voOrderAttribute.setAttrName("CNY");
            }
            if(AttributeUtil.EXCHANGE_RATE.equals(voOrderAttribute.getAttrValue())){
                voOrderAttribute.setAttrName("1.00");
            }
        }
        if(list.size()>0){
            orderAttributeService.insertForeach(list);
        }
       // orderSampleService.copySampleBySql(baseOrderDTO.getOrderNo(),oldGroup,tmpGroup);

        VOOrderBaseInfo voOrderBaseInfo=new VOOrderBaseInfo();
        voOrderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        voOrderBaseInfo.setTmpGroupNo(tmpGroup);
        voOrderBaseInfo.setSubState(0);
        orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfo);
        VOOrderGroup voOrderGroup=orderGroupService.getVOOrderGroup(baseOrderDTO.getOrderNo(),tmpGroup,2, GroupTypeEnum.CRM);
        orderGroupService.insert(voOrderGroup);
    }

    @Override
    public void remindPayment(VOOrderBaseInfo voOrderBaseInfo, BOSysPerson personInfo) throws Exception{
        if(ValidationUtil.isEmpty(voOrderBaseInfo) || ValidationUtil.isEmpty(voOrderBaseInfo.getOrderId())) {
            throw new BusinessException(ResultEnumCode.PARAMS_NULL);
        }

        VOOrderBaseInfo voOrderBaseInfoNew = orderBaseInfoService.selectByPrimaryKey(voOrderBaseInfo.getOrderId());
        if(ValidationUtil.isEmpty(voOrderBaseInfoNew)){
            throw  new BusinessException(ResultEnumCode.ORDER_NULL);
        }
        if(!ValidationUtil.isEmpty(voOrderBaseInfoNew.getPayState()) && !voOrderBaseInfoNew.getPayState().equals(OrderPayReceivedEnum.NO_PAY.getIndex())) {
            throw new BusinessException(ResultEnumCode.ORDER_STATE_CHANGE);
        }
        BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderId(voOrderBaseInfo.getOrderId());
        if(baseOrderDTO.getRealAmount()==null){
            throw new BusinessException(ResultEnumCode.PRICE_IS_NULL);
        }
        if(baseOrderDTO.getRealAmount().doubleValue()<=0){
            throw new BusinessException(ResultEnumCode.PRICE_IS_NULL);
        }

        orderOperatorLogDomainService.addLog(baseOrderDTO,OrderOperatorTypeEnum.OIQ_REMIND_PAYMENT, personInfo.getPersonCode());
        OiqMailEnum oiqMailEnum= OrderTypeEnum.isOiqOrder(baseOrderDTO.getOrderType())?OiqMailEnum.OIQ_REMIND_PAYMENT:OiqMailEnum.OIQ_PORTAL_REMIND_PAYMENT;
        mailEventUtil.sendMail(baseOrderDTO.getOrderNo(),baseOrderDTO.getOrderType(),oiqMailEnum, 1L);
        smsEventUtil.sendSms(baseOrderDTO.getOrderNo(),baseOrderDTO.getOrderType(),OiqSmsEnum.OIQ_REMIND_PAYMENT);
        if("2700".equals(baseOrderDTO.getBu())){
            return;
        }
        apiEventUtil.sendWechatMsg(voOrderBaseInfoNew.getOrderNo(), EventEnum.SEND_WECHAT_MSG, WechatEnum.OIQ_REMIND_PAYMENT);
    }


    public OrderDetailMoreDTO getOrderDetailMoreDTO(List<OrderDetailDTO> orderDetailDTOList, List<OrderApplicationAttrDTO> orderApplicationAttrDTOList, String ruler) {
        // 代表取  2-1  如果是检测标准的话是需要判断是否是采用标准
        OrderDetailMoreDTO orderDetailMoreDTO = new OrderDetailMoreDTO();
        String testItem = ""; //检测项目
        String standardCode = "";//检测标准
        Map<Integer, List<OrderDetailDTO>> orderDetailGroup = new HashMap<>();
        List<OrderDetailDTO> detailDTOList1 = new ArrayList<>();
        List<OrderDetailDTO> detailDTOList2 = new ArrayList<>();
        if (!ValidationUtil.isEmpty(orderDetailDTOList)) {
            orderDetailGroup= orderDetailDTOList.stream().collect(Collectors.groupingBy(E -> E.getItemType()));
            detailDTOList1 = orderDetailGroup.get(1);
            detailDTOList2 = orderDetailGroup.get(2);
        }
        testItem =!ValidationUtil.isEmpty(detailDTOList2) ? detailDTOList2.stream().filter(orderDetailDTO -> !ValidationUtil.isEmpty(orderDetailDTO.getItemName())).map(OrderDetailDTO::getItemName).collect(Collectors.joining("；")):
                        !ValidationUtil.isEmpty(detailDTOList1) ? detailDTOList1.stream().filter(orderDetailDTO -> !ValidationUtil.isEmpty(orderDetailDTO.getTestName())).map(OrderDetailDTO::getTestName).collect(Collectors.joining("；")):"";

        if(!ValidationUtil.isEmpty(orderApplicationAttrDTOList)){
            standardCode = orderApplicationAttrDTOList.stream().filter(orderApplicationAttrDTO1 -> !ValidationUtil.isEmpty(orderApplicationAttrDTO1.getAttrValue())).map(OrderApplicationAttrDTO::getAttrValue).collect(Collectors.joining("、"));
        }
        if(standardCode.length() == 0){
            standardCode =!ValidationUtil.isEmpty(detailDTOList2)?detailDTOList2.stream().filter(orderDetailDTO -> !ValidationUtil.isEmpty(orderDetailDTO.getStandardCode())).map(OrderDetailDTO::getStandardCode).collect(Collectors.joining("；")):
                            !ValidationUtil.isEmpty(detailDTOList1)?detailDTOList1.stream().filter(orderDetailDTO -> !ValidationUtil.isEmpty(orderDetailDTO.getStandardCode())).map(OrderDetailDTO::getStandardCode).collect(Collectors.joining("；")):"";

        }
        orderDetailMoreDTO.setStandardCode(standardCode);
        orderDetailMoreDTO.setTestItem(testItem);
        orderDetailMoreDTO.setPackageName(ValidationUtil.isEmpty(detailDTOList1)?"":!ValidationUtil.isEmpty(detailDTOList1.get(0).getItemName())?"【"+detailDTOList1.get(0).getItemName()+"】":"");
        orderDetailMoreDTO.setPointsNum(ValidationUtil.isEmpty(detailDTOList1)?"":!ValidationUtil.isEmpty(detailDTOList1.get(0).getPointsNum())?"【"+detailDTOList1.get(0).getPointsNum()+"】":"");

        return orderDetailMoreDTO;
    }


    public List<OrderDetailDTO> selectOrderDetailListByGroup(String orderNo, String selectGroup) {
        return memberRestTemplateService.getDetailByGroupNo(orderNo,selectGroup);
    }





    public void delOrderDetailByGroup(String groupNo) {
        orderDetailMapper.delOrderDetailByGroup(groupNo);
    }

    @Master
    @Transactional
    public String saveOrderSample(OrderSampleReq orderSampleReq, BOSysPerson boSysPerson, PrivilegeLevelDTO privilegeLevelDTO)throws Exception{

        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderSampleReq.getOrderId(),boSysPerson,privilegeLevelDTO);


        if(String.valueOf(orderBaseInfoCheckDTO.getState()).equals(OrderStateEnum.CLOSE.getIndex())){
            throw new BusinessException(ResultEnumCode.ORDER_STATE_ERROR);
        }
        String orderNo=orderBaseInfoCheckDTO.getOrderNo();
        String tmpGroupNo=getSelectGroup(orderBaseInfoCheckDTO);

        if(StringUtils.isBlank(orderSampleReq.getSampleNo())){
            String sampleNo=orderSampleService.insertSelective(orderSampleReq,orderBaseInfoCheckDTO.getOrderNo(),tmpGroupNo);
            orderSampleRelateService.insertByDetailId(orderNo,tmpGroupNo,sampleNo);
            return sampleNo;
        }
        //不为空的情况

        String sampleNo=orderSampleReq.getSampleNo();
        Map map=new HashMap();
        map.put(SelectMapUtil.ORDER_NO,orderNo);
        map.put(SelectMapUtil.GROUP_NO,tmpGroupNo);
        map.put("sampleNo",sampleNo);
        List<OrderSampleDTO> orderSampleDTOS=orderSampleService.selectListByMap(map);
        if(orderSampleDTOS.size()==0){
            //selectGroup新的情况下 无数据
            String sampleNo1=orderSampleService.insertSelective(orderSampleReq,orderBaseInfoCheckDTO.getOrderNo(),tmpGroupNo);
            orderSampleRelateService.insertByDetailId(orderNo,tmpGroupNo,sampleNo);
            return sampleNo1;
        }
        Long sampleId=orderSampleDTOS.get(0).getSampleId();



        orderSampleFromService.updateFormByState(orderNo,tmpGroupNo,sampleNo);
        List<OrderSampleFrom> list=new ArrayList<>();
        if(!ValidationUtil.isEmpty(orderSampleReq.getSampleFromDTOList())){
            for(OrderSampleFromDTO orderSampleFromDTO:orderSampleReq.getSampleFromDTOList()){
                OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
                baseCopyObj.copyWithNull(orderSampleFrom,orderSampleFromDTO);
                orderSampleFrom.setState(1);
                orderSampleFrom.setOrderNo(orderNo);
                orderSampleFrom.setGroupNo(tmpGroupNo);
                orderSampleFrom.setSampleNo(sampleNo);
                list.add(orderSampleFrom);
            }
            Map<String, Object> collectMap = orderSampleReq.getSampleFromDTOList().stream().collect(Collectors.toMap(E->E.getSampleKey(),
                    E->E.getSampleValue(), (key1, key2) -> key2));
            //样品重新赋值
            orderSampleReq.setSampleNameCn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_CN,"").toString());
            orderSampleReq.setSampleNameEn(collectMap.getOrDefault(OrderSampleFromDO.OIQ_SAMPLE_NAME_EN,"").toString());
        }

        orderSampleService.updateByPrimaryKeySelective(orderSampleReq,sampleId);
        orderSampleFromDomainService.insertForeachAddCenter(list,Long.parseLong(String.valueOf(orderBaseInfoCheckDTO.getApplicationLineId())));
        return sampleNo;
    }


    //获取当前要操作的group。若无历史保存新group且同步数据
    @Transactional
    public String getSelectGroup(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO){
        String tmpGroupNo="";
        // 是否有历史
        Boolean havaTmpGroupNo=false;

        if(StringUtils.isNotBlank(orderBaseInfoCheckDTO.getTmpGroupNo())){
            tmpGroupNo=orderBaseInfoCheckDTO.getTmpGroupNo();//
            havaTmpGroupNo=true;
        }else{
            tmpGroupNo=orderUtilService.getNewGroupNo();
        }

        if(!havaTmpGroupNo){//没历史的情况下
            VOOrderBaseInfo voOrderBaseInfoUpdate=new VOOrderBaseInfo();
            voOrderBaseInfoUpdate.setOrderId(orderBaseInfoCheckDTO.getOrderId());
            voOrderBaseInfoUpdate.setTmpGroupNo(tmpGroupNo);
            orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfoUpdate);

            //是否复制已经推送给用户的attribate 判断规则为 是否有groupNo；
            if(StringUtils.isNotBlank(orderBaseInfoCheckDTO.getGroupNo())){

                copyAttributeListFirst(orderBaseInfoCheckDTO, tmpGroupNo);
                String oldGroupNo=orderBaseInfoCheckDTO.getGroupNo();
                copyGroupByOldGroup(oldGroupNo,tmpGroupNo,orderBaseInfoCheckDTO.getOrderNo());

            }
        }

        return tmpGroupNo;

    }

    public void copyGroupByOldGroup(String oldGroupNo,String newGroupNo,String orderNo){
        Map mapCopy=new HashMap();
        mapCopy.put("oldGroupNo",oldGroupNo);
        mapCopy.put("newGroupNo",newGroupNo);
        mapCopy.put("orderNo",orderNo);
        mapCopy.put("newOrderNo",orderNo);
        orderDetailService.copyDetailBySql(mapCopy);
        //复制sample
        orderSampleService.copySampleBySql(orderNo,orderNo,oldGroupNo,newGroupNo);

        //复制关联关系
        orderSampleRelateService.copySampleRelateBySql(mapCopy);

        //更新关联关系
        orderSampleRelateService.updateSampleRelateBySql(mapCopy);
    }








    @Override
    public void copyDetailBySql(Map mapCopy) {
        orderDetailMapper.copyDetailBySql(mapCopy);
    }


    public void addBaseTmpGroup(BaseOrderDTO baseOrderDTO, JSONObject jsonObject) throws Exception{
        //第一次生成初始的偏好及推荐项目数据
        String tmpGroup=orderUtilService.getNewGroupNo();
        List<VOOrderAttribute> list=issoRestTemplateService.personAttr(baseOrderDTO,tmpGroup);
        if(!ValidationUtil.isEmpty(list)){
            orderAttributeService.insertForeach(list);
        }

        if(jsonObject.containsKey("centerItemReq")){
            List<VOOrderDetail> detailList=centerRestTemplateService.getAnswerItem(baseOrderDTO.getOrderNo(),tmpGroup,jsonObject.get("centerItemReq"),"");
            if(!ValidationUtil.isEmpty(detailList)){
                insertForeach(detailList);
            }
        }

        VOOrderBaseInfo voOrderBaseInfo=new VOOrderBaseInfo();
        voOrderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        voOrderBaseInfo.setTmpGroupNo(tmpGroup);
        orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfo);
        VOOrderGroup voOrderGroup=orderGroupService.getVOOrderGroup(baseOrderDTO.getOrderNo(),tmpGroup,2, GroupTypeEnum.CRM);
        orderGroupService.insert(voOrderGroup);
    }

    @Override
    public void modSampleRequirements(OrderSampleReq orderSampleReq, BOSysPerson boSysPerson, PrivilegeLevelDTO power) {
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(orderSampleReq.getOrderId(),boSysPerson,power);

        VOOrderBaseInfo voOrderBaseInfo=new VOOrderBaseInfo();
        voOrderBaseInfo.setOrderId(orderBaseInfoCheckDTO.getOrderId());
        voOrderBaseInfo.setSampleRequirements(orderSampleReq.getSampleRequirements());
        orderBaseInfoService.updateByPrimaryKeySelective(voOrderBaseInfo);
        orderLogService.addOrderOperatorLogByOrder(orderBaseInfoCheckDTO.getOrderNo(), boSysPerson.getPersonCode(),
            OrderOperatorTypeEnum.SAMPLE_REQUIREMENTS, orderSampleReq.getSampleRequirements(), "", null, null, null);

    }

    public void insertForeach(List<VOOrderDetail> orderDetailList){
        orderDetailMapper.insertForeach(orderDetailList);
    }



    //第一次暂存且已经推送给用户的情况First 是要保存到base和同步数据，
    public void copyAttributeListFirst(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, String newTmpGroupNo) {
        String oldGroupNo=orderBaseInfoCheckDTO.getGroupNo();
        if(StringUtils.isBlank(oldGroupNo)){
            return;
        }

        Map map=new HashMap();
        map.put(SelectMapUtil.GROUP_NO,oldGroupNo);
        List<VOOrderAttribute> list=orderAttributeService.selectVOListByMap(map);
        for(VOOrderAttribute voOrderAttribute:list){
            voOrderAttribute.setGroupNo(newTmpGroupNo);
            voOrderAttribute.setCreateDate(UseDateUtil.getDateString(new Date()));
        }

        if(list.size()>0){
            orderAttributeService.insertForeach(list);
        }

    }

    @Transactional
	@Override
	public void addTestItem(ExpDetailReq expDetailReq, BOSysPerson personInfo, PrivilegeLevelDTO power) {
		 OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(expDetailReq.getOrderId()+"",personInfo,power);
		if(StringUtils.isBlank(expDetailReq.getItemName()) || expDetailReq.getPrice() == null || expDetailReq.getBuyNums() <=0)
			throw new BusinessException(ResultEnumCode.OPERATE_ERROR);

		 VOOrderDetail voOrderDetail = new VOOrderDetail();
		 voOrderDetail.setOrderNo(orderBaseInfoCheckDTO.getOrderNo());
		 voOrderDetail.setItemName(expDetailReq.getItemName());
		 voOrderDetail.setPrice(expDetailReq.getPrice());
		 voOrderDetail.setBuyNums(expDetailReq.getBuyNums());
		 voOrderDetail.setTotalPrice(expDetailReq.getPrice().multiply(new BigDecimal(expDetailReq.getBuyNums())));
		 voOrderDetail.setCreateDate(UseDateUtil.getDateString(new Date()));
		 voOrderDetail.setStateDate(UseDateUtil.getDateString(new Date()));
		 voOrderDetail.setMemo(expDetailReq.getMemo());
		 voOrderDetail.setItemType("3");
		 voOrderDetail.setState(1);
		 insertSelective(voOrderDetail);
		 String text = expDetailReq.getItemName()+" 数量："+expDetailReq.getBuyNums()+" 单价："+expDetailReq.getPrice();
		 if(!StringUtils.isBlank(expDetailReq.getMemo())){
			 text += " 备注："+expDetailReq.getMemo();
		 }
		 BaseLog baseLog = new BaseLog(orderBaseInfoCheckDTO.getOrderNo(), "100000",
				 OrderOperatorTypeEnum.TIC_ADD,text,null, personInfo.getPersonCode(),1);

		 VOOrderOperatorLog voOrderOperatorLog = new VOOrderOperatorLog();
		 voOrderOperatorLog.setCurrency("CNY");
		 orderOperatorLogService.addLogByBase(voOrderOperatorLog , baseLog);

	}

    @Transactional
	@Override
	public void updateTestItem(ExpDetailReq expDetailReq, BOSysPerson personInfo, PrivilegeLevelDTO power) {
		 OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(expDetailReq.getOrderId()+"",personInfo,power);
		 if(StringUtils.isBlank(expDetailReq.getItemName()) || expDetailReq.getPrice() == null || expDetailReq.getBuyNums() <=0)
				throw new BusinessException(ResultEnumCode.OPERATE_ERROR);


		 Map<String, Object> detailMap=new HashMap<>();
         detailMap.put(SelectMapUtil.DETAIL_ID,expDetailReq.getDetailId());

         VOOrderDetail detailDto = selectByPrimaryKey(expDetailReq.getDetailId());
		 String textBefore = detailDto.getItemName()+" 数量："+detailDto.getBuyNums()+" 单价："+detailDto.getPrice();
		 if(!StringUtils.isBlank(detailDto.getMemo())){
			 textBefore += " 备注："+detailDto.getMemo();
		 }
		 String textAfter =" 修改后补充测试项目："+expDetailReq.getItemName()+" 数量："+expDetailReq.getBuyNums()+" 单价："+expDetailReq.getPrice();
		 if(!StringUtils.isBlank(expDetailReq.getMemo())){
			 textAfter += " 备注："+expDetailReq.getMemo();
		 }

		 VOOrderDetail voOrderDetail = new VOOrderDetail();
		 voOrderDetail.setDetailId(expDetailReq.getDetailId());
		 voOrderDetail.setOrderNo(orderBaseInfoCheckDTO.getOrderNo());
		 voOrderDetail.setItemName(expDetailReq.getItemName());
		 voOrderDetail.setPrice(expDetailReq.getPrice());
		 voOrderDetail.setBuyNums(expDetailReq.getBuyNums());
		 voOrderDetail.setTotalPrice(expDetailReq.getPrice().multiply(new BigDecimal(expDetailReq.getBuyNums())));
		 voOrderDetail.setStateDate(UseDateUtil.getDateString(new Date()));
		 voOrderDetail.setMemo(expDetailReq.getMemo());
		 updateByPrimaryKeySelective(voOrderDetail);

		 BaseLog baseLog = new BaseLog(orderBaseInfoCheckDTO.getOrderNo(), "100000",
				 OrderOperatorTypeEnum.TIC_MOD,textBefore+textAfter, null, personInfo.getPersonCode(),1);

		 VOOrderOperatorLog voOrderOperatorLog = new VOOrderOperatorLog();
		 voOrderOperatorLog.setCurrency("CNY");
		 orderOperatorLogService.addLogByBase(voOrderOperatorLog , baseLog);
	}

    @Transactional
	@Override
	public void deleteTestItem(ExpDetailReq expDetailReq, BOSysPerson personInfo, PrivilegeLevelDTO power) {
		 OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.checkOrderBase(expDetailReq.getOrderId()+"",personInfo,power);

        VOOrderDetail detailDto = selectByPrimaryKey(expDetailReq.getDetailId());

        VOOrderDetail voOrderDetail = new VOOrderDetail();
		 voOrderDetail.setDetailId(expDetailReq.getDetailId());
		 voOrderDetail.setState(0);
		 updateByPrimaryKeySelective(voOrderDetail);

		 Map<String, Object> detailMap=new HashMap<>();
         detailMap.put(SelectMapUtil.DETAIL_ID,expDetailReq.getDetailId());

		 String text = detailDto.getItemName()+" 数量： "+detailDto.getBuyNums()+" 单价： "+detailDto.getPrice();
		 if(!StringUtils.isBlank(detailDto.getMemo())){
			 text += " 备注： "+detailDto.getMemo();
		 }
		 BaseLog baseLog = new BaseLog(orderBaseInfoCheckDTO.getOrderNo(), "100000",
				 OrderOperatorTypeEnum.TIC_DEL,text, null, personInfo.getPersonCode(),1);

		 VOOrderOperatorLog voOrderOperatorLog = new VOOrderOperatorLog();
		 voOrderOperatorLog.setCurrency("CNY");
		 orderOperatorLogService.addLogByBase(voOrderOperatorLog , baseLog);

	}


	@Override
	public JSONArray queryTestItem(OrderReq orderReq, BOSysPerson personInfo) throws Exception {
		 OrderBaseInfoCheckDTO orderBaseInfoCheckDTO=orderBaseInfoService.qryBase(orderReq.getOrderId());
		 Map<String, Object> detailMap=new HashMap<>();
         detailMap.put(SelectMapUtil.ORDER_NO,orderBaseInfoCheckDTO.getOrderNo());
         detailMap.put(SelectMapUtil.ITEM_TYPE,3);
         detailMap.put(SelectMapUtil.STATE,1);
         return jsonTransUtil.toJSONArray(OrderDetailDTO.class, selectListByMap(detailMap), BaseQryFilter.Default.class);
	}


    public List<OrderDetailDTO> selectListByGroupMap(Map map) {
        return orderDetailMapper.selectListByGroupMap(map);
    }

    @Transactional
    @Override
    public OrderChangePriceDTO changeOrderPrice(VOChangeOrderPrice changeOrderPrice, BOSysPerson personInfo) throws Exception {
        String dateStr= UseDateUtil.getDateString(new Date());

        // 移除前端传过来的最低售价明细
        Optional.ofNullable(changeOrderPrice.getParentDetails()).ifPresent(
            parentDetails -> parentDetails.forEach(
                parentDetail -> Optional.ofNullable(parentDetail.getSubDetails()).ifPresent(
                    subDetails -> subDetails.removeIf(
                        subDetail -> subDetail != null && subDetail.isLowestPriceDetail()))));

        //先拿订单号查询数据
        BaseOrderDTO baseOrderDTO = orderBaseInfoDomainService.selectBaseByOrderNo(changeOrderPrice.getOrderNo());
        orderBaseInfoDomainService.checkChangePriceData(changeOrderPrice,baseOrderDTO);
        computeAmount(changeOrderPrice,baseOrderDTO);
        /**
         * 改价处理
         * 1、对于detail来说不需要计算优惠金额，DETAIL直接存储即可
         * 2、将parent的总金额累加后分摊，需要增加每个parent总金额与这个parent下detail累加的金额一致
          */
        List<OrderAttachment> fileList = new ArrayList<>();
        List<OrderLogAttachmentDTO> addFileList = new ArrayList<>();//新增附件
        StringBuilder sb = new StringBuilder();
        Map<String,String> flagMap =initFlagMap();
        operatorLogDomainService.addPriceLog( baseOrderDTO,changeOrderPrice,sb);
        // 校验product金额与detail是否一致，返回detail信息
        orderDetailsService.checkParentPrice(changeOrderPrice.getParentDetails());

        // 处理 product数据
        orderProductDomainService.recalculateOrderProduct(baseOrderDTO, changeOrderPrice.getParentDetails(),flagMap,dateStr,changeOrderPrice);

        // 处理detail数据
        orderDetailsService.dealOrderDetailByNo(baseOrderDTO.getOrderNo(), changeOrderPrice.getParentDetails(),dateStr,flagMap,fileList,addFileList,baseOrderDTO);
        //删除附件表
        orderAttachmentDomainService.delByOrderNo(baseOrderDTO.getOrderNo(), OrderAttachmentTypeEnum.PACKAGE_DETAIL_FILES);
        //更新 附件表
        orderAttachmentDomainService.batchInsert(fileList);
        //更新orderBaseInfo表
        orderBaseInfoDomainService.updateAmountToBaseOrder(baseOrderDTO,personInfo,changeOrderPrice,flagMap);
        //记录日志
        Boolean isMonth = saveLog(baseOrderDTO, sb, personInfo, changeOrderPrice.getRealAmount(),addFileList,flagMap);
//        int c = 1/0;
        OrderChangePriceDTO orderChangePriceDTO = new OrderChangePriceDTO(isMonth,baseOrderDTO);
        return orderChangePriceDTO;
    }

    @Override
    public void sendChangeMailAndSms(OrderChangePriceDTO orderChangePriceDTO) {
        // TODO 发送邮件
        sendMsg(orderChangePriceDTO.getMonth(),orderChangePriceDTO.getBaseOrderDTO());
    }

    public void sendMsg(Boolean isMonth, BaseOrderDTO baseOrderDTO) {
        TicOtherMailDTO ticOtherMailDTO = new  TicOtherMailDTO();
        OrderPriceReq orderPriceReq = new OrderPriceReq();
        orderPriceReq.setPrice(baseOrderDTO.getRealAmount().toString());
        ticOtherMailDTO.setOrderPriceReq(orderPriceReq);
        //发送邮件
        if(isMonth){
            eventMailUtil.sendTicMail(baseOrderDTO.getOrderId(), OiqMailEnum.TIC_SERVICE_CHANGE_PRICE_AB,1L,ticOtherMailDTO);
            eventSmsUtil.sendSMS(baseOrderDTO.getOrderId(), OiqSmsEnum.TIC_SERVICE_CHANGE_PRICE_AB);
        }else{
            eventMailUtil.sendTicMail(baseOrderDTO.getOrderId(), OiqMailEnum.TIC_SERVICE_CHANGE_PRICE,1L,ticOtherMailDTO);
            eventSmsUtil.sendSMS(baseOrderDTO.getOrderId(), OiqSmsEnum.TIC_SERVICE_CHANGE_PRICE);
        }
    }

    private Boolean saveLog(BaseOrderDTO baseOrderDTO, StringBuilder sb, BOSysPerson personInfo, BigDecimal realAmount, List<OrderLogAttachmentDTO> addFileList, Map<String, String> flagMap) {
        appendLogs(sb,flagMap);


        Boolean isMonth = baseOrderDTO.getMonthPay() != 1 && baseOrderDTO.getMonthPay() != 3 ?false:true;
        OrderOperatorTypeEnum orderOperatorTypeEnum=isMonth? OrderOperatorTypeEnum.MONTH_CHANGE: OrderOperatorTypeEnum.CHANGEPRICE;
        orderOperatorLogService.addLogByPrice(new BaseLog(baseOrderDTO.getOrderNo(), String.valueOf(baseOrderDTO.getOrderType()), orderOperatorTypeEnum,
                sb.toString(), null, personInfo.getPersonCode(), 1), realAmount, baseOrderDTO.getRealAmount(), com.alibaba.fastjson.JSONObject.toJSONString(addFileList));
        return isMonth;
    }

    private void appendLogs(StringBuilder sb, Map<String, String> flagMap) {
        StringBuilder momoSb = new StringBuilder();//测试备注
        momoSb.append("ok".equals(flagMap.get("addOrDelServiceProject"))?" 增减客服定制项目；":"")
                .append("ok".equals(flagMap.get("customerUpdate"))?" 增加测点；":"")
                .append("ok".equals(flagMap.get("serviceUpdate"))?" 客服定制-增加测点；":"")
                .append("ok".equals(flagMap.get("updateServicePrice"))?" 编辑客服定制-单价；":"")
                .append("ok".equals(flagMap.get("customerMemo"))?" 编辑测试备注；":"")
                .append("ok".equals(flagMap.get("serviceMemo"))?" 编辑客服定制-测试备注；":"")
                .append("ok".equals(flagMap.get("servicePackage"))?" 编辑客服定制-测试套餐；":"")
                .append("ok".equals(flagMap.get("serviceStander"))?" 编辑客服定制-测试标准；":"")
                .append("ok".equals(flagMap.get("serviceDiscount"))?" 编辑客服折扣；":"")
                .append("ok".equals(flagMap.get("updateUrgent"))?" 编辑测试周期；":"")
                .append("ok".equals(flagMap.get("customerMemoFile"))?" 编辑测试文件；":"")
                .append("ok".equals(flagMap.get("serviceMemoFile"))?" 客服定制-编辑测试文件；":"");
        if(momoSb.length()>0){
            sb.append(" 改价理由：").append(momoSb.toString());
        }
    }

    private Map<String, String> initFlagMap() {
        Map<String,String> flagMap = new HashMap<>();
//        String addOrDelServiceProject = "fail";//【添加/删除自定义项目】：编辑客服定制项目；
//        String customerUpdate = "fail";//【数量】（每个套餐的对应数量，非总数量）：添加测点;
//        String serviceUpdate = "fail";//【数量】（每个套餐的对应数量，非总数量）：   客服定制-增加测点
//        String updateServicePrice = "fail";//【单价】：客服定制-单价
//        String customerMemo = "fail";//【测试备注】：测试备注；客服定制-测试备注；
//        String serviceMemo = "fail";//【测试备注】：测试备注；客服定制-测试备注；
//        String customerMemoFile = "fail";//【测试备注文件XXX】；添加测试文件； 客服定制-添加测试文件：{文件1}；{文件2}；{文件x}     点击可预览；
//        String serviceMemoFile = "fail";//【测试备注文件XXX】；添加测试文件； 客服定制-添加测试文件：{文件1}；{文件2}；{文件x}     点击可预览；
//        String servicePackage = "fail";//【测试套餐】：客服定制-测试套餐；
//        String serviceStander = "fail";//【测试标准】：客服定制-测试标准；
        flagMap.put("addOrDelServiceProject","fail");
        flagMap.put("customerUpdate","fail");
        flagMap.put("serviceUpdate","fail");
        flagMap.put("updateServicePrice","fail");
        flagMap.put("customerMemo","fail");
        flagMap.put("serviceMemo","fail");
        flagMap.put("customerMemoFile","fail");
        flagMap.put("serviceMemoFile","fail");
        flagMap.put("servicePackage","fail");
        flagMap.put("serviceStander","fail");
        return flagMap;
    }

    private void computeAmount(VOChangeOrderPrice changeOrderPrice, BaseOrderDTO baseOrderDTO) throws Exception {
        String subBuCode = orderProductDomainService.qrySubBuCode(baseOrderDTO.getOrderNo(), baseOrderDTO.getBu());
        Integer isUrgent = changeOrderPrice.getIsUrgent();
        BigDecimal urgentRange = redisUtils.qryUrgentToRedis(subBuCode, isUrgent,changeOrderPrice);
        if(ValidationUtil.isEmpty(urgentRange))
            throw new BusinessException(ResultCode.ENUM_IS_NULL);


        urgentRange = urgentRange.divide(new BigDecimal(100),2, BigDecimal.ROUND_HALF_UP);
//        BigDecimal urgentRange =new BigDecimal(collect.get(0));//拿到加急折扣率
        BigDecimal realAmount = changeOrderPrice.getRealAmount();
        BigDecimal shopDisAmount = ValidationUtil.isEmpty(baseOrderDTO.getShopDisAmount())?BigDecimal.ZERO:baseOrderDTO.getShopDisAmount();
        BigDecimal discountAmount =ValidationUtil.isEmpty(baseOrderDTO.getDiscountAmount())?BigDecimal.ZERO:baseOrderDTO.getDiscountAmount();
        BigDecimal newOrderAmount = BigDecimal.ZERO;
        for (VOOrderDetail detail : changeOrderPrice.getParentDetails()) {
            newOrderAmount = newOrderAmount.add(detail.getPrice().multiply(new BigDecimal(detail.getBuyNums())));
        }
        changeOrderPrice.setOrderAmount(newOrderAmount);
        //计算加急费
        changeOrderPrice.setUrgentAmount(realAmount.divide((urgentRange.add(BigDecimal.ONE)), 8, BigDecimal.ROUND_HALF_UP).multiply(urgentRange).setScale(2, BigDecimal.ROUND_HALF_UP));
        //客服优惠后的金额 = 订单实付金额-订单实付金额*加急折扣率
        BigDecimal realCsAfterAmount = realAmount.subtract(changeOrderPrice.getUrgentAmount());
        changeOrderPrice.setRealCsAfterAmount(realCsAfterAmount);
        //计算客服折扣金额
        BigDecimal subtract = changeOrderPrice.getOrderAmount().subtract(shopDisAmount).subtract(discountAmount);
        changeOrderPrice.setCsDiscountAmount(subtract.subtract(realCsAfterAmount));
        //计算客服折扣率
        BigDecimal csDisCountRate = realCsAfterAmount.divide(subtract, 6, BigDecimal.ROUND_HALF_UP);
        changeOrderPrice.setCsDisCountRate(csDisCountRate);


    }


}
