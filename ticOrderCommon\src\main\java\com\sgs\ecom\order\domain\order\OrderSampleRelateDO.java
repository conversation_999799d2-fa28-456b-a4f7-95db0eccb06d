package com.sgs.ecom.order.domain.order;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OiqSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleDTO;
import com.sgs.ecom.order.dto.order.OrderSampleRelateDTO;
import com.sgs.ecom.order.entity.order.OrderDetail;
import com.sgs.ecom.order.entity.order.OrderSampleRelate;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class OrderSampleRelateDO extends OrderSampleRelate {


   public OrderSampleRelate getSampleRelate(OrderDetail orderDetail, String sampleNo){
       OrderSampleRelate orderSampleRelate=new OrderSampleRelate();
       orderSampleRelate.setDetailId(orderDetail.getDetailId());
       orderSampleRelate.setOrderNo(orderDetail.getOrderNo());
       orderSampleRelate.setGroupNo(orderDetail.getGroupNo());
       orderSampleRelate.setSampleNo(sampleNo);
       orderSampleRelate.setState(1);
       orderSampleRelate.setCreateDate(UseDateUtil.getDateString(new Date()));
       orderSampleRelate.setStateDate(UseDateUtil.getDateString(new Date()));
       return orderSampleRelate;
   }
    public OrderSampleRelate getSampleRelate(String orderNo,String groupNo, String sampleNo,Long detailId){
       if(StringUtil.isBlank(sampleNo)||detailId==null){
           return null;
       }

        OrderSampleRelate orderSampleRelate=new OrderSampleRelate();
        orderSampleRelate.setDetailId(detailId);
        orderSampleRelate.setOrderNo(orderNo);
        orderSampleRelate.setGroupNo(groupNo);
        orderSampleRelate.setSampleNo(sampleNo);
        orderSampleRelate.setState(1);
        orderSampleRelate.setCreateDate(UseDateUtil.getDateString(new Date()));
        orderSampleRelate.setStateDate(UseDateUtil.getDateString(new Date()));
        return orderSampleRelate;
    }



    public List<OiqSampleDTO>  getSampleListByRelate(List<OrderSampleRelateDTO> list, Map<String,OiqSampleDTO> sampleReqMap){
        if(ValidationUtil.isEmpty(list) ){
            return new ArrayList<>();
        }
        if(ValidationUtil.isEmpty(sampleReqMap) ){
            return new ArrayList<>();
        }
        List<OiqSampleDTO> sampleDTOList=new ArrayList<>();
        for(int n=0;n<list.size();n++){
            OrderSampleRelateDTO orderSampleRelateDTO=list.get(n);
            OiqSampleDTO base=new OiqSampleDTO();
            BaseCopyObj baseCopyObj=new BaseCopyObj();
            baseCopyObj.copyWithNull(base,sampleReqMap.getOrDefault(orderSampleRelateDTO.getSampleNo(),new OiqSampleDTO()));
            if(!ValidationUtil.isEmpty(base)){
                base.setSampleFromDTOList(new ArrayList<>());
                sampleDTOList.add(base);
            }
        }
        return sampleDTOList;
    }

    //LV3月结改价
    public OrderSampleRelate saveMonthOrderSampleRelate(OrderSampleDTO orderSampleDTO, String orderNo, String dateStr, String groupNo) {
        OrderSampleRelate orderSampleRelate = new OrderSampleRelateDO();
        orderSampleRelate.setOrderNo(orderNo);
        orderSampleRelate.setDetailId(orderSampleDTO.getDetailId());
        orderSampleRelate.setState(1);
        orderSampleRelate.setCreateDate(dateStr);
        orderSampleRelate.setStateDate(dateStr);
        orderSampleRelate.setGroupNo(groupNo);
        orderSampleRelate.setSampleNo(orderSampleDTO.getSampleNo());
        orderSampleRelate.setBuyNums(orderSampleDTO.getBuyNums());
        return orderSampleRelate;
    }

    //待提交申请表的时候
    public OrderSampleRelate saveSampleRelateBeforeSubmit(OrderSampleRelateDTO orderSampleRelateDTO, Map<Long, Long> updateMap, String orderNo, String dateStr, String groupNo) {
        OrderSampleRelate orderSampleRelate = new OrderSampleRelateDO();
        orderSampleRelate.setOrderNo(orderNo);
        orderSampleRelate.setDetailId(updateMap.get(Long.parseLong(orderSampleRelateDTO.getDetailId())));
        orderSampleRelate.setState(1);
        orderSampleRelate.setCreateDate(dateStr);
        orderSampleRelate.setStateDate(dateStr);
        orderSampleRelate.setGroupNo(groupNo);
        orderSampleRelate.setSampleNo(orderSampleRelateDTO.getSampleNo());
        orderSampleRelate.setBuyNums(orderSampleRelateDTO.getBuyNums());
        return orderSampleRelate;
    }
}
