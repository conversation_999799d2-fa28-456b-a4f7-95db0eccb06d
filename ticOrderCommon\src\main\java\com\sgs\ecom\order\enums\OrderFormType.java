package com.sgs.ecom.order.enums;
/**
 * @Description :申请表类型
 * <AUTHOR>
 * @Date  2024/1/19
 **/
public enum OrderFormType {

	HL("HL", "HL-NB"),

    ;


    // 成员变量
    private String name;
    private String index;
    // 构造方法
    private OrderFormType(String name, String index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(String index) {  
        for (OrderFormType c : OrderFormType.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public String getIndex() {  
        return index;  
    }   
}
