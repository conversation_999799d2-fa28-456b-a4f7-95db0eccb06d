package com.sgs.ecom.order.enums;

import com.platform.annotation.ExplainAnno;
import com.platform.enums.EnumMessage;

import javax.persistence.criteria.CriteriaBuilder;

@ExplainAnno(value="enum",name="订单附件类型")
public enum OrderAttachmentType implements EnumMessage {

	REPORT("报告文件", 1),
	INVOICE_REPORT("发票文件", 2);

    // 成员变量
    private String name;
    private Integer index;
    // 构造方法
    private OrderAttachmentType(String name, Integer index) {
        this.name = name;  
        this.index = index;  
    }  

    // 普通方法  
    public static String getName(Integer index) {
        for (OrderAttachmentType c : OrderAttachmentType.values()) {
            if (c.getIndex().equals(index)) {  
                return c.name;  
            }  
        }  
        return null;  
    }  
    // get set 方法  
    public String getName() {  
        return name;  
    }  

    public Integer getIndex() {
        return index;  
    }  

	@Override
	public String getKey() {
		return String.valueOf(index);
	}

	@Override
	public String getValue() {
		return name;
	}  
}
