package com.sgs.ecom.order.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.enumtool.application.TestCycleEnum;
import com.sgs.ecom.order.util.collection.StrUtil;


public class OiqOrderBaseDTO {

    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String bu;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String businessCode;
    //sgs业务顾问
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String csEmail;
    //sgs业务顾问
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String businessPersonEmail;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private Long labId;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private Integer isUrgent = 1;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String isUrgentShow;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String businessName;

    //返回订单创建时间
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private int state;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String createDate;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String orderNo;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String labName;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String salesEmail;
    private Long lineId;
    private Long applicationLineId;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private String orderType;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private Long userId;
    @ApiAnno(groups = {BaseOrderFilter.OiqFormInfo.class})
    private Long orderId;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public Integer getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(Integer isUrgent) {
        this.isUrgent = isUrgent;
    }

    public String getBu() {
        return bu;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }

    public String getBusinessPersonEmail() {
        return businessPersonEmail;
    }

    public void setBusinessPersonEmail(String businessPersonEmail) {
        this.businessPersonEmail = businessPersonEmail;
    }

    public String getCreateDate() {
        return StrUtil.isTime(createDate);
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }

    public String getIsUrgentShow() {
        if (isUrgent == null) {
            return "";
        }
        return TestCycleEnum.getNameCh(isUrgent.toString());
    }

    public void setIsUrgentShow(String isUrgentShow) {
        this.isUrgentShow = isUrgentShow;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }

    public Long getLineId() {
        return lineId;
    }

    public void setLineId(Long lineId) {
        this.lineId = lineId;
    }

    public Long getApplicationLineId() {
        return applicationLineId;
    }

    public void setApplicationLineId(Long applicationLineId) {
        this.applicationLineId = applicationLineId;
    }

    public String getSalesEmail() {
        return salesEmail;
    }

    public void setSalesEmail(String salesEmail) {
        this.salesEmail = salesEmail;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }
}
