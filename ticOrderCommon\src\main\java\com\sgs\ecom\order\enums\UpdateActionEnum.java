package com.sgs.ecom.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 更新行为枚举类
 * @date 2025/3/4 15:43
 */
@Getter
@AllArgsConstructor
public enum UpdateActionEnum {

    UPDATE_BUSINESS_PERSON_MAIL("businessPersonEmail", "更新业务顾问邮箱"),
    UPDATE_SALES_MAIL("salesEmail", "更新销售顾问邮箱"),
    ;

    private final String code;
    private final String des;

    public static UpdateActionEnum getEnum(String code) {
        for (UpdateActionEnum actionEnum : UpdateActionEnum.values()) {
            if (actionEnum.getCode().equals(code)) {
                return actionEnum;
            }
        }
        return null;
    }
}
