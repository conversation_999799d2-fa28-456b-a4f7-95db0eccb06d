package com.sgs.ecom.order.bo; 
 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="")
public class BOTicApplicationFormCPCH{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "TIC_ApplicationFormCPCH"; 
 
 	public static final String OWNER ="bbc";

 	public static final String HAZARDDESCRIPTIONTYPE="hazarddescriptiontype";
 	public static final String REPORTADDRESS="reportaddress";
 	public static final String COMPANYNAMEEN="companynameen";
 	public static final String REPORTPROVICE="reportprovice";
 	public static final String STORAGEREQUIREMENT="storagerequirement";
 	public static final String ORDERID="orderid";
 	public static final String SPECIALREMARKS="specialremarks";
 	public static final String BRANDNAME="brandname";
 	public static final String ID="id";
 	public static final String OTHERREQUIREMENTS="otherrequirements";
 	public static final String REPORTSHOW="reportshow";
 	public static final String APPFORMID="appformid";
 	public static final String SAMPLENAME="samplename";
 	public static final String PRODUCTDATE="productdate";
 	public static final String RESULTJUDGE="resultjudge";
 	public static final String STORAGEREQUIREMENTTYPE="storagerequirementtype";
 	public static final String HAZARDDESCRIPTION="hazarddescription";
 	public static final String REPORTDISTRICT="reportdistrict";
 	public static final String COMPANYNAMEADD="companynameadd";
 	public static final String QRCODE="qrcode";
 	public static final String REPORTTITLE="reporttitle";
 	public static final String HAZARDDESCRIPTIONREMARKS="hazarddescriptionremarks";
 	public static final String REPORTCITY="reportcity";
 	public static final String STORAGEREQUIREMENTREMARKS="storagerequirementremarks";
 	public static final String PRODUCTERNAME="productername";

 	@BeanAnno("HazardDescriptionType")
 	private String hazarddescriptiontype;
 	@BeanAnno("ReportAddress")
 	private String reportaddress;
 	@BeanAnno("CompanyNameEN")
 	private String companynameen;
 	@BeanAnno("ReportProvice")
 	private String reportprovice;
 	@BeanAnno("StorageRequirement")
 	private String storagerequirement;
 	@BeanAnno("OrderID")
 	private String orderid;
 	@BeanAnno("SpecialRemarks")
 	private String specialremarks;
 	@BeanAnno("BrandName")
 	private String brandname;
 	@BeanAnno("ID")
 	private String id;
 	@BeanAnno("OtherRequirements")
 	private String otherrequirements;
 	@BeanAnno("ReportShow")
 	private String reportshow;
 	@BeanAnno("AppFormID")
 	private String appformid;
 	@BeanAnno("SampleName")
 	private String samplename;
 	@BeanAnno("ProductDate")
 	private String productdate;
 	@BeanAnno("ResultJudge")
 	private String resultjudge;
 	@BeanAnno("StorageRequirementType")
 	private String storagerequirementtype;
 	@BeanAnno("HazardDescription")
 	private String hazarddescription;
 	@BeanAnno("ReportDistrict")
 	private String reportdistrict;
 	@BeanAnno("CompanyNameAdd")
 	private String companynameadd;
 	@BeanAnno("QRCode")
 	private String qrcode;
 	@BeanAnno("ReportTitle")
 	private String reporttitle;
 	@BeanAnno("HazardDescriptionRemarks")
 	private String hazarddescriptionremarks;
 	@BeanAnno("ReportCity")
 	private String reportcity;
 	@BeanAnno("StorageRequirementRemarks")
 	private String storagerequirementremarks;
 	@BeanAnno("ProducterName")
 	private String productername;

 	@CharacterVaild(len = 200) 
 	public void setHazarddescriptiontype(String hazarddescriptiontype){
 		 this.hazarddescriptiontype=hazarddescriptiontype;
 	}
 	@CheckAnno(len = 200) 
 	public String getHazarddescriptiontype(){
 		 return this.hazarddescriptiontype;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setReportaddress(String reportaddress){
 		 this.reportaddress=reportaddress;
 	}
 	@CheckAnno(len = 500) 
 	public String getReportaddress(){
 		 return this.reportaddress;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanynameen(String companynameen){
 		 this.companynameen=companynameen;
 	}
 	@CheckAnno(len = 500) 
 	public String getCompanynameen(){
 		 return this.companynameen;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReportprovice(String reportprovice){
 		 this.reportprovice=reportprovice;
 	}
 	@CheckAnno(len = 100) 
 	public String getReportprovice(){
 		 return this.reportprovice;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setStoragerequirement(String storagerequirement){
 		 this.storagerequirement=storagerequirement;
 	}
 	@CheckAnno(len = 200) 
 	public String getStoragerequirement(){
 		 return this.storagerequirement;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setOrderid(String orderid){
 		 this.orderid=orderid;
 	}
 	@CheckAnno(len = 50) 
 	public String getOrderid(){
 		 return this.orderid;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSpecialremarks(String specialremarks){
 		 this.specialremarks=specialremarks;
 	}
 	@CheckAnno(len = 200) 
 	public String getSpecialremarks(){
 		 return this.specialremarks;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setBrandname(String brandname){
 		 this.brandname=brandname;
 	}
 	@CheckAnno(len = 200) 
 	public String getBrandname(){
 		 return this.brandname;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	@CheckAnno(len = 50) 
 	public String getId(){
 		 return this.id;
 	}
 
 	 
 	@CharacterVaild(len = 2000) 
 	public void setOtherrequirements(String otherrequirements){
 		 this.otherrequirements=otherrequirements;
 	}
 	@CheckAnno(len = 2000) 
 	public String getOtherrequirements(){
 		 return this.otherrequirements;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setReportshow(String reportshow){
 		 this.reportshow=reportshow;
 	}
 	@CheckAnno(len = 10) 
 	public String getReportshow(){
 		 return this.reportshow;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setAppformid(String appformid){
 		 this.appformid=appformid;
 	}
 	@CheckAnno(len = 50) 
 	public String getAppformid(){
 		 return this.appformid;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setSamplename(String samplename){
 		 this.samplename=samplename;
 	}
 	@CheckAnno(len = 200) 
 	public String getSamplename(){
 		 return this.samplename;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setProductdate(String productdate){
 		 this.productdate=productdate;
 	}
 	@CheckAnno(len = 200) 
 	public String getProductdate(){
 		 return this.productdate;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setResultjudge(String resultjudge){
 		 this.resultjudge=resultjudge;
 	}
 	@CheckAnno(len = 50) 
 	public String getResultjudge(){
 		 return this.resultjudge;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setStoragerequirementtype(String storagerequirementtype){
 		 this.storagerequirementtype=storagerequirementtype;
 	}
 	@CheckAnno(len = 200) 
 	public String getStoragerequirementtype(){
 		 return this.storagerequirementtype;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setHazarddescription(String hazarddescription){
 		 this.hazarddescription=hazarddescription;
 	}
 	@CheckAnno(len = 200) 
 	public String getHazarddescription(){
 		 return this.hazarddescription;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReportdistrict(String reportdistrict){
 		 this.reportdistrict=reportdistrict;
 	}
 	@CheckAnno(len = 100) 
 	public String getReportdistrict(){
 		 return this.reportdistrict;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanynameadd(String companynameadd){
 		 this.companynameadd=companynameadd;
 	}
 	@CheckAnno(len = 500) 
 	public String getCompanynameadd(){
 		 return this.companynameadd;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setQrcode(String qrcode){
 		 this.qrcode=qrcode;
 	}
 	@CheckAnno(len = 50) 
 	public String getQrcode(){
 		 return this.qrcode;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setReporttitle(String reporttitle){
 		 this.reporttitle=reporttitle;
 	}
 	@CheckAnno(len = 50) 
 	public String getReporttitle(){
 		 return this.reporttitle;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setHazarddescriptionremarks(String hazarddescriptionremarks){
 		 this.hazarddescriptionremarks=hazarddescriptionremarks;
 	}
 	@CheckAnno(len = 200) 
 	public String getHazarddescriptionremarks(){
 		 return this.hazarddescriptionremarks;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReportcity(String reportcity){
 		 this.reportcity=reportcity;
 	}
 	@CheckAnno(len = 100) 
 	public String getReportcity(){
 		 return this.reportcity;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setStoragerequirementremarks(String storagerequirementremarks){
 		 this.storagerequirementremarks=storagerequirementremarks;
 	}
 	@CheckAnno(len = 200) 
 	public String getStoragerequirementremarks(){
 		 return this.storagerequirementremarks;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setProductername(String productername){
 		 this.productername=productername;
 	}
 	@CheckAnno(len = 200) 
 	public String getProductername(){
 		 return this.productername;
 	}
 
 	 
}