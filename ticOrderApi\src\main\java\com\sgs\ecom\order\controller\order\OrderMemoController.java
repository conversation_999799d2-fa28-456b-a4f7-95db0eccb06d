package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.request.operator.AttachmentReq;
import com.sgs.ecom.order.request.order.OrderMemoReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderMemoSV;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/memo")
public class OrderMemoController extends ControllerUtil {

	@Autowired
	private IOrderMemoSV orderMemoSV;

	/**
	 *@Function: addMemo
	 *@Description  备注 加添加测试说明解释
	 *@param: [logAddReq, token]
	 *@author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true,permission={"4307","4502","4514"})
	@RequestMapping(value = "addMemo", method = { RequestMethod.POST })
	public ResultBody addMemo(
		@RequestHeader(value="accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody OrderMemoReq orderMemoReq) throws Exception {
		orderMemoSV.addOrderMemoByType(orderMemoReq,getPersonInfo(token));
		return ResultBody.success();
	}


	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true,permission={"4307","4502","4514"})
	@RequestMapping(value = "delMemoUrl", method = { RequestMethod.POST })
	public ResultBody delMemoUrl(
		@RequestHeader(value="accessToken") String token,
		@Validated(BaseBean.Insert.class)
		@RequestBody AttachmentReq attachmentReq) throws Exception {
		orderMemoSV.delMemoUrl(attachmentReq,getPersonInfo(token));
		return ResultBody.success();
	}


	/**
	*@Function: qryList
	*@Description 查询备注列表（申请表备注 formMemo 订单备注 orderMemo）
	*@param: [orderMemoReq, token]
	*@author: Xiwei_Qiu @date: 2022/5/27 @version:
	**/
	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true,permission={"4307","4502","4514"})
	@RequestMapping(value = "qryList", method = { RequestMethod.POST })
	public ResultBody qryList(
		@RequestHeader(value="accessToken") String token,
		@Validated(BaseBean.Query.class)
		@RequestBody OrderMemoReq orderMemoReq) throws Exception {
		return ResultBody.newInstance(orderMemoSV.qryMemoList(orderMemoReq,getPersonInfo(token)));
	}
}
