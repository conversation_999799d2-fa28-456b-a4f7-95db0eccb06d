package com.sgs.ecom.order.dto;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter.QuerySummary;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SysPersonDTO {
    private String personName;
    private String personNameEn;

 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="PERSON_CODE", getName="getPersonCode", setName="setPersonCode")
 	private String personCode;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="PERSON_PHONE", getName="getPersonPhone", setName="setPersonPhone")
 	private String personPhone;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="PERSON_MAIL", getName="getPersonMail", setName="setPersonMail")
 	private String personMail;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="CUST_CODE", getName="getCustCode", setName="setCustCode")
 	private String custCode;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="CUST_ID", getName="getCustId", setName="setCustId")
 	private Long custId;
 	@ApiAnno(groups={QuerySummary.class})
 	@BeanAnno(value="CURRENCY", getName="getCurrency", setName="setCurrency")
 	private String currency;

	@ApiAnno(groups={QuerySummary.class})
	private String custName;


	private String otherQrCode;

	private List<Map> lineItem=new ArrayList<>();

	private List<Long> labIds=new ArrayList<>();
 	
    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    public String getPersonNameEn() {
        return personNameEn;
    }

    public void setPersonNameEn(String personNameEn) {
        this.personNameEn = personNameEn;
    }

	public String getPersonCode() {
		return personCode;
	}

	public void setPersonCode(String personCode) {
		this.personCode = personCode;
	}

	public String getPersonPhone() {
		return personPhone;
	}

	public void setPersonPhone(String personPhone) {
		this.personPhone = personPhone;
	}

	public String getPersonMail() {
		return personMail;
	}

	public void setPersonMail(String personMail) {
		this.personMail = personMail;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public String getCustCode() {
		return custCode;
	}

	public void setCustCode(String custCode) {
		this.custCode = custCode;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public List<Map> getLineItem() {
		return lineItem;
	}

	public void setLineItem(List<Map> lineItem) {
		this.lineItem = lineItem;
	}

	public String getOtherQrCode() {
		return otherQrCode;
	}

	public void setOtherQrCode(String otherQrCode) {
		this.otherQrCode = otherQrCode;
	}

	public String getCustName() {
		return custName;
	}

	public void setCustName(String custName) {
		this.custName = custName;
	}

	public List<Long> getLabIds() {
		return labIds;
	}

	public void setLabIds(List<Long> labIds) {
		this.labIds = labIds;
	}
}
