package com.sgs.ecom.order.dto.order;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.util.serializer.PriceNumFormatSerializer;

import java.math.BigDecimal;

public class SubOrderInfoDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private int num;
    @JsonSerialize(using = PriceNumFormatSerializer.class)
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private BigDecimal price;

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }
}
