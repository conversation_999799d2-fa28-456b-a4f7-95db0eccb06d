package com.sgs.ecom.order.bill.controller;

import com.platform.annotation.AuthRequired;
import com.platform.annotation.RateLimit;
import com.platform.web.BaseAction;
import com.sgs.ecom.order.service.bill.interfaces.IBillInfoService;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
//@RestControllerAdvice
@RequestMapping("/business/api.v1.bill/bill")
public class BillInfoAction extends BaseAction {

	@Resource
	private IBillInfoService iBillInfoService;

    /**
	* @Function: expBill
	* @Description: 导出账单详情
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-2-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-2-17  shenyi    v1.0                 新增
	*/

	@RateLimit(limitNum = 5)
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "expBill", method = { RequestMethod.POST })
    public void expBill(@RequestBody String data,HttpServletResponse res) throws Exception {
//    	res.addHeader("Access-Control-Expose-Headers", "*");
//    	billInfoSV.expBill(data, res);
		iBillInfoService.expBill(data, res);
	}
}
