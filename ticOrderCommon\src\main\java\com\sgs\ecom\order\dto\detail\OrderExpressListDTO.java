package com.sgs.ecom.order.dto.detail;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseListFilter;
import com.sgs.ecom.order.enumtool.application.ExpressCodeEnum;

public class OrderExpressListDTO {
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressCode;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressNo;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressCodeShow;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String expressType;




    private Long expressId;

    private String other;

    private int  isCs;


    public String getOther() {
        return other;
    }

    public void setOther(String other) {
        this.other = other;
    }

    public int getIsCs() {
        return isCs;
    }

    public void setIsCs(int isCs) {
        this.isCs = isCs;
    }

    public Long getExpressId() {
        return expressId;
    }

    public void setExpressId(Long expressId) {
        this.expressId = expressId;
    }

    public String getExpressCode() {
        return expressCode;
    }

    public void setExpressCode(String expressCode) {
        this.expressCode = expressCode;
    }

    public String getExpressNo() {
        return expressNo;
    }

    public void setExpressNo(String expressNo) {
        this.expressNo = expressNo;
    }
    public String getExpressCodeShow() {
        return ExpressCodeEnum.getNameCh(expressCode);
    }
    public String getExpressType() {
        return expressType;
    }

    public void setExpressType(String expressType) {
        this.expressType = expressType;
    }


}
