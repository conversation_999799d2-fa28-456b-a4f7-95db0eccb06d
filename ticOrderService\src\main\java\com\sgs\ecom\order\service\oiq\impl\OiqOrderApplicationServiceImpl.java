package com.sgs.ecom.order.service.oiq.impl;

import com.alibaba.fastjson.JSON;
import com.platform.annotation.Master;
import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.bo.UserInfo;
import com.sgs.ecom.order.config.OiqApplicationProperties;
import com.sgs.ecom.order.domain.order.OrderOperatorLogDO;
import com.sgs.ecom.order.domain.order.OrderSampleDO;
import com.sgs.ecom.order.domain.order.OrderShippingContactDO;
import com.sgs.ecom.order.domain.order.service.interfaces.IOrderCustomerDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationAttrDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderApplicationFormDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderDetailDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderExpressDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderLabelDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderLinkDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderOperatorLogDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderReportDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderSampleDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderShippingContactDomainService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderShippingDomainService;
import com.sgs.ecom.order.domain.service.user.interfaces.IUserInfoDomainService;
import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.center.BusinessLineDTO;
import com.sgs.ecom.order.dto.center.EnumDTO;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.dml.DmlMainDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OrderShippingContactDTO;
import com.sgs.ecom.order.dto.oiq.applicationInfo.OrderShippingDTO;
import com.sgs.ecom.order.dto.send.ApiOtherDTO;
import com.sgs.ecom.order.dto.user.UserDTO;
import com.sgs.ecom.order.enums.OperationTypeEnum;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.dml.BusinessCodeEnum;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.order.BaseOrderStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderLabelCodeEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.factory.lab.LabPermissionFactory;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.infrastructure.rpc.template.CenterRespository;
import com.sgs.ecom.order.request.base.OiqOrderInfoReq;
import com.sgs.ecom.order.request.base.OrderNoReq;
import com.sgs.ecom.order.request.oiq.OiqApplicationReq;
import com.sgs.ecom.order.request.oiq.OiqLineReq;
import com.sgs.ecom.order.request.oiq.OiqOrderInvoiceReq;
import com.sgs.ecom.order.request.oiq.OiqOrderReq;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.IOpenTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.ISSOTemplateSV;
import com.sgs.ecom.order.service.oiq.interfaces.IOiqOrderApplicationService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderInvoiceService;
import com.sgs.ecom.order.service.template.interfaces.ICenterRestTemplateService;
import com.sgs.ecom.order.service.user.interfaces.IUserInfoSV;
import com.sgs.ecom.order.service.util.interfaces.IDmlSV;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.select.RedisKeyUtil;
import com.sgs.ecom.order.util.select.SelectMapUtil;
import com.sgs.ecom.order.vo.VOOrderExpress;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;

@Service
public class OiqOrderApplicationServiceImpl extends BaseService implements IOiqOrderApplicationService {

    @Resource
    private IOrderUtilService orderUtilService;
    @Resource
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Resource
    private IOrderBaseInfoDomainService orderBaseInfoDomainService;
    @Resource
    private ICenterRestTemplateService centerRestTemplateService;
    @Resource
    private CenterRespository centerRespository;
    @Resource
    private IOrderInvoiceService orderInvoiceService;
    @Resource
    private IOrderSampleDomainService orderSampleDomainService;
    @Resource
    private IOrderDetailDomainService orderDetailDomainService;
    @Resource
    private IOrderApplicationFormDomainService orderApplicationFormDomainService;
    @Resource
    private IOrderApplicationAttrDomainService orderApplicationAttrDomainService;
    @Resource
    private IOrderReportDomainService orderReportDomainService;
    @Resource
    private IOrderOperatorLogDomainService orderOperatorLogDomainService;
    @Resource
    private IOrderLinkDomainService orderLinkDomainService;
    @Resource
    private ISSOTemplateSV ssoTemplateSV;
    @Resource
    private ICenterTemplateSV centerTemplateSV;
    @Resource
    private IOrderCustomerDomainService orderCustomerDomainService;
    @Resource
    private LabPermissionFactory labPermissionFactory;
    @Resource
    private OiqApplicationProperties oiqApplicationProperties;
    @Resource
    private IDmlSV dmlSV;
    @Resource
    private IOpenTemplateSV openTemplateSV;
    @Resource
    private IOrderLabelDomainService orderLabelDomainService;
    @Resource
    private IUserInfoSV userInfoSV;
    @Resource
    private IUserInfoDomainService userInfoDomainService;
    @Resource
    private IOrderShippingDomainService orderShippingDomainService;
    @Resource
    private IOrderShippingContactDomainService orderShippingContactDomainService;
    @Resource
    private ApiEventUtil eventApiUtil;
    @Resource
    private MailEventUtil mailEventUtil;
    @Resource
    private SmsEventUtil smsEventUtil;
    @Resource
    private IOrderExpressDomainService orderExpressDomainService;


    @Override
    public JSONObject qryInfo(OrderNoReq orderNoReq, BOSysPerson sysPerson) throws Exception{
        OiqOrderInfoReq oiqOrderInfoReq = new OiqOrderInfoReq();
        if (ValidationUtil.isEmpty(orderNoReq.getPlatformCode())) {
            orderNoReq.setPlatformCode(orderUtilService.getEnumStringByKey(RedisKeyUtil.BU_DEFAULT_LINE, orderNoReq.getBu()));
        }
        EnumDTO enumDTO = orderUtilService.getEnumKey(RedisKeyUtil.APPLICATION_DEFAULT_VALUE, orderNoReq.getPlatformCode());
        baseCopyObj.copyWithNull(oiqOrderInfoReq, orderNoReq);
        BaseOrderDTO baseOrderDTO = orderBaseInfoDomainService.getBaseOrderDTO(oiqOrderInfoReq);
        if (!ValidationUtil.isEmpty(baseOrderDTO)) {
            OiqOrderDTO oiqOrderDTO = new OiqOrderDTO(oiqOrderInfoReq, enumDTO);
            oiqOrderDTO.setUseUseOrder(oiqOrderInfoReq.getUseOld() ? 1 : 0);
            //询价单的报告数据取本单的
            if (OrderTypeEnum.OIQ_ORDER.getIndex().equals(baseOrderDTO.getOrderType()) && oiqOrderInfoReq.getUseOld() && !ValidationUtil.isEmpty(oiqOrderInfoReq.getOldOrder())) {
                baseOrderDTO.setReportLuaCode(oiqOrderInfoReq.getOldOrder().getReportLuaCode());
                baseOrderDTO.setReportLua(oiqOrderInfoReq.getOldOrder().getReportLua());
                baseOrderDTO.setReportFormCode(oiqOrderInfoReq.getOldOrder().getReportFormCode());
                baseOrderDTO.setReportForm(oiqOrderInfoReq.getOldOrder().getReportForm());
            }
            orderBaseInfoDomainService.getFormInfoMore(oiqOrderDTO, baseOrderDTO);
            //历史逻辑
            if (oiqOrderInfoReq.getUseOld()) {
                oiqOrderDTO.setCustomerList(new ArrayList<>());
                oiqOrderDTO.setTails("");
                userInfoDomainService.selectUserToApplication(baseOrderDTO.getUserId(), oiqOrderDTO.getApplication());
                if (oiqOrderDTO.getPortal()) {
                    oiqOrderDTO.setItemList(new ArrayList<>());
                    oiqOrderDTO.setSampleList(new ArrayList<>());
                    oiqOrderDTO.getOrderBase().setState(0);
                    oiqOrderDTO.setDestinationList(new ArrayList<>());
                    oiqOrderDTO.setFormAttrList(new ArrayList<>());
                    oiqOrderDTO.setCostInformationList(new ArrayList<>());
                    oiqOrderDTO.setGoods(new ArrayList<>());
                    oiqOrderDTO.setDocumentsInfo(new ArrayList<>());
                    oiqOrderDTO.setOrderShipping(new OrderShippingDTO());
                    oiqOrderDTO.setNotify(new OrderShippingContactDTO());
                    oiqOrderDTO.setConsignee(new OrderShippingContactDTO());
                    oiqOrderDTO.setShipper(new OrderShippingContactDTO());
                }
            }
            return jsonTransUtil.transBoToJson(OiqOrderDTO.class, oiqOrderDTO, BaseOrderFilter.OiqFormInfo.class);
        } else {
            OiqOrderDTO oiqOrderDTO=new OiqOrderDTO(enumDTO);
            return jsonTransUtil.transBoToJson(OiqOrderDTO.class, oiqOrderDTO, BaseOrderFilter.OiqFormInfo.class);
        }
    }

    @Override
    public String checkFormToSendFlg(OiqOrderReq oiqOrderReq) throws Exception {
        // 新增不做处理
        if (ValidationUtil.isEmpty(oiqOrderReq.getOrderNo())) {
            return "";
        }
        BaseOrderDTO baseOrderDTO = orderBaseInfoDomainService.selectBaseByOrderNo(oiqOrderReq.getOrderNo());
        // 检查客服BU权限
        if (!ValidationUtil.isEmpty(oiqOrderReq.getSysPerson()) && oiqOrderReq.getPersonBuMap() != null) {
            if (!oiqOrderReq.getPersonBuMap().containsKey(baseOrderDTO.getBu())) {
                throw new BusinessException(ResultEnumCode.ORDER_EDIT_UNABLE);
            }
        }
        // 如果是询价单订单，订单状态不是待提交申请表，抛出异常
        if (OrderTypeEnum.OIQ_ORDER.getIndex().equals(baseOrderDTO.getOrderType())) {
            if (baseOrderDTO.getState() != BaseOrderStateEnum.WAITAPPLY.getIndex()) {
                throw new BusinessException(ResultEnumCode.ORDER_STATE, BaseOrderStateEnum.getEnum(baseOrderDTO.getState()));
            }
            return "";
        }
        // 如果订单状态是服务中、已完成、已取消，抛出异常
        if (baseOrderDTO.getState() == BaseOrderStateEnum.SERVICE.getIndex()
            || baseOrderDTO.getState() == BaseOrderStateEnum.END.getIndex()
            || baseOrderDTO.getState() == BaseOrderStateEnum.CANCELLED.getIndex()) {
            throw new BusinessException(ResultEnumCode.ORDER_STATE, BaseOrderStateEnum.getEnum(baseOrderDTO.getState()));
        }
        // 如果前端是提交操作，但是订单状态不是待提交申请表，将操作置为修改
        if (oiqOrderReq.getType() == OperationTypeEnum.SUBMIT.getCode() && baseOrderDTO.getState() != BaseOrderStateEnum.WAITAPPLY.getIndex()) {
            oiqOrderReq.setType(OperationTypeEnum.MODIFY.getCode());
        }
        if (oiqOrderReq.getType() != OperationTypeEnum.MODIFY.getCode()) {
            return "";
        }
        BaseOrderDTO inquiryDTO = orderBaseInfoDomainService.selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());
        if (inquiryDTO.getState() != BaseOrderStateEnum.DISTRIBUTION.getIndex().intValue()) {
            if("2700".equals(baseOrderDTO.getBu())){
                return "";
            }
            oiqOrderReq.setSendMailToDml(1);
            OrderNoReq orderNoReq=new OrderNoReq();
            orderNoReq.setOrderNo(baseOrderDTO.getOrderNo());
            JSONObject jsonObject=qryInfo(orderNoReq, oiqOrderReq.getSysPerson());
            String jsonStr2= JSON.toJSONString(jsonObject);
            OiqOrderDTO oiqOrderDTO2=JSON.parseObject(jsonStr2,OiqOrderDTO.class);
            BusinessLineDTO businessLineDTO=centerRespository.qryBusinessLineDTOByLineId(oiqOrderDTO2.getOrderBase().getLineId());
            if(oiqOrderDTO2.getPortal() && BusinessCodeEnum.toMin(businessLineDTO.getPlatformCode())){
                if(!oiqOrderDTO2.getSampleCategory().getSampleCategoryCode().equals(oiqOrderReq.getSampleCategory().getSampleCategoryCode())){
                    throw new BusinessException(ResultEnumCode.SAMPLE_CATEGORY_CODE_ERROR);
                }
            }
            return JSON.toJSONString(jsonObject);
        }
        return "";
    }

    /**
     * @params [oiqOrderReq, userDTO]
     * @return java.lang.String
     * @description 保存申请表
     * <AUTHOR> || created at 2023/8/22 11:47
     */
    @Override
    @Master
    @Transactional
    public JSONObject saveForm(OiqOrderReq oiqOrderReq) throws Exception{
        // 初始化订单处理过程对象
        OiqOrderReqDTO oiqOrderReqDTO = initOiqOrderReqDTO(oiqOrderReq);
        // 如果是修改，保留原订单的信息
        retainOriginalInfoIfModify(oiqOrderReq, oiqOrderReqDTO);
        // 获取业务顾问和销售顾问信息
        getBusinessAndSalesInfo(oiqOrderReq, oiqOrderReqDTO);
        // 获取实验室信息
        getLabInfo(oiqOrderReqDTO);

        // 保存发票数据
        orderInvoiceService.saveOiqOrderInvoice(oiqOrderReqDTO);
        // 保存申请表里的主表数据
        orderBaseInfoDomainService.saveApplicationOrder(oiqOrderReqDTO);
        // 地址和发票数据 新老共用
        orderApplicationFormDomainService.saveApplicationForm(oiqOrderReqDTO);
        // 保存属性表
        orderApplicationAttrDomainService.saveForm(oiqOrderReq.getApplication(), oiqOrderReqDTO);
        // 保存报告
        orderReportDomainService.save(oiqOrderReq.getReport(), oiqOrderReqDTO);
        // 处理样品数据
        OrderSampleDO orderSampleDO = new OrderSampleDO();
        orderSampleDomainService.updateSampleList(orderSampleDO.addOther(oiqOrderReq.getSampleList(), oiqOrderReq.getSampleCategory()), oiqOrderReqDTO);
        // 处理项目
        orderDetailDomainService.saveItemBySaveForm(oiqOrderReq, oiqOrderReqDTO);
        // 保存orderLink的数据 申请表 报告 和发票里的联系邮箱
        orderLinkDomainService.saveOrderLinkByOrder(oiqOrderReq, oiqOrderReqDTO);
        // 保存订单和企业的关系
        orderCustomerDomainService.saveCustomer(oiqOrderReqDTO.getOrderNo(), oiqOrderReq.getCustomerList(), oiqOrderReq.getType() == 0 ? 1 : 0);
        //保存小尾巴
        orderLabelDomainService.saveOrderLabelByCode(oiqOrderReqDTO.getOrderNo(), oiqOrderReq.getTails(), OrderLabelCodeEnum.TAILS);
        // 保存航运信息、发货方、收货方、通知方
        if ("2700".equals(oiqOrderReq.getOrderBase().getBu())) {
            orderShippingDomainService.saveOrderShipping(oiqOrderReq.getOrderShipping(), oiqOrderReqDTO);
            orderShippingContactDomainService.saveOrderShippingContact(OrderShippingContactDO.reqToList(oiqOrderReq), oiqOrderReqDTO);
        }
        // dml对象处理
        dmlExtra(oiqOrderReq, oiqOrderReqDTO);
        // 记录日志
        logRecord(oiqOrderReq, oiqOrderReqDTO);
        // 推送执行系统
        pushExecutionSystem(oiqOrderReq, oiqOrderReqDTO);

        // 获取orderId
        getOrderId(oiqOrderReqDTO);

        // 返回结果
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(SelectMapUtil.ORDER_ID, oiqOrderReqDTO.getOrderId());
        jsonObject.put(SelectMapUtil.ORDER_NO, oiqOrderReqDTO.getOrderNo());
        jsonObject.put(SelectMapUtil.SAVE_TIME, UseDateUtil.getDateString(new Date()));
        return jsonObject;
    }

    private OiqOrderReqDTO initOiqOrderReqDTO(OiqOrderReq oiqOrderReq) {
        //根据订单号判定是新增还是修改,新增的只能是小门户
        boolean isAdd = StringUtils.isBlank(oiqOrderReq.getOrderNo());
        String inquiryOrderNo = orderUtilService.getOrderNoByRedis(oiqOrderReq.getApplication().getLinkPhone(), oiqOrderReq.getOrderBase().getBu());
        String orderNo = isAdd ? orderUtilService.getNewOrderNo(oiqOrderReq.getOrderBase().getBu()) : oiqOrderReq.getOrderNo();
        String groupNo = orderUtilService.getNewGroupNo();
        String inquiryGroupNo = orderUtilService.getNewGroupNo();
        OiqOrderReqDTO oiqOrderReqDTO = new OiqOrderReqDTO(inquiryOrderNo, inquiryGroupNo, orderNo, groupNo, isAdd, !ValidationUtil.isEmpty(oiqOrderReq.getUserDTO()) ? oiqOrderReq.getUserDTO() : new UserDTO());
        oiqOrderReqDTO.setOiqOrderReq(oiqOrderReq);
        return oiqOrderReqDTO;
    }

    private void retainOriginalInfoIfModify(OiqOrderReq oiqOrderReq, OiqOrderReqDTO oiqOrderReqDTO) throws Exception {
        if (StringUtils.isBlank(oiqOrderReq.getOrderNo())) {
            return;
        }
        //修改逻辑处理
        BaseOrderDTO baseOrderDTO = orderBaseInfoCustomService.selectBaseByOrderNo(oiqOrderReq.getOrderNo());
        oiqOrderReqDTO.setOrderDTO(baseOrderDTO);
        oiqOrderReqDTO.setOrderId(baseOrderDTO.getOrderId());
        oiqOrderReqDTO.setOrderNo(baseOrderDTO.getOrderNo());
        if (!ValidationUtil.isEmpty(oiqOrderReqDTO.getUserDTO().getUserId()) && !ValidationUtil.isEmpty(baseOrderDTO.getUserId())) {
            UserInfo userInfo = userInfoSV.selectByPrimaryKey(baseOrderDTO.getUserId());
            if (Objects.nonNull(userInfo)) {
                baseCopyObj.copy(oiqOrderReqDTO.getUserDTO(), userInfo);
            }
        }
        oiqOrderReqDTO.setInquiryOrderNo(orderUtilService.getOrderNoByRedis(oiqOrderReq.getApplication().getLinkPhone(), oiqOrderReq.getOrderBase().getBu()));
        if (!ValidationUtil.isEmpty(baseOrderDTO.getRelateOrderNo())) {
            BaseOrderDTO inquiryDTO = orderBaseInfoCustomService.selectBaseByOrderNo(baseOrderDTO.getRelateOrderNo());
            OiqOrderReqDTO.addOrder(oiqOrderReqDTO, inquiryDTO, baseOrderDTO);
        }
    }

    private void getBusinessAndSalesInfo(OiqOrderReq oiqOrderReq, OiqOrderReqDTO oiqOrderReqDTO) {
        if (!oiqOrderReqDTO.getPortal()) {
            return;
        }
        oiqOrderReq.getOrderBase().setSampleBusinessCode(oiqOrderReq.getOrderBase().getBusinessCode());

        // 根据原始businessCode获取APPLICATION_LINE_ID
        BusinessLineDTO applicationLineDTO = centerRespository.qryByPlatformCode(oiqOrderReq.getOrderBase().getBusinessCode());
        if(applicationLineDTO == null || applicationLineDTO.getConfigId() == null) {
            throw new BusinessException(ResultEnumCode.ENUM_ERROR);
        }
        oiqOrderReqDTO.setApplicationLineId(applicationLineDTO.getConfigId());

        String defaultLine = orderUtilService.getEnumStringByKey(RedisKeyUtil.BU_DEFAULT_LINE, oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBu());
        oiqOrderReq.getOrderBase().setBusinessCode(defaultLine);
        oiqOrderReqDTO.setBusinessLineDTO(centerRespository.qryByPlatformCode(defaultLine));
        oiqOrderReq.getOrderBase().setBu(oiqOrderReqDTO.getBusinessLineDTO().getBu());
        // 获取业务顾问邮箱
        if (oiqOrderReqDTO.getOiqOrderReq().getApplication().getApplicationAttr().getDefaultEmailFlg() == 1) {
            String mail = orderUtilService.getEnumStringByKey(RedisKeyUtil.DML_LINE_BUSINESS_EMAIL, oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode());
            oiqOrderReqDTO.getOiqOrderReq().getOrderBase().setBusinessPersonEmail(mail);
        }
        SysPersonDTO personDTO = ssoTemplateSV.qryPersonByEmail(oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBusinessPersonEmail());
        if (oiqOrderReq.getType() != OperationTypeEnum.TEMP_STORE.getCode() && StringUtils.isBlank(personDTO.getPersonCode())) {
            throw new BusinessException(ResultEnumCode.BUSINESS_EMAIL_NULL);
        }
        oiqOrderReqDTO.setBusinessPerson(personDTO);
        // 获取销售顾问邮箱
        if (oiqOrderReqDTO.getOiqOrderReq().getApplication().getApplicationAttr().getSalesEmailFlg() == 1) {
            String mail = orderUtilService.getEnumStringByKey(RedisKeyUtil.BUSINESS_SALES_EMAIL, oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode());
            oiqOrderReqDTO.getOiqOrderReq().getOrderBase().setSalesEmail(mail);
        }
        // 提交/修改时，必要信息检查
        checkSaveForm(oiqOrderReq, oiqOrderReqDTO.getBusinessLineDTO());
    }

    private void checkSaveForm(OiqOrderReq oiqOrderReq, BusinessLineDTO businessLineDTO) {
        if (oiqOrderReq.getType() == 1 || oiqOrderReq.getType() == 2) {
            OiqApplicationReq applicationReq = oiqOrderReq.getApplication();
            if (ValidationUtil.isEmpty(applicationReq)) {
                throw new BusinessException(ResultEnumCode.APPLICATION_ERROR);
            }
            if (StringUtils.isBlank(applicationReq.getLinkPhone()) || StringUtils.isBlank(applicationReq.getLinkPerson())) {
                throw new BusinessException(ResultEnumCode.APPLICATION_ERROR);
            }
            if (!BusinessCodeEnum.toMin(businessLineDTO.getPlatformCode()) && StringUtils.isBlank(applicationReq.getLinkEmail())) {
                throw new BusinessException(ResultEnumCode.APPLICATION_ERROR);
            }
            OiqOrderInvoiceReq oiqOrderInvoiceReq = oiqOrderReq.getOrderInvoice();
            if (ValidationUtil.isEmpty(oiqOrderInvoiceReq)) {
                throw new BusinessException(ResultEnumCode.APPLICATION_REC_ERROR);
            }
            if (StringUtils.isBlank(oiqOrderInvoiceReq.getPayerName()) || StringUtils.isBlank(oiqOrderInvoiceReq.getPayerPhone())) {
                throw new BusinessException(ResultEnumCode.APPLICATION_REC_ERROR);
            }
            if (!BusinessCodeEnum.toMin(businessLineDTO.getPlatformCode()) && StringUtils.isBlank(oiqOrderInvoiceReq.getPayerEmail())) {
                throw new BusinessException(ResultEnumCode.APPLICATION_REC_ERROR);
            }
        }
    }

    private void getLabInfo(OiqOrderReqDTO oiqOrderReqDTO) throws Exception {
        // 取实验室信息
        // 询价单和订单都用前端给的实验室id
        LabDTO labDTO;
        if (oiqOrderReqDTO.getPortal()) {
            labDTO = labPermissionFactory.createService(oiqApplicationProperties.getLabChoiceByLine(oiqOrderReqDTO.getBusinessLineDTO().getConfigCode())).getLabByPrivilege(oiqOrderReqDTO);
        } else {
            labDTO = centerRestTemplateService.labQryByUser(oiqOrderReqDTO.getOrderDTO().getLabId());
        }
        if (ValidationUtil.isEmpty(labDTO)) {
            throw new BusinessException(ResultEnumCode.LAB_ERROR);
        }
        oiqOrderReqDTO.setLabDTO(labDTO);
    }

    private void dmlExtra(OiqOrderReq oiqOrderReq, OiqOrderReqDTO oiqOrderReqDTO) {
        if (!oiqOrderReqDTO.getPortal()) {
            return;
        }
        oiqOrderReqDTO.getDmlMainReqDTO().setLine(BusinessCodeEnum.getNameCh(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode()));
        oiqOrderReqDTO.getDmlMainReqDTO().setOwnerEmail(oiqOrderReq.getOrderBase().getBusinessPersonEmail());
        oiqOrderReqDTO.getDmlMainReqDTO().setType(oiqOrderReq.getType());
        oiqOrderReqDTO.getDmlMainReqDTO().setSampleCategoryReq(oiqOrderReq.getSampleCategory());
    }

    private void logRecord(OiqOrderReq oiqOrderReq, OiqOrderReqDTO oiqOrderReqDTO) {
        //记录日志 小门户和询报价的提交有日志 暂存小门户有日志
        OrderOperatorTypeEnum typeEnum = OrderOperatorLogDO.getSaveFormLogEnum(oiqOrderReq.getType());
        String csCode;
        if (!ValidationUtil.isEmpty(oiqOrderReq.getSysPerson()) && !ValidationUtil.isEmpty(oiqOrderReq.getSysPerson().getPersonCode())) {
            csCode = oiqOrderReq.getSysPerson().getPersonCode();
        } else {
            csCode = oiqOrderReqDTO.getUserDTO().getLogUserShow();
        }
        orderOperatorLogDomainService.addLog(new BaseOrderDTO(oiqOrderReqDTO.getOrderNo(), oiqOrderReqDTO.getOrderType()), typeEnum, csCode);
    }

    private void pushExecutionSystem(OiqOrderReq oiqOrderReq, OiqOrderReqDTO oiqOrderReqDTO) throws Exception {
        // 如果操作不是提交或修改就不推送执行系统
        if (oiqOrderReq.getType() != 1 && oiqOrderReq.getType() != 2) {
            return;
        }
        //小门户往dml推送 和发客服邮件
        if (oiqOrderReqDTO.getPortal() && oiqOrderReq.getSendMailToDml() == 0) {
            if (BusinessCodeEnum.toDml(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode())) {
                DmlMainDTO dmlMainDTO = dmlSV.getBaseDmlMainDTO(oiqOrderReqDTO.getDmlMainReqDTO());
                if (oiqOrderReqDTO.getDmlMainReqDTO().getType() == 1) {
                    openTemplateSV.createApplication(dmlMainDTO);
                }
                if (oiqOrderReqDTO.getDmlMainReqDTO().getType() == 2) {
                    openTemplateSV.updateApplication(dmlMainDTO);
                }
            }
            if (BusinessCodeEnum.toMin(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode())) {
                openTemplateSV.saveOrderToOther(oiqOrderReqDTO.getDmlMainReqDTO(), BusinessCodeEnum.toOther(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode()));
                //修改的场景下 才会去查样品物流数据
                if(oiqOrderReq.getType()==2){
                    BaseOrderDTO expressBaseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(oiqOrderReqDTO.getOrderNo());
                    OrderExpressDTO orderExpressDTO=orderExpressDomainService.selectApplicationExpress(oiqOrderReqDTO.getOrderNo());
                    //有寄养才推送物流
                    if(!ValidationUtil.isEmpty(orderExpressDTO)){
                        VOOrderExpress voOrderExpress =new VOOrderExpress();
                        baseCopyObj.copy(voOrderExpress,orderExpressDTO);
                        openTemplateSV.sendExpressToOther(expressBaseOrderDTO,voOrderExpress,BusinessCodeEnum.toOther(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode()));
                    }
                }

            }
        }
        oiqOrderReq.setEventFlg(1);
        oiqOrderReq.setEventOrderNo(oiqOrderReqDTO.getOrderNo());
        oiqOrderReq.setOrderType(oiqOrderReqDTO.getPortal() ? OrderTypeEnum.OIQ_SMALL_ORDER.getIndex() : OrderTypeEnum.OIQ_ORDER.getIndex());
    }

    private void getOrderId(OiqOrderReqDTO oiqOrderReqDTO) throws Exception {
        if (!ValidationUtil.isEmpty(oiqOrderReqDTO.getOrderId())) {
            return;
        }
        BaseOrderDTO baseOrderDTO = orderBaseInfoDomainService.selectBaseByOrderNo(oiqOrderReqDTO.getOrderNo());
        oiqOrderReqDTO.setOrderId(baseOrderDTO.getOrderId());
    }

    @Override
    public void sendNotifications(OiqOrderReq oiqOrderReq, String orderInfo) throws Exception {
        //发给客户的短信已经合并 发客服就小门户会发
        if (StringUtils.isNotBlank(oiqOrderReq.getEventOrderNo())) {
            eventApiUtil.saveEvent(oiqOrderReq.getEventOrderNo(), EventEnum.SAVE_INVOICE_BOSS);
        }
        boolean sendWeChat = false;
        if (oiqOrderReq.getEventFlg() == 1 && OrderTypeEnum.OIQ_SMALL_ORDER.getIndex().equals(oiqOrderReq.getOrderType()) && StringUtils.isBlank(orderInfo)) {
            if("2700".equals(oiqOrderReq.getOrderBase().getBu())){


                mailEventUtil.sendMail(oiqOrderReq.getEventOrderNo(), oiqOrderReq.getOrderType(),  oiqOrderReq.getType()==1?
                        OiqMailEnum.PORTAL_CRM_CREATE_FORM:OiqMailEnum.PORTAL_CRM_UPDATE_FORM, 1L);
                smsEventUtil.sendSms(oiqOrderReq.getEventOrderNo(), oiqOrderReq.getOrderType(), oiqOrderReq.getType()==1?
                        OiqSmsEnum.PORTAL_CRM_CREATE_FORM:OiqSmsEnum.PORTAL_CRM_UPDATE_FORM);
            }
            eventApiUtil.saveEvent(oiqOrderReq.getEventOrderNo(), EventEnum.MOD_USER_LABEL);
            sendWeChat = true;
        }
        if (oiqOrderReq.getEventFlg() == 1 && OrderTypeEnum.OIQ_ORDER.getIndex().equals(oiqOrderReq.getOrderType())) {
            mailEventUtil.sendMail(oiqOrderReq.getEventOrderNo(), oiqOrderReq.getOrderType(), OiqMailEnum.USER_SAVE_FORM, 1L);
            smsEventUtil.sendSms(oiqOrderReq.getEventOrderNo(), oiqOrderReq.getOrderType(), OiqSmsEnum.USER_SAVE_FORM);
            sendWeChat = true;
        }
        if (StringUtils.isNotBlank(orderInfo)) {
            eventApiUtil.saveEvent(oiqOrderReq.getEventOrderNo(), EventEnum.SAVE_TO_SEND_DML_MAIL, new ApiOtherDTO(orderInfo));
        }
        if (oiqOrderReq.getSaveOrderFlg() == 1 && StringUtils.isNotBlank(oiqOrderReq.getApiOtherDTO().getInquiryOrderNo())) {
            eventApiUtil.saveEvent(oiqOrderReq.getApiOtherDTO().getInquiryOrderNo(), EventEnum.CREATE_ORDER_ADD_LABEL);
        }
        if (sendWeChat) {
            BaseOrderDTO baseOrderDTO = orderBaseInfoDomainService.selectBaseByOrderNo(oiqOrderReq.getEventOrderNo());
            if (BaseOrderStateEnum.WAITEXAMINE.getIndex().equals(baseOrderDTO.getState())) {
                eventApiUtil.sendWechatMsg(oiqOrderReq.getEventOrderNo(), EventEnum.SEND_WECHAT_MSG, WechatEnum.USER_SAVE_FORM);
            }
        }
    }



    @Override
    public JSONObject qryLineByLineCode(OiqLineReq oiqLineReq) throws Exception {
        BusinessLineDTO businessLineDTO=centerRespository.qryByPlatformCode(oiqLineReq.getLineCode());
        if(businessLineDTO.getConfigId()==null || !businessLineDTO.getBu().equals(oiqLineReq.getBu())){
            String defaultLine=orderUtilService.getLineEnumByKey(RedisKeyUtil.BU_DEFAULT_LINE,oiqLineReq.getBu());
            businessLineDTO=centerRespository.qryByPlatformCode(defaultLine);
        }
        return jsonTransUtil.fromBoToJson(BusinessLineDTO.class, businessLineDTO, BaseQryFilter.QueryDtl.class);
    }
}
