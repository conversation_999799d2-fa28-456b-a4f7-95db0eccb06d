package com.sgs.ecom.order.domain.order;

import com.sgs.ecom.order.entity.order.OrderLabel;
import com.sgs.ecom.order.enumtool.order.OrderLabelCodeEnum;
import com.sgs.ecom.order.util.UseDateUtil;

import java.util.Date;

public class OrderLabelDO {

    public OrderLabel getOrderLabel(String orderNo, OrderLabelCodeEnum orderLabelCodeEnum){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderLabel orderLabel=new OrderLabel();
        orderLabel.setCreateDate(dateStr);
        orderLabel.setState(1);
        orderLabel.setOrderNo(orderNo);
        orderLabel.setLabelCode(orderLabelCodeEnum.getName());
        orderLabel.setLabelGroup("ORDER");
        orderLabel.setLabelValue("1");
        return orderLabel;
    }

    public OrderLabel getOrderLabel(String orderNo, String value,OrderLabelCodeEnum orderLabelCodeEnum){
        String dateStr= UseDateUtil.getDateString(new Date());
        OrderLabel orderLabel=new OrderLabel();
        orderLabel.setCreateDate(dateStr);
        orderLabel.setState(1);
        orderLabel.setOrderNo(orderNo);
        orderLabel.setLabelCode(orderLabelCodeEnum.getName());
        orderLabel.setLabelGroup("ORDER");
        orderLabel.setLabelValue(value);
        return orderLabel;
    }
}
