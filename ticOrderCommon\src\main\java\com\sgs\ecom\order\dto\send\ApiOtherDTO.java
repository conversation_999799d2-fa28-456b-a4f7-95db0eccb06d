package com.sgs.ecom.order.dto.send;


import com.sgs.ecom.order.entity.order.OrderOperatorLog;
import com.sgs.ecom.order.vo.VOOrderPay;

/**
 * <AUTHOR>
 */
public class ApiOtherDTO {
    //5合1确认的日志
    private OrderOperatorLog log;
    //dml 原始推送数据
    private String dmlFormStr;

    private String inquiryOrderNo;
    //支付的参数逻辑
    private VOOrderPay voOrderPay;

    private int payState;



    public ApiOtherDTO() {
    }

    public ApiOtherDTO(String dmlFormStr) {
        this.dmlFormStr = dmlFormStr;
    }

    public String getDmlFormStr() {
        return dmlFormStr;
    }

    public void setDmlFormStr(String dmlFormStr) {
        this.dmlFormStr = dmlFormStr;
    }

    public OrderOperatorLog getLog() {
        return log;
    }

    public void setLog(OrderOperatorLog log) {
        this.log = log;
    }

    public String getInquiryOrderNo() {
        return inquiryOrderNo;
    }

    public void setInquiryOrderNo(String inquiryOrderNo) {
        this.inquiryOrderNo = inquiryOrderNo;
    }

    public VOOrderPay getVoOrderPay() {
        return voOrderPay;
    }

    public void setVoOrderPay(VOOrderPay voOrderPay) {
        this.voOrderPay = voOrderPay;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }

}
