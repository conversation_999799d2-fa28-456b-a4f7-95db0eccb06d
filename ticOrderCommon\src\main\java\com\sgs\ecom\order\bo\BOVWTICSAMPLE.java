package com.sgs.ecom.order.bo; 
 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="")
public class BOVWTICSAMPLE{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "VW_TIC_SAMPLE"; 
 
 	public static final String OWNER ="bbc";

 	public static final String NUM="num";
 	public static final String ID="id";
 	public static final String SAMPLE="sample";

 	@BeanAnno("num")
 	private String num;
 	@BeanAnno("id")
 	private String id;
 	@BeanAnno("sample")
 	private String sample;

 	@CharacterVaild(len = 500) 
 	public void setNum(String num){
 		 this.num=num;
 	}
 	@CheckAnno(len = 500) 
 	public String getNum(){
 		 return this.num;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setId(String id){
 		 this.id=id;
 	}
 	@CheckAnno(len = 50) 
 	public String getId(){
 		 return this.id;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setSample(String sample){
 		 this.sample=sample;
 	}
 	@CheckAnno(len = 500) 
 	public String getSample(){
 		 return this.sample;
 	}
 
 	 
}