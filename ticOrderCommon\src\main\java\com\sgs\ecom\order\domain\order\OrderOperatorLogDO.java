package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.log.BaseLog;
import com.sgs.ecom.order.dto.order.OrderOperatorLogDTO;
import com.sgs.ecom.order.entity.order.OrderOperatorLog;
import com.sgs.ecom.order.enumtool.OrderOperatorTypeEnum;
import com.sgs.ecom.order.util.UseDateUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class OrderOperatorLogDO extends OrderOperatorLog {

    public OrderOperatorLogDO() {
    }



    public  OrderOperatorLog addLog(BaseOrderDTO baseOrderDTO,OrderOperatorTypeEnum orderOperatorTypeEnum
    ,String text,String memo,String csCode,int isShow,String fileUrl){
        OrderOperatorLog orderOperatorLog=new OrderOperatorLog();
        //基础数据
        orderOperatorLog.setOrderNo(baseOrderDTO.getOrderNo());
        orderOperatorLog.setOrderType(Integer.parseInt(baseOrderDTO.getOrderType()));
        orderOperatorLog.setOperatorType(Integer.parseInt(orderOperatorTypeEnum.getIndex()));
        orderOperatorLog.setOperatorText(text);
        orderOperatorLog.setMemo(memo);
        orderOperatorLog.setCsCode(csCode);
        orderOperatorLog.setIsShow(isShow);
        orderOperatorLog.setFileUrl(fileUrl);
        orderOperatorLog.setOperatorDate(UseDateUtil.getDateString(new Date()));
        return orderOperatorLog;
    }

    public  OrderOperatorLog addOptLog(String  orderNo,Integer orderType,String optType
            ,String text,String memo,String csCode,int isShow){
        OrderOperatorLog orderOperatorLog=new OrderOperatorLogDO();
        //基础数据
        orderOperatorLog.setOrderNo(orderNo);
        orderOperatorLog.setOrderType(orderType);
        orderOperatorLog.setOperatorType(Integer.parseInt(optType));
        orderOperatorLog.setOperatorText(text);
        orderOperatorLog.setMemo(memo);
        orderOperatorLog.setCsCode(csCode);
        orderOperatorLog.setIsShow(isShow);
        orderOperatorLog.setOperatorDate(UseDateUtil.getDateString(new Date()));
        return orderOperatorLog;
    }

    public OrderOperatorLog addLogByInfo(BaseLog baseLog) {
        OrderOperatorLog   orderOperatorLog = new OrderOperatorLog();

        //基础数据
        orderOperatorLog.setOrderNo(baseLog.getOrderNo());
        orderOperatorLog.setOrderType(Integer.parseInt(baseLog.getOrderType()));
        orderOperatorLog.setDetailNo(baseLog.getDetailNo());
        orderOperatorLog.setOperatorType(Integer.parseInt(baseLog.getOrderOperatorTypeEnum().getIndex()));
        orderOperatorLog.setOperatorText(baseLog.getOperatorText());
        orderOperatorLog.setMemo(baseLog.getMemo());
        orderOperatorLog.setCsCode(baseLog.getCsCode());
        orderOperatorLog.setIsShow(baseLog.getIsShow());

        if (StringUtils.isBlank(orderOperatorLog.getOperatorDate())) {
            orderOperatorLog.setOperatorDate(UseDateUtil.getDateString(new Date()));
        }
        return orderOperatorLog;
    }

    public static OrderOperatorLogDTO getSubLogByMain(List<OrderOperatorLogDTO> logDTOList, String orderNo){
        List<OrderOperatorLogDTO> log=logDTOList.stream().filter(a->!(orderNo.equals(a.getOperatorText()))).
                sorted(Comparator.comparing(OrderOperatorLogDTO::getLogId).reversed()).
                collect(Collectors.toList());
        if(ValidationUtil.isEmpty(log)){
            return null;
        }
        return log.get(0);
    }

    public static OrderOperatorTypeEnum getSaveFormLogEnum(int type) {
        if (type == 0) {
            return OrderOperatorTypeEnum.TIC_CS_CREATE_FORM;
        }
        if (type == 1) {
            return OrderOperatorTypeEnum.TIC_CS_SAVE_FORM;
        }
        if (type == 2) {
            return OrderOperatorTypeEnum.TIC_CS_UPDATE_FORM;
        }
        return null;
    }
}
