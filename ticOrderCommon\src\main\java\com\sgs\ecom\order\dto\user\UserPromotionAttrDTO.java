package com.sgs.ecom.order.dto.user;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;

import java.util.ArrayList;
import java.util.List;

public class UserPromotionAttrDTO  extends BaseQryFilter {
	@ApiAnno(groups={Default.class})
	private String activityCode;
	@ApiAnno(groups={Default.class})
	private String attrKey;
	@ApiAnno(groups={Default.class})
	private String attrName;
	@ApiAnno(groups={Default.class})
	private String attrValue;
	@ApiAnno(groups={Default.class})
	private Long userId;

	private String code;

	private List<String> strList=new ArrayList<>();



	public String getActivityCode() {
		return activityCode;
	}

	public void setActivityCode(String activityCode) {
		this.activityCode = activityCode;
	}

	public String getAttrKey() {
		return attrKey;
	}

	public void setAttrKey(String attrKey) {
		this.attrKey = attrKey;
	}

	public String getAttrName() {
		return attrName;
	}

	public void setAttrName(String attrName) {
		this.attrName = attrName;
	}

	public String getAttrValue() {
		return attrValue;
	}

	public void setAttrValue(String attrValue) {
		this.attrValue = attrValue;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public List<String> getStrList() {
		return strList;
	}

	public void setStrList(List<String> strList) {
		this.strList = strList;
	}
}
