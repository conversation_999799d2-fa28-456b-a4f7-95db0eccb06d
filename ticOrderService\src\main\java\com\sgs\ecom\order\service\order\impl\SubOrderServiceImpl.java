package com.sgs.ecom.order.service.order.impl;

import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseService;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderInvoiceDomainService;
import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.custom.OrderCheckStateDTO;
import com.sgs.ecom.order.dto.detail.OrderApplicationFormDTO;
import com.sgs.ecom.order.dto.detail.OrderInvoiceDTO;
import com.sgs.ecom.order.dto.export.ExpOiqOrderDTO;
import com.sgs.ecom.order.dto.export.ExpOiqSubOrderDTO;
import com.sgs.ecom.order.dto.order.*;
import com.sgs.ecom.order.dto.pay.BankDTO;
import com.sgs.ecom.order.dto.pdf.QuotationDataDTO;
import com.sgs.ecom.order.dto.pdf.QuotationFormDataDTO;
import com.sgs.ecom.order.dto.pdf.QuotationOtherDTO;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.bbc.BbcStateEnum;
import com.sgs.ecom.order.enumtool.order.OrderRefundStateEnum;
import com.sgs.ecom.order.mapper.order.SubOrderMapper;
import com.sgs.ecom.order.request.OrderPriceReq;
import com.sgs.ecom.order.service.member.interfaces.ICenterTemplateSV;
import com.sgs.ecom.order.service.member.interfaces.ISSOTemplateSV;
import com.sgs.ecom.order.service.order.interfaces.*;
import com.sgs.ecom.order.service.template.interfaces.IPayTemplateSV;
import com.sgs.ecom.order.service.util.interfaces.IOrderUtilService;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.collection.StrUtil;
import com.sgs.ecom.order.util.order.OrderUtil;
import com.sgs.ecom.order.util.select.*;
import com.sgs.ecom.order.util.time.TimeCalendarUtil;
import com.sgs.ecom.order.vo.VOOrderBaseInfo;
import com.sgs.ecom.order.vo.VOSubOrder;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SubOrderServiceImpl extends BaseService implements ISubOrderService {



    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Autowired
    private IOrderDetailService orderDetailService;
    @Autowired
    private IOrderAttributeService orderAttributeService;
    @Autowired
    private IOrderInvoiceDomainService orderInvoiceDomainService;
    @Autowired
    private IOrderApplicationFormService orderApplicationFormService;
    @Autowired
    private ISSOTemplateSV issoTemplateSV;
    @Autowired
    private ICenterTemplateSV centerTemplateSV;
    @Autowired
    private IPayTemplateSV payTemplateSV;
    @Autowired
    private IOrderBaseInfoService orderBaseInfoService;
    @Autowired
    private IOrderUtilService orderUtilService;

    @Autowired
    private SubOrderMapper subOrderMapper;


    @Override
    public void insertSelective(VOSubOrder voSubOrder) {
        subOrderMapper.insertSelective(voSubOrder);
    }

    @Override
    public void updateByPrimaryKeySelective(VOSubOrder voSubOrder) {
        subOrderMapper.updateByPrimaryKeySelective(voSubOrder);
    }

    public VOSubOrder getVOSubOrder(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, OrderPriceReq orderPriceReq, BOSysPerson boSysPerson,String newOrderNo){
        VOSubOrder voSubOrder=getBaseSub();
        voSubOrder.setLineId(orderBaseInfoCheckDTO.getLineId());
        voSubOrder.setApplicationLineId(ValidationUtil.isEmpty(orderBaseInfoCheckDTO.getApplicationLineId()) ? orderBaseInfoCheckDTO.getLineId() : orderBaseInfoCheckDTO.getApplicationLineId().intValue());
        voSubOrder.setOrderNo(newOrderNo);
        BigDecimal newPrice=new BigDecimal(orderPriceReq.getPrice()).setScale(2,BigDecimal.ROUND_HALF_UP);
        voSubOrder.setRealAmount(newPrice);
        voSubOrder.setRecommendReason(orderPriceReq.getMemo());
        voSubOrder.setRelateOrderNo(orderBaseInfoCheckDTO.getOrderNo());
        voSubOrder.setIsPayReceived(0);
        voSubOrder.setOrderType(Integer.parseInt(OrderTypeEnum.OIQ_SUB_ORDER.getIndex()));
        voSubOrder.setOperatorCode(boSysPerson.getPersonCode());
        voSubOrder.setUserId(orderBaseInfoCheckDTO.getUserId());
        voSubOrder.setLabId(orderBaseInfoCheckDTO.getLabId());
        voSubOrder.setPayState(0);
        return voSubOrder;
    }




    public VOSubOrder getBaseSub(){
        VOSubOrder voSubOrder=new VOSubOrder();
        String date= UseDateUtil.getDateString(new Date());
        voSubOrder.setCreateDate(date);
        voSubOrder.setStateDate(date);
        voSubOrder.setTotalNums(1);
        return voSubOrder;
    }

    public List<SubOrderDTO> selectListByMap(Map map) {
        return subOrderMapper.selectListByMap(map);
    }

    @Override
    public void orderDetailAddSub(OrderBaseInfoMoreDTO orderBaseInfoMoreDTO) {
        Map map=new HashMap();
        map.put(SelectMapUtil.RELATE_ORDER_NO,orderBaseInfoMoreDTO.getOrderNo());
        int n=selectCountByMap(map);
        if(n>0){
            orderBaseInfoMoreDTO.setSubOrderFlg(1);
            List<SubOrderDTO> subOrderDTOList=selectListByMap(map);
            orderBaseInfoMoreDTO.setSubOrderDTOList(subOrderDTOList);
        }
    }

    @Override
    public int selectCountByMap(Map map) {
        return subOrderMapper.selectCountByMap(map);
    }

    @Override
    public int selectCountByReceived(String relateOrderNo) {
        Map map = getMap(relateOrderNo);
        return subOrderMapper.selectCountByMap(map);
    }

    private Map getMap(String relateOrderNo) {
        Map map=new HashMap();
        map.put(SelectMapUtil.SUB_ORDER_TYPE,SelectMapUtil.SUB_ORDER_TYPE);
        map.put(SelectMapUtil.RELATE_ORDER_NO, relateOrderNo);
        map.put(VOOrderBaseInfo.IS_PAY_RECEIVED,0);
        map.put(SelectMapUtil.STATE_NOT, OrderStateEnum.CANCELLED.getIndex());
        return map;
    }

    @Override
    public int qryRefundCountByReceived(String relateOrderNo) {
        Map map = getMap(relateOrderNo);
        map.put(SelectMapUtil.REFUND_STATE_NOT_LIST,Arrays.asList(OrderRefundStateEnum.REFUND_CONFIRMATION.getIndex(),OrderRefundStateEnum.PART_REFUND_CONFIRMATION.getIndex()));
        return subOrderMapper.selectCountByMap(map);
    }


    public int selectCountByReceived(List<OrderCheckStateDTO> orderCheckStateDTOList) {
        List<String> list=orderCheckStateDTOList.stream().map(OrderCheckStateDTO::getOrderNo).collect(Collectors.toList());
        Map<String,Object> map=new HashMap();
        map.put(SelectMapUtil.SUB_ORDER_TYPE,SelectMapUtil.SUB_ORDER_TYPE);
        map.put(SelectMapUtil.RELATE_ORDER_NO,list);
        map.put(VOOrderBaseInfo.IS_PAY_RECEIVED,0);
        map.put(SelectMapUtil.STATE_NOT, OrderStateEnum.CANCELLED.getIndex());
        return subOrderMapper.selectCountByMap(map);
    }



    @Override
    public List<ExpOiqOrderDTO> orderExpAddSubExp(List<ExpOiqOrderDTO> expOiqOrderDTOList, List<ExpOiqSubOrderDTO> expOiqSubOrderDTOList) {

        List<ExpOiqOrderDTO> expOiqOrderDTOListNew=new ArrayList<>();
        if(ValidationUtil.isEmpty(expOiqOrderDTOList)){
            return expOiqOrderDTOList;
        }

       if(!ValidationUtil.isEmpty(expOiqOrderDTOList)){
           Map<String, List<ExpOiqSubOrderDTO>> groupBySub=expOiqSubOrderDTOList.stream().collect(Collectors.groupingBy(E ->E.getRelateOrderNo()));

           for(int n=0;n<expOiqOrderDTOList.size();n++){
               ExpOiqOrderDTO expOiqOrderDTO=expOiqOrderDTOList.get(n);
               expOiqOrderDTOListNew.add(expOiqOrderDTO);
               if(groupBySub.containsKey(expOiqOrderDTO.getOrderNo())){
                   List<ExpOiqSubOrderDTO> list=groupBySub.get(expOiqOrderDTO.getOrderNo());
                   for(int i=0;i<list.size();i++){
                       ExpOiqSubOrderDTO base=list.get(i);
                       ExpOiqOrderDTO expOiqOrderDTOAddSub=new ExpOiqOrderDTO();
                       //赋值旧的值 在赋值补差价的值
                       baseCopyObj.copyWithNull(expOiqOrderDTOAddSub,expOiqOrderDTO);
                       baseCopyObj.copyWithNull(expOiqOrderDTOAddSub,base);

                       expOiqOrderDTOAddSub.setOrderNo(base.getRelateOrderNo());
                       expOiqOrderDTOAddSub.setRelateOrderNo(expOiqOrderDTO.getRelateOrderNo());
                       expOiqOrderDTOAddSub.setSubOrderNo(base.getOrderNo());
                       expOiqOrderDTOAddSub.setRealAmountTotal("");

                       String state=base.getState()==0?expOiqOrderDTO.getState():"91";
                       expOiqOrderDTOAddSub.setState(state);
                       expOiqOrderDTOListNew.add(expOiqOrderDTOAddSub);
                   }


               }
           }

       }
       return expOiqOrderDTOListNew;


    }



    public QuotationDataDTO  qryQuotationData(String subOrderNo) throws Exception{

        //补差价的数据
        BaseOrderDTO subBaseOrder=orderBaseInfoCustomService.selectBaseByOrderNo(subOrderNo);
        OrderBaseInfoMoreDTO subMore=orderBaseInfoService.selectMoreDTOByOrderId(String.valueOf(subBaseOrder.getOrderId()));

        String mainOrderNo=subBaseOrder.getRelateOrderNo();

        BaseOrderDTO mainBaseOrder=orderBaseInfoCustomService.selectBaseByOrderNo(mainOrderNo);

        OrderBaseInfoMoreDTO orderBaseInfoMoreDTO=orderBaseInfoService.selectMoreDTOByOrderId(String.valueOf(mainBaseOrder.getOrderId()));

        QuotationDataDTO quotationDataDTO=new QuotationDataDTO(subBaseOrder);
        Map<String,String> currencyMarkMap= orderUtilService.getCurrencyMark(RedisKeyUtil.OIQ_CURRENCY);
        quotationDataDTO.setCurrencyMark(currencyMarkMap.getOrDefault(quotationDataDTO.getCurrency(),""));

        Map<String, Object> map = new HashMap<>();
        map.put(SelectListUtil.ORDER_NO_LIST, Arrays.asList(subBaseOrder.getOrderNo()));
        quotationDataDTO.setOrderDetailDTOList(orderDetailService.selectSubListByMap(map));

        OrderAttributeDTO orderAttributeDTO=orderAttributeService.selectCustom(subBaseOrder.getOrderNo(),subBaseOrder.getGroupNo(), AttributeUtil.TAX_RATES);
        if(!ValidationUtil.isEmpty(orderAttributeDTO)){
            quotationDataDTO.setTaxRates(new BigDecimal(orderAttributeDTO.getAttrName()));
        }

        OrderInvoiceDTO orderInvoiceDTO=orderInvoiceDomainService.qryOneOrderInvoiceByOrder(mainBaseOrder.getOrderNo());
        Boolean flg=orderInvoiceDTO == null ? false : true;
        OrderApplicationFormDTO orderApplicationFormDTO =orderApplicationFormService.selectFormDTOByOrderNo(mainBaseOrder.getOrderNo());



        LabDTO labDTO =  centerTemplateSV.labQryByUser(String.valueOf(mainBaseOrder.getLabId()));
        BankDTO bankDTO =payTemplateSV.bankQryByAccountNo(labDTO.getAccountNo());


        QuotationFormDataDTO quotationFormDataDTO=new QuotationFormDataDTO(subBaseOrder);
        quotationDataDTO.setLabDTO(labDTO);
        quotationDataDTO.setBankDTO(bankDTO);
        quotationFormDataDTO.setSubmitFlg(orderApplicationFormDTO==null?0:1);
        quotationFormDataDTO.setSubmitShow(orderApplicationFormDTO==null?"申请表未提交":"申请表已提交");

        quotationFormDataDTO.setTo(flg?orderInvoiceDTO.getInvoiceTitle(): StrUtil.toStr(orderBaseInfoMoreDTO.getUserName())+"（申请表提交后可修改）");
        quotationFormDataDTO.setFrom(bankDTO==null?"":bankDTO.getAccountName());
        quotationFormDataDTO.setAttn(orderInvoiceDTO==null?"":orderInvoiceDTO.getPayerName());
        SysPersonDTO sysPerson = issoTemplateSV.qryPersonByUser(orderBaseInfoMoreDTO.getCsCode());

        quotationFormDataDTO.setContact(OrderUtil.getSysPersonDTOStr(sysPerson));
        quotationFormDataDTO.setAdd(flg?orderInvoiceDTO.getRegisterAddr():"");
        quotationFormDataDTO.setCsEmail(orderBaseInfoMoreDTO.getCsEmail());
        quotationFormDataDTO.setTel(orderInvoiceDTO==null?StrUtil.toStr(orderBaseInfoMoreDTO.getUserName())+
            "（申请表提交后可修改）":orderInvoiceDTO.getPayerPhone());
        quotationFormDataDTO.setEmail(orderInvoiceDTO==null?orderBaseInfoMoreDTO.getUserEmail()+"（申请表提交后可修改）":orderInvoiceDTO.getPayerEmail());


        quotationDataDTO.setQuotationFormDataDTO(quotationFormDataDTO);
        QuotationOtherDTO quotationOtherDTO=new QuotationOtherDTO(orderBaseInfoMoreDTO);
        quotationOtherDTO.setTestCycleMemo( StringUtils.isBlank(orderBaseInfoMoreDTO.getTestCycleMemo())?"":"【"+ MapClearUtil.pdfString(orderBaseInfoMoreDTO.getTestCycleMemo())+"】");
        quotationOtherDTO.setTestCycle(String.valueOf(orderBaseInfoMoreDTO.getTestCycle().intValue()));
        quotationOtherDTO.setRecommendReason(StringUtils.isBlank(subMore.getRecommendReason())?"":subMore.getRecommendReason());
        SimpleDateFormat datetimeFormat = new SimpleDateFormat("yyyy/MM/dd");
        String stateDate = datetimeFormat.format(TimeCalendarUtil.getStringToDate(subBaseOrder.getCreateDate()));
        quotationOtherDTO.setStateDate(stateDate);

        quotationDataDTO.setQuotationOtherDTO(quotationOtherDTO);
        quotationDataDTO.setBu(mainBaseOrder.getBu());

        return quotationDataDTO;
    }

    public BigDecimal getRstsAmount(String relateOrderNo) {
        Map<String,Object> map=new HashMap<>();
        map.put(SelectMapUtil.RELATE_ORDER_NO,relateOrderNo);
        map.put(SelectMapUtil.STATE_NOT,91);
        SubOrderInfoDTO subOrderInfoDTO=selectSubOrderInfoDTO(map);
        if(ValidationUtil.isEmpty(subOrderInfoDTO)){
            return BigDecimal.ZERO;
        }
        return subOrderInfoDTO.getPrice();
    }

    public SubOrderInfoDTO selectSubOrderInfoDTO(Map map) {
        return subOrderMapper.selectSubOrderInfoDTO(map);
    }

    @Override
    public int selectCountForsubNotCancel(String orderNo) {
        Map map=new HashMap();
        map.put(SelectMapUtil.SUB_ORDER_TYPE,SelectMapUtil.SUB_ORDER_TYPE);
        map.put(SelectMapUtil.RELATE_ORDER_NO,orderNo);
        map.put(SelectMapUtil.STATE_NOT, BbcStateEnum.CLOSE.getIndex());
        return subOrderMapper.selectCountByMap(map);
}




}
