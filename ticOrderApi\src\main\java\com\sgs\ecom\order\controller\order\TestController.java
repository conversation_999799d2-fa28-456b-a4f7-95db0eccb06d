package com.sgs.ecom.order.controller.order;


import com.alibaba.fastjson.JSON;
import com.sgs.ecom.op.model.leads.LeadsInfoModel;
import com.sgs.ecom.op.model.request.leads.LeadsSyncRequest;
import com.sgs.ecom.op.model.response.leads.LeadsSyncResponse;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoDTO;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.mq.EcomKafkaMessageSender;
import com.sgs.ecom.order.service.bbc.IBBCSV;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoCustomService;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.util.leads.SgsLeadsUtil;
import com.sgs.util.calendar.LunarWorkday;
import com.sgs.util.json.JsonTransUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/business/api.v2.order/test")
public class TestController {

    private static final Logger logger = LoggerFactory.getLogger(TestController.class);
    @Autowired
    private EcomKafkaMessageSender sender1;
    @Autowired
    protected JsonTransUtil jsonTransUtil;
    @Autowired
    private IOrderBaseInfoService iOrderBaseInfoService;
    @Autowired
    private IBBCSV ibbcsv;
    @Autowired
    private IOrderBaseInfoCustomService orderBaseInfoCustomService;
    @Autowired
    private MailEventUtil mailEventUtil;
    @Autowired
    private LunarWorkday lunarWorkday;


    @RequestMapping(value = "test", method = { RequestMethod.GET,RequestMethod.POST })
    public ResultBody test(	) throws Exception {
        BaseOrderDTO baseOrderDTO=orderBaseInfoCustomService.selectBaseByOrderNo("TIC100120231124103618IA0H");
        mailEventUtil.sendMail(baseOrderDTO.getOrderNo(),baseOrderDTO.getOrderType(), OiqMailEnum.CRM_CONFIRM_ORDER, 1L);
        return ResultBody.success();
    }

    @RequestMapping(value = "test1", method = { RequestMethod.GET,RequestMethod.POST })
    public ResultBody test1(@RequestBody LeadsInfoModel leadsInfoModel) throws Exception {

        LeadsSyncRequest request = new LeadsSyncRequest();
        request.setModel(leadsInfoModel);
        //test
        SgsLeadsUtil sgsLeadsUtil = new SgsLeadsUtil();
        logger.info("leadsSync==req" + JSON.toJSONString(leadsInfoModel));
        LeadsSyncResponse response =  sgsLeadsUtil.getLeadsClientUat().leadsSync(request);
        logger.info("leadsSync==response" + JSON.toJSONString(response));
        return ResultBody.success();
    }



    public static void main(String[] args) {
        List<OrderBaseInfoDTO> list=new ArrayList<>();
        for(OrderBaseInfoDTO o:list){
            System.out.println(o.getOrderNo());
        }



    }



    public boolean isSubsequence(String s, String t) {

        //int ns=0;
        int nt=0;
        for(int ns=0;ns<s.length();){
            char a=s.charAt(ns);
            for(;nt<t.length();nt++){
                if(a==t.charAt(nt)){
                    ns++;
                }
            }

        }
        return false;
    }






}
