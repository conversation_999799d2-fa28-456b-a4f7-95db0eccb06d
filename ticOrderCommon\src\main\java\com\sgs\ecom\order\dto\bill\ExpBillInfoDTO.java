package com.sgs.ecom.order.dto.bill;

import com.platform.annotation.ApiAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.enums.MonthPayStatementEnum;
import com.sgs.ecom.order.enums.MonthStatementEnum;
import com.sgs.ecom.order.enumtool.bill.BillBuEnum;

/**
 * 月结账单导出
 */
public class ExpBillInfoDTO {

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String billCycle;//账单周期

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userName;//用户名称

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String userPhone;//用户手机号

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String companyName;//月结客户

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceTitle;//付款方

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private double billAmount;//账单金额

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String storeName;//服务类型

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int state;//出票标记

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String stateName;//出票标记中文

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int payState;//付款标记

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String payStateName;//付款标记中文


	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String invoiceUpStateName;//发票上传标记

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String expressNo;//快递单号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String categoryName;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String labName;


	private String bu;

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getCategoryName() {
		return categoryName;
	}

	public void setCategoryName(String categoryName) {
		this.categoryName = categoryName;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public String getBillCycle() {
		return billCycle;
	}

	public void setBillCycle(String billCycle) {
		this.billCycle = billCycle;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public double getBillAmount() {
		return billAmount;
	}

	public void setBillAmount(double billAmount) {
		this.billAmount = billAmount;
	}

	public String getStoreName() {
		return BillBuEnum.getNameCh(bu);
	}

	public void setStoreName(String storeName) {
		this.storeName = storeName;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public int getPayState() {
		return payState;
	}

	public void setPayState(int payState) {
		this.payState = payState;
	}


	public String getExpressNo() {
		return expressNo;
	}

	public void setExpressNo(String expressNo) {
		this.expressNo = expressNo;
	}

	public String getStateName() {
		if(ValidationUtil.isEmpty(state))
			return "";

		return MonthStatementEnum.getName(state);
	}

	public String getPayStateName() {
		if(ValidationUtil.isEmpty(payState))
			return "";

		return MonthPayStatementEnum.getName(payState);
	}

	public String getInvoiceUpStateName() {
		return invoiceUpStateName;
	}

	public void setInvoiceUpStateName(String invoiceUpStateName) {
		this.invoiceUpStateName = invoiceUpStateName;
	}
}