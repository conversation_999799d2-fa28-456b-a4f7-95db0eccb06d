<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.ecom.order.mapper.order.SubOrderMapper" >

  <insert id="insertSelective" parameterType="com.sgs.ecom.order.vo.VOSubOrder" >
    insert into ORDER_BASE_INFO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        ORDER_ID,
      </if>
      <if test="orderNo != null" >
        ORDER_NO,
      </if>
      <if test="groupNo != null" >
        GROUP_NO,
      </if>
      <if test="orderType != null" >
        ORDER_TYPE,
      </if>
      <if test="relateOrderNo != null" >
        RELATE_ORDER_NO,
      </if>
      <if test="createDate != null" >
        CREATE_DATE,
      </if>
      <if test="stateDate != null" >
        STATE_DATE,
      </if>
      <if test="isPayReceived != null" >
        IS_PAY_RECEIVED,
      </if>
      <if test="recommendReason != null" >
        RECOMMEND_REASON,
      </if>
      <if test="operatorCode != null" >
        OPERATOR_CODE,
      </if>
      <if test="totalNums != null" >
        TOTAL_NUMS,
      </if>
      <if test="lineId != null" >
        LINE_ID,
      </if>
      <if test="applicationLineId != null" >
        APPLICATION_LINE_ID,
      </if>
      <if test="state != null" >
        STATE,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="labId != null" >
        LAB_ID,
      </if>
      <if test="labName != null" >
        LAB_NAME,
      </if>
      <if test="orderAmount != null" >
        ORDER_AMOUNT,
      </if>
      <if test="realAmount != null" >
        REAL_AMOUNT,
      </if>
      <if test="payState != null" >
        PAY_STATE,
      </if>
      <if test="platform != null" >
        PLATFORM,
      </if>
      <if test="platformOrder != null" >
        PLATFORM_ORDER,
      </if>
      <if test="platformAmount != null" >
        PLATFORM_AMOUNT,
      </if>
      <if test="currency != null" >
        CURRENCY,
      </if>
      <if test="isRemind != null" >
        IS_REMIND,
      </if>
      <if test="exchangeRate != null" >
        EXCHANGE_RATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="orderId != null" >
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderNo != null" >
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="groupNo != null" >
        #{groupNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null" >
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="relateOrderNo != null" >
        #{relateOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="stateDate != null" >
        #{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isPayReceived != null" >
        #{isPayReceived,jdbcType=INTEGER},
      </if>
      <if test="recommendReason != null" >
        #{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="operatorCode != null" >
        #{operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="totalNums != null" >
        #{totalNums,jdbcType=INTEGER},
      </if>
      <if test="lineId != null" >
        #{lineId,jdbcType=INTEGER},
      </if>
      <if test="applicationLineId != null" >
        #{applicationLineId,jdbcType=INTEGER},
      </if>
      <if test="state != null" >
        #{state},
      </if>
      <if test="userId != null" >
        #{userId},
      </if>
      <if test="labId != null" >
        #{labId},
      </if>
      <if test="labName != null" >
        #{labName},
      </if>
      <if test="orderAmount != null" >
        #{orderAmount},
      </if>
      <if test="realAmount != null" >
        #{realAmount},
      </if>
      <if test="payState != null" >
        #{payState},
      </if>
      <if test="platform != null" >
        #{platform},
      </if>
      <if test="platformOrder != null" >
        #{platformOrder},
      </if>
      <if test="platformAmount != null" >
        #{platformAmount},
      </if>
      <if test="currency != null" >
        #{currency},
      </if>
      <if test="isRemind != null" >
        #{isRemind},
      </if>
      <if test="exchangeRate != null" >
        #{exchangeRate},
      </if>
    </trim>
  </insert>

  <select id="selectSubOrderInfoDTO" resultType="com.sgs.ecom.order.dto.order.SubOrderInfoDTO">
    select count(1) as num,IFNULL(sum(REAL_AMOUNT),0) as price from ORDER_BASE_INFO obi
    <include refid="baseQueryWhere"/>
  </select>



  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.ecom.order.vo.VOSubOrder" >
    update ORDER_BASE_INFO
    <set >
      <if test="orderNo != null" >
        ORDER_NO=#{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="relateOrderNo != null" >
        RELATE_ORDER_NO=#{relateOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="stateDate != null" >
        STATE_DATE=#{stateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="isPayReceived != null" >
        IS_PAY_RECEIVED=#{isPayReceived,jdbcType=INTEGER},
      </if>
      <if test="recommendReason != null" >
        RECOMMEND_REASON=#{recommendReason,jdbcType=VARCHAR},
      </if>
      <if test="operatorCode != null" >
        OPERATOR_CODE=#{operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="state != null" >
        STATE=#{state},
      </if>
      <if test="realAmount != null" >
        REAL_AMOUNT=#{realAmount},
      </if>
      <if test="payState != null" >
        PAY_STATE=#{payState},
      </if>
      <if test="platform != null" >
        PLATFORM=#{platform},
      </if>
      <if test="platformOrder != null" >
        PLATFORM_ORDER=#{platformOrder},
      </if>
      <if test="platformAmount != null" >
        PLATFORM_AMOUNT=#{platformAmount},
      </if>
      <if test="currency != null" >
        CURRENCY=#{currency},
      </if>

    </set>
    where ORDER_ID = #{orderId,jdbcType=BIGINT}
  </update>


  <sql id="sqlListDTO" >
    ORDER_ID,ORDER_NO,ORDER_TYPE,RELATE_ORDER_NO,ORDER_AMOUNT,REAL_AMOUNT,OPERATOR_CODE,CREATE_DATE,GROUP_NO,
    PAY_STATE,TOTAL_NUMS,RECOMMEND_REASON,IS_PAY_RECEIVED,REFUND_STATE,STATE,PAY_METHOD,IS_REMIND,CURRENCY
  </sql>
  <resultMap id="resultDTO" type="com.sgs.ecom.order.dto.order.SubOrderDTO" >
    <id column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
    <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR" />
    <result column="GROUP_NO" property="groupNo" jdbcType="VARCHAR" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR" />
    <result column="RELATE_ORDER_NO" property="relateOrderNo" jdbcType="VARCHAR" />
    <result column="REAL_AMOUNT" property="realAmount" jdbcType="DECIMAL" />
    <result column="ORDER_AMOUNT" property="orderAmount" jdbcType="DECIMAL" />
    <result column="OPERATOR_CODE" property="operatorCode" jdbcType="VARCHAR" />
    <result column="CREATE_DATE" property="createDate" jdbcType="TIMESTAMP" />
    <result column="PAY_STATE" property="payState" jdbcType="INTEGER" />
    <result column="PAY_METHOD" property="payMethod" jdbcType="VARCHAR" />
    <result column="STATE" property="state" jdbcType="INTEGER" />
    <result column="REFUND_STATE" property="refundState" jdbcType="INTEGER" />
    <result column="IS_PAY_RECEIVED" property="isPayReceived" jdbcType="INTEGER" />
    <result column="TOTAL_NUMS" property="totalNums" jdbcType="INTEGER" />
    <result column="IS_REMIND" property="isRemind" jdbcType="INTEGER" />
    <result column="RECOMMEND_REASON" property="recommendReason" jdbcType="INTEGER" />
    <result column="CURRENCY" property="currency" jdbcType="VARCHAR" />
  </resultMap>

  <select id="selectListByMap" resultMap="resultDTO" >
    select
    <include refid="sqlListDTO" />
    from ORDER_BASE_INFO
    <include refid="baseQueryWhere"/>
    order by ORDER_ID DESC
  </select>

  <select id="selectCountByMap" resultType="Integer">
    select
    count(1)
    from ORDER_BASE_INFO
    <include refid="baseQueryWhere"/>
  </select>
  <sql id="baseQueryWhere">
    where 1=1
    <if test="orderNo!= null">
      AND ORDER_NO=#{orderNo}
    </if>
    <if test="orderType!= null">
      AND ORDER_TYPE=#{orderType}
    </if>
    <if test="subOrderType!= null">
      AND ORDER_TYPE in (101000,211000,301000)
    </if>
    <if test="relateOrderNo!= null">
      AND RELATE_ORDER_NO=#{relateOrderNo}
    </if>
    <if test="isPayReceived!= null">
      AND IS_PAY_RECEIVED=#{isPayReceived}
    </if>
    <if test="stateNot!= null">
      AND STATE !=#{stateNot}
    </if>
    <if test="roPayState != null">
      <if test="roPayState==0">
        and obi.PAY_METHOD is null
      </if>
      <if test="roPayState==1">
        and obi.PAY_METHOD is not null
      </if>
    </if>


    <if test="refundStateNotList != null ">
      AND REFUND_STATE  not  in
      <foreach collection="refundStateNotList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

    <if test="relateOrderNoList != null ">
      AND RELATE_ORDER_NO in
      <foreach collection="relateOrderNoList" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>

  </sql>


</mapper>