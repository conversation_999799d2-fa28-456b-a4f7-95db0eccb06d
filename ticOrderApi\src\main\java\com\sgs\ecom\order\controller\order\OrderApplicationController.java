package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.dto.send.TicOtherApiDTO;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.event.EventEnum;
import com.sgs.ecom.order.enumtool.send.OiqMailEnum;
import com.sgs.ecom.order.enumtool.send.OiqSmsEnum;
import com.sgs.ecom.order.enumtool.send.WechatEnum;
import com.sgs.ecom.order.infrastructure.event.ApiEventUtil;
import com.sgs.ecom.order.infrastructure.event.MailEventUtil;
import com.sgs.ecom.order.infrastructure.event.SmsEventUtil;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.request.operator.OrderOperatorReq;
import com.sgs.ecom.order.request.order.BaseOrderReq;
import com.sgs.ecom.order.request.tic.BbcCheckDateReq;
import com.sgs.ecom.order.service.util.interfaces.IOrderApplicationService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/application")
public class OrderApplicationController extends ControllerUtil {

	@Autowired
	private IOrderApplicationService orderApplicationService;
	@Autowired
	private ApiEventUtil apiEventUtil;
	@Autowired
	private SmsEventUtil smsEventUtil;
	@Autowired
	private MailEventUtil mailEventUtil;




	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "express", method = {RequestMethod.POST})
	public ResultBody express(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderReq orderReq) throws Exception {
		return ResultBody.newInstance(orderApplicationService.selectExpressMore(orderReq.getOrderId(), getPersonInfo(token)));
	}

	/**
	 * @Function: form
	 * @Description 查询申请方信息
	 * @param: [token, orderBaseIdReq]
	 * @version:
	 * @author: Xiwei_Qiu
	 * @date: 2020/12/16
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "form", method = {RequestMethod.POST})
	public ResultBody form(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderIdReq orderIdReq
	) throws Exception {
		return ResultBody.newInstance(orderApplicationService.selectForm(orderIdReq, getPersonInfo(token), getPower(token)));
	}

	/**
	 * @Function: formInfo
	 * @Description 查询申请方信息（按类型拆分）
	 * @param: [token, orderBaseIdReq]
	 * @author: Xiwei_Qiu
	 * @date: 2020/12/16
	 **/
	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "formInfo", method = {RequestMethod.POST})
	public ResultBody formInfo(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderReq orderReq) throws Exception {
		return ResultBody.newInstance(orderApplicationService.selectFormInfo(orderReq, getPersonInfo(token)));
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "formRoInfo", method = {RequestMethod.POST})
	public ResultBody formRoInfo(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderReq orderReq
	) throws Exception {
		return ResultBody.newInstance(orderApplicationService.selectFormInfo(orderReq, getPersonInfo(token)));
	}


	/**
	 * @Function: confirm
	 * @Description
	 * @param: [token, orderOperatorReq]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@CheckType
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true, permission = {"4305", "4515", "4516"})
	@RequestMapping(value = "confirm", method = {RequestMethod.POST})
	public ResultBody confirm(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderOperatorReq orderOperatorReq) throws Exception {
		orderApplicationService.confirmForm(orderOperatorReq, getPersonInfo(token));
		if(OrderTypeEnum.OIQ_ORDER.getIndex().equals(orderOperatorReq.getOrderType())){
			smsEventUtil.sendSms(orderOperatorReq.getEventOrderNo(),orderOperatorReq.getOrderType(),orderOperatorReq.getConfirmFormFlg()? OiqSmsEnum.CONFIRMFORM:OiqSmsEnum.BACKFORM);
			mailEventUtil.sendMail(orderOperatorReq.getEventOrderNo(),orderOperatorReq.getOrderType(),orderOperatorReq.getConfirmFormFlg()? OiqMailEnum.CONFIRM_FORM:OiqMailEnum.BACK_FORM,1L);
			apiEventUtil.sendWechatMsg(orderOperatorReq.getEventOrderNo(), EventEnum.SEND_WECHAT_MSG,orderOperatorReq.getConfirmFormFlg()?WechatEnum.CONFIRMFORM:WechatEnum.BACKFORM);
		}
		if(OrderTypeEnum.OIQ_SMALL_ORDER.getIndex().equals(orderOperatorReq.getOrderType()) && orderOperatorReq.getConfirmFormFlg()){
			smsEventUtil.sendSms(orderOperatorReq.getEventOrderNo(),orderOperatorReq.getOrderType(), OiqSmsEnum.PORTAL_CONFIRM_FORM);
			mailEventUtil.sendMail(orderOperatorReq.getEventOrderNo(),orderOperatorReq.getOrderType(),OiqMailEnum.PORTAL_CONFIRM_FORM,1L);
		}

		return ResultBody.success();
	}

	/**
	*@Function: confirm新（bbc）
	*@Description
	*@param: [token, orderOperatorReq]
	*@author: Xiwei_Qiu @date: 2022/5/24 @version:
	**/
	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true, permission = {"4305", "4515", "4516"})
	@RequestMapping(value = "confirmForm", method = {RequestMethod.POST})
	public ResultBody confirmForm(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderOperatorReq orderOperatorReq) throws Exception {
		orderApplicationService.applicationConfirm(orderOperatorReq, getPersonInfo(token));
		return ResultBody.success();
	}

	/**
	 *
	 * @param token
	 * @param
	 * @return
	 * @throws Exception
	 */
	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true, permission = {"4305", "4515", "4516"})
	@RequestMapping(value = "modDeadlineTime", method = {RequestMethod.POST})
	public ResultBody modDeadlineTime(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody OrderOperatorReq orderOperatorReq) throws Exception {
		orderApplicationService.modDeadlineTime(orderOperatorReq, getPersonInfo(token));
		return ResultBody.success();
	}




	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "invoice", method = {RequestMethod.POST})
	public ResultBody invoice(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderIdReq orderIdReq
	) throws Exception {
		return ResultBody.newInstance(orderApplicationService.selectInvoice(orderIdReq.getOrderId(), getPersonInfo(token), getPower(token)));
	}



	/**
	 * @Function: formPDF
	 * @Description 导出申请表
	 * @param: [orderIdReq, token, httpServletResponse]
	 * @author: Xiwei_Qiu @date: 2021/5/14 @version:
	 **/
	@AuthRequired(login = "SGS", sign = true, permission = {"4510"})
	@RequestMapping(value = "formPDF", method = {RequestMethod.POST})
	public void formPDF(
		@RequestBody OrderIdReq orderIdReq,
		@RequestHeader(value = "accessToken") String token,
		HttpServletResponse httpServletResponse
	) throws Exception {
		orderApplicationService.getFormPDF(orderIdReq.getOrderId(), getPersonInfo(token), getPower(token), httpServletResponse);
		httpServletResponse.setContentType("application/pdf");
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "memoPDF", method = {RequestMethod.POST})
	public void memoPDF(
		@RequestBody OrderIdReq orderIdReq,
		@RequestHeader(value = "accessToken") String token,
		HttpServletResponse httpServletResponse
	) throws Exception {
		orderApplicationService.getMemoPDF(orderIdReq.getOrderId(), getPersonInfo(token), getPower(token), httpServletResponse);
	}


	/**
	 *@Function: saveCheckDate
	 *@Description 保存验货信息
	 *@param: [orderIdReq, token]
	 *@author: Xiwei_Qiu @date: 2022/6/10 @version:
	 **/
	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "saveCheckDate", method = {RequestMethod.POST})
	public ResultBody saveCheckDate(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody BbcCheckDateReq bbcCheckDateReq) throws Exception {
		orderApplicationService.saveCheckDate(bbcCheckDateReq, getPersonInfo(token));
		return ResultBody.success();
	}


	/**
	* @params [token, orderReq]
	* @return com.sgs.ecom.order.util.ResultBody
	* @description lv申请表数据
	* <AUTHOR> || created at 2023/5/18 9:38
	*/
	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryFormData", method = {RequestMethod.POST})
	public ResultBody qryFormData(
		@RequestHeader(value = "accessToken") String token,
		@Validated(BaseBean.Query.class)
		@RequestBody OrderReq orderReq) throws Exception {
		return ResultBody.newInstance(orderApplicationService.qryFormData(orderReq, getPersonInfo(token)));
	}

	@HystrixCommand
//	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryCategoryList", method = {RequestMethod.POST})
	public ResultBody qryCategoryList(
			@RequestHeader(value = "accessToken") String token
	) throws Exception {
		return ResultBody.newInstance(orderApplicationService.qryCategoryList( getPersonInfo(token), getPower(token)));
	}

	/**
	 * @Description : 根据订单号查询是否是上海账号支付
	 * <AUTHOR> Zhang
	 * @Date  2023/8/11
	 * @param baseOrderReq:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "NULL", sign = true)
	@RequestMapping(value = "qryIsElectron", method = {RequestMethod.POST})
	public ResultBody qryIsElectron(
			@RequestBody BaseOrderReq baseOrderReq
	) throws Exception {
		return ResultBody.newInstance(orderApplicationService.qryIsElectron(baseOrderReq.getOrderNo()));
	}

	/**
	 * @Description : RSTS-打印申请表数据接口整合
	 * <AUTHOR> Zhang
	 * @Date  2023/11/23
	 * @param token:
	 * @param orderReq:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryFormDataByRSTS", method = {RequestMethod.POST})
	public ResultBody qryFormDataByRSTS(
			@RequestHeader(value = "accessToken") String token,
			@RequestBody OrderReq orderReq
	) throws Exception {
		return ResultBody.newInstance(orderApplicationService.qryFormDataByRSTS(orderReq, getPersonInfo(token)));
	}
}
