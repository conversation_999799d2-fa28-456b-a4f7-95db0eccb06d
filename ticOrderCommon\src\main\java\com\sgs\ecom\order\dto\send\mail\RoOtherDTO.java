package com.sgs.ecom.order.dto.send.mail;

import com.sgs.ecom.order.dto.detail.OrderExpressDTO;
import com.sgs.ecom.order.dto.order.OrderOperatorLogDTO;
import com.sgs.ecom.order.dto.send.OperatorReqDTO;
import com.sgs.ecom.order.vo.VOOrderExpress;
import com.sgs.ecom.order.vo.VOSubOrder;
import java.util.List;

public class RoOtherDTO {

    private int sendSalesFlg=1;
    private int addPdfFlg;
    private Long logId;
    private VOSubOrder subOrder;
    private OperatorReqDTO operatorReqDTO;
    private List<OrderOperatorLogDTO> logList;
    private List<OrderExpressDTO> expressList;
    private VOOrderExpress voOrderExpress;
    private String sendCsEmail;
    private int dateNum;

    public int getSendSalesFlg() {
        return sendSalesFlg;
    }

    public void setSendSalesFlg(int sendSalesFlg) {
        this.sendSalesFlg = sendSalesFlg;
    }

    public VOSubOrder getSubOrder() {
        return subOrder;
    }

    public void setSubOrder(VOSubOrder subOrder) {
        this.subOrder = subOrder;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public OperatorReqDTO getOperatorReqDTO() {
        return operatorReqDTO;
    }

    public void setOperatorReqDTO(OperatorReqDTO operatorReqDTO) {
        this.operatorReqDTO = operatorReqDTO;
    }

    public int getAddPdfFlg() {
        return addPdfFlg;
    }

    public void setAddPdfFlg(int addPdfFlg) {
        this.addPdfFlg = addPdfFlg;
    }

    public String getSendCsEmail() {
        return sendCsEmail;
    }

    public void setSendCsEmail(String sendCsEmail) {
        this.sendCsEmail = sendCsEmail;
    }

    public int getDateNum() {
        return dateNum;
    }

    public void setDateNum(int dateNum) {
        this.dateNum = dateNum;
    }

    public List<OrderOperatorLogDTO> getLogList() {
        return logList;
    }

    public void setLogList(List<OrderOperatorLogDTO> logList) {
        this.logList = logList;
    }

    public List<OrderExpressDTO> getExpressList() {
        return expressList;
    }

    public void setExpressList(List<OrderExpressDTO> expressList) {
        this.expressList = expressList;
    }

    public VOOrderExpress getVoOrderExpress() {
        return voOrderExpress;
    }

    public void setVoOrderExpress(VOOrderExpress voOrderExpress) {
        this.voOrderExpress = voOrderExpress;
    }
}
