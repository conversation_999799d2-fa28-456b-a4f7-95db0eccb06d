package com.sgs.ecom.order.controller.order;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.request.order.OrderCustomerReq;
import com.sgs.ecom.order.service.order.interfaces.IOrderCustomerService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/business/api.v2.order/customer")
public class OrderCustomerController extends ControllerUtil {

	@Resource
	private IOrderCustomerService orderCustomerService;

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "getCustomerIdList", method = {RequestMethod.POST})
	public ResultBody getCustomerIdList(
			@RequestBody OrderCustomerReq orderCustomerReq) throws Exception {
		return ResultBody.newInstance(orderCustomerService.getCustomerIdList(orderCustomerReq));
	}

	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "saveCustomer", method = {RequestMethod.POST})
	public ResultBody saveCustomer(
			@RequestBody OrderCustomerReq orderCustomerReq) throws Exception {
		orderCustomerService.saveCustomer(orderCustomerReq);
		return ResultBody.success();
	}
}
