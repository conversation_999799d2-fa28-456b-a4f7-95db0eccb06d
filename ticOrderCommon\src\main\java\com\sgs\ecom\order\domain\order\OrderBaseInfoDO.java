package com.sgs.ecom.order.domain.order;

import com.platform.bo.BOSysPerson;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.SysPersonDTO;
import com.sgs.ecom.order.dto.center.BusinessLineDTO;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.dml.DmlMainReqDTO;
import com.sgs.ecom.order.dto.oiq.OiqOrderReqDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoCheckDTO;
import com.sgs.ecom.order.dto.order.OrderBaseInfoDTO;
import com.sgs.ecom.order.dto.rpc.dml.BaseInfoRpcDTO;
import com.sgs.ecom.order.dto.rpc.dml.CreateInquiryRpcDTO;
import com.sgs.ecom.order.dto.rpc.dml.DmlApplicationDTO;
import com.sgs.ecom.order.dto.user.UserDTO;
import com.sgs.ecom.order.entity.order.OrderBaseInfo;
import com.sgs.ecom.order.enums.PayMethod;
import com.sgs.ecom.order.enumtool.OrderStateEnum;
import com.sgs.ecom.order.enumtool.OrderTypeEnum;
import com.sgs.ecom.order.enumtool.application.ReportLuaEnum;
import com.sgs.ecom.order.enumtool.dml.ReportFormCodeEnum;
import com.sgs.ecom.order.enumtool.order.BaseOrderStateEnum;
import com.sgs.ecom.order.enumtool.order.FromSourceEnum;
import com.sgs.ecom.order.request.oiq.OiqApplicationReq;
import com.sgs.ecom.order.request.oiq.OiqOrderBaseReq;
import com.sgs.ecom.order.request.oiq.OiqOrderReq;
import com.sgs.ecom.order.request.operator.OrderCloseReq;
import com.sgs.ecom.order.request.order.OrderUploadReportReq;
import com.sgs.ecom.order.request.rsts.SubCreateReq;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.vo.VOSubOrder;
import com.sgs.ecom.order.vo.order.VOChangeOrderPrice;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public class OrderBaseInfoDO extends OrderBaseInfo {

    private BaseCopyObj baseCopy = new BaseCopyObj();

    public void copyPrice(OrderBaseInfo orderBaseInfo,OrderBaseInfo inquiry){
        orderBaseInfo.setOrderAmount(inquiry.getOrderAmount());
        orderBaseInfo.setRealAmount(inquiry.getRealAmount());
        orderBaseInfo.setDiscountAmount(inquiry.getDiscountAmount());
    }


    public  OrderBaseInfo getOrderInfoByState(BaseOrderDTO orderDTO, int state){
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setState(state);
        orderBaseInfo.setOrderId(orderDTO.getOrderId());
        orderBaseInfo.setStateDate(orderDTO.getDateStr());
        orderBaseInfo.setHisState(orderDTO.getState());
       return orderBaseInfo;
    }

    public OrderBaseInfo getOiqInquiryByDmlRpc(BaseOrderDTO baseOrderDTO,CreateInquiryRpcDTO createInquiryRpcDTO) {
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        //状态为待确认
        initState(orderBaseInfo, OrderStateEnum.waitConfirm.getIndex());
        //baseInfo数据
        BaseInfoRpcDTO baseInfoRpcDTO=createInquiryRpcDTO.getBaseInfo();
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        baseCopyObj.copy(orderBaseInfo,baseInfoRpcDTO);
        if(baseInfoRpcDTO.getTestCycle()!=null){
            orderBaseInfo.setTestCycle(BigDecimal.valueOf(baseInfoRpcDTO.getTestCycle()));
        }
        BigDecimal finalBig = createInquiryRpcDTO.getBaseInfo().getOrderAmount();
        BigDecimal tax=createInquiryRpcDTO.getBaseInfo().getTaxRates();
        BigDecimal otherAmount = finalBig.multiply(tax.divide(new BigDecimal(100))).setScale(2, BigDecimal.ROUND_HALF_UP);
        //计算已选项目税后总价
        BigDecimal total=finalBig.add(otherAmount).setScale(2, BigDecimal.ROUND_HALF_UP);
        BigDecimal disCountAmount=total.subtract(baseInfoRpcDTO.getRealAmount());

        orderBaseInfo.setDiscountAmount(disCountAmount);
        orderBaseInfo.setOrderNo(baseOrderDTO.getRelateOrderNo());
        orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        return orderBaseInfo;
    }


    public void getOrderBaseInfoMoreByRpc(OrderBaseInfo orderBaseInfo, LabDTO labDTO, BusinessLineDTO businessLineDTO){
        if(!ValidationUtil.isEmpty(labDTO)){
            orderBaseInfo.setLabId(labDTO.getLabId());
            orderBaseInfo.setLabName(labDTO.getLabName());
        }else{
            orderBaseInfo.setLabName("");
        }
        if(!ValidationUtil.isEmpty(businessLineDTO)){
            orderBaseInfo.setLineId(businessLineDTO.getConfigId());
            orderBaseInfo.setBusinessLine(businessLineDTO.getConfigName());
        }
    }

    private void initState(OrderBaseInfo orderBaseInfo,String state) {
        orderBaseInfo.setState(Integer.parseInt(state));
    }




    private void update(OrderBaseInfo orderBaseInfo, Long orderId, String dateStr, UserDTO userDTO) {
        undelete(orderBaseInfo);
        orderBaseInfo.setUserId(userDTO.getUserId());
       /* if(ValidationUtil.isEmpty(userDTO.getUserId())){
            orderBaseInfo.setUserIdFlag(1);
        }*/
        orderBaseInfo.setOrderId(orderId);
        orderBaseInfo.setStateDate(dateStr);
        orderBaseInfo.setTotalNums(1);
    }

    private void undelete(OrderBaseInfo orderBaseInfo) {
        orderBaseInfo.setIsDelete(0);
    }


    private void add(OrderBaseInfo orderBaseInfo,String orderNo,UserDTO userDTO,String dateStr) {
        undelete(orderBaseInfo);
        orderBaseInfo.setUserId(userDTO.getUserId());
        orderBaseInfo.setCreateDate(dateStr);
        orderBaseInfo.setStateDate(dateStr);
        orderBaseInfo.setOrderNo(orderNo);
        orderBaseInfo.setFromSource(FromSourceEnum.ORDER_CREATE.getFromSource());
        orderBaseInfo.setTotalNums(1);
    }







    public OrderBaseInfo getOrderBaseInfo(OrderCloseReq orderCloseReq, OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, String enumStr) {
        OrderBaseInfo orderBaseInfo = initOrderData(orderBaseInfoCheckDTO,Integer.parseInt(OrderStateEnum.CANCELLED.getIndex()));
        orderBaseInfo.setCloseCode(orderCloseReq.getCloseCode());
        orderBaseInfo.setCloseReason(enumStr);
        orderBaseInfo.setHisState(orderBaseInfoCheckDTO.getHisState());
        return orderBaseInfo;
    }

    private OrderBaseInfo initOrderData(OrderBaseInfoCheckDTO orderBaseInfoCheckDTO, int state) {
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        orderBaseInfo.setOrderId(orderBaseInfoCheckDTO.getOrderId());
        orderBaseInfo.setState(state);
        orderBaseInfo.setStateDate(UseDateUtil.getDateString(new Date()));
        return orderBaseInfo;
    }



    //移动端使用 后期都使用
    public void companyInfo(OiqOrderReq oiqOrderReq, OrderBaseInfo orderBaseInfo) {
        OiqApplicationReq application = oiqOrderReq.getApplication();
        orderBaseInfo.setCompanyName(ValidationUtil.isEmpty(application)?"":application.getCompanyNameCn());
        orderBaseInfo.setCompanyNameEn(ValidationUtil.isEmpty(application)?"":application.getCompanyNameEn());
        orderBaseInfo.setCompanyAddressCn(ValidationUtil.isEmpty(application)?"":application.getCompanyAddressCn());
        orderBaseInfo.setCompanyAddressEn(ValidationUtil.isEmpty(application)?"":application.getCompanyAddressEn());
    }


    public OrderBaseInfo getOrderBaseInfoByClose(BaseOrderDTO baseOrderDTO, String enumStr) {
        OrderBaseInfoCheckDTO orderBaseInfoCheckDTO = new OrderBaseInfoCheckDTO();
        orderBaseInfoCheckDTO.setOrderId(baseOrderDTO.getOrderId());
        OrderBaseInfo orderBaseInfo = initOrderData(orderBaseInfoCheckDTO,Integer.parseInt(OrderStateEnum.CANCELLED.getIndex()));
        orderBaseInfo.setCloseReason(enumStr);
        orderBaseInfo.setHisState(orderBaseInfoCheckDTO.getHisState());
        return orderBaseInfo;
    }

    public OrderBaseInfo copyOrderBaseInfo(List<OrderBaseInfoDTO> orderBaseInfoDTOList, OrderUploadReportReq uploadReportReq) {
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        baseCopy.copy(orderBaseInfo,orderBaseInfoDTOList.get(0));
        orderBaseInfo.setPlatform("SODA");
        orderBaseInfo.setPlatformOrder(uploadReportReq.getPlatformOrder());
        return orderBaseInfo;
    }

    //小门户使用 给订单添加数据
    public OrderBaseInfo addOrderByApplication(OiqOrderReqDTO oiqOrderReqDTO, OrderTypeEnum orderTypeEnum){
        OiqOrderBaseReq orderBaseReq=oiqOrderReqDTO.getOiqOrderReq().getOrderBase();
        OrderBaseInfo orderBaseInfo = init(orderBaseReq,oiqOrderReqDTO,orderTypeEnum);
        String orderNo=(orderTypeEnum==OrderTypeEnum.OIQ_SMALL_INQUIRY)?oiqOrderReqDTO.getInquiryOrderNo():oiqOrderReqDTO.getOrderNo();
        add(orderBaseInfo,orderNo,oiqOrderReqDTO.getUserDTO(),oiqOrderReqDTO.getDateStr());
        if (OrderTypeEnum.OIQ_SMALL_INQUIRY == orderTypeEnum && !ValidationUtil.isEmpty(oiqOrderReqDTO.getOiqOrderReq().getSysPerson())) {
            orderBaseInfo.setCreateCode(oiqOrderReqDTO.getOiqOrderReq().getSysPerson().getPersonCode());
        }
        orderBaseInfo.setOrderType(Integer.parseInt(orderTypeEnum.getIndex()));
        orderBaseInfo.setIsTest(oiqOrderReqDTO.getUserDTO().getIsTest());
        int state = orderTypeEnum == OrderTypeEnum.OIQ_SMALL_INQUIRY
                ? BaseOrderStateEnum.DISTRIBUTION.getIndex()
                : oiqOrderReqDTO.getOiqOrderReq().getType() == 0
                ? BaseOrderStateEnum.WAITAPPLY.getIndex()
                : BaseOrderStateEnum.WAITSEND.getIndex();
        if(orderTypeEnum==OrderTypeEnum.OIQ_SMALL_ORDER && "2700".equals(oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBu())
                && oiqOrderReqDTO.getOiqOrderReq().getType()!=0){
            state= BaseOrderStateEnum.WAITEXAMINE.getIndex();
        }


        orderBaseInfo.setState(state);
        orderBaseInfo.setUserPhone(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPhone());
        orderBaseInfo.setUserName(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPerson());
        orderBaseInfo.setUserEmail(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkEmail());
        orderBaseInfo.setLabId(oiqOrderReqDTO.getLabDTO().getLabId());
        return orderBaseInfo;
    }


    //移动端使用 更新数据
    public OrderBaseInfo updateOrderByApplication(OiqOrderReqDTO oiqOrderReqDTO, OrderTypeEnum orderTypeEnum){
        OiqOrderBaseReq oiqOrderBaseReq=oiqOrderReqDTO.getOiqOrderReq().getOrderBase();
        OrderBaseInfo orderBaseInfo = init(oiqOrderBaseReq,oiqOrderReqDTO,orderTypeEnum);
        Long orderId=(orderTypeEnum==OrderTypeEnum.OIQ_SMALL_INQUIRY)?oiqOrderReqDTO.getInquiryOrderId():oiqOrderReqDTO.getOrderDTO().getOrderId();
        String orderNo=(orderTypeEnum==OrderTypeEnum.OIQ_SMALL_INQUIRY)?oiqOrderReqDTO.getInquiryOrderNo():oiqOrderReqDTO.getOrderNo();
        orderBaseInfo.setOrderNo(orderNo);
        orderBaseInfo.setIsTest(oiqOrderReqDTO.getUserDTO().getIsTest());
        if (orderTypeEnum == OrderTypeEnum.OIQ_SMALL_INQUIRY && (oiqOrderReqDTO.getOiqOrderReq().getType() == 1 || oiqOrderReqDTO.getOiqOrderReq().getType() == 2)) {
            orderBaseInfo.setState(BaseOrderStateEnum.WAITSEND.getIndex());
        }
        if(orderTypeEnum==OrderTypeEnum.OIQ_SMALL_ORDER && "2700".equals(oiqOrderReqDTO.getOiqOrderReq().getOrderBase().getBu())
                && oiqOrderReqDTO.getOiqOrderReq().getType()!=0){
            orderBaseInfo.setState( BaseOrderStateEnum.WAITEXAMINE.getIndex());
        }
        update(orderBaseInfo,orderId,oiqOrderReqDTO.getDateStr(),oiqOrderReqDTO.getUserDTO());
        orderBaseInfo.setUserPhone(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPhone());
        orderBaseInfo.setUserName(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkPerson());
        orderBaseInfo.setUserEmail(oiqOrderReqDTO.getOiqOrderReq().getApplication().getLinkEmail());
        orderBaseInfo.setLabId(oiqOrderReqDTO.getLabDTO().getLabId());
        return orderBaseInfo;
    }


    //移动端使用 后期都使用
    private OrderBaseInfo init(OiqOrderBaseReq orderBaseReq, OiqOrderReqDTO oiqOrderReqDTO, OrderTypeEnum orderTypeEnum) {
        OrderBaseInfo orderBaseInfo = new OrderBaseInfo();
        baseCopy.copyWithNull(orderBaseInfo,orderBaseReq);
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getBusinessLineDTO())){
            orderBaseInfo.setLineId(oiqOrderReqDTO.getBusinessLineDTO().getConfigId());
            orderBaseInfo.setBusinessLine(oiqOrderReqDTO.getBusinessLineDTO().getConfigName());
        }
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getLabDTO())){
            orderBaseInfo.setLabName(oiqOrderReqDTO.getLabDTO().getLabName());
        }
        orderBaseInfo.setGroupNo(oiqOrderReqDTO.getGroupNo());
        //
        if(!ValidationUtil.isEmpty(oiqOrderReqDTO.getOrderReport())) {
            orderBaseInfo.setReportLuaCode(oiqOrderReqDTO.getOrderReport().getReportLuaCode());
            orderBaseInfo.setReportLua(oiqOrderReqDTO.getOrderReport().getReportLua());
            orderBaseInfo.setReportFormCode(oiqOrderReqDTO.getOrderReport().getReportFormCode());
            orderBaseInfo.setReportForm(oiqOrderReqDTO.getOrderReport().getReportForm());
        }
        int state=(orderTypeEnum==OrderTypeEnum.OIQ_SMALL_INQUIRY)? BaseOrderStateEnum.DISTRIBUTION.getIndex():
                oiqOrderReqDTO.getOiqOrderReq().getType()==0? BaseOrderStateEnum.WAITAPPLY.getIndex(): BaseOrderStateEnum.WAITSEND.getIndex();
        orderBaseInfo.setState(state);
        return orderBaseInfo;
    }


    public OrderBaseInfo updateByDmlApplication(BaseOrderDTO baseOrderDTO, DmlApplicationDTO dmlApplicationDTO){
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        orderBaseInfo.setPlatform(dmlApplicationDTO.getPlatform());
        orderBaseInfo.setPlatformOrder(dmlApplicationDTO.getPlatformOrder());
        //更新数据在orderBase里面
        BaseInfoRpcDTO baseInfoRpcDTO=dmlApplicationDTO.getBaseInfo();

        //private String labCode;
        //orderBaseInfo.setCsCode(baseInfoRpcDTO.getCsCode());
        //orderBaseInfo.setCsEmail(baseInfoRpcDTO.getCsEmail());
        //orderBaseInfo.setCsName(baseInfoRpcDTO.getCsName());
        orderBaseInfo.setOperatorCode(baseInfoRpcDTO.getCsCode());
        orderBaseInfo.setBusinessPersonEmail(baseInfoRpcDTO.getCsEmail());
        orderBaseInfo.setCsPhone(baseInfoRpcDTO.getCsPhone());
        orderBaseInfo.setIsUrgent(baseInfoRpcDTO.getIsUrgent());
        ReportLuaEnum reportLuaEnum=ReportLuaEnum.getEnumByDmlCode(baseInfoRpcDTO.getReportLua());
        orderBaseInfo.setReportLuaCode(reportLuaEnum.getName().toUpperCase());
        orderBaseInfo.setReportLua(reportLuaEnum.getNameCh());
        orderBaseInfo.setReportFormCode(baseInfoRpcDTO.getReportForm());
        orderBaseInfo.setReportForm(ReportFormCodeEnum.getNameCh(baseInfoRpcDTO.getReportForm()));
        return orderBaseInfo;
    }


    public OrderBaseInfo copyBaseOrder(BaseOrderDTO baseOrderDTO, BOSysPerson personInfo, VOChangeOrderPrice changeOrderPrice) {
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        orderBaseInfo.setServiceAmount(changeOrderPrice.getUrgentAmount());
        orderBaseInfo.setRealAmount(changeOrderPrice.getRealAmount());
        orderBaseInfo.setIsUrgent(changeOrderPrice.getIsUrgent());
        orderBaseInfo.setOrderAmount(changeOrderPrice.getOrderAmount());
        orderBaseInfo.setCsDiscountAmount(changeOrderPrice.getCsDiscountAmount());
        orderBaseInfo.setUrgentName(changeOrderPrice.getUrgentName());
        if(StringUtils.isBlank(personInfo.getPersonCode())){
            orderBaseInfo.setCsCode(personInfo.getPersonCode());
            orderBaseInfo.setCsEmail(personInfo.getPersonMail());
        }
        return orderBaseInfo;
    }

    public OrderBaseInfo EntitySetMonthPay(BaseOrderDTO baseOrderDTO){
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        orderBaseInfo.setPayState(1);
        orderBaseInfo.setIsPayReceived(1);
        orderBaseInfo.setMonthPay(2);
        orderBaseInfo.setPayMethod(PayMethod.FALSE_MONTH.getIndex());
        return orderBaseInfo;
    }

    public OrderBaseInfo EntitySetUnMonthPay(BaseOrderDTO baseOrderDTO){
        OrderBaseInfo orderBaseInfo=new OrderBaseInfo();
        orderBaseInfo.setOrderId(baseOrderDTO.getOrderId());
        orderBaseInfo.setPayState(0);
        orderBaseInfo.setIsPayReceived(0);
        orderBaseInfo.setMonthPay(0);
        orderBaseInfo.setPayMethod(PayMethod.OFFLINE.getIndex());
        return orderBaseInfo;
    }



    //移动端使用 后期都使用
    public void oiqOrderToDml(OiqOrderReqDTO oiqOrderReqDTO, OrderBaseInfo orderBaseInfo){
        BaseOrderDTO baseOrderDTO=new BaseOrderDTO();
        baseCopy.copy(baseOrderDTO,orderBaseInfo);
        DmlMainReqDTO dmlMainReqDTO=oiqOrderReqDTO.getDmlMainReqDTO();

        baseOrderDTO.setLineCode(oiqOrderReqDTO.getBusinessLineDTO().getPlatformCode());
        baseOrderDTO.setBusinessLine(oiqOrderReqDTO.getBusinessLineDTO().getConfigName());
        dmlMainReqDTO.setBaseOrderDTO(baseOrderDTO);
        dmlMainReqDTO.setOwnerEmail(baseOrderDTO.getCsEmail());
    }


    public void entityAddCsPerson(OrderBaseInfo orderBaseInfo, SysPersonDTO personDTO){
        orderBaseInfo.setCsCode(personDTO.getPersonCode());
        orderBaseInfo.setCsName(personDTO.getPersonName());
        orderBaseInfo.setCsPhone(personDTO.getPersonPhone());
        orderBaseInfo.setCsEmail(personDTO.getPersonMail());
        orderBaseInfo.setCsNameEn(personDTO.getPersonNameEn());

    }



    /**
    * 获取rsts的补充订单
    * @param subOrderNo
    * @param subCreateReq
    * @param baseOrderDTO
    * @return com.sgs.ecom.order.vo.VOSubOrder
    * <AUTHOR> || created at 2025/1/9 10:11
    * @throws Exception 抛出错误
    */
    public static VOSubOrder getRSTSSubOrder(String subOrderNo, SubCreateReq subCreateReq, BaseOrderDTO baseOrderDTO){

        VOSubOrder voSubOrder=getBaseSub();
        voSubOrder.setOrderNo(subOrderNo);
        voSubOrder.setRelateOrderNo(subCreateReq.getOrderNo());
        voSubOrder.setIsPayReceived(0);
        voSubOrder.setUserId(baseOrderDTO.getUserId());
        voSubOrder.setPlatform(subCreateReq.getPlatform());
        voSubOrder.setPlatformOrder(subCreateReq.getPlatformOrder());
        voSubOrder.setRecommendReason(subCreateReq.getMemo());
        voSubOrder.setOrderType(Integer.parseInt(OrderTypeEnum.RSTS_SUB_ORDER.getIndex()));
        voSubOrder.setOperatorCode(subCreateReq.getCsCode());
        BigDecimal newPrice=subCreateReq.getOrderAmount().setScale(2,BigDecimal.ROUND_HALF_UP);
        voSubOrder.setRealAmount(newPrice);
        voSubOrder.setPlatformAmount(newPrice);
        voSubOrder.setCurrency(subCreateReq.getCurrency());
        voSubOrder.setPayState(subCreateReq.getOrderState()==80?1:0);
        voSubOrder.setState(subCreateReq.getOrderState());
        voSubOrder.setLabId(baseOrderDTO.getLabId());
        voSubOrder.setLabName(baseOrderDTO.getLabName());
        return voSubOrder;
    }


    public static VOSubOrder getBaseSub(){
        VOSubOrder voSubOrder=new VOSubOrder();
        String date= UseDateUtil.getDateString(new Date());
        voSubOrder.setCreateDate(date);
        voSubOrder.setStateDate(date);
        voSubOrder.setTotalNums(1);
        return voSubOrder;
    }
}
