package com.sgs.ecom.order.dto.rpc.dml;

import com.sgs.ecom.order.request.base.BaseFileReq;

import java.util.ArrayList;
import java.util.List;

public class QuotationFileDTO extends BaseFileReq {
    private String quotationNo;

    public String getQuotationNo() {
        return quotationNo;
    }

    public void setQuotationNo(String quotationNo) {
        this.quotationNo = quotationNo;
    }

    public static List<BaseFileReq> getBase(List<QuotationFileDTO> list){
        List<BaseFileReq> baseFileReqList=new ArrayList<>();
        for(int n=0;n<list.size();n++){
            QuotationFileDTO quotationFileDTO=list.get(n);
            baseFileReqList.add(quotationFileDTO);
        }
        return baseFileReqList;
    }
}
