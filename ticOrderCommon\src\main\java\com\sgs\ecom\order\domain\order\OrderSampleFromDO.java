package com.sgs.ecom.order.domain.order;

import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.rpc.CenterSampleDTO;
import com.sgs.ecom.order.dto.rpc.dml.CreateInquiryRpcDTO;
import com.sgs.ecom.order.dto.rpc.dml.SampleFormRpcDTO;
import com.sgs.ecom.order.entity.order.OrderSampleFrom;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang.StringUtils;

public class OrderSampleFromDO extends OrderSampleFrom {

    public static final String SAMPLE_NAME="sampleName";
    public static final String SAMPLE_NAME_CN="sampleNameCn";
    public static final String SAMPLE_NAME_EN="sampleNameEn";

    public static final String OIQ_SAMPLE_NAME_CN="SAMPLE_NAME";
    public static final String OIQ_SAMPLE_NAME_EN="SAMPLE_NAME_EN";

    public static final String OIQ_PRODUCT_INFO="PRODUCT_INFO";
    public static final String OIQ_PRODUCT_INFO_EN="PRODUCT_INFO_EN";
    public static final String OIQ_PRODUCT_BATCH="PRODUCT_BATCH";
    public static final String OIQ_PRODUCT_BATCH_EN="PRODUCT_BATCH_EN";
    public static final String OIQ_MATERIAL_GRADE="MATERIAL_GRADE";
    public static final String OIQ_MATERIAL_GRADE_EN="MATERIAL_GRADE_EN";
    public static final String OIQ_HEAT_NO="HEAT_NO";
    public static final String OIQ_HEAT_NO_EN="HEAT_NO_EN";
    public static final String OIQ_BUYERS_NAME="BUYERS_NAME";
    public static final String OIQ_BUYERS_NAME_EN="BUYERS_NAME_EN";
    public static final String OIQ_PRODUCTER_NAME="PRODUCTER_NAME";
    public static final String OIQ_PRODUCTER_NAME_EN="PRODUCTER_NAME_EN";
    public static final String OIQ_REMARK="REMARK";
    public static final String OIQ_REMARK_EN="REMARK_EN";

    public static final String OIQ_USING_CHEMISTRY="USING_CHEMISTRY";
    public static final String OIQ_SAMPLE_COMPOSITION="SAMPLE_COMPOSITION";
    public static final String OIQ_SAMPLE_COLOR="SAMPLE_COLOR";
    public static final String OIQ_SAMPLE_FEATURES="SAMPLE_FEATURES";


    private BaseCopyObj baseCopyObj = new BaseCopyObj();


    public List<OrderSampleFrom> rpcFormToForm(BaseOrderDTO orderDTO, CreateInquiryRpcDTO createInquiryRpcDTO){
        List<OrderSampleFrom> orderSampleFromList=new ArrayList<>();
        for(SampleFormRpcDTO sampleFormRpcDTO:createInquiryRpcDTO.getSampleForm()){
            orderSampleFromList.add(sampleFormRpcDTOToEntity(sampleFormRpcDTO,orderDTO.getOrderNo(),createInquiryRpcDTO.getDmlRpcReqDTO().getNewGroupNo()));
        }
        return orderSampleFromList;
    }

    public OrderSampleFrom sampleFormRpcDTOToEntity(SampleFormRpcDTO sampleFormRpcDTO,String orderNo,String groupNo){
        OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
        baseCopyObj.copy(orderSampleFrom,sampleFormRpcDTO);
        orderSampleFrom.setOrderNo(orderNo);
        orderSampleFrom.setGroupNo(groupNo);
        orderSampleFrom.setState(1);
        if(StringUtils.isBlank(orderSampleFrom.getSampleValue())){
            orderSampleFrom.setSampleValue("");
        }
        return orderSampleFrom;
    }


    public List<OrderSampleFrom> getBaseFromListByInquiry(String orderNo,Long lineId,  List<CenterSampleDTO> centerSampleDTOList) {
        List<OrderSampleFrom> orderSampleFromList=new ArrayList<>();
        if(!ValidationUtil.isEmpty(centerSampleDTOList)){
            for (CenterSampleDTO centerSampleDTO:centerSampleDTOList){
                OrderSampleFrom orderSampleFrom=new OrderSampleFrom();
                baseCopyObj.copyWithNull(orderSampleFrom,centerSampleDTO);
                orderSampleFrom.setOrderNo(orderNo);
                orderSampleFrom.setGroupNo("baseGroup");
                orderSampleFrom.setState(1);
                orderSampleFrom.setSampleNo("baseSample");
                orderSampleFromList.add(orderSampleFrom);
            }
        }
        return orderSampleFromList;
    }

    //获取化学的申请表
    public static List<String> getChemistryList(){
        List<String> list=new ArrayList<>();
        list.add(OIQ_USING_CHEMISTRY);
        list.add(OIQ_SAMPLE_COMPOSITION);
        list.add(OIQ_SAMPLE_COLOR);
        list.add(OIQ_SAMPLE_FEATURES);
        return list;
    }
}
