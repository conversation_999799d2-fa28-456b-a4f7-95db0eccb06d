package com.sgs.ecom.order.dto.rpc;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.pay.PayBaseDTO;


public class PayToOtherDTO {
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private String orderNo;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private String platformOrder;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private String belongSystem;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private PayBaseDTO orderPay;
    @ApiAnno(groups={BaseOrderFilter.PayToOther.class})
    private int payState;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getPlatformOrder() {
        return platformOrder;
    }

    public void setPlatformOrder(String platformOrder) {
        this.platformOrder = platformOrder;
    }

    public String getBelongSystem() {
        return belongSystem;
    }

    public void setBelongSystem(String belongSystem) {
        this.belongSystem = belongSystem;
    }


    public PayBaseDTO getOrderPay() {
        return orderPay;
    }

    public void setOrderPay(PayBaseDTO orderPay) {
        this.orderPay = orderPay;
    }

    public int getPayState() {
        return payState;
    }

    public void setPayState(int payState) {
        this.payState = payState;
    }
}
