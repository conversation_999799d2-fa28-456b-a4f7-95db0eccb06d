package com.sgs.ecom.order.dto.send.mail;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.request.base.BaseFileReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OiqOtherDTO {

    private String reportNoStr;
    private List<BaseFileReq> fileReqList;
    private int sendOperatorCodeFlg;
    private String useMemo;
    private Long orderId;
    private String invoiceFileUrl;

    private MailExpressDTO mailExpressDTO;
    private BaseOrderDTO sendOrderDTO;

    public List<BaseFileReq> getFileReqList() {
        return fileReqList;
    }

    public void setFileReqList(List<BaseFileReq> fileReqList) {
        this.fileReqList = fileReqList;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public String getUseMemo() {
        return useMemo;
    }

    public void setUseMemo(String useMemo) {
        this.useMemo = useMemo;
    }

    public int getSendOperatorCodeFlg() {
        return sendOperatorCodeFlg;
    }

    public void setSendOperatorCodeFlg(int sendOperatorCodeFlg) {
        this.sendOperatorCodeFlg = sendOperatorCodeFlg;
    }

    public MailExpressDTO getMailExpressDTO() {
        return mailExpressDTO;
    }

    public void setMailExpressDTO(MailExpressDTO mailExpressDTO) {
        this.mailExpressDTO = mailExpressDTO;
    }

    public String getInvoiceFileUrl() {
        return invoiceFileUrl;
    }

    public void setInvoiceFileUrl(String invoiceFileUrl) {
        this.invoiceFileUrl = invoiceFileUrl;
    }

    public String getReportNoStr() {
        return reportNoStr;
    }

    public void setReportNoStr(String reportNoStr) {
        this.reportNoStr = reportNoStr;
    }

    public BaseOrderDTO getSendOrderDTO() {
        return sendOrderDTO;
    }

    public void setSendOrderDTO(BaseOrderDTO sendOrderDTO) {
        this.sendOrderDTO = sendOrderDTO;
    }
}
