package com.sgs.ecom.order.tic.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.order.service.interfaces.IBbcInfoSV;
import com.sgs.ecom.order.util.ResultBody;

@RestController
@RequestMapping("/business/api.v1.bbc/bbc")
public class BbcInfoController extends BaseAction {

	private static IBbcInfoSV bbcInfoSV = CollectionService.getService(IBbcInfoSV.class);
	
	/**   
	* @Function: qryShop
	* @Description: 查询店铺列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2019-11-06
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2019-11-06  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryShop", method = { RequestMethod.POST })
    public ResultBody qryShop(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(bbcInfoSV.qryShop(data));
	}
}
