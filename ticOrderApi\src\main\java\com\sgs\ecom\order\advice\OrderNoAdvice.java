package com.sgs.ecom.order.advice;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.domain.service.order.interfaces.IOrderBaseInfoDomainService;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.PrivilegeLevelDTO;
import com.sgs.ecom.order.dto.permission.OrderPermissionDTO;
import com.sgs.ecom.order.service.order.interfaces.IOrderBaseInfoService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultEnumCode;
import com.sgs.ecom.order.util.collection.MapUtil;
import com.sgs.ecom.order.util.order.OrderPermissionUtil;
import com.sgs.ecom.order.util.select.SelectBaseUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Aspect
@Component
public class OrderNoAdvice extends ControllerUtil {

	@Resource
	private IOrderBaseInfoService orderBaseInfoService;
	@Resource
	private OrderPermissionUtil orderPermissionUtil;
	@Resource
	private IOrderBaseInfoDomainService orderBaseInfoDomainService;


	@Pointcut("@annotation(com.sgs.ecom.order.advice.CheckOrderNo)")
	@Order(10)
	public void checkOrderNo() {
	}

	@Before("checkOrderNo()")
	public void doBefore(JoinPoint joinPoint) throws Exception {

		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		//第一个为token 第2个为对象 对象包含orderId
		Object[] args = joinPoint.getArgs();
		//当小于两个对象的时候不处理
		if (args.length < 2 && args[1] instanceof String) {
			return;
		}
		Map<String, Object> map = MapUtil.objectToMap(args[1]);
		if (!map.containsKey(SelectBaseUtil.ORDER_NO)) {
			return;
		}
		String order=map.get(SelectBaseUtil.ORDER_NO).toString();
		if(order.contains(",")){
			return;
		}
		BaseOrderDTO baseOrderDTO=orderBaseInfoDomainService.selectBaseByOrderNo(order);
		OrderPermissionDTO orderPermissionDTO = orderBaseInfoService.selectPermission(baseOrderDTO.getOrderId());
		if (ValidationUtil.isEmpty(orderPermissionDTO)) {
			throw new BusinessException(ResultEnumCode.ORDER_EDIT_UNABLE);
		}
		orderPermissionDTO.setBoSysPerson(getPersonInfo(request.getHeader("accessToken")));
		PrivilegeLevelDTO power = getPower(request.getHeader("accessToken"), request.getHeader("system"));
		orderPermissionDTO.setPrivilegeLevelDTO(power);
		Boolean flg = orderPermissionUtil.checkOrderPermission(orderPermissionDTO);
		if (!flg) {
			throw new BusinessException(ResultEnumCode.ORDER_EDIT_UNABLE);
		}

	}


}
