package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import com.platform.annotation.CheckAnno; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import com.platform.annotation.ExplainAnno; 
import com.platform.annotation.BeanAnno; 

@ExplainAnno(value="bean",name="用户公司")
public class BOUserCompany{
 
 	public static final String SEQUENCE = "COMPANY_ID"; 
  
 	public static final String BO_SQL = "USER_COMPANY"; 
 
 	public static final String OWNER ="member";

 	public static final String PROVICE="provice";
 	public static final String STATE_DATE="stateDate";
 	public static final String USER_ID="userId";
 	public static final String STATE="state";
 	public static final String COMPANY_ADDR_EN="companyAddrEn";
 	public static final String COMPANY_NAME="companyName";
 	public static final String CREATE_DATE="createDate";
 	public static final String COUNTRY="country";
 	public static final String CITY="city";
 	public static final String TOWN="town";
 	public static final String ADDRESS="address";
 	public static final String COMPANY_ID="companyId";
 	public static final String COMPANY_NAME_EN="companyNameEn";

 	@BeanAnno("PROVICE")
 	private String provice;
 	@BeanAnno("STATE_DATE")
 	private Timestamp stateDate;
 	@BeanAnno("USER_ID")
 	private long userId;
 	@BeanAnno("STATE")
 	private int state;
 	@BeanAnno("COMPANY_ADDR_EN")
 	private String companyAddrEn;
 	@BeanAnno("COMPANY_NAME")
 	private String companyName;
 	@BeanAnno("CREATE_DATE")
 	private Timestamp createDate;
 	@BeanAnno("COUNTRY")
 	private String country;
 	@BeanAnno("CITY")
 	private String city;
 	@BeanAnno("TOWN")
 	private String town;
 	@BeanAnno("ADDRESS")
 	private String address;
 	@BeanAnno("COMPANY_ID")
 	private long companyId;
 	@BeanAnno("COMPANY_NAME_EN")
 	private String companyNameEn;

 	@CharacterVaild(len = 50) 
 	public void setProvice(String provice){
 		 this.provice=provice;
 	}
 	@CheckAnno(len = 50) 
 	public String getProvice(){
 		 return this.provice;
 	}
 
 	 
 	public void setStateDate(Timestamp stateDate){
 		 this.stateDate=stateDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStateDate(){
 		 return this.stateDate;
 	}
 
 	 
 	public void setUserId(long userId){
 		 this.userId=userId;
 	}
 	public long getUserId(){
 		 return this.userId;
 	}
 
 	 
 	public void setState(int state){
 		 this.state=state;
 	}
 	public int getState(){
 		 return this.state;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyAddrEn(String companyAddrEn){
 		 this.companyAddrEn=companyAddrEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyAddrEn(){
 		 return this.companyAddrEn;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyName(String companyName){
 		 this.companyName=companyName;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyName(){
 		 return this.companyName;
 	}
 
 	 
 	public void setCreateDate(Timestamp createDate){
 		 this.createDate=createDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreateDate(){
 		 return this.createDate;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCountry(String country){
 		 this.country=country;
 	}
 	@CheckAnno(len = 30) 
 	public String getCountry(){
 		 return this.country;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	@CheckAnno(len = 30) 
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 30) 
 	public void setTown(String town){
 		 this.town=town;
 	}
 	@CheckAnno(len = 30) 
 	public String getTown(){
 		 return this.town;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setAddress(String address){
 		 this.address=address;
 	}
 	@CheckAnno(len = 500) 
 	public String getAddress(){
 		 return this.address;
 	}
 
 	 
 	public void setCompanyId(long companyId){
 		 this.companyId=companyId;
 	}
 	public long getCompanyId(){
 		 return this.companyId;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCompanyNameEn(String companyNameEn){
 		 this.companyNameEn=companyNameEn;
 	}
 	@CheckAnno(len = 200) 
 	public String getCompanyNameEn(){
 		 return this.companyNameEn;
 	}
 
 	 
}