package com.sgs.ecom.order.dto.oiq.applicationInfo;

import com.platform.annotation.ApiAnno;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.oiq.OiqOrderLinkDTO;
import com.sgs.ecom.order.enumtool.application.ReportMethodEnum;
import com.sgs.ecom.order.enumtool.oiq.OiqReportMethodEnum;
import com.sgs.ecom.order.enumtool.oiq.OiqReportTitleTypeEnum;


import java.util.ArrayList;
import java.util.List;

public class OiqReportDTO {

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportLuaCode;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportFormCode="2";
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportCompanyNameCn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportCompanyNameEn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportAddressCn;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportAddressEn;

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private Integer reportMethod=1;//一份样品还是多份样品对应报告
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportMethodMemo;//其他的备注
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private String reportMethodShow;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqOrderLinkDTO> orderLinkList= new ArrayList<>();
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private List<OiqAddressDTO> addressList=new ArrayList<>();

    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class, BaseOrderFilter.OrderToOther.class})
    private Integer reportTitleType;
    @ApiAnno(groups={BaseOrderFilter.OrderToOther.class})
    private String reportTitleTypeShow;


    //返回使用 报告语言和报告形式
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String reportLuaValue;
    @ApiAnno(groups={BaseOrderFilter.OiqFormInfo.class})
    private String reportFormValue;


    public String getReportLuaCode() {
        return reportLuaCode;
    }

    public void setReportLuaCode(String reportLuaCode) {
        this.reportLuaCode = reportLuaCode;
    }

    public String getReportFormCode() {
        return reportFormCode;
    }

    public void setReportFormCode(String reportFormCode) {
        this.reportFormCode = reportFormCode;
    }


    public Integer getReportMethod() {
        return reportMethod;
    }

    public void setReportMethod(Integer reportMethod) {
        this.reportMethod = reportMethod;
    }



    public String getReportCompanyNameCn() {
        return reportCompanyNameCn;
    }

    public void setReportCompanyNameCn(String reportCompanyNameCn) {
        this.reportCompanyNameCn = reportCompanyNameCn;
    }

    public String getReportCompanyNameEn() {
        return reportCompanyNameEn;
    }

    public void setReportCompanyNameEn(String reportCompanyNameEn) {
        this.reportCompanyNameEn = reportCompanyNameEn;
    }

    public String getReportAddressCn() {
        return reportAddressCn;
    }

    public void setReportAddressCn(String reportAddressCn) {
        this.reportAddressCn = reportAddressCn;
    }

    public String getReportAddressEn() {
        return reportAddressEn;
    }

    public void setReportAddressEn(String reportAddressEn) {
        this.reportAddressEn = reportAddressEn;
    }


    public String getReportMethodMemo() {
        return reportMethodMemo;
    }

    public void setReportMethodMemo(String reportMethodMemo) {
        this.reportMethodMemo = reportMethodMemo;
    }


    public String getReportLuaValue() {
        return reportLuaValue;
    }

    public void setReportLuaValue(String reportLuaValue) {
        this.reportLuaValue = reportLuaValue;
    }

    public String getReportFormValue() {
        return reportFormValue;
    }

    public void setReportFormValue(String reportFormValue) {
        this.reportFormValue = reportFormValue;
    }

    public List<OiqOrderLinkDTO> getOrderLinkList() {
        return orderLinkList;
    }

    public void setOrderLinkList(List<OiqOrderLinkDTO> orderLinkList) {
        this.orderLinkList = orderLinkList;
    }


    public Integer getReportTitleType() {
        return reportTitleType;
    }

    public void setReportTitleType(Integer reportTitleType) {
        this.reportTitleType = reportTitleType;
    }

    public List<OiqAddressDTO> getAddressList() {
        return addressList;
    }

    public void setAddressList(List<OiqAddressDTO> addressList) {
        this.addressList = addressList;
    }

    public String getReportMethodShow() {
        if(reportMethod==null){
            return "";
        }

        if(OiqReportMethodEnum.OTHER.getIndex()==reportMethod){
            return reportMethodMemo;
        }
        return OiqReportMethodEnum.getNameCh(reportMethod);
    }

    public void setReportMethodShow(String reportMethodShow) {
        this.reportMethodShow = reportMethodShow;
    }

    public String getReportTitleTypeShow() {
        if(reportTitleType==null){
            return "";
        }

        return OiqReportTitleTypeEnum.getName(reportTitleType);
    }

    public void setReportTitleTypeShow(String reportTitleTypeShow) {
        this.reportTitleTypeShow = reportTitleTypeShow;
    }
}
