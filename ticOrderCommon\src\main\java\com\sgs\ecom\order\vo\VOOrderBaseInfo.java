package com.sgs.ecom.order.vo; 
 
import com.platform.annotation.BeanAnno;
import com.platform.annotation.ExplainAnno;
import com.sgs.base.BaseBean;

import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

@ExplainAnno(value="bean",name="")
public class VOOrderBaseInfo extends BaseBean {
 
 	public static final String CREATE_SQL = "ORDER_BASE_INFO"; 
 
 
 	public static final String USER_SEX="userSex";
 	public static final String USER_EMAIL="userEmail";
 	public static final String PAY_DATE="payDate";
 	public static final String USER_ID="userId";
 	public static final String RECOMMEND_REASON="recommendReason";
 	public static final String HIS_STATE="hisState";
 	public static final String URGENT_AMOUNT="urgentAmount";
 	public static final String IS_DELETE="isDelete";
 	public static final String PLATFORM="platform";
 	public static final String LINE_ID="lineId";
 	public static final String PAY_STATE="payState";
 	public static final String SAMPLE_REQUIREMENTS="sampleRequirements";
 	public static final String BUSINESS_LINE="businessLine";
 	public static final String OFFER_DATE="offerDate";
 	public static final String REAL_AMOUNT="realAmount";
 	public static final String RELATE_ORDER_NO="relateOrderNo";
 	public static final String IS_PAY_RECEIVED="isPayReceived";
 	public static final String IS_READ="isRead";
 	public static final String STATE_DATE="stateDate";
 	public static final String TMP_GROUP_NO="tmpGroupNo";
 	public static final String IS_INVOICE="isInvoice";
 	public static final String PRODUCT_NAME="productName";
 	public static final String IS_TEST="isTest";
 	public static final String COMPANY_NAME="companyName";
 	public static final String REPORT_FORM="reportForm";
 	public static final String CREATE_DATE="createDate";
 	public static final String SUB_STATE="subState";
 	public static final String CS_NAME="csName";
 	public static final String REPORT_LUA_CODE="reportLuaCode";
 	public static final String REPORT_FORM_CODE="reportFormCode";
 	public static final String STATE="state";
 	public static final String ORDER_NO="orderNo";
 	public static final String CS_BRANCH="csBranch";
 	public static final String CS_EMAIL="csEmail";
 	public static final String PRODUCT_IMG="productImg";
 	public static final String LAB_ID="labId";
 	public static final String LAB_NAME="labName";
 	public static final String IS_URGENT="isUrgent";
 	public static final String BU="bu";
 	public static final String REPORT_LUA="reportLua";
 	public static final String TEST_CYCLE="testCycle";
 	public static final String QUESTION_ID="questionId";
 	public static final String CS_CODE="csCode";
 	public static final String ORDER_TYPE="orderType";
 	public static final String IS_REMIND="isRemind";
 	public static final String CS_NAME_EN="csNameEn";
 	public static final String USER_NAME="userName";
 	public static final String PLATFORM_ORDER="platformOrder";
 	public static final String ORDER_AMOUNT="orderAmount";
 	public static final String USER_PHONE="userPhone";
 	public static final String ORDER_ID="orderId";
 	public static final String CITY="city";
 	public static final String GROUP_NO="groupNo";
 	public static final String TOTAL_NUMS="totalNums";
 	public static final String CATEGORY_PATH="categoryPath";
 	public static final String AUDIT_CODE="auditCode";
 	public static final String CATAGORY_ID="catagoryId";
 	public static final String DISCOUNT_AMOUNT="discountAmount";
 	public static final String PROVINCE="province";
 	public static final String ORDER_EXP_DATE="orderExpDate";
 	public static final String SERVICE_AMOUNT="serviceAmount";
 	public static final String PAY_METHOD="payMethod";
 	public static final String DOWN_REPORT="downReport";
 	public static final String STATE_LIST="stateList";
 	public static final String IS_BILL="isBill";
 	public static final String ABSTRACT_CUSTCODE="abstractCustcode";

 	@BeanAnno("USER_SEX")
 	private Integer userSex;
 	@Size(min = 0,max =200,message = "USER_EMAIL参数长度超出范围,最大长度为:200", groups = {Insert.class,Update.class}) 
 	@BeanAnno("USER_EMAIL")
 	private String userEmail;
 	@Size(min = 0,max =19,message = "PAY_DATE参数长度超出范围,最大长度为:19", groups = {Insert.class,Update.class}) 
 	@BeanAnno("PAY_DATE")
 	private String payDate;
 	@BeanAnno("USER_ID")
 	private Long userId;
 	@Size(min = 0,max =1000,message = "RECOMMEND_REASON参数长度超出范围,最大长度为:1000", groups = {Insert.class,Update.class}) 
 	@BeanAnno("RECOMMEND_REASON")
 	private String recommendReason;
 	@BeanAnno("HIS_STATE")
 	private Integer hisState;
 	@BeanAnno("URGENT_AMOUNT")
 	private BigDecimal urgentAmount;
 	@BeanAnno("IS_DELETE")
 	private Integer isDelete;
 	@Size(min = 0,max =20,message = "PLATFORM参数长度超出范围,最大长度为:20", groups = {Insert.class,Update.class})
 	@BeanAnno("PLATFORM")
 	private String platform;
 	@BeanAnno("LINE_ID")
 	private Long lineId;
 	@BeanAnno("APPLICATION_LINE_ID")
 	private Long applicationLineId;
 	@BeanAnno("PAY_STATE")
 	private Integer payState;
 	@Size(min = 0,max =1000,message = "SAMPLE_REQUIREMENTS参数长度超出范围,最大长度为:1000", groups = {Insert.class,Update.class})
 	@BeanAnno("SAMPLE_REQUIREMENTS")
 	private String sampleRequirements;
 	@Size(min = 0,max =100,message = "BUSINESS_LINE参数长度超出范围,最大长度为:100", groups = {Insert.class,Update.class})
 	@BeanAnno("BUSINESS_LINE")
 	private String businessLine;
 	@Size(min = 0,max =19,message = "OFFER_DATE参数长度超出范围,最大长度为:19", groups = {Insert.class,Update.class})
 	@BeanAnno("OFFER_DATE")
 	private String offerDate;
 	@BeanAnno("REAL_AMOUNT")
 	private BigDecimal realAmount;
 	@Size(min = 0,max =30,message = "RELATE_ORDER_NO参数长度超出范围,最大长度为:30", groups = {Insert.class,Update.class})
 	@BeanAnno("RELATE_ORDER_NO")
 	private String relateOrderNo;
 	@BeanAnno("IS_PAY_RECEIVED")
 	private Integer isPayReceived;
 	@BeanAnno("IS_READ")
 	private Integer isRead;
 	@Size(min = 0,max =19,message = "STATE_DATE参数长度超出范围,最大长度为:19", groups = {Insert.class,Update.class})
 	@BeanAnno("STATE_DATE")
 	private String stateDate;
 	@Size(min = 0,max =50,message = "TMP_GROUP_NO参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("TMP_GROUP_NO")
 	private String tmpGroupNo;
 	@BeanAnno("IS_INVOICE")
 	private Integer isInvoice;
 	@Size(min = 0,max =100,message = "PRODUCT_NAME参数长度超出范围,最大长度为:100", groups = {Insert.class,Update.class})
 	@BeanAnno("PRODUCT_NAME")
 	private String productName;
 	@BeanAnno("IS_TEST")
 	private Integer isTest;
 	@Size(min = 0,max =500,message = "COMPANY_NAME参数长度超出范围,最大长度为:500", groups = {Insert.class,Update.class})
 	@BeanAnno("COMPANY_NAME")
 	private String companyName;
 	@Size(min = 0,max =50,message = "REPORT_FORM参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("REPORT_FORM")
 	private String reportForm;
 	@Size(min = 0,max =19,message = "CREATE_DATE参数长度超出范围,最大长度为:19", groups = {Insert.class,Update.class})
 	@BeanAnno("CREATE_DATE")
 	private String createDate;
 	@BeanAnno("SUB_STATE")
 	private Integer subState;
 	@Size(min = 0,max =50,message = "CS_NAME参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("CS_NAME")
 	private String csName;
 	@Size(min = 0,max =10,message = "REPORT_LUA_CODE参数长度超出范围,最大长度为:10", groups = {Insert.class,Update.class})
 	@BeanAnno("REPORT_LUA_CODE")
 	private String reportLuaCode;
 	@Size(min = 0,max =20,message = "REPORT_FORM_CODE参数长度超出范围,最大长度为:20", groups = {Insert.class,Update.class})
 	@BeanAnno("REPORT_FORM_CODE")
 	private String reportFormCode;
 	@BeanAnno("STATE")
 	private Integer state;
 	@Size(min = 0,max =30,message = "ORDER_NO参数长度超出范围,最大长度为:30", groups = {Insert.class,Update.class})
 	@BeanAnno("ORDER_NO")
 	private String orderNo;
 	@Size(min = 0,max =50,message = "CS_BRANCH参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("CS_BRANCH")
 	private String csBranch;
 	@Size(min = 0,max =50,message = "CS_EMAIL参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class}) 
 	@BeanAnno("CS_EMAIL")
 	private String csEmail;
 	@Size(min = 0,max =500,message = "PRODUCT_IMG参数长度超出范围,最大长度为:500", groups = {Insert.class,Update.class})
 	@BeanAnno("PRODUCT_IMG")
 	private String productImg;
 	@BeanAnno("LAB_ID")
 	private Long labId;
 	@Size(min = 0,max =50,message = "LAB_NAME参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class}) 
 	@BeanAnno("LAB_NAME")
 	private String labName;
 	@BeanAnno("IS_URGENT")
 	private Integer isUrgent;
 	@Size(min = 0,max =20,message = "BU参数长度超出范围,最大长度为:20", groups = {Insert.class,Update.class}) 
 	@BeanAnno("BU")
 	private String bu;
 	@Size(min = 0,max =50,message = "REPORT_LUA参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("REPORT_LUA")
 	private String reportLua;
 	@BeanAnno("TEST_CYCLE")
 	private BigDecimal testCycle;
 	@BeanAnno("QUESTION_ID")
 	private Long questionId;
 	@Size(min = 0,max =50,message = "CS_CODE参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class}) 
 	@BeanAnno("CS_CODE")
 	private String csCode;
 	@BeanAnno("ORDER_TYPE")
 	private Integer orderType;
 	@BeanAnno("IS_REMIND")
 	private Integer isRemind;
 	@Size(min = 0,max =50,message = "CS_NAME_EN参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("CS_NAME_EN")
 	private String csNameEn;
 	@Size(min = 0,max =50,message = "USER_NAME参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("USER_NAME")
 	private String userName;
 	@Size(min = 0,max =30,message = "PLATFORM_ORDER参数长度超出范围,最大长度为:30", groups = {Insert.class,Update.class}) 
 	@BeanAnno("PLATFORM_ORDER")
 	private String platformOrder;
 	@BeanAnno("ORDER_AMOUNT")
 	private BigDecimal orderAmount;
 	@Size(min = 0,max =11,message = "USER_PHONE参数长度超出范围,最大长度为:11", groups = {Insert.class,Update.class}) 
 	@BeanAnno("USER_PHONE")
 	private String userPhone;
 	@BeanAnno("ORDER_ID")
 	private Long orderId;
 	@Size(min = 0,max =50,message = "CITY参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class}) 
 	@BeanAnno("CITY")
 	private String city;
 	@Size(min = 0,max =20,message = "GROUP_NO参数长度超出范围,最大长度为:20", groups = {Insert.class,Update.class}) 
 	@BeanAnno("GROUP_NO")
 	private String groupNo;
 	@BeanAnno("TOTAL_NUMS")
 	private Integer totalNums;
 	@Size(min = 0,max =200,message = "CATEGORY_PATH参数长度超出范围,最大长度为:200", groups = {Insert.class,Update.class})
 	@BeanAnno("CATEGORY_PATH")
 	private String categoryPath;
 	@Size(min = 0,max =50,message = "AUDIT_CODE参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("AUDIT_CODE")
 	private String auditCode;
 	@BeanAnno("CATAGORY_ID")
 	private Long catagoryId;
 	@BeanAnno("DISCOUNT_AMOUNT")
 	private BigDecimal discountAmount;
 	@Size(min = 0,max =50,message = "PROVINCE参数长度超出范围,最大长度为:50", groups = {Insert.class,Update.class})
 	@BeanAnno("PROVINCE")
 	private String province;
 	@Size(min = 0,max =19,message = "ORDER_EXP_DATE参数长度超出范围,最大长度为:19", groups = {Insert.class,Update.class}) 
 	@BeanAnno("ORDER_EXP_DATE")
 	private String orderExpDate;
 	@BeanAnno("SERVICE_AMOUNT")
 	private BigDecimal serviceAmount;
	@BeanAnno("DOWN_REPORT")
	private Integer downReport;
	@BeanAnno("IS_BILL")
	private Integer isBill; 
	@BeanAnno("MONTH_PAY")
	private Integer monthPay; 
 	@BeanAnno("ABSTRACT_CUSTCODE")
 	private String abstractCustcode;

	private String testCycleMemo;

	private String recommendReasonImage;

	private String leadsCode;

	private String orderSource;

	private String operatorCode;

	private String applySubmitDate;

	private Integer testLabel;

	private String closeCode;
	private String closeReason;

	private String deadlineTime;

	private Integer noticeNum;

	private Integer refundState;

	private Integer isFinish;
	private String currency;
	private BigDecimal exchangeRate;
	private String slimNo;
	private String platformOrderNo;
	private BigDecimal platformAmount;

	private String lastResponseDate;

	private BigDecimal shopDisAmount;//店铺优惠金额

	private String companyAddressEn;
	private String companyAddressCn;
	private String companyNameEn;
	private String town;
	private Integer proState;


	
 	private Integer payMethod;
 	// 列表查询接口需要根据前端传递的订单状态查询数据
 	private List<Integer> stateList;

 	private String accountNo;

	private String confirmOrderDate;

	private String orderSourceFrom;
	private String operatorSource;

	private String salesCode;
	private String salesPhone;
	private String promoInfo;
	private String fromSource;
	private String bossNo;
	private Long custId;
	private Integer isElectron; //是否是上海开票显示电子 0-否 1-是
	private String createCode;
	private BigDecimal taxRates;
	@Size(min = 0,max =200,message = "outOrderNo参数长度超出范围,最大长度为:200", groups = {Insert.class,Update.class})
	private String outOrderNo;

	public Integer getIsElectron() {
		return isElectron;
	}

	public void setIsElectron(Integer isElectron) {
		this.isElectron = isElectron;
	}

	public List<Integer> getStateList() {
		return stateList;
	}

	public void setStateList(List<Integer> stateList) {
		this.stateList = stateList;
	}

	public Integer getDownReport() {
		return downReport;
	}

	public void setDownReport(Integer downReport) {
		this.downReport = downReport;
	}

	public Integer getPayMethod() {
		return payMethod;
	}

	public void setPayMethod(Integer payMethod) {
		this.payMethod = payMethod;
	}



	public Integer getUserSex() {
		return userSex;
	}

	public void setUserSex(Integer userSex) {
		this.userSex = userSex;
	}

	public String getUserEmail() {
		return userEmail;
	}

	public void setUserEmail(String userEmail) {
		this.userEmail = userEmail;
	}

	public String getPayDate() {
		return payDate;
	}

	public void setPayDate(String payDate) {
		this.payDate = payDate;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getRecommendReason() {
		return recommendReason;
	}

	public void setRecommendReason(String recommendReason) {
		this.recommendReason = recommendReason;
	}

	public Integer getHisState() {
		return hisState;
	}

	public void setHisState(Integer hisState) {
		this.hisState = hisState;
	}

	public BigDecimal getUrgentAmount() {
		return urgentAmount;
	}

	public void setUrgentAmount(BigDecimal urgentAmount) {
		this.urgentAmount = urgentAmount;
	}

	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public Long getLineId() {
		return lineId;
	}

	public void setLineId(Long lineId) {
		this.lineId = lineId;
	}

	public Long getApplicationLineId() {
		return applicationLineId;
	}

	public void setApplicationLineId(Long applicationLineId) {
		this.applicationLineId = applicationLineId;
	}

	public Integer getPayState() {
		return payState;
	}

	public void setPayState(Integer payState) {
		this.payState = payState;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	public String getOfferDate() {
		return offerDate;
	}

	public void setOfferDate(String offerDate) {
		this.offerDate = offerDate;
	}

	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public String getRelateOrderNo() {
		return relateOrderNo;
	}

	public void setRelateOrderNo(String relateOrderNo) {
		this.relateOrderNo = relateOrderNo;
	}

	public Integer getIsPayReceived() {
		return isPayReceived;
	}

	public void setIsPayReceived(Integer isPayReceived) {
		this.isPayReceived = isPayReceived;
	}

	public Integer getIsRead() {
		return isRead;
	}

	public void setIsRead(Integer isRead) {
		this.isRead = isRead;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getTmpGroupNo() {
		return tmpGroupNo;
	}

	public void setTmpGroupNo(String tmpGroupNo) {
		this.tmpGroupNo = tmpGroupNo;
	}

	public Integer getIsInvoice() {
		return isInvoice;
	}

	public void setIsInvoice(Integer isInvoice) {
		this.isInvoice = isInvoice;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Integer getIsTest() {
		return isTest;
	}

	public void setIsTest(Integer isTest) {
		this.isTest = isTest;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getReportForm() {
		return reportForm;
	}

	public void setReportForm(String reportForm) {
		this.reportForm = reportForm;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public Integer getSubState() {
		return subState;
	}

	public void setSubState(Integer subState) {
		this.subState = subState;
	}

	public String getCsName() {
		return csName;
	}

	public void setCsName(String csName) {
		this.csName = csName;
	}

	public String getReportLuaCode() {
		return reportLuaCode;
	}

	public void setReportLuaCode(String reportLuaCode) {
		this.reportLuaCode = reportLuaCode;
	}

	public String getReportFormCode() {
		return reportFormCode;
	}

	public void setReportFormCode(String reportFormCode) {
		this.reportFormCode = reportFormCode;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getCsBranch() {
		return csBranch;
	}

	public void setCsBranch(String csBranch) {
		this.csBranch = csBranch;
	}

	public String getCsEmail() {
		return csEmail;
	}

	public void setCsEmail(String csEmail) {
		this.csEmail = csEmail;
	}

	public String getProductImg() {
		return productImg;
	}

	public void setProductImg(String productImg) {
		this.productImg = productImg;
	}

	public Long getLabId() {
		return labId;
	}

	public void setLabId(Long labId) {
		this.labId = labId;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public Integer getIsUrgent() {
		return isUrgent;
	}

	public void setIsUrgent(Integer isUrgent) {
		this.isUrgent = isUrgent;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public String getReportLua() {
		return reportLua;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}

	public BigDecimal getTestCycle() {
		return testCycle;
	}

	public void setTestCycle(BigDecimal testCycle) {
		this.testCycle = testCycle;
	}

	public Long getQuestionId() {
		return questionId;
	}

	public void setQuestionId(Long questionId) {
		this.questionId = questionId;
	}

	public String getCsCode() {
		return csCode;
	}

	public void setCsCode(String csCode) {
		this.csCode = csCode;
	}

	public Integer getOrderType() {
		return orderType;
	}

	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}

	public Integer getIsRemind() {
		return isRemind;
	}

	public void setIsRemind(Integer isRemind) {
		this.isRemind = isRemind;
	}

	public String getCsNameEn() {
		return csNameEn;
	}

	public void setCsNameEn(String csNameEn) {
		this.csNameEn = csNameEn;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getPlatformOrder() {
		return platformOrder;
	}

	public void setPlatformOrder(String platformOrder) {
		this.platformOrder = platformOrder;
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public String getUserPhone() {
		return userPhone;
	}

	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}

	public Long getOrderId() {
		return orderId;
	}

	public void setOrderId(Long orderId) {
		this.orderId = orderId;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getGroupNo() {
		return groupNo;
	}

	public void setGroupNo(String groupNo) {
		this.groupNo = groupNo;
	}

	public Integer getTotalNums() {
		return totalNums;
	}

	public void setTotalNums(Integer totalNums) {
		this.totalNums = totalNums;
	}

	public String getCategoryPath() {
		return categoryPath;
	}

	public void setCategoryPath(String categoryPath) {
		this.categoryPath = categoryPath;
	}

	public String getAuditCode() {
		return auditCode;
	}

	public void setAuditCode(String auditCode) {
		this.auditCode = auditCode;
	}

	public Long getCatagoryId() {
		return catagoryId;
	}

	public void setCatagoryId(Long catagoryId) {
		this.catagoryId = catagoryId;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getOrderExpDate() {
		return orderExpDate;
	}

	public void setOrderExpDate(String orderExpDate) {
		this.orderExpDate = orderExpDate;
	}

	public BigDecimal getServiceAmount() {
		return serviceAmount;
	}

	public void setServiceAmount(BigDecimal serviceAmount) {
		this.serviceAmount = serviceAmount;
	}

	public String getTestCycleMemo() {
		return testCycleMemo;
	}

	public void setTestCycleMemo(String testCycleMemo) {
		this.testCycleMemo = testCycleMemo;
	}

	public String getLeadsCode() {
		return leadsCode;
	}

	public void setLeadsCode(String leadsCode) {
		this.leadsCode = leadsCode;
	}

	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	public String getOperatorCode() {
		return operatorCode;
	}

	public void setOperatorCode(String operatorCode) {
		this.operatorCode = operatorCode;
	}

	public String getApplySubmitDate() {
		return applySubmitDate;
	}

	public void setApplySubmitDate(String applySubmitDate) {
		this.applySubmitDate = applySubmitDate;
	}

	public String getRecommendReasonImage() {
		return recommendReasonImage;
	}

	public void setRecommendReasonImage(String recommendReasonImage) {
		this.recommendReasonImage = recommendReasonImage;
	}

	public Integer getTestLabel() {
		return testLabel;
	}

	public void setTestLabel(Integer testLabel) {
		this.testLabel = testLabel;
	}

	public String getCloseCode() {
		return closeCode;
	}

	public void setCloseCode(String closeCode) {
		this.closeCode = closeCode;
	}

	public String getCloseReason() {
		return closeReason;
	}

	public void setCloseReason(String closeReason) {
		this.closeReason = closeReason;
	}

	public String getDeadlineTime() {
		return deadlineTime;
	}

	public void setDeadlineTime(String deadlineTime) {
		this.deadlineTime = deadlineTime;
	}

	public Integer getNoticeNum() {
		return noticeNum;
	}

	public void setNoticeNum(Integer noticeNum) {
		this.noticeNum = noticeNum;
	}

	public Integer getRefundState() {
		return refundState;
	}

	public void setRefundState(Integer refundState) {
		this.refundState = refundState;
	}

	public Integer getIsFinish() {
		return isFinish;
	}

	public void setIsFinish(Integer isFinish) {
		this.isFinish = isFinish;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public BigDecimal getExchangeRate() {
		return exchangeRate;
	}

	public void setExchangeRate(BigDecimal exchangeRate) {
		this.exchangeRate = exchangeRate;
	}

	public String getSlimNo() {
		return slimNo;
	}

	public void setSlimNo(String slimNo) {
		this.slimNo = slimNo;
	}

	public String getPlatformOrderNo() {
		return platformOrderNo;
	}

	public void setPlatformOrderNo(String platformOrderNo) {
		this.platformOrderNo = platformOrderNo;
	}

	public BigDecimal getPlatformAmount() {
		return platformAmount;
	}

	public void setPlatformAmount(BigDecimal platformAmount) {
		this.platformAmount = platformAmount;
	}

	public Integer getIsBill() {
		return isBill;
	}

	public void setIsBill(Integer isBill) {
		this.isBill = isBill;
	}

	public String getAbstractCustcode() {
		return abstractCustcode;
	}

	public void setAbstractCustcode(String abstractCustcode) {
		this.abstractCustcode = abstractCustcode;
	}

	public Integer getMonthPay() {
		return monthPay;
	}

	public void setMonthPay(Integer monthPay) {
		this.monthPay = monthPay;
	}

	public String getLastResponseDate() {
		return lastResponseDate;
	}

	public void setLastResponseDate(String lastResponseDate) {
		this.lastResponseDate = lastResponseDate;
	}

	public String getAccountNo() {
		return accountNo;
	}

	public void setAccountNo(String accountNo) {
		this.accountNo = accountNo;
	}

	public BigDecimal getShopDisAmount() {
		return shopDisAmount;
	}

	public void setShopDisAmount(BigDecimal shopDisAmount) {
		this.shopDisAmount = shopDisAmount;
	}

	public String getCompanyAddressEn() {
		return companyAddressEn;
	}

	public void setCompanyAddressEn(String companyAddressEn) {
		this.companyAddressEn = companyAddressEn;
	}

	public String getCompanyAddressCn() {
		return companyAddressCn;
	}

	public void setCompanyAddressCn(String companyAddressCn) {
		this.companyAddressCn = companyAddressCn;
	}

	public String getCompanyNameEn() {
		return companyNameEn;
	}

	public void setCompanyNameEn(String companyNameEn) {
		this.companyNameEn = companyNameEn;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public Integer getProState() {
		return proState;
	}

	public void setProState(Integer proState) {
		this.proState = proState;
	}

	public String getConfirmOrderDate() {
		return confirmOrderDate;
	}

	public void setConfirmOrderDate(String confirmOrderDate) {
		this.confirmOrderDate = confirmOrderDate;
	}

	public String getOrderSourceFrom() {
		return orderSourceFrom;
	}

	public void setOrderSourceFrom(String orderSourceFrom) {
		this.orderSourceFrom = orderSourceFrom;
	}

	public String getOperatorSource() {
		return operatorSource;
	}

	public void setOperatorSource(String operatorSource) {
		this.operatorSource = operatorSource;
	}

	public String getSalesCode() {
		return salesCode;
	}

	public void setSalesCode(String salesCode) {
		this.salesCode = salesCode;
	}

	public String getSalesPhone() {
		return salesPhone;
	}

	public void setSalesPhone(String salesPhone) {
		this.salesPhone = salesPhone;
	}

	public String getPromoInfo() {
		return promoInfo;
	}

	public void setPromoInfo(String promoInfo) {
		this.promoInfo = promoInfo;
	}

	public String getFromSource() {
		return fromSource;
	}

	public void setFromSource(String fromSource) {
		this.fromSource = fromSource;
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}

	public Long getCustId() {
		return custId;
	}

	public void setCustId(Long custId) {
		this.custId = custId;
	}

	public String getCreateCode() {
		return createCode;
	}

	public void setCreateCode(String createCode) {
		this.createCode = createCode;
	}

	public BigDecimal getTaxRates() {
		return taxRates;
	}

	public void setTaxRates(BigDecimal taxRates) {
		this.taxRates = taxRates;
	}

	public String getOutOrderNo() {
		return outOrderNo;
	}

	public void setOutOrderNo(String outOrderNo) {
		this.outOrderNo = outOrderNo;
	}
}