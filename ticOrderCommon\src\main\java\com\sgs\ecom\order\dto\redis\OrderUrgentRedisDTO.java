package com.sgs.ecom.order.dto.redis;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description :
 * @date 2024/6/30
 */
public class OrderUrgentRedisDTO {
    private String applicationCode;
    private String configKey;
    private String configName;
    private BigDecimal configPrice;
    private String configValue;
    private Integer sortShow;
    private Integer state;

    public String getApplicationCode() {
        return applicationCode;
    }

    public void setApplicationCode(String applicationCode) {
        this.applicationCode = applicationCode;
    }

    public String getConfigKey() {
        return configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public BigDecimal getConfigPrice() {
        return configPrice;
    }

    public void setConfigPrice(BigDecimal configPrice) {
        this.configPrice = configPrice;
    }

    public String getConfigValue() {
        return configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    public Integer getSortShow() {
        return sortShow;
    }

    public void setSortShow(Integer sortShow) {
        this.sortShow = sortShow;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
}
