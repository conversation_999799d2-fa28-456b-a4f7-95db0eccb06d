package com.sgs.ecom.order.dto.order;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.dto.bbc.TicSampleJOSNDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.sys.SysEnumConfigDTO;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class ShowAttrDTO extends BaseQryFilter {
 
 	public static final String CREATE_SQL = "select CREATE_DATE,GROUP_NO,STATE_DATE,SAMPLE_NO,STATE,ORDER_NO,SAMPLE_KEY_NAME,FORM_ID,SAMPLE_VALUE,SAMPLE_KEY from ORDER_SAMPLE_FROM"; 
 
 	private String notShowArea;

	public String getNotShowArea() {
		return notShowArea;
	}

	public void setNotShowArea(String notShowArea) {
		this.notShowArea = notShowArea;
	}
}