package com.sgs.ecom.order.dto.bbc;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class BbcProductDTO {
	private String sku;
	private String skuAttr;
	private String orderNo;
	private BigDecimal price;
	private String subtitle;
	private String storeName;
	private String productName;
	private int quantity;
	private String remark;//存主表的 recommendReason
	private String testItem;
	private String productID;
	private BigDecimal totalPrice;
	private String packageName;//套餐/自选名称
	private String pointsNum;//点位数量
	/**
	 * 触发最低售价描述
	 */
	private String triggerLowestPriceStr;

}
