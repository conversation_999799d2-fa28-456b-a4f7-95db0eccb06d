package com.sgs.ecom.order.dto.oiq;

import com.sgs.ecom.order.dto.custom.BaseOrderDTO;

public class BaseInquiryDTO {
    private String orderNo;
    private int state;
    private String csCode;
    private String csEmail;


    public BaseInquiryDTO() {

    }



    public BaseInquiryDTO(BaseOrderDTO baseOrderDTO) {
        this.orderNo = baseOrderDTO.getOrderNo();
        this.state = baseOrderDTO.getState();
        this.csCode=baseOrderDTO.getCsCode();
        this.csEmail=baseOrderDTO.getCsEmail();
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public int getState() {
        return state;
    }

    public void setState(int state) {
        this.state = state;
    }


    public String getCsCode() {
        return csCode;
    }

    public void setCsCode(String csCode) {
        this.csCode = csCode;
    }

    public String getCsEmail() {
        return csEmail;
    }

    public void setCsEmail(String csEmail) {
        this.csEmail = csEmail;
    }


}
