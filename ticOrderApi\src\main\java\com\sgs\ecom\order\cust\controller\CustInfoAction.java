package com.sgs.ecom.order.cust.controller;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.platform.util.CollectionService;
import com.platform.web.BaseAction;
import com.sgs.ecom.order.cust.service.interfaces.ICustInfoSV;
import com.sgs.ecom.order.util.ResultBody;

@RestController
@RequestMapping("/business/api.v1.cust/cust")
public class CustInfoAction extends BaseAction {

	private static ICustInfoSV custInfoSV = CollectionService.getService(ICustInfoSV.class);
	
//    /**   
//	* @Function: addCust
//	* @Description: 新增月结用户
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "add", method = { RequestMethod.POST })
//    public ResultBody addCust(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//		custInfoSV.addCust(data, getUserInfo(token));
//		return ResultBody.success();
//	}
    
//    /**   
//	* @Function: modCust
//	* @Description: 修改月结用户
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "mod", method = { RequestMethod.POST })
//    public ResultBody modCust(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//		custInfoSV.modCust(data, getUserInfo(token));
//		return ResultBody.success();
//	}
    
//    /**   
//	* @Function: freezeCust
//	* @Description: 冻结/解冻月结用户
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "freeze", method = { RequestMethod.POST })
//    public ResultBody freezeCust(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//		custInfoSV.freezeCust(data, getUserInfo(token));
//		return ResultBody.success();
//	}
//    
//    /**   
//	* @Function: delCust
//	* @Description: 删除月结用户
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "del", method = { RequestMethod.POST })
//    public ResultBody delCust(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//		custInfoSV.delCust(data, getUserInfo(token));
//		return ResultBody.success();
//	}
    
//    /**   
//	* @Function: qryCust
//	* @Description: 查询月结用户
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "qry", method = { RequestMethod.POST })
//    public ResultBody qryCust(@RequestBody String data) throws Exception {
//		return ResultBody.newInstance(custInfoSV.qryCust(data));
//	}
    
//    /**   
//	* @Function: qryCustDtl
//	* @Description: 查询月结用户详情
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "qryDtl", method = { RequestMethod.POST })
//    public ResultBody qryCustDtl(@RequestBody String data) throws Exception {
//		return ResultBody.newInstance(custInfoSV.qryCustDtl(data));
//	}
    
    /**   
	* @Function: qryCustByPaycode
	* @Description: 根据付款码查询月结客户信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "qryByPaycode", method = { RequestMethod.POST })
    public ResultBody qryCustByPaycode(@RequestBody String data) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryCustByPaycode(data));
	}
    
//    /**   
//	* @Function: relateUser
//	* @Description: 月结客户关联
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "relateUser", method = { RequestMethod.POST })
//    public ResultBody relateUser(@RequestBody String data,@RequestHeader(value="accessToken") String token) throws Exception {
//    	custInfoSV.relateUser(data, getUserInfo(token));
//		return ResultBody.success();
//	}
    
//    /**   
//	* @Function: qryRelate
//	* @Description: 月结客户关联查询
//	* 
//	* @param: req
//	* @param: res
//	* @return: null
//	* 
//	* @version: 1.0
//	* @author: shenyi
//	* @date: 2020-1-7
//	* 
//	* Modification History: 
//	* Date         Author          Version            Description 
//	*---------------------------------------------------------* 
//	* 修改时间                          修改人                     版本                 修改原因
//	* 2020-1-7  shenyi    v1.0                 新增
//	*/
//    @AuthRequired(login = "SGS", sign = true)
//    @RequestMapping(value = "qryRelate", method = { RequestMethod.POST })
//    public ResultBody qryRelate(@RequestBody String data) throws Exception {
//		return ResultBody.newInstance(custInfoSV.qryRelate(data));
//	}
    
    /**   
	* @Function: checkCredit
	* @Description: 校验信用额度
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "NULL", sign = true)
    @RequestMapping(value = "checkCredit", method = { RequestMethod.POST })
    public ResultBody checkCredit(@RequestBody String data) throws Exception {
		custInfoSV.checkCredit(data);
		return ResultBody.success();
	}
}
