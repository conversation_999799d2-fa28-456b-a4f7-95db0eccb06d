package com.sgs.ecom.order.controller.bill;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.service.bill.interfaces.IBillDtlService;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOBillDtl;

@RestController
@RequestMapping("/business/api.v1.bill/billdtl")
public class BillDtlController extends ControllerUtil {

	@Autowired
	private IBillDtlService billDtlService;
	
	/**   
	* @Function: qryBillDtl
	* @Description: 查询账单订单列表
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qry", method = { RequestMethod.POST })
    public ResultBody qryBillDtl(@RequestBody VOBillDtl billDtl) throws Exception {
    	return ResultBody.newInstance(billDtlService.qryBillDtl(billDtl));
	}
    
    /**   
	* @Function: confirmTicket
	* @Description: 订单出票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "ticket", method = { RequestMethod.POST })
    public ResultBody confirmTicket(@RequestBody VOBillDtl billDtl,
    	@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlService.confirmTicket(billDtl, getPersonInfo(token));
    	return ResultBody.success();
	}
    
    /**   
	* @Function: confirmPay
	* @Description: 订单支付
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(method = {RequestMethod.POST} , value = "pay")
    public ResultBody confirmPay(@RequestBody VOBillDtl billDtl,
    	@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlService.confirmPay(billDtl, getPersonInfo(token));
    	return ResultBody.success();
	}
    
    /**   
	* @Function: ticketByBill
	* @Description: 根据账单出票
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(method = {RequestMethod.POST} , value = "ticketByBill")
    public ResultBody ticketByBill(@RequestBody VOBillDtl billDtl,
    	@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlService.ticketByBill(billDtl, getPersonInfo(token));
    	return ResultBody.success();
	}
    
    /**   
	* @Function: payByBill
	* @Description: 根据账单支付
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(method = {RequestMethod.POST} , value = "payByBill")
    public ResultBody payByBill(@RequestBody VOBillDtl billDtl,
    	@RequestHeader(value="accessToken") String token) throws Exception {
    	billDtlService.payByBill(billDtl, getPersonInfo(token));
    	return ResultBody.success();
	}
}
