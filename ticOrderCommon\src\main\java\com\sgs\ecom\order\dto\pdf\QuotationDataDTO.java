package com.sgs.ecom.order.dto.pdf;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.custom.BaseOrderDTO;
import com.sgs.ecom.order.dto.order.OrderDetailDTO;
import com.sgs.ecom.order.dto.pay.BankDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class QuotationDataDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal orderAmount=BigDecimal.ZERO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal realAmount=BigDecimal.ZERO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BigDecimal taxRates=BigDecimal.ZERO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private List<OrderDetailDTO> orderDetailDTOList=new ArrayList<>();
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private QuotationFormDataDTO quotationFormDataDTO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private QuotationOtherDTO quotationOtherDTO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private LabDTO labDTO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private BankDTO bankDTO;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String bu;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String currency;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String currencyMark;





	public QuotationDataDTO() {
	}

	public QuotationDataDTO(BaseOrderDTO baseOrderDTO) {
		this.orderAmount = baseOrderDTO.getOrderAmount();
		this.realAmount = baseOrderDTO.getRealAmount();
		this.currency=baseOrderDTO.getCurrency();
	}

	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	public BigDecimal getRealAmount() {
		return realAmount;
	}

	public void setRealAmount(BigDecimal realAmount) {
		this.realAmount = realAmount;
	}

	public BigDecimal getTaxRates() {
		return taxRates;
	}

	public void setTaxRates(BigDecimal taxRates) {
		this.taxRates = taxRates;
	}

	public List<OrderDetailDTO> getOrderDetailDTOList() {
		return orderDetailDTOList;
	}

	public void setOrderDetailDTOList(List<OrderDetailDTO> orderDetailDTOList) {
		this.orderDetailDTOList = orderDetailDTOList;
	}

	public QuotationFormDataDTO getQuotationFormDataDTO() {
		return quotationFormDataDTO;
	}

	public void setQuotationFormDataDTO(QuotationFormDataDTO quotationFormDataDTO) {
		this.quotationFormDataDTO = quotationFormDataDTO;
	}

	public LabDTO getLabDTO() {
		return labDTO;
	}

	public void setLabDTO(LabDTO labDTO) {
		this.labDTO = labDTO;
	}

	public BankDTO getBankDTO() {
		return bankDTO;
	}

	public void setBankDTO(BankDTO bankDTO) {
		this.bankDTO = bankDTO;
	}

	public QuotationOtherDTO getQuotationOtherDTO() {
		return quotationOtherDTO;
	}

	public void setQuotationOtherDTO(QuotationOtherDTO quotationOtherDTO) {
		this.quotationOtherDTO = quotationOtherDTO;
	}

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public String getCurrency() {
		return currency;
	}

	public void setCurrency(String currency) {
		this.currency = currency;
	}

	public String getCurrencyMark() {
		return currencyMark;
	}

	public void setCurrencyMark(String currencyMark) {
		this.currencyMark = currencyMark;
	}
}
