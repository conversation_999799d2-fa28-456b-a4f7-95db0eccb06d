package com.sgs.ecom.order.controller.custom;

import com.platform.annotation.AuthRequired;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.base.BaseValidatedBean;
import com.sgs.ecom.order.service.custom.interfaces.ICustInfoSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOCustInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v1.cust/info")
public class CustInfoController extends ControllerUtil {

	@Autowired
	private ICustInfoSV custInfoSV;
	
    /**   
	* @Function: qryCustService
	* @Description: 查询客户销售信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qry", method = { RequestMethod.POST })
    public ResultBody qryCustByBusi(@RequestHeader(value="accessToken") String token,
    	@RequestHeader(value="system") String system,
    	@RequestBody VOCustInfo custInfo) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryCustByBusi(custInfo, 
			getPersonPrivilege(token, system), getPersonInfo(token).getPersonCode()));
	}
    
    /**   
	* @Function: addCust
	* @Description: 新增月结用户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "addMonth", method = { RequestMethod.POST })
    public ResultBody addCust(@RequestBody VOCustInfo custInfo,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		custInfoSV.addCust(custInfo, getPersonInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: modCust
	* @Description: 修改月结用户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "modMonth", method = { RequestMethod.POST })
    public ResultBody modCust(@RequestBody VOCustInfo custInfo,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		custInfoSV.modCust(custInfo, getPersonInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: freezeCust
	* @Description: 冻结/解冻月结用户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "freeze", method = { RequestMethod.POST })
    public ResultBody freezeCust(@RequestBody VOCustInfo custInfo,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		custInfoSV.freezeCust(custInfo, getPersonInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: delCust
	* @Description: 删除月结用户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-30
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-30  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "delMonth", method = { RequestMethod.POST })
    public ResultBody delCust(@RequestBody VOCustInfo custInfo,
    	@RequestHeader(value="accessToken") String token) throws Exception {
		custInfoSV.delCust(custInfo, getPersonInfo(token));
		return ResultBody.success();
	}
    
    /**   
	* @Function: qryMonthCust
	* @Description: 查询月结客户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-08-29
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-08-29  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryMonth", method = { RequestMethod.POST })
    public ResultBody qryMonthCust(@RequestBody VOCustInfo custInfo) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryMonthCust(custInfo));
	}
    
    /**   
	* @Function: qryCustDtl
	* @Description: 查询客户详情
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryDtl", method = { RequestMethod.POST })
    public ResultBody qryCustDtl(@RequestBody VOCustInfo custInfo) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryCustDtl(custInfo));
	}
    
    /**   
	* @Function: qryRelateCustByBusi
	* @Description: 查询客户销售信息
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "qryRelate", method = { RequestMethod.POST })
    public ResultBody qryRelateCustByBusi(@RequestBody VOCustInfo custInfo) throws Exception {
		return ResultBody.newInstance(custInfoSV.qryRelateCustByBusi(custInfo));
	}
    
    /**   
	* @Function: addCust
	* @Description: 新增客户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "add", method = { RequestMethod.POST })
    public ResultBody addCust(@Validated(value = BaseBean.Insert.class)
    	@RequestBody VOCustInfo custInfo) throws Exception {
		custInfoSV.addCustAndInvoices(custInfo);
		return ResultBody.success();
	}
    
    /**   
	* @Function: modCust
	* @Description: 修改客户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "mod", method = { RequestMethod.POST })
    public ResultBody modCust(@Validated(value = BaseBean.Update.class)
    	@RequestBody VOCustInfo custInfo) throws Exception {
    	custInfoSV.modCustAndInvoices(custInfo);
		return ResultBody.success();
	}


	/**
	 * @Description :批量修改host销售
	 * <AUTHOR> Zhang
	 * @Date  2024/3/20
	 * @param custInfo:
	 * @return: com.sgs.ecom.order.util.ResultBody
	 **/
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "batchModPersonCode", method = { RequestMethod.POST })
	public ResultBody batchModPersonCode(@Validated(value = BaseValidatedBean.BatchUpdate.class)
							  @RequestBody VOCustInfo custInfo) throws Exception {
		custInfoSV.batchModPersonCode(custInfo);
		return ResultBody.success();
	}
    
    /**   
	* @Function: modState
	* @Description: 启用/禁用客户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "modState", method = { RequestMethod.POST })
    public ResultBody modState(@RequestBody VOCustInfo custInfo) throws Exception {
    	custInfoSV.modState(custInfo);
		return ResultBody.success();
	}
    
    /**   
	* @Function: modState
	* @Description: 启用/禁用客户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "imp", method = { RequestMethod.POST })
    public ResultBody impCustInfo(@RequestBody VOCustInfo custInfo) throws Exception {
    	custInfoSV.impCustInfo(custInfo);
		return ResultBody.success();
	}
    
    /**   
	* @Function: delCust
	* @Description: 删除客户
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2020-1-7
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2020-1-7  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "del", method = { RequestMethod.POST })
    public ResultBody delCust(@Validated(value = BaseBean.Delete.class)
    	@RequestBody VOCustInfo custInfo) throws Exception {
    	custInfoSV.delCust(custInfo.getCustId());
		return ResultBody.success();
	}
    
    /**   
	* @Function: changeCurrency
	* @Description: 切换币种
	* 
	* @param: req
	* @param: res
	* @return: null
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-09-22
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-09-22  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "changeCurrency", method = { RequestMethod.POST })
    public ResultBody changeCurrency(@RequestBody VOCustInfo custInfo) throws Exception {
    	custInfoSV.changeCurrency(custInfo);
		return ResultBody.success();
	}
}
