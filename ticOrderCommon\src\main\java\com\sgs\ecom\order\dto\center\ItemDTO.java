package com.sgs.ecom.order.dto.center;

import java.math.BigDecimal;

public class ItemDTO {


	private String bu;
	private int buyNums;
	private String cmaLab;
	private String cnasLab;
	private Long itemId;
	private String itemName;
	private String labName;
	private String labelName;
	private String memo;
	private BigDecimal price;
	private String standardCode;
	private String sampleRequirements;
	private BigDecimal testDays;
	private String testMemo;
	private String testMethod;
	private String unit;
	private String businessLine;
	private int isOptional;
	private String itemAlias;

	public String getBu() {
		return bu;
	}

	public void setBu(String bu) {
		this.bu = bu;
	}

	public int getBuyNums() {
		return buyNums;
	}

	public void setBuyNums(int buyNums) {
		this.buyNums = buyNums;
	}

	public String getCmaLab() {
		return cmaLab;
	}

	public void setCmaLab(String cmaLab) {
		this.cmaLab = cmaLab;
	}

	public String getCnasLab() {
		return cnasLab;
	}

	public void setCnasLab(String cnasLab) {
		this.cnasLab = cnasLab;
	}

	public Long getItemId() {
		return itemId;
	}

	public void setItemId(Long itemId) {
		this.itemId = itemId;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getLabName() {
		return labName;
	}

	public void setLabName(String labName) {
		this.labName = labName;
	}

	public String getLabelName() {
		return labelName;
	}

	public void setLabelName(String labelName) {
		this.labelName = labelName;
	}

	public String getMemo() {
		return memo;
	}

	public void setMemo(String memo) {
		this.memo = memo;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public String getStandardCode() {
		return standardCode;
	}

	public void setStandardCode(String standardCode) {
		this.standardCode = standardCode;
	}

	public BigDecimal getTestDays() {
		return testDays;
	}

	public void setTestDays(BigDecimal testDays) {
		this.testDays = testDays;
	}

	public String getTestMemo() {
		return testMemo;
	}

	public void setTestMemo(String testMemo) {
		this.testMemo = testMemo;
	}

	public String getTestMethod() {
		return testMethod;
	}

	public void setTestMethod(String testMethod) {
		this.testMethod = testMethod;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public String getBusinessLine() {
		return businessLine;
	}

	public void setBusinessLine(String businessLine) {
		this.businessLine = businessLine;
	}

	public String getSampleRequirements() {
		return sampleRequirements;
	}

	public void setSampleRequirements(String sampleRequirements) {
		this.sampleRequirements = sampleRequirements;
	}

	public int getIsOptional() {
		return isOptional;
	}

	public void setIsOptional(int isOptional) {
		this.isOptional = isOptional;
	}

	public String getItemAlias() {
		return itemAlias;
	}

	public void setItemAlias(String itemAlias) {
		this.itemAlias = itemAlias;
	}
}
