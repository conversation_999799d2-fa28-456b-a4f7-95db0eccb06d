package com.sgs.ecom.order.dto.ro;

import com.platform.util.ValidationUtil;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


public class MailDetailDTO {


    private String unit;

    private BigDecimal originalPrice;

    private String categoryPath;

    private BigDecimal price;

    private BigDecimal totalPrice;

    private Integer buyNums;

    private String itemName;

    private String itemNameShow;


    private String testName;

    private String sampleRequirements;

    private String parentItemName;

    private List<MailDetailDTO> subDetailList=new ArrayList<>();

    private String subDetailListStr;

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getBuyNums() {
        return buyNums;
    }

    public void setBuyNums(Integer buyNums) {
        this.buyNums = buyNums;
    }

    public String getItemName() {
        return itemName;
    }

    public void setItemName(String itemName) {
        this.itemName = itemName;
    }



    public BigDecimal getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(BigDecimal totalPrice) {
        this.totalPrice = totalPrice;
    }


    public String getTestName() {
        return testName;
    }

    public void setTestName(String testName) {
        this.testName = testName;
    }


    public String getSampleRequirements() {
        return sampleRequirements;
    }

    public void setSampleRequirements(String sampleRequirements) {
        this.sampleRequirements = sampleRequirements;
    }



    public String getParentItemName() {
        return parentItemName;
    }

    public void setParentItemName(String parentItemName) {
        this.parentItemName = parentItemName;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public String getCategoryPath() {
        return categoryPath;
    }

    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }

    public String getItemNameShow() {
        if(StringUtils.isNotBlank(categoryPath)){
            return categoryPath+"/"+itemName;
        }
        return itemName;
    }

    public void setItemNameShow(String itemNameShow) {
        this.itemNameShow = itemNameShow;
    }


    public String getSubDetailListStr() {
        String str="";
        if(!ValidationUtil.isEmpty(subDetailList)){
            str="("+subDetailList.stream().map(MailDetailDTO::getItemName).collect(Collectors.joining("、"))+")";
        }
        return str;
    }

    public void setSubDetailListStr(String subDetailListStr) {
        this.subDetailListStr = subDetailListStr;
    }

    public List<MailDetailDTO> getSubDetailList() {
        return subDetailList;
    }

    public void setSubDetailList(List<MailDetailDTO> subDetailList) {
        this.subDetailList = subDetailList;
    }
}
