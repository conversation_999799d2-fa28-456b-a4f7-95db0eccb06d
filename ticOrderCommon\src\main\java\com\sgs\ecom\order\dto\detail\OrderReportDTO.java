package com.sgs.ecom.order.dto.detail;

import com.platform.annotation.ApiAnno;
import com.platform.annotation.BeanAnno;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseBean;
import com.sgs.base.BaseQryFilter;
import com.sgs.ecom.order.base.BaseOrderFilter;
import com.sgs.ecom.order.enums.IsJudgeType;
import com.sgs.ecom.order.enumtool.application.ReportMethodEnum;
import com.sgs.ecom.order.enumtool.bbc.storeSon.StoreSLEnum;

import javax.validation.constraints.Size;
import java.math.BigDecimal;

public class OrderReportDTO {
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportCompanyNameCn;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportCompanyNameEn;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportAddressCn;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportAddressEn;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportSendCc;//上传替换报告发给客服指定邮箱

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Long reportId;

	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String orderNo;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String province;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String city;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String town;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isJudge;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String isJudgeShow;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isOpen;//是否公开 0不公开 1公开
	@ApiAnno(groups={BaseQryFilter.Default.class, BaseOrderFilter.OrderQryList.class})
	private int reportMethod;//报告方式,一份样品还是多份样品对应报告
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportMethodName;//
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isPicture;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportLua;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportForm;
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Integer isFlow; //报告是否需要流程图  0-否 1-是
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportLuaCode; //报告编号
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportMethodMemo; //报告其他 的备注
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportRequirements; //报告特殊要求
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String reportPerson; //报告联系列表
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String judgeMemo; //评判描述
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String detectionSiteCategory; //检测场所类别
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String detectionSiteCategoryStr; // 检测场所类别(翻译)
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private Long detectionsite; //检测地址(地址簿)Id
	@ApiAnno(groups={BaseQryFilter.Default.class})
	private int reportRequirementsFlg; //报告特殊要求是否被勾选 0-否 1- 是


	@ApiAnno(groups={BaseQryFilter.Default.class})
	private String isUploadProject; //是否属于上海市环境监测社会化服务监管系统上传项目
	private String isUploadProjectShow; //是否属于上海市环境监测社会化服务监管系统上传项目
	private String subBuCode; //申请表类型

	private Integer reportTitleType;

	public String getSubBuCode() {
		return subBuCode;
	}

	public void setSubBuCode(String subBuCode) {
		this.subBuCode = subBuCode;
	}

	@Size(min = 0,max =30,message = "REPORT_FORM_CODE参数长度超出范围,最大长度为:30", groups = {BaseBean.Insert.class, BaseBean.Update.class})
	@ApiAnno("reportFormCode")
	@BeanAnno("REPORT_FORM_CODE")
	private String reportFormCode;

	public String getIsUploadProjectShow() {
		if(!ValidationUtil.isEmpty(isUploadProject)){
			this.isUploadProjectShow = isUploadProject.equals("0")?"否":isUploadProject.equals("1")?"是":"";

		}
		return isUploadProjectShow;
	}





	public String getIsUploadProject() {
		return isUploadProject;
	}

	public void setIsUploadProject(String isUploadProject) {
		this.isUploadProject = isUploadProject;
	}

	public String getReportFormCode() {
		return reportFormCode;
	}

	public void setReportFormCode(String reportFormCode) {
		this.reportFormCode = reportFormCode;
	}

	public BigDecimal getReportAmount() {
		return reportAmount;
	}

	public void setReportAmount(BigDecimal reportAmount) {
		this.reportAmount = reportAmount;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public Integer getReportType() {
		return reportType;
	}

	public void setReportType(Integer reportType) {
		this.reportType = reportType;
	}

	@ApiAnno("reportAmount")
	@BeanAnno("REPORT_AMOUNT")
	private BigDecimal reportAmount;


	@Size(min = 0,max =19,message = "STATE_DATE参数长度超出范围,最大长度为:19", groups = {BaseBean.Insert.class, BaseBean.Update.class})
	@ApiAnno("stateDate")
	@BeanAnno("STATE_DATE")
	private String stateDate;

	@ApiAnno("state")
	@BeanAnno("STATE")
	private Integer state;



	@Size(min = 0,max =19,message = "CREATE_DATE参数长度超出范围,最大长度为:19", groups = {BaseBean.Insert.class, BaseBean.Update.class})
	@ApiAnno("createDate")
	@BeanAnno("CREATE_DATE")
	private String createDate;



	@ApiAnno("reportType")
	@BeanAnno("REPORT_TYPE")
	private Integer reportType;




	public int getReportRequirementsFlg() {
		return reportRequirementsFlg;
	}

	public void setReportRequirementsFlg(int reportRequirementsFlg) {
		this.reportRequirementsFlg = reportRequirementsFlg;
	}

	public String getReportMethodName() {
		if(ValidationUtil.isEmpty(reportMethod) || reportMethod == 0){
			return "";
		}
		return ReportMethodEnum.getNameCh(reportMethod);
	}

	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

	public int getReportMethod() {
		return reportMethod;
	}

	public void setReportMethod(int reportMethod) {
		this.reportMethod = reportMethod;
	}

	public String getReportLuaCode() {
		return reportLuaCode;
	}

	public void setReportLuaCode(String reportLuaCode) {
		this.reportLuaCode = reportLuaCode;
	}

	public String getReportMethodMemo() {
		return reportMethodMemo;
	}

	public void setReportMethodMemo(String reportMethodMemo) {
		this.reportMethodMemo = reportMethodMemo;
	}

	public String getReportRequirements() {
		return reportRequirements;
	}

	public void setReportRequirements(String reportRequirements) {
		this.reportRequirements = reportRequirements;
	}

	public String getReportPerson() {
		return this.reportPerson;
	}

	public void setReportPerson(String reportPerson) {
		this.reportPerson = reportPerson;
	}

	public String getJudgeMemo() {
		return judgeMemo;
	}

	public void setJudgeMemo(String judgeMemo) {
		this.judgeMemo = judgeMemo;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public Long getReportId() {
		return reportId;
	}

	public void setReportId(Long reportId) {
		this.reportId = reportId;
	}

	public String getReportSendCc() {
		return reportSendCc;
	}

	public void setReportSendCc(String reportSendCc) {
		this.reportSendCc = reportSendCc;
	}

	public String getReportCompanyNameCn() {
		return reportCompanyNameCn;
	}

	public void setReportCompanyNameCn(String reportCompanyNameCn) {
		this.reportCompanyNameCn = reportCompanyNameCn;
	}

	public String getReportCompanyNameEn() {
		return reportCompanyNameEn;
	}

	public void setReportCompanyNameEn(String reportCompanyNameEn) {
		this.reportCompanyNameEn = reportCompanyNameEn;
	}

	public String getReportAddressCn() {
		return reportAddressCn;
	}

	public void setReportAddressCn(String reportAddressCn) {
		this.reportAddressCn = reportAddressCn;
	}

	public String getReportAddressEn() {
		return reportAddressEn;
	}

	public void setReportAddressEn(String reportAddressEn) {
		this.reportAddressEn = reportAddressEn;
	}

	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getTown() {
		return town;
	}

	public void setTown(String town) {
		this.town = town;
	}

	public String getReportLua() {
		return reportLua;
	}

	public Integer getIsJudge() {
		return isJudge;
	}

	public void setIsJudge(Integer isJudge) {
		this.isJudge = isJudge;
	}

	public Integer getIsPicture() {
		return isPicture;
	}

	public void setIsPicture(Integer isPicture) {
		this.isPicture = isPicture;
	}

	public void setReportLua(String reportLua) {
		this.reportLua = reportLua;
	}

	public String getReportForm() {
		return reportForm;
	}

	public void setReportForm(String reportForm) {
		this.reportForm = reportForm;
	}

	public Integer getIsFlow() {
		return isFlow;
	}

	public void setIsFlow(Integer isFlow) {
		this.isFlow = isFlow;
	}

	public String getDetectionSiteCategory() {
		return detectionSiteCategory;
	}

	public void setDetectionSiteCategory(String detectionSiteCategory) {
		this.detectionSiteCategory = detectionSiteCategory;
	}

	public String getDetectionSiteCategoryStr() {
		return detectionSiteCategoryStr;
	}

	public void setDetectionSiteCategoryStr(String detectionSiteCategoryStr) {
		this.detectionSiteCategoryStr = detectionSiteCategoryStr;
	}

	public String getIsJudgeShow() {
		if(ValidationUtil.isEmpty(isJudge))
			return "";

		if(!ValidationUtil.isEmpty(subBuCode) &&  subBuCode.equals(StoreSLEnum.SL_HZ_WB_REGULAR.getName()) && isJudge.equals(1)){
			return "按SGS推荐要求评判";
		}

		return IsJudgeType.getName(isJudge);
	}

	public Long getDetectionsite() {
		return detectionsite;
	}

	public void setDetectionsite(Long detectionsite) {
		this.detectionsite = detectionsite;
	}

	public Integer getReportTitleType() {
		return reportTitleType;
	}

	public void setReportTitleType(Integer reportTitleType) {
		this.reportTitleType = reportTitleType;
	}
}
