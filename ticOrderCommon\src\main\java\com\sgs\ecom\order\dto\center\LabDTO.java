package com.sgs.ecom.order.dto.center;

import com.platform.annotation.ApiAnno;
import com.sgs.base.BaseQryFilter;
import org.apache.commons.lang.StringUtils;

public class LabDTO {
    private Long labId;
    private String labName;
    private String province;
    private String city;
    private String town;
    private String labAddress;
    private String linkPerson;
    private String linkPhone;
    private String postcode;
    private String accountNo;
    private String subBu;
    private String businessLineId;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labAddressShow;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String alipayChannel;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String wxpayChannel;
    private String catName;
    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String categoryName;

    @ApiAnno(groups={BaseQryFilter.Default.class})
    private String labCode;

    private String orderNo;

    private String paymentChannelSource;

    private Integer labFlg;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getLabCode() {
        return labCode;
    }

    public void setLabCode(String labCode) {
        this.labCode = labCode;
    }

    public Long getLabId() {
        return labId;
    }

    public void setLabId(Long labId) {
        this.labId = labId;
    }

    public String getLabName() {
        return labName;
    }

    public void setLabName(String labName) {
        this.labName = labName;
    }



    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }



    public String getLabAddress() {
        return labAddress;
    }

    public void setLabAddress(String labAddress) {
        this.labAddress = labAddress;
    }

    public String getLinkPerson() {
        return linkPerson;
    }

    public void setLinkPerson(String linkPerson) {
        this.linkPerson = linkPerson;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }

    public String getPostcode() {
        return postcode;
    }

    public void setPostcode(String postcode) {
        this.postcode = postcode;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public String getSubBu() {
        return subBu;
    }

    public void setSubBu(String subBu) {
        this.subBu = subBu;
    }

    public String getBusinessLineId() {
        return businessLineId;
    }

    public void setBusinessLineId(String businessLineId) {
        this.businessLineId = businessLineId;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getLabAddressShow() {

        StringBuilder stringBuilder=new StringBuilder();
        if(StringUtils.isNotBlank(province)){
            stringBuilder.append(province);
        }
        if(StringUtils.isNotBlank(city)){
            stringBuilder.append(city);
        }
        if(StringUtils.isNotBlank(town)){
            stringBuilder.append(town);
        }
        if(StringUtils.isNotBlank(labAddress)){
            stringBuilder.append(labAddress);
        }

        return stringBuilder.toString();
    }

    public String getAlipayChannel() {
        return alipayChannel;
    }

    public void setAlipayChannel(String alipayChannel) {
        this.alipayChannel = alipayChannel;
    }

    public String getWxpayChannel() {
        return wxpayChannel;
    }

    public void setWxpayChannel(String wxpayChannel) {
        this.wxpayChannel = wxpayChannel;
    }

    public String getCatName() {
        return catName;
    }

    public void setCatName(String catName) {
        this.catName = catName;
    }

    public Integer getLabFlg() {
        return labFlg;
    }

    public void setLabFlg(Integer labFlg) {
        this.labFlg = labFlg;
    }

    public String getPaymentChannelSource() {
        return paymentChannelSource;
    }

    public void setPaymentChannelSource(String paymentChannelSource) {
        this.paymentChannelSource = paymentChannelSource;
    }
}
