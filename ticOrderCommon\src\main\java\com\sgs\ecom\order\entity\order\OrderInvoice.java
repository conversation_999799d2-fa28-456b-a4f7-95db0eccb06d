package com.sgs.ecom.order.entity.order;

import javax.persistence.Id;

public class OrderInvoice {
	@Id
	private Long id;
	private String orderNo;
	private String detailNo;
	private Long invoiceId;
	private Integer invoiceType;
	private String taxNo;
	private String bossNo;
	private String bossTag;
	private String registerAddr;
	private String registerPhone;
	private String bankAddr;
	private String bankNumber;
	private Long userId;
	private String deliveryName;
	private String deliveryPhone;
	private String deliveryAddr;
	private String deliveryProvince;
	private String deliveryCity;
	private String deliveryTown;
	private Integer state;
	private String createDate;
	private String stateDate;
	private String frontImg;
	private String backImg;
	private String deliverCompany;
	private String deliverMail;
	private Long addressId;
	private String linkPhone;
	private String linkPerson;
	private String linkEmail;
	private String monthCompanyName;
	private String monthCompanyNameEn;
	private String monthAddress;
	private String monthAddressEn;
	private String invoiceTitle;
	private Integer isForeign=0;

	private String country;
	private String foreignCity;
	private String postCode;
	private String contact;

	private String payerName;

	private String payerPhone;

	private String payerEmail;

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getForeignCity() {
		return foreignCity;
	}

	public void setForeignCity(String foreignCity) {
		this.foreignCity = foreignCity;
	}

	public String getPostCode() {
		return postCode;
	}

	public void setPostCode(String postCode) {
		this.postCode = postCode;
	}



	public String getInvoiceTitle() {
		return invoiceTitle;
	}

	public void setInvoiceTitle(String invoiceTitle) {
		this.invoiceTitle = invoiceTitle;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getOrderNo() {
		return orderNo;
	}

	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}

	public String getDetailNo() {
		return detailNo;
	}

	public void setDetailNo(String detailNo) {
		this.detailNo = detailNo;
	}

	public Long getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(Long invoiceId) {
		this.invoiceId = invoiceId;
	}

	public Integer getInvoiceType() {
		return invoiceType;
	}

	public void setInvoiceType(Integer invoiceType) {
		this.invoiceType = invoiceType;
	}

	public String getTaxNo() {
		return taxNo;
	}

	public void setTaxNo(String taxNo) {
		this.taxNo = taxNo;
	}

	public String getBossNo() {
		return bossNo;
	}

	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}

	public String getBossTag() {
		return bossTag;
	}

	public void setBossTag(String bossTag) {
		this.bossTag = bossTag;
	}

	public String getRegisterAddr() {
		return registerAddr;
	}

	public void setRegisterAddr(String registerAddr) {
		this.registerAddr = registerAddr;
	}

	public String getRegisterPhone() {
		return registerPhone;
	}

	public void setRegisterPhone(String registerPhone) {
		this.registerPhone = registerPhone;
	}

	public String getBankAddr() {
		return bankAddr;
	}

	public void setBankAddr(String bankAddr) {
		this.bankAddr = bankAddr;
	}

	public String getBankNumber() {
		return bankNumber;
	}

	public void setBankNumber(String bankNumber) {
		this.bankNumber = bankNumber;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getDeliveryName() {
		return deliveryName;
	}

	public void setDeliveryName(String deliveryName) {
		this.deliveryName = deliveryName;
	}

	public String getDeliveryPhone() {
		return deliveryPhone;
	}

	public void setDeliveryPhone(String deliveryPhone) {
		this.deliveryPhone = deliveryPhone;
	}

	public String getDeliveryAddr() {
		return deliveryAddr;
	}

	public void setDeliveryAddr(String deliveryAddr) {
		this.deliveryAddr = deliveryAddr;
	}

	public String getDeliveryProvince() {
		return deliveryProvince;
	}

	public void setDeliveryProvince(String deliveryProvince) {
		this.deliveryProvince = deliveryProvince;
	}

	public String getDeliveryCity() {
		return deliveryCity;
	}

	public void setDeliveryCity(String deliveryCity) {
		this.deliveryCity = deliveryCity;
	}

	public String getDeliveryTown() {
		return deliveryTown;
	}

	public void setDeliveryTown(String deliveryTown) {
		this.deliveryTown = deliveryTown;
	}

	public Integer getState() {
		return state;
	}

	public void setState(Integer state) {
		this.state = state;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public String getStateDate() {
		return stateDate;
	}

	public void setStateDate(String stateDate) {
		this.stateDate = stateDate;
	}

	public String getFrontImg() {
		return frontImg;
	}

	public void setFrontImg(String frontImg) {
		this.frontImg = frontImg;
	}

	public String getBackImg() {
		return backImg;
	}

	public void setBackImg(String backImg) {
		this.backImg = backImg;
	}

	public String getDeliverCompany() {
		return deliverCompany;
	}

	public void setDeliverCompany(String deliverCompany) {
		this.deliverCompany = deliverCompany;
	}

	public String getDeliverMail() {
		return deliverMail;
	}

	public void setDeliverMail(String deliverMail) {
		this.deliverMail = deliverMail;
	}

	public Long getAddressId() {
		return addressId;
	}

	public void setAddressId(Long addressId) {
		this.addressId = addressId;
	}

	public String getLinkPhone() {
		return linkPhone;
	}

	public void setLinkPhone(String linkPhone) {
		this.linkPhone = linkPhone;
	}

	public String getLinkPerson() {
		return linkPerson;
	}

	public void setLinkPerson(String linkPerson) {
		this.linkPerson = linkPerson;
	}

	public String getLinkEmail() {
		return linkEmail;
	}

	public void setLinkEmail(String linkEmail) {
		this.linkEmail = linkEmail;
	}

	public String getMonthCompanyName() {
		return monthCompanyName;
	}

	public void setMonthCompanyName(String monthCompanyName) {
		this.monthCompanyName = monthCompanyName;
	}

	public String getMonthCompanyNameEn() {
		return monthCompanyNameEn;
	}

	public void setMonthCompanyNameEn(String monthCompanyNameEn) {
		this.monthCompanyNameEn = monthCompanyNameEn;
	}

	public String getMonthAddress() {
		return monthAddress;
	}

	public void setMonthAddress(String monthAddress) {
		this.monthAddress = monthAddress;
	}

	public String getMonthAddressEn() {
		return monthAddressEn;
	}

	public void setMonthAddressEn(String monthAddressEn) {
		this.monthAddressEn = monthAddressEn;
	}

	public String getPayerName() {
		return payerName;
	}

	public void setPayerName(String payerName) {
		this.payerName = payerName;
	}

	public String getPayerPhone() {
		return payerPhone;
	}

	public void setPayerPhone(String payerPhone) {
		this.payerPhone = payerPhone;
	}

	public String getPayerEmail() {
		return payerEmail;
	}

	public void setPayerEmail(String payerEmail) {
		this.payerEmail = payerEmail;
	}

	public void setIsForeign(Integer isForeign) {
		this.isForeign = isForeign;
	}

	public Integer getIsForeign() {
		return isForeign;
	}
}