package com.sgs.ecom.order.controller.order;

import com.netflix.hystrix.contrib.javanica.annotation.DefaultProperties;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.platform.util.SysException;
import com.sgs.base.BaseBean;
import com.sgs.ecom.order.advice.CheckType;
import com.sgs.ecom.order.request.base.OrderIdReq;
import com.sgs.ecom.order.request.base.OrderReq;
import com.sgs.ecom.order.request.question.OrderQuestionReq;
import com.sgs.ecom.order.request.question.QuestionOriginalReq;
import com.sgs.ecom.order.service.util.interfaces.IOrderQuestionService;
import com.sgs.ecom.order.util.BusinessException;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@DefaultProperties(defaultFallback = "fallBackMethod", ignoreExceptions = {BusinessException.class, SysException.class})
@RestController
@RequestMapping("/business/api.v2.order/question")
public class OrderQuestionController extends ControllerUtil {

	@Autowired
	private IOrderQuestionService orderQuestionService;

	/**
	 *@Function: copyQuestion
	 *@Description 修改问卷 往缓存塞数据
	 *@param: [orderBaseInfoUpdateReq, token]
	 *@author: Xiwei_Qiu @date: 2022/5/31 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "copyQuestion", method = {RequestMethod.POST})
	public ResultBody copyQuestion(
		@RequestBody QuestionOriginalReq questionOptionalReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(orderQuestionService.copyQuestion(questionOptionalReq, getPersonInfo(token), getPower(token)));
	}

	/**
	*@Function: saveQuestion
	*@Description 保存问卷
	*@param: [orderQuestionReq, token]
	*@author: Xiwei_Qiu @date: 2022/6/1 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "saveQuestion", method = {RequestMethod.POST})
	public ResultBody saveQuestion(
		@Validated(value = BaseBean.Insert.class)
		@RequestBody OrderQuestionReq orderQuestionReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		orderQuestionService.saveQuestion(orderQuestionReq, getPersonInfo(token), getPower(token));
		return ResultBody.success();
	}

	/**
	*@Function: qryQuestion
	*@Description 查询问卷数据单个
	*@param: [token, optionalReq]
	*@author: Xiwei_Qiu @date: 2022/6/1 @version:
	**/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryQuestion", method = {RequestMethod.POST})
	public ResultBody qryQuestion(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody QuestionOriginalReq optionalReq) throws Exception {
		return ResultBody.newInstance(orderQuestionService.qryQuestion(optionalReq, getPersonInfo(token), getPower(token)));
	}

	/**
	 *@Function: qryQuestion
	 *@Description 查询问卷数据单个
	 *@param: [token, optionalReq]
	 *@author: Xiwei_Qiu @date: 2022/6/1 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qryQuestionList", method = {RequestMethod.POST})
	public ResultBody qryQuestionList(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody QuestionOriginalReq optionalReq) throws Exception {
		return ResultBody.newInstance(orderQuestionService.qryQuestionList(optionalReq, getPersonInfo(token), getPower(token)));
	}

	@HystrixCommand
	@CheckType
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "delQuestion", method = {RequestMethod.POST})
	public ResultBody delQuestion(
		@RequestHeader(value = "accessToken") String token,
		@RequestBody OrderReq orderReq
	) throws Exception {
		orderQuestionService.delQuestion(orderReq, getPersonInfo(token));
		return ResultBody.success();
	}
}
