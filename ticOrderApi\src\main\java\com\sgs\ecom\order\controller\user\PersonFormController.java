package com.sgs.ecom.order.controller.user;

import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;
import com.platform.annotation.AuthRequired;
import com.sgs.base.BaseController;
import com.sgs.ecom.order.request.user.PersonFormReq;
import com.sgs.ecom.order.service.user.interfaces.IPersonFormSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/business/api.v1.person/form")
public class PersonFormController  extends ControllerUtil {
	@Autowired
	private IPersonFormSV personFormSV;

	/**
	 *@Function: savePersonForm
	 *@Description 保存测试项目的form的size
	 *@param: [orderIdReq, token]
	 *@author: <PERSON><PERSON>_<PERSON><PERSON> @date: 2022/3/28 @version:
	 **/
	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "save", method = {RequestMethod.POST})
	public ResultBody savePersonForm(
		@RequestBody PersonFormReq personFormReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		personFormSV.savePersonForm(personFormReq, getPersonInfo(token));
		return ResultBody.success();
	}

	@HystrixCommand
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "qry", method = {RequestMethod.POST})
	public ResultBody qry(
		@RequestBody PersonFormReq personFormReq,
		@RequestHeader(value = "accessToken") String token) throws Exception {
		return ResultBody.newInstance(personFormSV.qry(personFormReq, getPersonInfo(token)));
	}
}
