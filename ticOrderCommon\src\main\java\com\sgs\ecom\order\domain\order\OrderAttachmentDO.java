package com.sgs.ecom.order.domain.order;

import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.bill.ExpBillInfoDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.bbc.BbcSkuAttrDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.order.OrderLogAttachmentDTO;
import com.sgs.ecom.order.dto.detail.OrderAttachmentDTO;
import com.sgs.ecom.order.dto.rpc.dml.QuotationFileDTO;
import com.sgs.ecom.order.entity.order.OrderAttachment;
import com.sgs.ecom.order.enumtool.OrderAttachmentTypeEnum;
import com.sgs.ecom.order.request.FileReq;
import com.sgs.ecom.order.request.base.BaseFileReq;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.vo.VOOrderDetail;
import com.sgs.ecom.order.vo.order.VOOrderAttachment;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class OrderAttachmentDO {

    private BaseCopyObj baseCopy = new BaseCopyObj();


    public  List<OrderAttachment> getBaseFileReq(List<BaseFileReq> fileReqList, String orderNo, OrderAttachmentTypeEnum orderAttachmentTypeEnum, String groupNo,String dateStr) {
        List<OrderAttachment> list=new ArrayList<>();
        for(BaseFileReq baseFileReq:fileReqList){
            OrderAttachment orderAttachment=initBase(orderNo,orderAttachmentTypeEnum,baseFileReq,groupNo,dateStr);
            list.add(orderAttachment);
        }
        return list;
    }

    public List<QuotationFileDTO>  baseToQuotation(List<BaseFileReq> fileReqList){
        List<QuotationFileDTO> list=new ArrayList<>();
        for(BaseFileReq baseFileReq:fileReqList){
            QuotationFileDTO quotationFileDTO=new QuotationFileDTO();
            baseCopy.copyWithNull(quotationFileDTO,baseFileReq);
            list.add(quotationFileDTO);
        }
        return list;
    }


    public  List<OrderAttachment> getOrderAttachment(List<FileReq> fileReqList, String orderNo, OrderAttachmentTypeEnum orderAttachmentTypeEnum, String groupNo) {
        List<OrderAttachment> list=new ArrayList<>();
        for(FileReq fileReq:fileReqList){
            OrderAttachment orderAttachment=initOrderAttachment(orderNo,orderAttachmentTypeEnum,fileReq,groupNo);
            list.add(orderAttachment);
        }
        return list;
    }

    private OrderAttachment initOrderAttachment(String orderNo, OrderAttachmentTypeEnum orderAttachmentTypeEnum, FileReq fileReq, String groupNo) {
        OrderAttachment orderAttachment=new OrderAttachment();
        baseCopy.copy(orderAttachment,fileReq);
        String dateStr=UseDateUtil.getDateString(new Date());
        orderAttachment= init(orderAttachment,orderNo,orderAttachmentTypeEnum,groupNo, dateStr);
        return orderAttachment;
    }

    private OrderAttachment initBase(String orderNo, OrderAttachmentTypeEnum orderAttachmentTypeEnum, BaseFileReq baseFileReq, String groupNo,String dateStr) {
        OrderAttachment orderAttachment=new OrderAttachment();
        baseCopy.copy(orderAttachment,baseFileReq);
        orderAttachment= init(orderAttachment,orderNo,orderAttachmentTypeEnum,groupNo, dateStr);
        return orderAttachment;
    }

    private OrderAttachment init(OrderAttachment orderAttachment, String orderNo, OrderAttachmentTypeEnum orderAttachmentTypeEnum, String groupNo,String dateStr) {
        orderAttachment.setCreateDate(dateStr);
        orderAttachment.setStateDate(dateStr);
        orderAttachment.setState(1);
        orderAttachment.setOrderNo(orderNo);
        orderAttachment.setGroupNo(groupNo);
        orderAttachment.setAttType(orderAttachmentTypeEnum.getIndex());
        orderAttachment.setUploadType(2);
        return orderAttachment;
    }

    public List<OrderAttachment> getListByReq(List<FileReq> fileReqList, String orderNo, String groupNo,
                                              OrderAttachmentTypeEnum orderAttachmentTypeEnum, String dateStr){
        List<OrderAttachment> list=new ArrayList<>();
        for(FileReq fileReq:fileReqList){
            OrderAttachment orderAttachment=new OrderAttachment();
            BaseCopyObj baseCopyObj=new BaseCopyObj();
            baseCopyObj.copyWithNull(orderAttachment,fileReq);
            orderAttachment.setCreateDate(dateStr);
            orderAttachment.setStateDate(dateStr);
            orderAttachment.setState(1);
            orderAttachment.setOrderNo(orderNo);
            orderAttachment.setGroupNo(groupNo);
            orderAttachment.setAttType(orderAttachmentTypeEnum.getIndex());
            orderAttachment.setUploadType(2);
            list.add(orderAttachment);
        }
        return list;
    }

    public  List<OrderAttachment> getDistinctListByUploadTypeReq(List<FileReq> fileReqList, String orderNo, String groupNo,
                                                                 OrderAttachmentTypeEnum orderAttachmentTypeEnum, String dateStr, List<OrderAttachmentDTO> oldList){
        List<String> invoiceNumberList=oldList.stream().filter(a->StringUtils.isNotBlank(a.getInvoiceNumber())).map(OrderAttachmentDTO::getInvoiceNumber).distinct().collect(Collectors.toList());
        fileReqList=fileReqList.stream().filter(a->!invoiceNumberList.contains(a.getInvoiceNumber())).collect(Collectors.toList());
        List<OrderAttachment> list=new ArrayList<>();
        for(FileReq fileReq:fileReqList){
            OrderAttachment orderAttachment=new OrderAttachment();
            BaseCopyObj baseCopyObj=new BaseCopyObj();
            baseCopyObj.copyWithNull(orderAttachment,fileReq);
            orderAttachment.setCreateDate(dateStr);
            orderAttachment.setStateDate(dateStr);
            orderAttachment.setState(1);
            orderAttachment.setOrderNo(orderNo);
            orderAttachment.setGroupNo(groupNo);
            orderAttachment.setAttType(orderAttachmentTypeEnum.getIndex());
            orderAttachment.setUploadType(2);
            if(StringUtils.isBlank(orderAttachment.getFileId())){
                orderAttachment.setUploadType(1);
            }
            list.add(orderAttachment);
        }
        return list;
    }

    public List<OrderAttachment> initFiles(VOOrderDetail orderDetailDTO, String orderNo, Long detailId, String index, String dateStr,
                                           List<OrderLogAttachmentDTO> addFileList, Map<String, String> flagMap) {
        List<OrderAttachment> list = new ArrayList<>();
        for (VOOrderAttachment attachment : orderDetailDTO.getAttachments()) {
            if(attachment.getAttachmentId() == null){//新增
                OrderLogAttachmentDTO orderLogAttachmentDTO = new OrderLogAttachmentDTO();
                baseCopy.copy(orderLogAttachmentDTO,attachment);
                addFileList.add(orderLogAttachmentDTO);
                flagMap.put(orderDetailDTO.getIsCs() == 1?"serviceMemoFile":"customerMemoFile", "ok");
            }
            OrderAttachment orderAttachment=new OrderAttachment();
            baseCopy.copy(orderAttachment,attachment);
            initBaseInfo(orderAttachment,orderNo,dateStr);
            orderAttachment.setGroupNo(String.valueOf(detailId));
            orderAttachment.setAttType(index);
            orderAttachment.setUploadType(2);
            list.add(orderAttachment);
        }
        return list;
    }

    private void initBaseInfo(OrderAttachment orderAttachment, String orderNo, String dateStr) {
        orderAttachment.setOrderNo(orderNo);
        orderAttachment.setState(1);
        orderAttachment.setStateDate(dateStr);
        orderAttachment.setCreateDate(dateStr);
    }

    public static List<OrderAttachmentDTO> getFileListByAttType(Map<String, List<OrderAttachmentDTO>> listMap, String orderNo, OrderAttachmentTypeEnum typeEnum){
        String key=orderNo+"-"+typeEnum.getIndex();
        return listMap.getOrDefault(key,new ArrayList<>());
    }



}
