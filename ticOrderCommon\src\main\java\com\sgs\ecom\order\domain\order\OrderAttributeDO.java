package com.sgs.ecom.order.domain.order;

import com.alibaba.fastjson.JSON;
import com.platform.util.ValidationUtil;
import com.sgs.base.BaseCopyObj;
import com.sgs.ecom.order.dto.center.LabDTO;
import com.sgs.ecom.order.dto.order.OrderAttributeDTO;
import com.sgs.ecom.order.dto.pay.BankDTO;
import com.sgs.ecom.order.dto.pay.PaymentChannelsDTO;
import com.sgs.ecom.order.entity.order.OrderAttribute;
import com.sgs.ecom.order.enumtool.dml.ReportFormCodeEnum;
import com.sgs.ecom.order.enumtool.dml.ReportLuaCodeEnum;
import com.sgs.ecom.order.util.UseDateUtil;
import com.sgs.ecom.order.util.select.AttributeUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public class OrderAttributeDO extends OrderAttribute {



    public OrderAttribute getLabAttr(LabDTO labDTO, String orderNo, String groupNo){
        if(ValidationUtil.isEmpty(labDTO)){
            return null;
        }
        if(labDTO.getLabId()==0L){
            return null;
        }
        OrderAttribute orderAttribute=new OrderAttributeDO();
        init(orderAttribute,orderNo,groupNo);
        orderAttribute.setAttrCode(String.valueOf(labDTO.getLabId()));
        orderAttribute.setAttrValue(AttributeUtil.LAB_NAME);
        orderAttribute.setAttrName(labDTO.getLabName());
        return orderAttribute;
    }

    public OrderAttribute getLuaAttr(String reportLua,String orderNo,String groupNo){
        OrderAttribute orderAttribute=new OrderAttributeDO();
        init(orderAttribute,orderNo,groupNo);
        orderAttribute.setAttrCode(reportLua);
        orderAttribute.setAttrValue(AttributeUtil.REPORT_LUA);
        orderAttribute.setAttrName(ReportLuaCodeEnum.getNameCh(reportLua));
        return orderAttribute;
    }

    public OrderAttribute getFormAttr(String reportForm,String orderNo,String groupNo){
        OrderAttribute orderAttribute=new OrderAttributeDO();
        init(orderAttribute,orderNo,groupNo);
        orderAttribute.setAttrCode(reportForm);
        orderAttribute.setAttrValue(AttributeUtil.REPORT_FORM);
        orderAttribute.setAttrName(ReportFormCodeEnum.getNameCh(reportForm));
        return orderAttribute;
    }

    public OrderAttribute getBaseAttr(String key,String value,String orderNo,String groupNo){
        OrderAttribute orderAttribute=new OrderAttributeDO();
        init(orderAttribute,orderNo,groupNo);
        orderAttribute.setAttrValue(key);
        orderAttribute.setAttrName(value);
        return orderAttribute;
    }

    public OrderAttribute getBasePriceAttr(String key,String value,String orderNo,String groupNo){
        OrderAttribute orderAttribute=new OrderAttributeDO();
        init(orderAttribute,orderNo,groupNo);
        orderAttribute.setAttrValue(key);
        orderAttribute.setAttrName(value);
        orderAttribute.setAttrAmount(new BigDecimal(StringUtils.isBlank(value)?"0":value));
        return orderAttribute;
    }

    private void init(OrderAttribute orderAttribute,String orderNo,String groupNo) {
        orderAttribute.setOrderNo(orderNo);
        orderAttribute.setGroupNo(groupNo);
        orderAttribute.setIsDefault(1);
        orderAttribute.setCreateDate(UseDateUtil.getDateString(new Date()));
        orderAttribute.setAttrAmount(BigDecimal.ZERO);
    }

    public List<OrderAttribute> getPaymentAttrList(List<PaymentChannelsDTO> paymentChannelsDTOList, String orderNo, String groupNo){
        if(ValidationUtil.isEmpty(paymentChannelsDTOList)){
            return new ArrayList<>();
        }
        List<OrderAttribute> list=new ArrayList<>();
        for(PaymentChannelsDTO paymentChannelsDTO:paymentChannelsDTOList){
            OrderAttribute orderAttribute=new OrderAttributeDO();
            init(orderAttribute,orderNo,groupNo);
            orderAttribute.setAttrValue(AttributeUtil.PAYMENT_CHANNEL);
            orderAttribute.setAttrName("支付方式");
            orderAttribute.setAttrCode(paymentChannelsDTO.getPaymentMethod());
            if(paymentChannelsDTO.getPaymentMethod().equals("300000")){
                orderAttribute.setAttrName(JSON.toJSONString(paymentChannelsDTO));
            }else{
                orderAttribute.setAttrExtend(paymentChannelsDTO.getChannelCode());
            }
            list.add(orderAttribute);
        }
        return list;
    }

    public static Map<String, OrderAttributeDTO> getPaymentByAttr(Map<String, List<OrderAttributeDTO>> map, String key){
        List<OrderAttributeDTO> list=map.getOrDefault(key,new ArrayList<>());
        if(ValidationUtil.isEmpty(list)){
            return new HashMap<>();
        }
        return list.stream().collect(Collectors.toMap(OrderAttributeDTO::getAttrCode, Function.identity(), (key1, key2) -> key1, LinkedHashMap::new));
    }

    public static BankDTO getBankByAttribute(OrderAttributeDTO orderAttributeDTO){
        PaymentChannelsDTO paymentChannelsDTO= JSON.parseObject(orderAttributeDTO.getAttrName(), PaymentChannelsDTO.class);
        BankDTO bankDTO=new BankDTO();
        BaseCopyObj baseCopyObj=new BaseCopyObj();
        baseCopyObj.copyWithNull(bankDTO,paymentChannelsDTO);
        bankDTO.setAccountNo(paymentChannelsDTO.getBankNo());
        return bankDTO;
    }

}
