package com.sgs.ecom.order.dto.user;

import java.util.Date;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * 用户微信绑定信息
 *
 * <AUTHOR>
 * @since 2025-06-27 13:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserWechatBind {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 微信APPID
     */
    private String appId;

    /**
     * 微信OPENID
     */
    private String openId;

    /**
     * 微信UNIONID
     */
    private String unionId;

    /**
     * 状态 1：有效;2：授权中;3：授权码失效;4：已授权
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date stateDate;

    /**
     * 微信类型，MINI, WEB
     */
    private String wechatType;

    /**
     * 微信昵称
     */
    private String nickName;

    /**
     * 微信性别
     */
    private String sex;

    private String province;

    /**
     * 城市
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    /**
     * 头像
     */
    private String headImgUrl;
}
