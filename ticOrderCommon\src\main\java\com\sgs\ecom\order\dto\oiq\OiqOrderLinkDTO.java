package com.sgs.ecom.order.dto.oiq;

import com.platform.util.ValidationUtil;
import com.sgs.ecom.order.dto.order.OrderLinkDTO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class OiqOrderLinkDTO {
    private String linkName;
    private String linkEmail;
    private String linkPhone;



    public static String listToStr(Map<String, List<OrderLinkDTO>> linkMap, String key, String str){
        String returnStr="";
        List<OrderLinkDTO> list=linkMap.getOrDefault(key,new ArrayList<>());
        if(!ValidationUtil.isEmpty(list)){
            returnStr=list.stream().map(OrderLinkDTO::getLinkEmail).collect(Collectors.joining(str));
        }
        return returnStr;
    }

    public String getLinkName() {
        return linkName;
    }

    public void setLinkName(String linkName) {
        this.linkName = linkName;
    }

    public String getLinkEmail() {
        return linkEmail;
    }

    public void setLinkEmail(String linkEmail) {
        this.linkEmail = linkEmail;
    }

    public String getLinkPhone() {
        return linkPhone;
    }

    public void setLinkPhone(String linkPhone) {
        this.linkPhone = linkPhone;
    }
}
