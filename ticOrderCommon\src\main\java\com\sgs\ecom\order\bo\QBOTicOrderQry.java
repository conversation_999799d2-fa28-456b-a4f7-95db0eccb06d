package com.sgs.ecom.order.bo; 
 
import com.fasterxml.jackson.databind.annotation.JsonSerialize; 
import com.platform.annotation.CharacterVaild; 
import java.sql.Timestamp; 
import com.platform.util.json.TimeFormatSerializer; 
import java.util.Date; 
import com.platform.util.json.DateFormatSerializer; 

public class QBOTicOrderQry{
 
 	public static final String SEQUENCE = ""; 
  
 	public static final String BO_SQL = "select a.*,b.ProductID,b.<PERSON>,b.Quantity,b.<PERSON>ku<PERSON>ttr,b.Remark,'' fileName from TIC_Order a JOIN TIC_OrderLine b ON a.orderno=b.orderno LEFT JOIN TIC_Attachment c ON a.id=c.orderid"; 
 
 	public static final String OWNER ="bbc";

 	public static final String S_STATUSREFUND="StatusRefund";
 	public static final String S_ORDERCOMMENTS="OrderComments";
 	public static final String S_FAPIAOINFO="FapiaoInfo";
 	public static final String S_HAVEREAD="HaveRead";
 	public static final String S_ABSTRACT_STATUS="Abstract_Status";
 	public static final String S_OPERATOR="Operator";
 	public static final String S_RECOVERSTATUS="RecoverStatus";
 	public static final String S_ORIGINALPRICE="OriginalPrice";
 	public static final String S_CLOSEPAYORDER="ClosePayOrder";
 	public static final String S_STATUSRETURNCREATTIME="StatusReturnCreatTime";
 	public static final String S_CHANGESTATUS="ChangeStatus";
 	public static final String S_CONFIRMPAYMENTDATE="ConfirmPaymentDate";
 	public static final String S_SKUATTR="SkuAttr";
 	public static final String S_ORDERNO="OrderNo";
 	public static final String S_STORENAME="StoreName";
 	public static final String S_ABSTRACT_PAYMENT_CREATTIME="Abstract_Payment_Creattime";
 	public static final String S_ID="ID";
 	public static final String S_PAYACCOUNT="PayAccount";
 	public static final String S_PRODUCTNAME="ProductName";
 	public static final String S_CHANGEPRICEDATE="ChangePriceDate";
 	public static final String S_CONTACTTEL="ContactTel";
 	public static final String S_STATUSSERVICEAPPCREATTIME="StatusServiceAppCreatTime";
 	public static final String S_COMFIRMAPPDATE="ComfirmAppDate";
 	public static final String S_STATUSREFUNDCREATTIME="StatusRefundCreatTime";
 	public static final String S_FILEAUDITPICINFO="FileAuditPicInfo";
 	public static final String S_CANCELFLG="CancelFlg";
 	public static final String S_STATUSCANCEL="StatusCancel";
 	public static final String S_CATEGORYNAME="CategoryName";
 	public static final String S_STATUSCHECKAPPCREATTIME="StatusCheckAppCreatTime";
 	public static final String S_STATUSSUBMITAPPCREATTIME="StatusSubmitAppCreatTime";
 	public static final String S_READSTATUS="ReadStatus";
 	public static final String S_COMPANYNAMECN="CompanyNameCN";
 	public static final String S_PAYMENTSTATUS="PaymentStatus";
 	public static final String S_DISTRICT="District";
 	public static final String S_PRICE="Price";
 	public static final String S_MAILSEND="MailSend";
 	public static final String S_STATUSWAITPAYMENTCREATTIME="StatusWaitPaymentCreatTime";
 	public static final String S_STATUSPAYMENTDOCUMENTCREATTIME="StatusPaymentDocumentCreatTime";
 	public static final String S_CANCELREASON="CancelReason";
 	public static final String S_UPLOADREPORTDATE="UploadReportDate";
 	public static final String S_ABSTRACT_WAIT_PAYMENT="Abstract_Wait_Payment";
 	public static final String S_STATUSCANCELAPPCREATTIME="StatusCancelAppCreatTime";
 	public static final String S_TOTALPRICE="TotalPrice";
 	public static final String S_PRODUCTID="ProductID";
 	public static final String S_ABSTRACT_CODE="Abstract_Code";
 	public static final String S_COMPANYADDRESS="CompanyAddress";
 	public static final String S_ORDERSTARTTIME="OrderStartTime";
 	public static final String S_USERID="UserID";
 	public static final String S_INSPECTIONINFO="InspectionInfo";
 	public static final String S_USERNAME="UserName";
 	public static final String S_CANCELDATE="CancelDate";
 	public static final String S_STATUSCHECKAPP="StatusCheckApp";
 	public static final String S_REPORT_SEND_CC="report_send_cc";
 	public static final String S_ABSTRACT_WAIT_PAYMENT_CREATTIME="Abstract_Wait_Payment_Creattime";
 	public static final String S_CONFIRMAPPBY="ConfirmAppBy";
 	public static final String S_ABSTRACT_PAYMENT_DOCUMENT_CREATTIME="Abstract_Payment_Document_Creattime";
 	public static final String S_ABSTRACT_PAYMENT_DOCUMENT="Abstract_Payment_Document";
 	public static final String S_STATUSPAYMENTDOCUMENT="StatusPaymentDocument";
 	public static final String S_ABSTRACT_PAYMENT="Abstract_Payment";
 	public static final String S_CHANGEPRICECOMMENTS="ChangePriceComments";
 	public static final String S_STATUSRETURN="StatusReturn";
 	public static final String S_ENNAME="EnName";
 	public static final String S_SUBTITLE="Subtitle";
 	public static final String S_IS_TICKET="is_ticket";
 	public static final String S_QUANTITY="Quantity";
 	public static final String S_LEADS_CODE="Leads_Code";
 	public static final String S_EXPRESSINFO="ExpressInfo";
 	public static final String S_STATUSSERVICE="StatusService";
 	public static final String S_CONTACTNAME="ContactName";
 	public static final String S_CONFIRMDATE="ConfirmDate";
 	public static final String S_CONTACTEMAIL="ContactEmail";
 	public static final String S_MODIFIEDBY="ModifiedBy";
 	public static final String S_STATUSFINISHAPPCREATTIME="StatusFinishAppCreatTime";
 	public static final String S_STATUSWAITPAYMENT="StatusWaitPayment";
 	public static final String S_IS_MONTH_PAY="is_month_pay";
 	public static final String S_PROVINCE="Province";
 	public static final String S_ABSTRACT_CUSTCODE="Abstract_CustCode";
 	public static final String S_PARENTID="ParentID";
 	public static final String S_CITY="City";
 	public static final String S_STATUSSUBMITAPP="StatusSubmitApp";
 	public static final String S_OFFLINESTATUS="OfflineStatus";
 	public static final String S_MODIFIEDDATE="ModifiedDate";
 	public static final String S_STATUS="Status";
 	public static final String S_FILENAME="FileName";
 	public static final String S_COMPANYNAMEEN="CompanyNameEN";
 	public static final String S_SMSSEND="SmsSend";
 	public static final String S_STOREID="StoreID";
 	public static final String S_ORDERFINISHTIME="OrderFinishTime";
 	public static final String S_CREATEDBY="CreatedBy";
 	public static final String S_CREATEDDATE="CreatedDate";
 	public static final String S_PAYAMOUNT="PayAmount";
 	public static final String S_CLOSESTATUS="CloseStatus";
 	public static final String S_STATUSFINISH="StatusFinish";
 	public static final String S_REMARK="Remark";
 	public static final String S_PAYDATE="PayDate";

 	public static final String STATUSREFUND="statusRefund";
 	public static final String ORDERCOMMENTS="orderComments";
 	public static final String FAPIAOINFO="fapiaoInfo";
 	public static final String HAVEREAD="haveRead";
 	public static final String ABSTRACT_STATUS="abstractStatus";
 	public static final String OPERATOR="operator";
 	public static final String RECOVERSTATUS="recoverStatus";
 	public static final String ORIGINALPRICE="originalPrice";
 	public static final String CLOSEPAYORDER="closePayOrder";
 	public static final String STATUSRETURNCREATTIME="statusReturnCreatTime";
 	public static final String CHANGESTATUS="changeStatus";
 	public static final String CONFIRMPAYMENTDATE="confirmPaymentDate";
 	public static final String SKUATTR="skuAttr";
 	public static final String ORDERNO="orderNo";
 	public static final String STORENAME="storeName";
 	public static final String ABSTRACT_PAYMENT_CREATTIME="abstractPaymentCreattime";
 	public static final String ID="iD";
 	public static final String PAYACCOUNT="payAccount";
 	public static final String PRODUCTNAME="productName";
 	public static final String CHANGEPRICEDATE="changePriceDate";
 	public static final String CONTACTTEL="contactTel";
 	public static final String STATUSSERVICEAPPCREATTIME="statusServiceAppCreatTime";
 	public static final String COMFIRMAPPDATE="comfirmAppDate";
 	public static final String STATUSREFUNDCREATTIME="statusRefundCreatTime";
 	public static final String FILEAUDITPICINFO="fileAuditPicInfo";
 	public static final String CANCELFLG="cancelFlg";
 	public static final String STATUSCANCEL="statusCancel";
 	public static final String CATEGORYNAME="categoryName";
 	public static final String STATUSCHECKAPPCREATTIME="statusCheckAppCreatTime";
 	public static final String STATUSSUBMITAPPCREATTIME="statusSubmitAppCreatTime";
 	public static final String READSTATUS="readStatus";
 	public static final String COMPANYNAMECN="companyNameCN";
 	public static final String PAYMENTSTATUS="paymentStatus";
 	public static final String DISTRICT="district";
 	public static final String PRICE="price";
 	public static final String MAILSEND="mailSend";
 	public static final String STATUSWAITPAYMENTCREATTIME="statusWaitPaymentCreatTime";
 	public static final String STATUSPAYMENTDOCUMENTCREATTIME="statusPaymentDocumentCreatTime";
 	public static final String CANCELREASON="cancelReason";
 	public static final String UPLOADREPORTDATE="uploadReportDate";
 	public static final String ABSTRACT_WAIT_PAYMENT="abstractWaitPayment";
 	public static final String STATUSCANCELAPPCREATTIME="statusCancelAppCreatTime";
 	public static final String TOTALPRICE="totalPrice";
 	public static final String PRODUCTID="productID";
 	public static final String ABSTRACT_CODE="abstractCode";
 	public static final String COMPANYADDRESS="companyAddress";
 	public static final String ORDERSTARTTIME="orderStartTime";
 	public static final String USERID="userID";
 	public static final String INSPECTIONINFO="inspectionInfo";
 	public static final String USERNAME="userName";
 	public static final String CANCELDATE="cancelDate";
 	public static final String STATUSCHECKAPP="statusCheckApp";
 	public static final String REPORT_SEND_CC="reportSendCc";
 	public static final String ABSTRACT_WAIT_PAYMENT_CREATTIME="abstractWaitPaymentCreattime";
 	public static final String CONFIRMAPPBY="confirmAppBy";
 	public static final String ABSTRACT_PAYMENT_DOCUMENT_CREATTIME="abstractPaymentDocumentCreattime";
 	public static final String ABSTRACT_PAYMENT_DOCUMENT="abstractPaymentDocument";
 	public static final String STATUSPAYMENTDOCUMENT="statusPaymentDocument";
 	public static final String ABSTRACT_PAYMENT="abstractPayment";
 	public static final String CHANGEPRICECOMMENTS="changePriceComments";
 	public static final String STATUSRETURN="statusReturn";
 	public static final String ENNAME="enName";
 	public static final String SUBTITLE="subtitle";
 	public static final String IS_TICKET="isTicket";
 	public static final String QUANTITY="quantity";
 	public static final String LEADS_CODE="leadsCode";
 	public static final String EXPRESSINFO="expressInfo";
 	public static final String STATUSSERVICE="statusService";
 	public static final String CONTACTNAME="contactName";
 	public static final String CONFIRMDATE="confirmDate";
 	public static final String CONTACTEMAIL="contactEmail";
 	public static final String MODIFIEDBY="modifiedBy";
 	public static final String STATUSFINISHAPPCREATTIME="statusFinishAppCreatTime";
 	public static final String STATUSWAITPAYMENT="statusWaitPayment";
 	public static final String IS_MONTH_PAY="isMonthPay";
 	public static final String PROVINCE="province";
 	public static final String ABSTRACT_CUSTCODE="abstractCustCode";
 	public static final String PARENTID="parentID";
 	public static final String CITY="city";
 	public static final String STATUSSUBMITAPP="statusSubmitApp";
 	public static final String OFFLINESTATUS="offlineStatus";
 	public static final String MODIFIEDDATE="modifiedDate";
 	public static final String STATUS="status";
 	public static final String FILENAME="fileName";
 	public static final String COMPANYNAMEEN="companyNameEN";
 	public static final String SMSSEND="smsSend";
 	public static final String STOREID="storeID";
 	public static final String ORDERFINISHTIME="orderFinishTime";
 	public static final String CREATEDBY="createdBy";
 	public static final String CREATEDDATE="createdDate";
 	public static final String PAYAMOUNT="payAmount";
 	public static final String CLOSESTATUS="closeStatus";
 	public static final String STATUSFINISH="statusFinish";
 	public static final String REMARK="remark";
 	public static final String PAYDATE="payDate";

 	private String statusRefund;
 	private String orderComments;
 	private String fapiaoInfo;
 	private long haveRead;
 	private String abstractStatus;
 	private String operator;
 	private String recoverStatus;
 	private double originalPrice;
 	private String closePayOrder;
 	private Timestamp statusReturnCreatTime;
 	private String changeStatus;
 	private Timestamp confirmPaymentDate;
 	private String skuAttr;
 	private String orderNo;
 	private String storeName;
 	private Timestamp abstractPaymentCreattime;
 	private String iD;
 	private String payAccount;
 	private String productName;
 	private Timestamp changePriceDate;
 	private String contactTel;
 	private Timestamp statusServiceAppCreatTime;
 	private Timestamp comfirmAppDate;
 	private Timestamp statusRefundCreatTime;
 	private String fileAuditPicInfo;
 	private String cancelFlg;
 	private String statusCancel;
 	private String categoryName;
 	private Timestamp statusCheckAppCreatTime;
 	private Timestamp statusSubmitAppCreatTime;
 	private String readStatus;
 	private String companyNameCN;
 	private String paymentStatus;
 	private String district;
 	private double price;
 	private long mailSend;
 	private Timestamp statusWaitPaymentCreatTime;
 	private Timestamp statusPaymentDocumentCreatTime;
 	private String cancelReason;
 	private Timestamp uploadReportDate;
 	private String abstractWaitPayment;
 	private Timestamp statusCancelAppCreatTime;
 	private double totalPrice;
 	private String productID;
 	private String abstractCode;
 	private String companyAddress;
 	private String orderStartTime;
 	private String userID;
 	private String inspectionInfo;
 	private String userName;
 	private Timestamp cancelDate;
 	private String statusCheckApp;
 	private String reportSendCc;
 	private Timestamp abstractWaitPaymentCreattime;
 	private String confirmAppBy;
 	private Timestamp abstractPaymentDocumentCreattime;
 	private String abstractPaymentDocument;
 	private String statusPaymentDocument;
 	private String abstractPayment;
 	private String changePriceComments;
 	private String statusReturn;
 	private String enName;
 	private String subtitle;
 	private int isTicket;
 	private long quantity;
 	private String leadsCode;
 	private String expressInfo;
 	private String statusService;
 	private String contactName;
 	private Timestamp confirmDate;
 	private String contactEmail;
 	private String modifiedBy;
 	private Timestamp statusFinishAppCreatTime;
 	private String statusWaitPayment;
 	private int isMonthPay;
 	private String province;
 	private String abstractCustCode;
 	private String parentID;
 	private String city;
 	private String statusSubmitApp;
 	private String offlineStatus;
 	private Timestamp modifiedDate;
 	private String status;
 	private String fileName;
 	private String companyNameEN;
 	private long smsSend;
 	private String storeID;
 	private Date orderFinishTime;
 	private String createdBy;
 	private Timestamp createdDate;
 	private double payAmount;
 	private String closeStatus;
 	private String statusFinish;
 	private String remark;
 	private Timestamp payDate;

 	@CharacterVaild(len = 10) 
 	public void setStatusRefund(String statusRefund){
 		 this.statusRefund=statusRefund;
 	}
 	public String getStatusRefund(){
 		 return this.statusRefund;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setOrderComments(String orderComments){
 		 this.orderComments=orderComments;
 	}
 	public String getOrderComments(){
 		 return this.orderComments;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setFapiaoInfo(String fapiaoInfo){
 		 this.fapiaoInfo=fapiaoInfo;
 	}
 	public String getFapiaoInfo(){
 		 return this.fapiaoInfo;
 	}
 
 	 
 	public void setHaveRead(long haveRead){
 		 this.haveRead=haveRead;
 	}
 	public long getHaveRead(){
 		 return this.haveRead;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractStatus(String abstractStatus){
 		 this.abstractStatus=abstractStatus;
 	}
 	public String getAbstractStatus(){
 		 return this.abstractStatus;
 	}
 
 	 
 	@CharacterVaild(len = 20) 
 	public void setOperator(String operator){
 		 this.operator=operator;
 	}
 	public String getOperator(){
 		 return this.operator;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setRecoverStatus(String recoverStatus){
 		 this.recoverStatus=recoverStatus;
 	}
 	public String getRecoverStatus(){
 		 return this.recoverStatus;
 	}
 
 	 
 	public void setOriginalPrice(double originalPrice){
 		 this.originalPrice=originalPrice;
 	}
 	public double getOriginalPrice(){
 		 return this.originalPrice;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setClosePayOrder(String closePayOrder){
 		 this.closePayOrder=closePayOrder;
 	}
 	public String getClosePayOrder(){
 		 return this.closePayOrder;
 	}
 
 	 
 	public void setStatusReturnCreatTime(Timestamp statusReturnCreatTime){
 		 this.statusReturnCreatTime=statusReturnCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusReturnCreatTime(){
 		 return this.statusReturnCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setChangeStatus(String changeStatus){
 		 this.changeStatus=changeStatus;
 	}
 	public String getChangeStatus(){
 		 return this.changeStatus;
 	}
 
 	 
 	public void setConfirmPaymentDate(Timestamp confirmPaymentDate){
 		 this.confirmPaymentDate=confirmPaymentDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getConfirmPaymentDate(){
 		 return this.confirmPaymentDate;
 	}
 
 	 
 	@CharacterVaild(len = 715827882) 
 	public void setSkuAttr(String skuAttr){
 		 this.skuAttr=skuAttr;
 	}
 	public String getSkuAttr(){
 		 return this.skuAttr;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setOrderNo(String orderNo){
 		 this.orderNo=orderNo;
 	}
 	public String getOrderNo(){
 		 return this.orderNo;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setStoreName(String storeName){
 		 this.storeName=storeName;
 	}
 	public String getStoreName(){
 		 return this.storeName;
 	}
 
 	 
 	public void setAbstractPaymentCreattime(Timestamp abstractPaymentCreattime){
 		 this.abstractPaymentCreattime=abstractPaymentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getAbstractPaymentCreattime(){
 		 return this.abstractPaymentCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setID(String iD){
 		 this.iD=iD;
 	}
 	public String getID(){
 		 return this.iD;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setPayAccount(String payAccount){
 		 this.payAccount=payAccount;
 	}
 	public String getPayAccount(){
 		 return this.payAccount;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProductName(String productName){
 		 this.productName=productName;
 	}
 	public String getProductName(){
 		 return this.productName;
 	}
 
 	 
 	public void setChangePriceDate(Timestamp changePriceDate){
 		 this.changePriceDate=changePriceDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getChangePriceDate(){
 		 return this.changePriceDate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContactTel(String contactTel){
 		 this.contactTel=contactTel;
 	}
 	public String getContactTel(){
 		 return this.contactTel;
 	}
 
 	 
 	public void setStatusServiceAppCreatTime(Timestamp statusServiceAppCreatTime){
 		 this.statusServiceAppCreatTime=statusServiceAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusServiceAppCreatTime(){
 		 return this.statusServiceAppCreatTime;
 	}
 
 	 
 	public void setComfirmAppDate(Timestamp comfirmAppDate){
 		 this.comfirmAppDate=comfirmAppDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getComfirmAppDate(){
 		 return this.comfirmAppDate;
 	}
 
 	 
 	public void setStatusRefundCreatTime(Timestamp statusRefundCreatTime){
 		 this.statusRefundCreatTime=statusRefundCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusRefundCreatTime(){
 		 return this.statusRefundCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setFileAuditPicInfo(String fileAuditPicInfo){
 		 this.fileAuditPicInfo=fileAuditPicInfo;
 	}
 	public String getFileAuditPicInfo(){
 		 return this.fileAuditPicInfo;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setCancelFlg(String cancelFlg){
 		 this.cancelFlg=cancelFlg;
 	}
 	public String getCancelFlg(){
 		 return this.cancelFlg;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusCancel(String statusCancel){
 		 this.statusCancel=statusCancel;
 	}
 	public String getStatusCancel(){
 		 return this.statusCancel;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCategoryName(String categoryName){
 		 this.categoryName=categoryName;
 	}
 	public String getCategoryName(){
 		 return this.categoryName;
 	}
 
 	 
 	public void setStatusCheckAppCreatTime(Timestamp statusCheckAppCreatTime){
 		 this.statusCheckAppCreatTime=statusCheckAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusCheckAppCreatTime(){
 		 return this.statusCheckAppCreatTime;
 	}
 
 	 
 	public void setStatusSubmitAppCreatTime(Timestamp statusSubmitAppCreatTime){
 		 this.statusSubmitAppCreatTime=statusSubmitAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusSubmitAppCreatTime(){
 		 return this.statusSubmitAppCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setReadStatus(String readStatus){
 		 this.readStatus=readStatus;
 	}
 	public String getReadStatus(){
 		 return this.readStatus;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyNameCN(String companyNameCN){
 		 this.companyNameCN=companyNameCN;
 	}
 	public String getCompanyNameCN(){
 		 return this.companyNameCN;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setPaymentStatus(String paymentStatus){
 		 this.paymentStatus=paymentStatus;
 	}
 	public String getPaymentStatus(){
 		 return this.paymentStatus;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setDistrict(String district){
 		 this.district=district;
 	}
 	public String getDistrict(){
 		 return this.district;
 	}
 
 	 
 	public void setPrice(double price){
 		 this.price=price;
 	}
 	public double getPrice(){
 		 return this.price;
 	}
 
 	 
 	public void setMailSend(long mailSend){
 		 this.mailSend=mailSend;
 	}
 	public long getMailSend(){
 		 return this.mailSend;
 	}
 
 	 
 	public void setStatusWaitPaymentCreatTime(Timestamp statusWaitPaymentCreatTime){
 		 this.statusWaitPaymentCreatTime=statusWaitPaymentCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusWaitPaymentCreatTime(){
 		 return this.statusWaitPaymentCreatTime;
 	}
 
 	 
 	public void setStatusPaymentDocumentCreatTime(Timestamp statusPaymentDocumentCreatTime){
 		 this.statusPaymentDocumentCreatTime=statusPaymentDocumentCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusPaymentDocumentCreatTime(){
 		 return this.statusPaymentDocumentCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setCancelReason(String cancelReason){
 		 this.cancelReason=cancelReason;
 	}
 	public String getCancelReason(){
 		 return this.cancelReason;
 	}
 
 	 
 	public void setUploadReportDate(Timestamp uploadReportDate){
 		 this.uploadReportDate=uploadReportDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getUploadReportDate(){
 		 return this.uploadReportDate;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractWaitPayment(String abstractWaitPayment){
 		 this.abstractWaitPayment=abstractWaitPayment;
 	}
 	public String getAbstractWaitPayment(){
 		 return this.abstractWaitPayment;
 	}
 
 	 
 	public void setStatusCancelAppCreatTime(Timestamp statusCancelAppCreatTime){
 		 this.statusCancelAppCreatTime=statusCancelAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusCancelAppCreatTime(){
 		 return this.statusCancelAppCreatTime;
 	}
 
 	 
 	public void setTotalPrice(double totalPrice){
 		 this.totalPrice=totalPrice;
 	}
 	public double getTotalPrice(){
 		 return this.totalPrice;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setProductID(String productID){
 		 this.productID=productID;
 	}
 	public String getProductID(){
 		 return this.productID;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setAbstractCode(String abstractCode){
 		 this.abstractCode=abstractCode;
 	}
 	public String getAbstractCode(){
 		 return this.abstractCode;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyAddress(String companyAddress){
 		 this.companyAddress=companyAddress;
 	}
 	public String getCompanyAddress(){
 		 return this.companyAddress;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setOrderStartTime(String orderStartTime){
 		 this.orderStartTime=orderStartTime;
 	}
 	public String getOrderStartTime(){
 		 return this.orderStartTime;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserID(String userID){
 		 this.userID=userID;
 	}
 	public String getUserID(){
 		 return this.userID;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setInspectionInfo(String inspectionInfo){
 		 this.inspectionInfo=inspectionInfo;
 	}
 	public String getInspectionInfo(){
 		 return this.inspectionInfo;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setUserName(String userName){
 		 this.userName=userName;
 	}
 	public String getUserName(){
 		 return this.userName;
 	}
 
 	 
 	public void setCancelDate(Timestamp cancelDate){
 		 this.cancelDate=cancelDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCancelDate(){
 		 return this.cancelDate;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusCheckApp(String statusCheckApp){
 		 this.statusCheckApp=statusCheckApp;
 	}
 	public String getStatusCheckApp(){
 		 return this.statusCheckApp;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setReportSendCc(String reportSendCc){
 		 this.reportSendCc=reportSendCc;
 	}
 	public String getReportSendCc(){
 		 return this.reportSendCc;
 	}
 
 	 
 	public void setAbstractWaitPaymentCreattime(Timestamp abstractWaitPaymentCreattime){
 		 this.abstractWaitPaymentCreattime=abstractWaitPaymentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getAbstractWaitPaymentCreattime(){
 		 return this.abstractWaitPaymentCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setConfirmAppBy(String confirmAppBy){
 		 this.confirmAppBy=confirmAppBy;
 	}
 	public String getConfirmAppBy(){
 		 return this.confirmAppBy;
 	}
 
 	 
 	public void setAbstractPaymentDocumentCreattime(Timestamp abstractPaymentDocumentCreattime){
 		 this.abstractPaymentDocumentCreattime=abstractPaymentDocumentCreattime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getAbstractPaymentDocumentCreattime(){
 		 return this.abstractPaymentDocumentCreattime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractPaymentDocument(String abstractPaymentDocument){
 		 this.abstractPaymentDocument=abstractPaymentDocument;
 	}
 	public String getAbstractPaymentDocument(){
 		 return this.abstractPaymentDocument;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusPaymentDocument(String statusPaymentDocument){
 		 this.statusPaymentDocument=statusPaymentDocument;
 	}
 	public String getStatusPaymentDocument(){
 		 return this.statusPaymentDocument;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setAbstractPayment(String abstractPayment){
 		 this.abstractPayment=abstractPayment;
 	}
 	public String getAbstractPayment(){
 		 return this.abstractPayment;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setChangePriceComments(String changePriceComments){
 		 this.changePriceComments=changePriceComments;
 	}
 	public String getChangePriceComments(){
 		 return this.changePriceComments;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusReturn(String statusReturn){
 		 this.statusReturn=statusReturn;
 	}
 	public String getStatusReturn(){
 		 return this.statusReturn;
 	}
 
 	 
 	@CharacterVaild(len = 200) 
 	public void setEnName(String enName){
 		 this.enName=enName;
 	}
 	public String getEnName(){
 		 return this.enName;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setSubtitle(String subtitle){
 		 this.subtitle=subtitle;
 	}
 	public String getSubtitle(){
 		 return this.subtitle;
 	}
 
 	 
 	public void setIsTicket(int isTicket){
 		 this.isTicket=isTicket;
 	}
 	public int getIsTicket(){
 		 return this.isTicket;
 	}
 
 	 
 	public void setQuantity(long quantity){
 		 this.quantity=quantity;
 	}
 	public long getQuantity(){
 		 return this.quantity;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setLeadsCode(String leadsCode){
 		 this.leadsCode=leadsCode;
 	}
 	public String getLeadsCode(){
 		 return this.leadsCode;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setExpressInfo(String expressInfo){
 		 this.expressInfo=expressInfo;
 	}
 	public String getExpressInfo(){
 		 return this.expressInfo;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusService(String statusService){
 		 this.statusService=statusService;
 	}
 	public String getStatusService(){
 		 return this.statusService;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setContactName(String contactName){
 		 this.contactName=contactName;
 	}
 	public String getContactName(){
 		 return this.contactName;
 	}
 
 	 
 	public void setConfirmDate(Timestamp confirmDate){
 		 this.confirmDate=confirmDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getConfirmDate(){
 		 return this.confirmDate;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setContactEmail(String contactEmail){
 		 this.contactEmail=contactEmail;
 	}
 	public String getContactEmail(){
 		 return this.contactEmail;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setModifiedBy(String modifiedBy){
 		 this.modifiedBy=modifiedBy;
 	}
 	public String getModifiedBy(){
 		 return this.modifiedBy;
 	}
 
 	 
 	public void setStatusFinishAppCreatTime(Timestamp statusFinishAppCreatTime){
 		 this.statusFinishAppCreatTime=statusFinishAppCreatTime;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getStatusFinishAppCreatTime(){
 		 return this.statusFinishAppCreatTime;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusWaitPayment(String statusWaitPayment){
 		 this.statusWaitPayment=statusWaitPayment;
 	}
 	public String getStatusWaitPayment(){
 		 return this.statusWaitPayment;
 	}
 
 	 
 	public void setIsMonthPay(int isMonthPay){
 		 this.isMonthPay=isMonthPay;
 	}
 	public int getIsMonthPay(){
 		 return this.isMonthPay;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setProvince(String province){
 		 this.province=province;
 	}
 	public String getProvince(){
 		 return this.province;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setAbstractCustCode(String abstractCustCode){
 		 this.abstractCustCode=abstractCustCode;
 	}
 	public String getAbstractCustCode(){
 		 return this.abstractCustCode;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setParentID(String parentID){
 		 this.parentID=parentID;
 	}
 	public String getParentID(){
 		 return this.parentID;
 	}
 
 	 
 	@CharacterVaild(len = 100) 
 	public void setCity(String city){
 		 this.city=city;
 	}
 	public String getCity(){
 		 return this.city;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusSubmitApp(String statusSubmitApp){
 		 this.statusSubmitApp=statusSubmitApp;
 	}
 	public String getStatusSubmitApp(){
 		 return this.statusSubmitApp;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setOfflineStatus(String offlineStatus){
 		 this.offlineStatus=offlineStatus;
 	}
 	public String getOfflineStatus(){
 		 return this.offlineStatus;
 	}
 
 	 
 	public void setModifiedDate(Timestamp modifiedDate){
 		 this.modifiedDate=modifiedDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getModifiedDate(){
 		 return this.modifiedDate;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatus(String status){
 		 this.status=status;
 	}
 	public String getStatus(){
 		 return this.status;
 	}
 
 	 
 	@CharacterVaild(len = 0) 
 	public void setFileName(String fileName){
 		 this.fileName=fileName;
 	}
 	public String getFileName(){
 		 return this.fileName;
 	}
 
 	 
 	@CharacterVaild(len = 500) 
 	public void setCompanyNameEN(String companyNameEN){
 		 this.companyNameEN=companyNameEN;
 	}
 	public String getCompanyNameEN(){
 		 return this.companyNameEN;
 	}
 
 	 
 	public void setSmsSend(long smsSend){
 		 this.smsSend=smsSend;
 	}
 	public long getSmsSend(){
 		 return this.smsSend;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setStoreID(String storeID){
 		 this.storeID=storeID;
 	}
 	public String getStoreID(){
 		 return this.storeID;
 	}
 
 	 
 	public void setOrderFinishTime(Date orderFinishTime){
 		 this.orderFinishTime=orderFinishTime;
 	}
 	@JsonSerialize(using = DateFormatSerializer.class) 
 	public Date getOrderFinishTime(){
 		 return this.orderFinishTime;
 	}
 
 	 
 	@CharacterVaild(len = 50) 
 	public void setCreatedBy(String createdBy){
 		 this.createdBy=createdBy;
 	}
 	public String getCreatedBy(){
 		 return this.createdBy;
 	}
 
 	 
 	public void setCreatedDate(Timestamp createdDate){
 		 this.createdDate=createdDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getCreatedDate(){
 		 return this.createdDate;
 	}
 
 	 
 	public void setPayAmount(double payAmount){
 		 this.payAmount=payAmount;
 	}
 	public double getPayAmount(){
 		 return this.payAmount;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setCloseStatus(String closeStatus){
 		 this.closeStatus=closeStatus;
 	}
 	public String getCloseStatus(){
 		 return this.closeStatus;
 	}
 
 	 
 	@CharacterVaild(len = 10) 
 	public void setStatusFinish(String statusFinish){
 		 this.statusFinish=statusFinish;
 	}
 	public String getStatusFinish(){
 		 return this.statusFinish;
 	}
 
 	 
 	@CharacterVaild(len = 1000) 
 	public void setRemark(String remark){
 		 this.remark=remark;
 	}
 	public String getRemark(){
 		 return this.remark;
 	}
 
 	 
 	public void setPayDate(Timestamp payDate){
 		 this.payDate=payDate;
 	}
 	@JsonSerialize(using = TimeFormatSerializer.class) 
 	public Timestamp getPayDate(){
 		 return this.payDate;
 	}
 
 	 
}