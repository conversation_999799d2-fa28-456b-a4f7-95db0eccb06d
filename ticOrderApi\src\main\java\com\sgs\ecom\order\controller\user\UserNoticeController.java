package com.sgs.ecom.order.controller.user;

import com.sgs.ecom.order.vo.VOUserNoticeBatch;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.platform.annotation.AuthRequired;
import com.sgs.ecom.order.service.user.interfaces.IUserNoticeSV;
import com.sgs.ecom.order.util.ControllerUtil;
import com.sgs.ecom.order.util.ResultBody;
import com.sgs.ecom.order.vo.VOUserNotice;

@RestController
@RequestMapping("/business/api.v1.user/notice")
public class UserNoticeController extends ControllerUtil {
	
	@Resource
	private IUserNoticeSV userNoticeSV;

	/**   
	* @Function: setUserNotice
	* @Description: 设置用户提醒
	* 
	* @param: userNotice
	* @return: ResultBody
	* 
	* @version: 1.0
	* @author: shenyi
	* @date: 2022-11-17
	* 
	* Modification History: 
	* Date         Author          Version            Description 
	*---------------------------------------------------------* 
	* 修改时间                          修改人                     版本                 修改原因
	* 2022-11-17  shenyi    v1.0                 新增
	*/
    @AuthRequired(login = "SGS", sign = true)
    @RequestMapping(value = "set", method = { RequestMethod.POST })
    public ResultBody setUserNotice(@RequestBody VOUserNotice userNotice) throws Exception {
    	userNoticeSV.setUserNotice(userNotice);
		return ResultBody.success();
	}

	/**
	 * @Function: setUserNotice
	 * @Description: 批量设置用户提醒
	 *
	 * @param: userNotice
	 * @return: ResultBody
	 *
	 * @version: 1.0
	 * @author: huangting.lu
	 * @date: 2025-07-09
	 *
	 * Modification History:
	 * Date         Author          Version            Description
	 *---------------------------------------------------------*
	 * 修改时间                          修改人                     版本                 修改原因
	 * 2025-07-09  huangting.lu    v1.0                 新增
	 */
	@AuthRequired(login = "SGS", sign = true)
	@RequestMapping(value = "batchSetByBossNo", method = { RequestMethod.POST })
	public ResultBody batchSetUserNotice(@RequestBody VOUserNoticeBatch UserNoticeBatch) throws Exception {
		userNoticeSV.batchSetUserNoticeByBossNo(UserNoticeBatch);
		return ResultBody.success();
	}
}
