package com.sgs.ecom.order.base;

import com.sgs.base.BaseQryFilter;

public class BaseOrderFilter extends BaseQryFilter {
    public BaseOrderFilter() {
    }

    public interface OrderDetail {

    }
    public interface InquiryDetail {

    }
    public interface RstsDetail {

    }
    public interface TicList {

    }

    public interface TicDetail {

    }

    public interface InquiryList {

    }

    public interface TicForm {

    }

    public interface RstsList {

    }
    public interface SampleAndReportList {

    }
    public interface OrderQryList {

    }
    public interface OrderQryList1 {

    }
    public interface OrderStateNumList {

    }

    public interface OiqFormInfo {

    }

    public interface CustMarkList {

    }

    public interface PayToOther {

    }
    public interface OrderToOther {

    }
    public interface ExpressToOther {

    }

}
